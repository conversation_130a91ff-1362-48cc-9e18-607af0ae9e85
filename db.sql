SELECT 
    (SELECT MAX(t.RecordYearMonth) FROM F_CombineAll t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.CalculateDate = (SELECT MAX(_t.CalculateDate) FROM F_CombineAll _t) AND t.CarbonAmount > 0 AND t.CarbonAmount IS NOT NULL) AS LastRecordMonth,  
    (SELECT MAX(t.RecordYearMonth) 
	FROM Tzh_EmissionReductionHead h 
	LEFT JOIN Tzh_EmissionReduction t ON t.IsDeleted = 0 AND t.HeadId = h.Id 
	WHERE h.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' 
	AND h.IsDeleted = 0 
	AND t.CarbonReductionAmount > 0 AND t.CarbonReductionAmount IS NOT NULL) AS LastReductionRecordMonth,  
    (SELECT SUM(ISNULL(t.CarbonAmount, 0))/1000 FROM F_CombineAll t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.CarbonEmissionLocation = N'地盤' AND t.CalculateDate = (SELECT max(CalculateDate) FROM F_CombineAll)) AS CarbonAmountSite,   
    (SELECT SUM(ISNULL(t.CarbonAmount, 0))/1000 FROM F_CombineAll t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.CarbonEmissionLocation = N'辦公室' AND t.CalculateDate = (SELECT max(CalculateDate) FROM F_CombineAll)) AS CarbonAmountOffice,   
    (SELECT SUM(ISNULL(t.CarbonAmount, 0))/1000 FROM F_CombineAll t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND CalculateDate = (SELECT max(CalculateDate) FROM F_CombineAll)) AS CarbonAmountTotal,   
    (SELECT SUM(ISNULL(t.CarbonAmount, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.IsDeleted <> 1 AND t.CarbonEmissionLocation = N'地盤') AS ExpectedCarbonAmountSite,   
    (SELECT SUM(ISNULL(t.CarbonAmount, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.IsDeleted <> 1 AND t.CarbonEmissionLocation = N'辦公室') AS ExpectedCarbonAmountOffice,   
    (SELECT SUM(ISNULL(t.CarbonAmount, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.IsDeleted <> 1) AS ExpectedCarbonAmountTotal,   
    (SELECT SUM(ISNULL(t.CarbonAmountUnderMeasure, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.IsDeleted <> 1 AND t.CarbonEmissionLocation = N'地盤') AS ExpectedCarbonAmountUnderMeasureSite,   
    (SELECT SUM(ISNULL(t.CarbonAmountUnderMeasure, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.IsDeleted <> 1 AND t.CarbonEmissionLocation = N'辦公室') AS ExpectedCarbonAmountUnderMeasureOffice,   
    (SELECT SUM(ISNULL(t.CarbonAmountUnderMeasure, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.IsDeleted <> 1) AS ExpectedCarbonAmountUnderMeasureTotal,   
    (SELECT SUM(ISNULL(t.CarbonReductionAmount, 0))/1000 FROM (  
    SELECT h.SiteName, h.CarbonEmissionLocation, t.CarbonReductionAmount  
    FROM [Tzh_EmissionReductionHead] h  
    LEFT JOIN  [Tzh_EmissionReduction] t ON t.headId = h.id AND t.IsDeleted <> 1
    WHERE h.IsDeleted <> 1  
    ) t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.CarbonEmissionLocation = N'地盤') AS CarbonReductionAmountSite,  
    (SELECT SUM(ISNULL(t.CarbonReductionAmount, 0))/1000 FROM (  
    SELECT h.SiteName, h.CarbonEmissionLocation, t.CarbonReductionAmount  
    FROM [Tzh_EmissionReductionHead] h  
    LEFT JOIN  [Tzh_EmissionReduction] t ON t.headId = h.id AND t.IsDeleted <> 1
    WHERE h.IsDeleted <> 1  
    ) t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.CarbonEmissionLocation = N'辦公室') AS CarbonReductionAmountOffice,  
    (SELECT SUM(ISNULL(t.CarbonReductionAmount, 0))/1000 FROM (  
    SELECT h.SiteName, h.CarbonEmissionLocation, t.CarbonReductionAmount  
    FROM [Tzh_EmissionReductionHead] h  
    LEFT JOIN  [Tzh_EmissionReduction] t ON t.headId = h.id AND t.IsDeleted <> 1  
    WHERE h.IsDeleted <> 1  
    ) t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心') AS CarbonReductionAmountTotal 


	
--減排管理>減排填表 表一
SELECT 
(SELECT MAX(t.RecordYearMonth) FROM F_CombineAll t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.CalculateDate = (SELECT MAX(_t.CalculateDate) FROM F_CombineAll _t) AND t.CarbonAmount > 0 AND t.CarbonAmount IS NOT NULL) AS LastRecordMonth, 
(SELECT SUM(ISNULL(t.CarbonAmount, 0))/1000 FROM F_CombineAll t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.CarbonEmissionLocation = N'地盘' AND t.CalculateDate = (SELECT max(CalculateDate) FROM F_CombineAll)) AS CarbonAmountSite, 
(SELECT SUM(ISNULL(t.CarbonAmount, 0))/1000 FROM F_CombineAll t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.CarbonEmissionLocation = N'办公室' AND t.CalculateDate = (SELECT max(CalculateDate) FROM F_CombineAll)) AS CarbonAmountOffice, 
(SELECT SUM(ISNULL(t.CarbonAmount, 0))/1000 FROM F_CombineAll t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND CalculateDate = (SELECT max(CalculateDate) FROM F_CombineAll)) AS CarbonAmountTotal, 
(SELECT SUM(ISNULL(t.CarbonAmount, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.CarbonEmissionLocation = N'地盤') AS ExpectedCarbonAmountSite, 
(SELECT SUM(ISNULL(t.CarbonAmount, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.CarbonEmissionLocation = N'辦公室') AS ExpectedCarbonAmountOffice, 
(SELECT SUM(ISNULL(t.CarbonAmount, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心') AS ExpectedCarbonAmountTotal, 
(SELECT SUM(ISNULL(t.CarbonAmountUnderMeasure, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.CarbonEmissionLocation = N'地盤') AS ExpectedCarbonAmountUnderMeasureSite, 
(SELECT SUM(ISNULL(t.CarbonAmountUnderMeasure, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.CarbonEmissionLocation = N'辦公室') AS ExpectedCarbonAmountUnderMeasureOffice, 
(SELECT SUM(ISNULL(t.CarbonAmountUnderMeasure, 0)) FROM Tzh_ExpectedEmissionIso t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心') AS ExpectedCarbonAmountUnderMeasureTotal, 
(SELECT SUM(ISNULL(t.CarbonReductionAmount, 0))/1000 FROM ( 
SELECT h.SiteName, h.CarbonEmissionLocation, t.CarbonReductionAmount 
FROM [Tzh_EmissionReductionHead] h 
LEFT JOIN  [Tzh_EmissionReduction] t ON t.headId = h.id AND t.isDeleted <> 1 
WHERE h.isDeleted <> 1 
) t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.CarbonEmissionLocation = N'地盤') AS CarbonReductionAmountSite, 
(SELECT SUM(ISNULL(t.CarbonReductionAmount, 0))/1000 FROM ( 
SELECT h.SiteName, h.CarbonEmissionLocation, t.CarbonReductionAmount 
FROM [Tzh_EmissionReductionHead] h 
LEFT JOIN  [Tzh_EmissionReduction] t ON t.headId = h.id AND t.isDeleted <> 1 
WHERE h.isDeleted <> 1 
) t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' AND t.CarbonEmissionLocation = N'辦公室') AS CarbonReductionAmountOffice, 
(SELECT SUM(ISNULL(t.CarbonReductionAmount, 0))/1000 FROM ( 
SELECT h.SiteName, h.CarbonEmissionLocation, t.CarbonReductionAmount 
FROM [Tzh_EmissionReductionHead] h 
LEFT JOIN  [Tzh_EmissionReduction] t ON t.headId = h.id AND t.isDeleted <> 1 
WHERE h.isDeleted <> 1 
) t WHERE t.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心') AS CarbonReductionAmountTotal 

--減排管理>減排填表 表二
SELECT t.Id, h.SiteName, h.ScopeMain, h.ScopeDetail, h.CarbonEmissionLocation, t.RecordYearMonth, 
t.CarbonReductionAmount, t.CarbonUnit, t.IsDeleted FROM [Tzh_EmissionReductionHead] h 
LEFT JOIN  [Tzh_EmissionReduction] t ON t.headId = h.id AND t.isDeleted <> 1 
WHERE h.isDeleted <> 1 
AND h.SiteName = N'設計及建造將軍澳中醫院及政府中藥檢測中心' 
AND t.RecordYearMonth >= 202211 AND t.RecordYearMonth <= 202212 


--物資數量展示(來源自CDMS)
SELECT d.Region AS region, d.SiteName AS sitename, d.RecordYearMonth AS recordyearmonth, d.CarbonEmissionLocation AS carbonemissionlocation, d.MaterialCode AS materialcode, 
d.ChineseName AS chinesename, d.Unit AS unit, ISNULL(d.Description, '') AS description, d.CarbonFactor AS carbonfactor, d.CarbonFactorUnit AS carbonfactorunit, 
t.TransportFactor AS transportfactor, ISNULL(t.TransportFactorUnit, '') AS transportfactorunit, t.TransportDistance AS transportdistance, ISNULL(t.TransportDistanceUnit, '') AS transportdistanceunit, 
d.Qty AS qty, d.CarbonAmount AS carbonamount, d.Scope AS scope, ISNULL(d.TransportScope, '') AS transportscope, d.BillNo AS billno, 
ISNULL(t.CreatedBy, '') AS createdby, ISNULL(t.CreatedTime, '') AS createdtime, t.IsDeleted AS isdeleted 
FROM ( 
SELECT _d.Region, _d.SiteName, _d.RecordYearMonth, _d.CarbonEmissionLocation, _d.MaterialCode, 
_d.ChineseName, _d.Unit, _d.Description, _d.CarbonFactor, _d.CarbonFactorUnit, 
SUM(ISNULL(_d.Qty, 0)) AS Qty, SUM(ISNULL(_d.CarbonAmount, 0)) AS CarbonAmount, _d.Scope, _d.TransportScope, _d.BillNo 
FROM F_Material_Detail _d 
WHERE _d.CalculateDate = (SELECT MAX(CalculateDate) FROM F_Material_Detail) 
GROUP BY _d.Region, _d.SiteName, _d.RecordYearMonth, _d.CarbonEmissionLocation, _d.MaterialCode, 
_d.ChineseName, _d.Unit, _d.Description, _d.CarbonFactor, _d.CarbonFactorUnit, 
_d.Scope, _d.TransportScope, _d.BillNo 
) d 
LEFT JOIN Tzh_MaterialInvoiceTransport t 
ON d.BillNo = t.BillNo 
AND d.MaterialCode = t.MaterialCode 
AND d.SiteName = t.SiteName 
AND t.IsDeleted <> 1 
WHERE d.SiteName LIKE N'設計及建造將軍澳中醫院及政府中藥檢測中心' 
AND d.RecordYearMonth >= 202106 AND d.RecordYearMonth <= 202106 
ORDER BY d.RecordYearMonth, d.SiteName, d.MaterialCode, d.ChineseName 


--獲取付辦單id
SELECT TOP 1 Id FROM purPayment 
WHERE BillNo IN ( 
SELECT TOP 1 _t.FubandanNo FROM F_Material_Detail _t 
WHERE _t.CalculateDate =(SELECT max(CalculateDate) FROM F_Material_Detail) 
AND _t.BillNo = N'K493449'
) 
AND IsDeleted = 0 

--獲取Scope Main列表
SELECT DISTINCT ScopeMain FROM D_Scope WHERE Protocol = N'GB 55015' 

--獲取Scope Detail列表
SELECT DISTINCT ScopeDetail FROM D_Scope WHERE Protocol = N'GB 55015' 