-- wstDecorMaterialWastage 装饰材料损耗（地盘自己填写）
  select ORG.Code, ORG.Name, G.MaterialName,Total_AttritionRate,TotalCompletedQty,损耗率 = 100.0*Total_AttritionRate/TotalCompletedQty
    from wstDecorMaterialWastage MW
        join sysOrganization ORG on MW.DepartmentId=ORG.Id
        join (
        -- wstDecorMaterialWastageDetail 装饰材料损耗明细
        -- MaterialName装饰材料名称 UsedQty是使用数量，CompletedQty是完成数量
      select MWD.WastageId, MWD.MaterialName, SUM((MWD.UsedQty-MWD.CompletedQty)/(MWD.UsedQty+0.001)*MWD.CompletedQty) Total_AttritionRate
        from wstDecorMaterialWastageDetail MWD
        group by MWD.WastageId, MWD.MaterialName
        ) as G on G.WastageId=MW.Id
        join (
          select WastageId, MaterialName, sum(CompletedQty) TotalCompletedQty from wstDecorMaterialWastageDetail
            group by WastageId, MaterialName
        ) as T on G.WastageId=T.WastageId and G.MaterialName=T.MaterialName
        where G.MaterialName in ('瓦仔', '瓦仔膠')
      and MW.IsDeleted=0 
          and G.WastageId in
       (
           -- 这里很明显是想限定ORG.Code，但是我们在测试的时候最好放开
         select max(MW.Id)
           from wstDecorMaterialWastage MW
           join sysOrganization ORG on MW.DepartmentId=ORG.Id
           where MW.IsDeleted=0 --and ORG.Code in ('BID', 'BLD', 'BJX')
               group by MW.DepartmentId
       )

select ORG.Code, ORG.Name, MaterialClass, 
	-- WS.contractQty表示合同数量，WS.DeliverQty表示已供货数量
	-- 自然，我们理解，已经供货数量除以合同数量就是物料维度的进度，二者相等则表示
	   供货进度 = iif(WS.contractQty=0, null, 100.0*WS.DeliverQty/WS.ContractQty),
	-- FlotsamSelled表示废料出售的数量， DeliverQty表示合同数量，PrepareQty表示备料数量
	-- 自然，我们仅仅需要的物料数量就是合同数量减去备料数量，即我们采购的数量，而废料则是有问题的物料，数量相除即可
       损耗率1 = 100*[FlotsamSelled]/([DeliverQty]-[PrepareQty]+0.001),
	-- DeliverQty表示合同数量，PrepareQty表示备料数量， C.TotalCompletedQty表示分盘工程完工数量， M表示非主要工程的完工数量
       损耗率2 = 100*([DeliverQty]-[PrepareQty]-C.[TotalCompletedQty]-M.[TotalCompletedQty])/([DeliverQty]-[PrepareQty]-M.[TotalCompletedQty]+0.001),
	-- WorkType是工程类型，2的含义不清楚，1表示一般工程，0表示连工包料的外包工程（0是供应商自己负责采购）
       修订损耗率 = IIf([WorkType]=2,100*([DeliverQty]-[PrepareQty]-C.[TotalCompletedQty]-M.TotalCompletedQty)/([DeliverQty]-[PrepareQty]-M.TotalCompletedQty+0.001), (100*[FlotsamSelled]/([DeliverQty]-[PrepareQty]+0.001)+100*([DeliverQty]-[PrepareQty]-C.[TotalCompletedQty]-M.TotalCompletedQty)/([DeliverQty]-[PrepareQty]-M.TotalCompletedQty+0.001))/2),
	   *
  from wstBulkMaterialWastageSum WS
  join wstBulkMaterialWastage MW on WS.WastageId=MW.Id
  join sysOrganization ORG on MW.DepartmentId=ORG.Id
  left join(
  --wstBulkMaterialCompleted分判工程完成量（地盘自己填写）， CompletedQty表示完工数量
    select SumId, sum(CompletedQty) TotalCompletedQty
	  from wstBulkMaterialCompleted
	  where IsDeleted=0
	  group by SumId
  ) as C on WS.Id=C.SumId
  left join(
  --wstBulkMaterialWorkingMeasure非主要工程用量，  CompletedQty表示完工数量
    select SumId, sum(CompletedQty) TotalCompletedQty
	  from wstBulkMaterialWorkingMeasure
	  where IsDeleted=0
	  group by SumId
  ) as M on WS.Id=M.SumId
  where WS.WastageId in 
  (
  -- 这里很明显是想限定ORG.Code，但是我们在测试的时候最好放开
    select max(MW.Id)
      from wstBulkMaterialWastage MW
      join sysOrganization ORG on MW.DepartmentId=ORG.Id
      where MW.IsDeleted=0 --and ORG.Code in ('BID', 'BLD', 'BJX')
	  group by MW.DepartmentId
  )