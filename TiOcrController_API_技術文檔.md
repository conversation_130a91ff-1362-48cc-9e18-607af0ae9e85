# TiOcrController API 技術文檔

## 概述

`TiOcrController` 是一個基於 Spring Boot 的 REST 控制器，專門用於處理 OCR（光學字符識別）相關的 API 請求。該控制器主要負責解析各種類型的賬單和票據，包括水費單、電費單、火車票和機票等文檔。

## 控制器基本信息

- **包路徑**: `com.csci.susdev.controller.TiOcrController`
- **基礎路徑**: `/api/tiOcr`
- **響應格式**: `application/json`
- **功能標籤**: TiOCR 接口展示
- **日誌記錄**: 使用 `@LogMethod` 註解進行方法級別的日誌記錄

## API 詳細說明

### 1. 水費單解析 API

#### 接口信息
- **路徑**: `POST /api/tiOcr/analysisWaterBillInfo`
- **描述**: 解析水費數據
- **請求格式**: `multipart/form-data`
- **響應格式**: `application/json`

#### 實現原理

<augment_code_snippet path="src/main/java/com/csci/susdev/controller/TiOcrController.java" mode="EXCERPT">
````java
@PostMapping(value = "/analysisWaterBillInfo", produces = "application/json", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
@Operation(description = "解析水费数据")
@Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
@ApiResponse(responseCode = "200")
public ResultBean<TiOcrEnergyBillListVO> analysisWaterBillInfo(@RequestPart("file") MultipartFile file)
        throws FileProcessingException, OcrAnalysisException, JsonProcessingException {
    String json = tiOcrService.convertFileToStr(file);
    return new ResultBean<>(tiOcrService.analysisTiOcrEnergyBillInfoByAi(json, "水費單"));
}
````
</augment_code_snippet>

#### 處理流程

1. **文件接收與驗證**
   - 接收 `MultipartFile` 類型的文件參數
   - 驗證文件是否為空、大小是否超過限制（25MB）
   - 支持的文件格式：JPG、JPEG、PNG、PDF

2. **文件轉換處理**
   - 調用 `tiOcrService.convertFileToStr(file)` 方法
   - 將文件轉換為 Base64 編碼
   - 如果是 PDF 文件，先轉換為 JPG 格式再編碼

3. **OCR 識別**
   - 使用 TiOCR 服務進行文字識別
   - 調用第三方 OCR API 獲取結構化文本數據
   - 處理識別結果並格式化為可解析的字符串

4. **AI 智能解析**
   - 調用 `tiOcrService.analysisTiOcrEnergyBillInfoByAi(json, "水費單")`
   - 使用 HiAgent AI 服務進行智能數據提取
   - 提取水費單的關鍵信息：帳單號、起始日期、結束日期、用量

5. **結果封裝**
   - 將解析結果封裝為 `TiOcrEnergyBillListVO` 對象
   - 使用 `ResultBean` 統一響應格式返回

### 2. 電費單解析 API

#### 接口信息
- **路徑**: `POST /api/tiOcr/analysisElectricityBillInfo`
- **描述**: 解析電費數據
- **請求格式**: `multipart/form-data`
- **響應格式**: `application/json`

#### 實現原理

<augment_code_snippet path="src/main/java/com/csci/susdev/controller/TiOcrController.java" mode="EXCERPT">
````java
@PostMapping(value = "/analysisElectricityBillInfo", produces = "application/json", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
@Operation(description = "解析电费数据")
@Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
@ApiResponse(responseCode = "200")
public ResultBean<TiOcrEnergyBillListVO> analysisElectricityBillInfo(@RequestPart("file") MultipartFile file)
        throws FileProcessingException, OcrAnalysisException, JsonProcessingException {
    String json = tiOcrService.convertFileToStr(file);
    return new ResultBean<>(tiOcrService.analysisTiOcrEnergyBillInfoByAi(json, "電費單"));
}
````
</augment_code_snippet>

#### 處理流程
與水費單解析流程基本相同，區別在於：
- 傳遞給 AI 解析服務的類型參數為 "電費單"
- AI 會根據電費單的特定格式進行數據提取
- 識別電費單特有的字段和格式模式

### 3. 商務旅行-火車票解析 API

#### 接口信息
- **路徑**: `POST /api/tiOcr/analysisBusinessTripTrainInfo`
- **描述**: 解析商務旅行-火車數據
- **請求格式**: `multipart/form-data`
- **響應格式**: `application/json`

#### 實現原理

<augment_code_snippet path="src/main/java/com/csci/susdev/controller/TiOcrController.java" mode="EXCERPT">
````java
@PostMapping(value = "/analysisBusinessTripTrainInfo", produces = "application/json", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
@Operation(description = "解析商务旅行-火车数据")
@Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
@ApiResponse(responseCode = "200")
public ResultBean<TiOcrBusinessTripListVO> analysisBusinessTripTrainInfo(@RequestPart("file") MultipartFile file)
        throws FileProcessingException, OcrAnalysisException, JsonProcessingException {
    String json = tiOcrService.convertFileToStr(file);
    return new ResultBean<>(tiOcrService.analysisBusinessTripInfoByAi(json, "車票"));
}
````
</augment_code_snippet>

#### 處理流程

1. **文件處理階段**（與前述相同）
   - 文件驗證、轉換、OCR 識別

2. **商務旅行數據解析**
   - 調用 `tiOcrService.analysisBusinessTripInfoByAi(json, "車票")`
   - 使用專門的商務旅行 AI 模型（app=2）
   - 提取火車票特定信息：起始地、目的地、座位等級、月份

3. **結果封裝**
   - 返回 `TiOcrBusinessTripListVO` 對象
   - 包含火車票的詳細信息

### 4. 商務旅行-機票解析 API

#### 接口信息
- **路徑**: `POST /api/tiOcr/analysisBusinessTripAirInfo`
- **描述**: 解析商務旅行-飛機數據
- **請求格式**: `multipart/form-data`
- **響應格式**: `application/json`

#### 實現原理

<augment_code_snippet path="src/main/java/com/csci/susdev/controller/TiOcrController.java" mode="EXCERPT">
````java
@PostMapping(value = "/analysisBusinessTripAirInfo", produces = "application/json", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
@Operation(description = "解析商务旅行-飞机数据")
@Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
@ApiResponse(responseCode = "200")
public ResultBean<TiOcrBusinessTripListVO> analysisBusinessTripAirInfo(@RequestPart("file") MultipartFile file)
        throws FileProcessingException, OcrAnalysisException, JsonProcessingException {
    String json = tiOcrService.convertFileToStr(file);
    return new ResultBean<>(tiOcrService.analysisBusinessTripInfoByAi(json, "機票"));
}
````
</augment_code_snippet>

#### 處理流程
與火車票解析類似，但針對機票的特定格式：
- 傳遞類型參數為 "機票"
- AI 識別機票特有的字段格式
- 提取航班相關的商務旅行信息

## 核心服務調用詳解

### TiOcrService.convertFileToStr() 方法

該方法負責將上傳的文件轉換為可供 OCR 處理的字符串：

1. **文件驗證**
   - 檢查文件是否為空
   - 驗證文件大小（最大 25MB）
   - 驗證文件類型（支持 JPG、JPEG、PNG、PDF）

2. **文件轉換**
   - PDF 文件：使用 PDFBox 轉換為 JPG 圖像
   - 圖像文件：直接轉換為 Base64 編碼

3. **OCR 處理**
   - 調用 TiOCR API 進行文字識別
   - 處理識別結果，按坐標排序並格式化文本

### AI 解析服務調用

#### 能源賬單解析 (analysisTiOcrEnergyBillInfoByAi)

1. **會話管理**
   - 從 Redis 緩存獲取或創建新的 AI 會話
   - 使用 app=3 的 AI 模型

2. **查詢構建**
   - 構建特定的查詢語句：`"提取" + type + "帳單號、起始日期、結束日期、用量"`
   - 將 OCR 結果附加到查詢中

3. **AI 處理**
   - 發送查詢到 HiAgent 服務
   - 獲取消息 ID 並等待處理結果
   - 解析 JSON 響應為 `TiOcrEnergyBillListVO` 對象

#### 商務旅行解析 (analysisBusinessTripInfoByAi)

1. **會話管理**
   - 使用 app=2 的專門商務旅行 AI 模型
   - 管理獨立的會話上下文

2. **查詢構建**
   - 構建查詢：`"提取" + type + "起始地、目的地、座位等级、月份"`

3. **結果處理**
   - 解析為 `TiOcrBusinessTripListVO` 對象
   - 包含旅行相關的詳細信息

## 異常處理機制

### 異常類型

1. **FileProcessingException**
   - 文件處理相關異常
   - 包括文件為空、格式不支持、大小超限等

2. **OcrAnalysisException**
   - OCR 分析相關異常
   - 包括 OCR 識別失敗、AI 分析失敗等

3. **JsonProcessingException**
   - JSON 處理異常
   - AI 響應解析失敗時拋出

### 錯誤處理流程

1. **文件驗證階段**
   - 捕獲文件相關異常並轉換為業務異常
   - 提供詳細的錯誤信息

2. **OCR 處理階段**
   - 處理 OCR 服務調用失敗
   - 驗證 OCR 結果的有效性

3. **AI 解析階段**
   - 處理 AI 服務調用異常
   - 驗證解析結果的完整性

## 安全性考慮

### 認證授權
- 所有 API 都需要在 Header 中提供有效的 Token
- 使用 `SusDevConsts.HEADER_TOKEN_KEY` 進行身份驗證

### 文件安全
- 限制文件大小（25MB）
- 限制文件類型（僅支持圖像和 PDF）
- 文件內容驗證和清理

### 數據隱私
- 敏感數據處理遵循隱私保護原則
- OCR 結果和 AI 分析結果的安全存儲

## 性能優化

### 緩存機制
- 使用 Redis 緩存 AI 會話信息
- 避免重複創建會話，提高響應速度

### 異步處理
- OCR 和 AI 處理採用異步模式
- 通過消息 ID 追蹤處理狀態

### 資源管理
- 及時釋放文件處理相關資源
- 優化內存使用，避免大文件導致的內存溢出

## 監控與日誌

### 日誌記錄
- 使用 `@LogMethod` 註解記錄方法調用
- 詳細記錄 OCR 和 AI 處理過程
- 包含會話 ID 和處理時間等關鍵信息

### 性能監控
- 監控 API 響應時間
- 追蹤文件處理成功率
- 監控 AI 服務調用狀態

## 總結

`TiOcrController` 提供了完整的 OCR 文檔解析解決方案，結合了傳統 OCR 技術和現代 AI 智能分析，能夠高效準確地處理各種類型的賬單和票據。通過模塊化的設計和完善的異常處理機制，確保了系統的穩定性和可靠性。
