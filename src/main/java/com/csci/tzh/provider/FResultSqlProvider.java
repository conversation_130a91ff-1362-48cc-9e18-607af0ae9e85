package com.csci.tzh.provider;

import java.util.Map;
import java.util.logging.Logger;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

import com.csci.tzh.qo.DProjectPageableQO;

public class FResultSqlProvider {

    public String listFResultTotalSql(Map<String, Object> map){
    	
    	String siteName = (String) map.get("siteName");
    	int recordYearMonthFrom = (int) map.get("recordYearMonthFrom");
    	int recordYearMonthTo = (int) map.get("recordYearMonthTo");
    	
    	String select = "SELECT RecordYearMonth, CarbonEmissionLocation, ScopeMain, CONVERT(DECIMAL(10,3), SUM(ISNULL(CarbonAmount, 0)) * 0.001) AS CarbonAmount FROM  ((SELECT * FROM F_Result_Latest) UNION (SELECT NULL AS SiteId, * FROM Tzh_Result)) t ";
    	String where = "WHERE RecordYearMonth IS NOT NULL  "
    			+ "AND CalculateDate = (SELECT MAX(CalculateDate) FROM F_Result_Latest) ";
    	if(StringUtils.isNotBlank(siteName)) {
    		where += "	  AND SiteName = N'" + siteName + "' ";
    	}
    	if(recordYearMonthFrom != 0) {
    		where += "	  AND RecordYearMonth >= " + recordYearMonthFrom + " ";
    	}
    	if(recordYearMonthTo != 0) {
    		where += "	  AND RecordYearMonth <= " + recordYearMonthTo + " ";
    	}
    	String groupBy = "GROUP BY RecordYearMonth, CarbonEmissionLocation, ScopeMain";
    	
        return select + where + groupBy;
    }
    
    public String listCarbonAmountByScopeMainSql(Map<String, Object> map){

    	String region = (String) map.get("region");
    	String siteName = (String) map.get("siteName");
    	String carbonEmissionLocation = (String) map.get("carbonEmissionLocation");
    	int recordYearMonthFrom = (int) map.get("recordYearMonthFrom");
    	int recordYearMonthTo = (int) map.get("recordYearMonthTo");
    	
    	String select = "SELECT ScopeMain, CONVERT(DECIMAL(10,3), SUM(ISNULL(CarbonAmount,0) * 0.001)) AS CarbonAmount "
    			+ "FROM [dbo].[F_Result_Latest] r "
    			+ "LEFT JOIN [dbo].[D_Project] p "
    			+ "ON r.SiteId = p.SiteId ";
    	String where = "WHERE RecordYearMonth IS NOT NULL "
    			+ "AND CalculateDate = (SELECT MAX(CalculateDate) FROM F_Result_Latest) ";
    	if(StringUtils.isNotBlank(region)) {
    		where += "	  AND Region = N'" + region + "' ";
    	}
    	if(StringUtils.isNotBlank(siteName)) {
    		where += "	  AND r.SiteName = N'" + siteName + "' ";
    	}
    	if(StringUtils.isNotBlank(carbonEmissionLocation)) {
    		where += "	  AND CarbonEmissionLocation = N'" + carbonEmissionLocation + "' ";
    	}
    	if(recordYearMonthFrom != 0) {
    		where += "	  AND RecordYearMonth >= " + recordYearMonthFrom + " ";
    	}
    	if(recordYearMonthTo != 0) {
    		where += "	  AND RecordYearMonth <= " + recordYearMonthTo + " ";
    	}
    	String groupBy = "GROUP BY ScopeMain ";
    	
    	String orderBy = "ORDER BY ScopeMain ";
    	
        return select + where + groupBy + orderBy;
    }
    
    public String listCarbonAmountByScopeDetailSql(Map<String, Object> map){

		String region = (String) map.get("region");
		String siteName = (String) map.get("siteName");
		String carbonEmissionLocation = (String) map.get("carbonEmissionLocation");
		int recordYearMonthFrom = (int) map.get("recordYearMonthFrom");
		int recordYearMonthTo = (int) map.get("recordYearMonthTo");
    	
    	String select = "SELECT ScopeDetail, CONVERT(DECIMAL(10,3), SUM(ISNULL(CarbonAmount,0) * 0.001)) AS CarbonAmount "
    			+ "FROM [dbo].[F_Result_Latest] r "
    			+ "LEFT JOIN [dbo].[D_Project] p "
    			+ "ON r.SiteId = p.SiteId ";
    	String where = "WHERE RecordYearMonth IS NOT NULL  "
    			+ "AND CalculateDate = (SELECT MAX(CalculateDate) FROM F_Result_Latest) ";
    	if(StringUtils.isNotBlank(region)) {
    		where += "	  AND Region = #{region} ";
    	}
    	if(StringUtils.isNotBlank(siteName)) {
    		where += "	  AND r.SiteName = #{siteName} ";
    	}
    	if(StringUtils.isNotBlank(carbonEmissionLocation)) {
    		where += "	  AND CarbonEmissionLocation = #{carbonEmissionLocation} ";
    	}
    	if(recordYearMonthFrom != 0) {
    		where += "	  AND RecordYearMonth >= #{recordYearMonthFrom} ";
    	}
    	if(recordYearMonthTo != 0) {
    		where += "	  AND RecordYearMonth <= #{recordYearMonthTo} ";
    	}
    	String groupBy = "GROUP BY ScopeDetail ";
    	
    	String orderBy = "ORDER BY ScopeDetail ";
    	
        return select + where + groupBy + orderBy;
    }
    
    public String listCarbonAmountPercentageByScopeDetailSql(Map<String, Object> map){

    	String region = (String) map.get("region");
    	String siteName = (String) map.get("siteName");
    	String carbonEmissionLocation = (String) map.get("carbonEmissionLocation");
    	int recordYearMonthFrom = (int) map.get("recordYearMonthFrom");
    	int recordYearMonthTo = (int) map.get("recordYearMonthTo");
    	
    	String baseSelect = "SELECT ScopeMain, ScopeDetail, CONVERT(DECIMAL(10,2), SUM(ISNULL(CarbonAmount,0) * 0.001)) AS CarbonAmount "
    			+ "	FROM [dbo].[F_Result_Latest] r "
    			+ "	LEFT JOIN [dbo].[D_Project] p "
    			+ "	ON r.SiteId = p.SiteId ";
    	String baseWhere = "WHERE RecordYearMonth IS NOT NULL  "
    			+ "AND CalculateDate = (SELECT MAX(CalculateDate) FROM F_Result_Latest) ";
    	if(StringUtils.isNotBlank(region)) {
    		baseWhere += "	  AND Region = #{region} ";
    	}
    	if(StringUtils.isNotBlank(siteName)) {
    		baseWhere += "	  AND r.SiteName = #{siteName} ";
    	}
    	if(StringUtils.isNotBlank(carbonEmissionLocation)) {
    		baseWhere += "	  AND CarbonEmissionLocation = #{carbonEmissionLocation} ";
    	}
    	if(recordYearMonthFrom != 0) {
    		baseWhere += "	  AND RecordYearMonth >= #{recordYearMonthFrom} ";
    	}
    	if(recordYearMonthTo != 0) {
    		baseWhere += "	  AND RecordYearMonth <= #{recordYearMonthTo} ";
    	}
    	String baseGroupBy = "GROUP BY ScopeMain, ScopeDetail";
    	
    	String baseSql = baseSelect + baseWhere + baseGroupBy;
    	
    	
    	String subSelect = "SELECT SUM(ISNULL(CarbonAmount,0) * 0.001) AS TotalCarbonAmount "
    			+ "	FROM [dbo].[F_Result_Latest] r "
    			+ "	LEFT JOIN [dbo].[D_Project] p "
    			+ "	ON r.SiteId = p.SiteId ";
    	String subWhere = "WHERE RecordYearMonth IS NOT NULL "
    			+ "	AND CalculateDate = (SELECT MAX(CalculateDate) FROM F_Result_Latest) ";
    	if(StringUtils.isNotBlank(region)) {
    		subWhere += "	  AND Region = #{region} ";
    	}
    	if(StringUtils.isNotBlank(siteName)) {
    		subWhere += "	  AND r.SiteName = #{siteName} ";
    	}
    	if(StringUtils.isNotBlank(carbonEmissionLocation)) {
    		subWhere += "	  AND CarbonEmissionLocation = #{carbonEmissionLocation} ";
    	}
    	if(recordYearMonthFrom != 0) {
    		subWhere += "	  AND RecordYearMonth >= #{recordYearMonthFrom} ";
    	}
    	if(recordYearMonthTo != 0) {
    		subWhere += "	  AND RecordYearMonth <= #{recordYearMonthTo} ";
    	}

    	String subSql = subSelect + subWhere;
    	
    	String sql = "SELECT ScopeMain, ScopeDetail, CarbonAmount, "
    			+ "CONVERT(DECIMAL(10,2), CarbonAmount/(" + subSql + ")*100) AS CarbonAmountPercentage "
    			+ "FROM "
    			+ "(" + baseSql + ") AS r ";
    	
    	String orderBy = "ORDER BY ScopeMain, ScopeDetail ";
    	
        return sql + orderBy;
    }
}