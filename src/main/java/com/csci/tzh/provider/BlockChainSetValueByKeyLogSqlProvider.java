package com.csci.tzh.provider;

import java.util.Map;
import java.util.logging.Logger;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

import com.csci.tzh.qo.DProjectPageableQO;

public class BlockChainSetValueByKeyLogSqlProvider {

	/*
	 * SELECT TOP 1 [MD5Code] 
		FROM [dbo].[BlockChain_SetValueByKey_Log] 
		WHERE [FileName] LIKE 'dbo_F_Result_Latest_%'
		AND RequestStatusCode = 200 AND RequestText = 'true'
	   ORDER BY Logdatetime DESC
	 */
    public String listBlockChainSetValueByKeyLogSql(){
    	
    	String select = "SELECT TOP 1 [MD5Code] "
    			+ "  FROM [dbo].[BlockChain_SetValueByKey_Log] ";
    	String where = "WHERE [FileName] LIKE 'dbo_F_Result_%' "
    			+ "  AND RequestStatusCode = 200 "
    			+ "  AND RequestText = 'true' "
    			+ "  ORDER BY Logdatetime DESC";
    	
        return select + where;
    }
    
}