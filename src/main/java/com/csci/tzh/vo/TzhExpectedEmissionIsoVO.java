package com.csci.tzh.vo;

import com.csci.tzh.model.TzhExpectedEmissionIso;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "碳中和-減排預測表 表单数据")
public class TzhExpectedEmissionIsoVO {

    private String sitename;

    private String protocolsubcategoryid;

    private String categoryname;

    private String categorynamesc;

    private String categorynameen;

    private String subcategoryname;

    private String subcategorynamesc;

    private String subcategorynameen;

    private String carbonemissionlocationid;

    private String carbonemissionlocation;

    private String carbonemissionlocationsc;

    private String carbonemissionlocationen;

    private String carbonamount;

    private String carbonamountundermeasure;
}
