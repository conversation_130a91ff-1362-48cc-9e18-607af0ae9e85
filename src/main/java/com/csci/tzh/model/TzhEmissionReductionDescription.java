package com.csci.tzh.model;

import java.time.LocalDateTime;

public class TzhEmissionReductionDescription {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionDescription.Id
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionDescription.SiteName
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	private String sitename;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionDescription.ProtocolId
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	private String protocolid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionDescription.Title
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	private String title;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionDescription.TitleSC
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	private String titlesc;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionDescription.TitleEN
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	private String titleen;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionDescription.Seq
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	private Integer seq;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionDescription.Description
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	private String description;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionDescription.DescriptionSC
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	private String descriptionsc;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionDescription.DescriptionEN
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	private String descriptionen;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionDescription.PhotoId
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	private String photoid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionDescription.CreatedBy
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	private String createdby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionDescription.CreatedTime
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	private LocalDateTime createdtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionDescription.DeletedBy
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	private String deletedby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionDescription.DeletedTime
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	private LocalDateTime deletedtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionDescription.IsDeleted
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	private Boolean isdeleted;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionDescription.Id
	 * @return  the value of Tzh_EmissionReductionDescription.Id
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionDescription.Id
	 * @param id  the value for Tzh_EmissionReductionDescription.Id
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionDescription.SiteName
	 * @return  the value of Tzh_EmissionReductionDescription.SiteName
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public String getSitename() {
		return sitename;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionDescription.SiteName
	 * @param sitename  the value for Tzh_EmissionReductionDescription.SiteName
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setSitename(String sitename) {
		this.sitename = sitename == null ? null : sitename.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionDescription.ProtocolId
	 * @return  the value of Tzh_EmissionReductionDescription.ProtocolId
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public String getProtocolid() {
		return protocolid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionDescription.ProtocolId
	 * @param protocolid  the value for Tzh_EmissionReductionDescription.ProtocolId
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setProtocolid(String protocolid) {
		this.protocolid = protocolid == null ? null : protocolid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionDescription.Title
	 * @return  the value of Tzh_EmissionReductionDescription.Title
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public String getTitle() {
		return title;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionDescription.Title
	 * @param title  the value for Tzh_EmissionReductionDescription.Title
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setTitle(String title) {
		this.title = title == null ? null : title.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionDescription.TitleSC
	 * @return  the value of Tzh_EmissionReductionDescription.TitleSC
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public String getTitlesc() {
		return titlesc;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionDescription.TitleSC
	 * @param titlesc  the value for Tzh_EmissionReductionDescription.TitleSC
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setTitlesc(String titlesc) {
		this.titlesc = titlesc == null ? null : titlesc.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionDescription.TitleEN
	 * @return  the value of Tzh_EmissionReductionDescription.TitleEN
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public String getTitleen() {
		return titleen;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionDescription.TitleEN
	 * @param titleen  the value for Tzh_EmissionReductionDescription.TitleEN
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setTitleen(String titleen) {
		this.titleen = titleen == null ? null : titleen.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionDescription.Seq
	 * @return  the value of Tzh_EmissionReductionDescription.Seq
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public Integer getSeq() {
		return seq;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionDescription.Seq
	 * @param seq  the value for Tzh_EmissionReductionDescription.Seq
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setSeq(Integer seq) {
		this.seq = seq;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionDescription.Description
	 * @return  the value of Tzh_EmissionReductionDescription.Description
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionDescription.Description
	 * @param description  the value for Tzh_EmissionReductionDescription.Description
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setDescription(String description) {
		this.description = description == null ? null : description.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionDescription.DescriptionSC
	 * @return  the value of Tzh_EmissionReductionDescription.DescriptionSC
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public String getDescriptionsc() {
		return descriptionsc;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionDescription.DescriptionSC
	 * @param descriptionsc  the value for Tzh_EmissionReductionDescription.DescriptionSC
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setDescriptionsc(String descriptionsc) {
		this.descriptionsc = descriptionsc == null ? null : descriptionsc.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionDescription.DescriptionEN
	 * @return  the value of Tzh_EmissionReductionDescription.DescriptionEN
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public String getDescriptionen() {
		return descriptionen;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionDescription.DescriptionEN
	 * @param descriptionen  the value for Tzh_EmissionReductionDescription.DescriptionEN
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setDescriptionen(String descriptionen) {
		this.descriptionen = descriptionen == null ? null : descriptionen.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionDescription.PhotoId
	 * @return  the value of Tzh_EmissionReductionDescription.PhotoId
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public String getPhotoid() {
		return photoid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionDescription.PhotoId
	 * @param photoid  the value for Tzh_EmissionReductionDescription.PhotoId
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setPhotoid(String photoid) {
		this.photoid = photoid == null ? null : photoid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionDescription.CreatedBy
	 * @return  the value of Tzh_EmissionReductionDescription.CreatedBy
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public String getCreatedby() {
		return createdby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionDescription.CreatedBy
	 * @param createdby  the value for Tzh_EmissionReductionDescription.CreatedBy
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setCreatedby(String createdby) {
		this.createdby = createdby == null ? null : createdby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionDescription.CreatedTime
	 * @return  the value of Tzh_EmissionReductionDescription.CreatedTime
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public LocalDateTime getCreatedtime() {
		return createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionDescription.CreatedTime
	 * @param createdtime  the value for Tzh_EmissionReductionDescription.CreatedTime
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setCreatedtime(LocalDateTime createdtime) {
		this.createdtime = createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionDescription.DeletedBy
	 * @return  the value of Tzh_EmissionReductionDescription.DeletedBy
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public String getDeletedby() {
		return deletedby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionDescription.DeletedBy
	 * @param deletedby  the value for Tzh_EmissionReductionDescription.DeletedBy
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setDeletedby(String deletedby) {
		this.deletedby = deletedby == null ? null : deletedby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionDescription.DeletedTime
	 * @return  the value of Tzh_EmissionReductionDescription.DeletedTime
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public LocalDateTime getDeletedtime() {
		return deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionDescription.DeletedTime
	 * @param deletedtime  the value for Tzh_EmissionReductionDescription.DeletedTime
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setDeletedtime(LocalDateTime deletedtime) {
		this.deletedtime = deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionDescription.IsDeleted
	 * @return  the value of Tzh_EmissionReductionDescription.IsDeleted
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public Boolean getIsdeleted() {
		return isdeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionDescription.IsDeleted
	 * @param isdeleted  the value for Tzh_EmissionReductionDescription.IsDeleted
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setIsdeleted(Boolean isdeleted) {
		this.isdeleted = isdeleted;
	}
}