package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class TzhEquipmentUsageExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	public TzhEquipmentUsageExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("Id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("Id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("Id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("Id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("Id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("Id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("Id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("Id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("Id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("Id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("Id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("Id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("Id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("Id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andSitenameIsNull() {
			addCriterion("SiteName is null");
			return (Criteria) this;
		}

		public Criteria andSitenameIsNotNull() {
			addCriterion("SiteName is not null");
			return (Criteria) this;
		}

		public Criteria andSitenameEqualTo(String value) {
			addCriterion("SiteName =", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotEqualTo(String value) {
			addCriterion("SiteName <>", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameGreaterThan(String value) {
			addCriterion("SiteName >", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameGreaterThanOrEqualTo(String value) {
			addCriterion("SiteName >=", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLessThan(String value) {
			addCriterion("SiteName <", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLessThanOrEqualTo(String value) {
			addCriterion("SiteName <=", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLike(String value) {
			addCriterion("SiteName like", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotLike(String value) {
			addCriterion("SiteName not like", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameIn(List<String> values) {
			addCriterion("SiteName in", values, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotIn(List<String> values) {
			addCriterion("SiteName not in", values, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameBetween(String value1, String value2) {
			addCriterion("SiteName between", value1, value2, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotBetween(String value1, String value2) {
			addCriterion("SiteName not between", value1, value2, "sitename");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidIsNull() {
			addCriterion("ProtocolSubCategoryId is null");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidIsNotNull() {
			addCriterion("ProtocolSubCategoryId is not null");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidEqualTo(String value) {
			addCriterion("ProtocolSubCategoryId =", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidNotEqualTo(String value) {
			addCriterion("ProtocolSubCategoryId <>", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidGreaterThan(String value) {
			addCriterion("ProtocolSubCategoryId >", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidGreaterThanOrEqualTo(String value) {
			addCriterion("ProtocolSubCategoryId >=", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidLessThan(String value) {
			addCriterion("ProtocolSubCategoryId <", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidLessThanOrEqualTo(String value) {
			addCriterion("ProtocolSubCategoryId <=", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidLike(String value) {
			addCriterion("ProtocolSubCategoryId like", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidNotLike(String value) {
			addCriterion("ProtocolSubCategoryId not like", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidIn(List<String> values) {
			addCriterion("ProtocolSubCategoryId in", values, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidNotIn(List<String> values) {
			addCriterion("ProtocolSubCategoryId not in", values, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidBetween(String value1, String value2) {
			addCriterion("ProtocolSubCategoryId between", value1, value2, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidNotBetween(String value1, String value2) {
			addCriterion("ProtocolSubCategoryId not between", value1, value2, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andNameIsNull() {
			addCriterion("Name is null");
			return (Criteria) this;
		}

		public Criteria andNameIsNotNull() {
			addCriterion("Name is not null");
			return (Criteria) this;
		}

		public Criteria andNameEqualTo(String value) {
			addCriterion("Name =", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotEqualTo(String value) {
			addCriterion("Name <>", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameGreaterThan(String value) {
			addCriterion("Name >", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameGreaterThanOrEqualTo(String value) {
			addCriterion("Name >=", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLessThan(String value) {
			addCriterion("Name <", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLessThanOrEqualTo(String value) {
			addCriterion("Name <=", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLike(String value) {
			addCriterion("Name like", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotLike(String value) {
			addCriterion("Name not like", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameIn(List<String> values) {
			addCriterion("Name in", values, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotIn(List<String> values) {
			addCriterion("Name not in", values, "name");
			return (Criteria) this;
		}

		public Criteria andNameBetween(String value1, String value2) {
			addCriterion("Name between", value1, value2, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotBetween(String value1, String value2) {
			addCriterion("Name not between", value1, value2, "name");
			return (Criteria) this;
		}

		public Criteria andModelIsNull() {
			addCriterion("Model is null");
			return (Criteria) this;
		}

		public Criteria andModelIsNotNull() {
			addCriterion("Model is not null");
			return (Criteria) this;
		}

		public Criteria andModelEqualTo(String value) {
			addCriterion("Model =", value, "model");
			return (Criteria) this;
		}

		public Criteria andModelNotEqualTo(String value) {
			addCriterion("Model <>", value, "model");
			return (Criteria) this;
		}

		public Criteria andModelGreaterThan(String value) {
			addCriterion("Model >", value, "model");
			return (Criteria) this;
		}

		public Criteria andModelGreaterThanOrEqualTo(String value) {
			addCriterion("Model >=", value, "model");
			return (Criteria) this;
		}

		public Criteria andModelLessThan(String value) {
			addCriterion("Model <", value, "model");
			return (Criteria) this;
		}

		public Criteria andModelLessThanOrEqualTo(String value) {
			addCriterion("Model <=", value, "model");
			return (Criteria) this;
		}

		public Criteria andModelLike(String value) {
			addCriterion("Model like", value, "model");
			return (Criteria) this;
		}

		public Criteria andModelNotLike(String value) {
			addCriterion("Model not like", value, "model");
			return (Criteria) this;
		}

		public Criteria andModelIn(List<String> values) {
			addCriterion("Model in", values, "model");
			return (Criteria) this;
		}

		public Criteria andModelNotIn(List<String> values) {
			addCriterion("Model not in", values, "model");
			return (Criteria) this;
		}

		public Criteria andModelBetween(String value1, String value2) {
			addCriterion("Model between", value1, value2, "model");
			return (Criteria) this;
		}

		public Criteria andModelNotBetween(String value1, String value2) {
			addCriterion("Model not between", value1, value2, "model");
			return (Criteria) this;
		}

		public Criteria andDailyworkinghourIsNull() {
			addCriterion("DailyWorkingHour is null");
			return (Criteria) this;
		}

		public Criteria andDailyworkinghourIsNotNull() {
			addCriterion("DailyWorkingHour is not null");
			return (Criteria) this;
		}

		public Criteria andDailyworkinghourEqualTo(BigDecimal value) {
			addCriterion("DailyWorkingHour =", value, "dailyworkinghour");
			return (Criteria) this;
		}

		public Criteria andDailyworkinghourNotEqualTo(BigDecimal value) {
			addCriterion("DailyWorkingHour <>", value, "dailyworkinghour");
			return (Criteria) this;
		}

		public Criteria andDailyworkinghourGreaterThan(BigDecimal value) {
			addCriterion("DailyWorkingHour >", value, "dailyworkinghour");
			return (Criteria) this;
		}

		public Criteria andDailyworkinghourGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("DailyWorkingHour >=", value, "dailyworkinghour");
			return (Criteria) this;
		}

		public Criteria andDailyworkinghourLessThan(BigDecimal value) {
			addCriterion("DailyWorkingHour <", value, "dailyworkinghour");
			return (Criteria) this;
		}

		public Criteria andDailyworkinghourLessThanOrEqualTo(BigDecimal value) {
			addCriterion("DailyWorkingHour <=", value, "dailyworkinghour");
			return (Criteria) this;
		}

		public Criteria andDailyworkinghourIn(List<BigDecimal> values) {
			addCriterion("DailyWorkingHour in", values, "dailyworkinghour");
			return (Criteria) this;
		}

		public Criteria andDailyworkinghourNotIn(List<BigDecimal> values) {
			addCriterion("DailyWorkingHour not in", values, "dailyworkinghour");
			return (Criteria) this;
		}

		public Criteria andDailyworkinghourBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("DailyWorkingHour between", value1, value2, "dailyworkinghour");
			return (Criteria) this;
		}

		public Criteria andDailyworkinghourNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("DailyWorkingHour not between", value1, value2, "dailyworkinghour");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorIsNull() {
			addCriterion("CarbonFactor is null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorIsNotNull() {
			addCriterion("CarbonFactor is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor =", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorNotEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor <>", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorGreaterThan(BigDecimal value) {
			addCriterion("CarbonFactor >", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor >=", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorLessThan(BigDecimal value) {
			addCriterion("CarbonFactor <", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorLessThanOrEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor <=", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorIn(List<BigDecimal> values) {
			addCriterion("CarbonFactor in", values, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorNotIn(List<BigDecimal> values) {
			addCriterion("CarbonFactor not in", values, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("CarbonFactor between", value1, value2, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("CarbonFactor not between", value1, value2, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitIsNull() {
			addCriterion("CarbonFactorUnit is null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitIsNotNull() {
			addCriterion("CarbonFactorUnit is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitEqualTo(String value) {
			addCriterion("CarbonFactorUnit =", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotEqualTo(String value) {
			addCriterion("CarbonFactorUnit <>", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitGreaterThan(String value) {
			addCriterion("CarbonFactorUnit >", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitGreaterThanOrEqualTo(String value) {
			addCriterion("CarbonFactorUnit >=", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitLessThan(String value) {
			addCriterion("CarbonFactorUnit <", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitLessThanOrEqualTo(String value) {
			addCriterion("CarbonFactorUnit <=", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitLike(String value) {
			addCriterion("CarbonFactorUnit like", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotLike(String value) {
			addCriterion("CarbonFactorUnit not like", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitIn(List<String> values) {
			addCriterion("CarbonFactorUnit in", values, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotIn(List<String> values) {
			addCriterion("CarbonFactorUnit not in", values, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitBetween(String value1, String value2) {
			addCriterion("CarbonFactorUnit between", value1, value2, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotBetween(String value1, String value2) {
			addCriterion("CarbonFactorUnit not between", value1, value2, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andStartdateIsNull() {
			addCriterion("StartDate is null");
			return (Criteria) this;
		}

		public Criteria andStartdateIsNotNull() {
			addCriterion("StartDate is not null");
			return (Criteria) this;
		}

		public Criteria andStartdateEqualTo(LocalDateTime value) {
			addCriterion("StartDate =", value, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateNotEqualTo(LocalDateTime value) {
			addCriterion("StartDate <>", value, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateGreaterThan(LocalDateTime value) {
			addCriterion("StartDate >", value, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("StartDate >=", value, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateLessThan(LocalDateTime value) {
			addCriterion("StartDate <", value, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("StartDate <=", value, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateIn(List<LocalDateTime> values) {
			addCriterion("StartDate in", values, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateNotIn(List<LocalDateTime> values) {
			addCriterion("StartDate not in", values, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("StartDate between", value1, value2, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("StartDate not between", value1, value2, "startdate");
			return (Criteria) this;
		}

		public Criteria andEnddateIsNull() {
			addCriterion("EndDate is null");
			return (Criteria) this;
		}

		public Criteria andEnddateIsNotNull() {
			addCriterion("EndDate is not null");
			return (Criteria) this;
		}

		public Criteria andEnddateEqualTo(LocalDateTime value) {
			addCriterion("EndDate =", value, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateNotEqualTo(LocalDateTime value) {
			addCriterion("EndDate <>", value, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateGreaterThan(LocalDateTime value) {
			addCriterion("EndDate >", value, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("EndDate >=", value, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateLessThan(LocalDateTime value) {
			addCriterion("EndDate <", value, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("EndDate <=", value, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateIn(List<LocalDateTime> values) {
			addCriterion("EndDate in", values, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateNotIn(List<LocalDateTime> values) {
			addCriterion("EndDate not in", values, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("EndDate between", value1, value2, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("EndDate not between", value1, value2, "enddate");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNull() {
			addCriterion("CreatedBy is null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNotNull() {
			addCriterion("CreatedBy is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyEqualTo(String value) {
			addCriterion("CreatedBy =", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotEqualTo(String value) {
			addCriterion("CreatedBy <>", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThan(String value) {
			addCriterion("CreatedBy >", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
			addCriterion("CreatedBy >=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThan(String value) {
			addCriterion("CreatedBy <", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThanOrEqualTo(String value) {
			addCriterion("CreatedBy <=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLike(String value) {
			addCriterion("CreatedBy like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotLike(String value) {
			addCriterion("CreatedBy not like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIn(List<String> values) {
			addCriterion("CreatedBy in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotIn(List<String> values) {
			addCriterion("CreatedBy not in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyBetween(String value1, String value2) {
			addCriterion("CreatedBy between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotBetween(String value1, String value2) {
			addCriterion("CreatedBy not between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNull() {
			addCriterion("CreatedTime is null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNotNull() {
			addCriterion("CreatedTime is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime =", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <>", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThan(LocalDateTime value) {
			addCriterion("CreatedTime >", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime >=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThan(LocalDateTime value) {
			addCriterion("CreatedTime <", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime not in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime not between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNull() {
			addCriterion("DeletedBy is null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNotNull() {
			addCriterion("DeletedBy is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyEqualTo(String value) {
			addCriterion("DeletedBy =", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotEqualTo(String value) {
			addCriterion("DeletedBy <>", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThan(String value) {
			addCriterion("DeletedBy >", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThanOrEqualTo(String value) {
			addCriterion("DeletedBy >=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThan(String value) {
			addCriterion("DeletedBy <", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThanOrEqualTo(String value) {
			addCriterion("DeletedBy <=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLike(String value) {
			addCriterion("DeletedBy like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotLike(String value) {
			addCriterion("DeletedBy not like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIn(List<String> values) {
			addCriterion("DeletedBy in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotIn(List<String> values) {
			addCriterion("DeletedBy not in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyBetween(String value1, String value2) {
			addCriterion("DeletedBy between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotBetween(String value1, String value2) {
			addCriterion("DeletedBy not between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNull() {
			addCriterion("DeletedTime is null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNotNull() {
			addCriterion("DeletedTime is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime =", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <>", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThan(LocalDateTime value) {
			addCriterion("DeletedTime >", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime >=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThan(LocalDateTime value) {
			addCriterion("DeletedTime <", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime not in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime not between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNull() {
			addCriterion("IsDeleted is null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNotNull() {
			addCriterion("IsDeleted is not null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedEqualTo(Boolean value) {
			addCriterion("IsDeleted =", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotEqualTo(Boolean value) {
			addCriterion("IsDeleted <>", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThan(Boolean value) {
			addCriterion("IsDeleted >", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted >=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThan(Boolean value) {
			addCriterion("IsDeleted <", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted <=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIn(List<Boolean> values) {
			addCriterion("IsDeleted in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotIn(List<Boolean> values) {
			addCriterion("IsDeleted not in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted between", value1, value2, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted not between", value1, value2, "isdeleted");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_EquipmentUsage
     *
     * @mbg.generated do_not_delete_during_merge Tue May 16 14:33:01 HKT 2023
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}