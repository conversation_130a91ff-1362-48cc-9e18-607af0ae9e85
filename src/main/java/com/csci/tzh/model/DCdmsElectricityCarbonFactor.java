package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class DCdmsElectricityCarbonFactor {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.Id
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.SiteName
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private String sitename;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.ProtocolId
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private String protocolid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.CarbonEmissionLocationId
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private String carbonemissionlocationid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.CarbonFactorRecordYearMonth
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private Integer carbonfactorrecordyearmonth;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.AccountNo
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private String accountno;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.ElectricConsumption
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private BigDecimal electricconsumption;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.ElectricUnit
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private String electricunit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.CarbonFactor
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private BigDecimal carbonfactor;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.CarbonFactorUnit
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private String carbonfactorunit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.Source
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private String source;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.SourceRefId
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private String sourcerefid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.CreatedBy
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private String createdby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.CreatedTime
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private LocalDateTime createdtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.DeletedBy
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private String deletedby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.DeletedTime
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private LocalDateTime deletedtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_ElectricityCarbonFactor.IsDeleted
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	private Boolean isdeleted;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.Id
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.Id
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.Id
	 * @param id  the value for D_CDMS_ElectricityCarbonFactor.Id
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.SiteName
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.SiteName
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public String getSitename() {
		return sitename;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.SiteName
	 * @param sitename  the value for D_CDMS_ElectricityCarbonFactor.SiteName
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setSitename(String sitename) {
		this.sitename = sitename == null ? null : sitename.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.ProtocolId
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.ProtocolId
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public String getProtocolid() {
		return protocolid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.ProtocolId
	 * @param protocolid  the value for D_CDMS_ElectricityCarbonFactor.ProtocolId
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setProtocolid(String protocolid) {
		this.protocolid = protocolid == null ? null : protocolid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.CarbonEmissionLocationId
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.CarbonEmissionLocationId
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public String getCarbonemissionlocationid() {
		return carbonemissionlocationid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.CarbonEmissionLocationId
	 * @param carbonemissionlocationid  the value for D_CDMS_ElectricityCarbonFactor.CarbonEmissionLocationId
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setCarbonemissionlocationid(String carbonemissionlocationid) {
		this.carbonemissionlocationid = carbonemissionlocationid == null ? null : carbonemissionlocationid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.CarbonFactorRecordYearMonth
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.CarbonFactorRecordYearMonth
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public Integer getCarbonfactorrecordyearmonth() {
		return carbonfactorrecordyearmonth;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.CarbonFactorRecordYearMonth
	 * @param carbonfactorrecordyearmonth  the value for D_CDMS_ElectricityCarbonFactor.CarbonFactorRecordYearMonth
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setCarbonfactorrecordyearmonth(Integer carbonfactorrecordyearmonth) {
		this.carbonfactorrecordyearmonth = carbonfactorrecordyearmonth;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.AccountNo
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.AccountNo
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public String getAccountno() {
		return accountno;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.AccountNo
	 * @param accountno  the value for D_CDMS_ElectricityCarbonFactor.AccountNo
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setAccountno(String accountno) {
		this.accountno = accountno == null ? null : accountno.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.ElectricConsumption
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.ElectricConsumption
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public BigDecimal getElectricconsumption() {
		return electricconsumption;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.ElectricConsumption
	 * @param electricconsumption  the value for D_CDMS_ElectricityCarbonFactor.ElectricConsumption
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setElectricconsumption(BigDecimal electricconsumption) {
		this.electricconsumption = electricconsumption;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.ElectricUnit
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.ElectricUnit
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public String getElectricunit() {
		return electricunit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.ElectricUnit
	 * @param electricunit  the value for D_CDMS_ElectricityCarbonFactor.ElectricUnit
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setElectricunit(String electricunit) {
		this.electricunit = electricunit == null ? null : electricunit.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.CarbonFactor
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.CarbonFactor
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public BigDecimal getCarbonfactor() {
		return carbonfactor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.CarbonFactor
	 * @param carbonfactor  the value for D_CDMS_ElectricityCarbonFactor.CarbonFactor
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setCarbonfactor(BigDecimal carbonfactor) {
		this.carbonfactor = carbonfactor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.CarbonFactorUnit
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.CarbonFactorUnit
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public String getCarbonfactorunit() {
		return carbonfactorunit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.CarbonFactorUnit
	 * @param carbonfactorunit  the value for D_CDMS_ElectricityCarbonFactor.CarbonFactorUnit
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setCarbonfactorunit(String carbonfactorunit) {
		this.carbonfactorunit = carbonfactorunit == null ? null : carbonfactorunit.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.Source
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.Source
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public String getSource() {
		return source;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.Source
	 * @param source  the value for D_CDMS_ElectricityCarbonFactor.Source
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setSource(String source) {
		this.source = source == null ? null : source.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.SourceRefId
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.SourceRefId
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public String getSourcerefid() {
		return sourcerefid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.SourceRefId
	 * @param sourcerefid  the value for D_CDMS_ElectricityCarbonFactor.SourceRefId
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setSourcerefid(String sourcerefid) {
		this.sourcerefid = sourcerefid == null ? null : sourcerefid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.CreatedBy
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.CreatedBy
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public String getCreatedby() {
		return createdby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.CreatedBy
	 * @param createdby  the value for D_CDMS_ElectricityCarbonFactor.CreatedBy
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setCreatedby(String createdby) {
		this.createdby = createdby == null ? null : createdby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.CreatedTime
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.CreatedTime
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public LocalDateTime getCreatedtime() {
		return createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.CreatedTime
	 * @param createdtime  the value for D_CDMS_ElectricityCarbonFactor.CreatedTime
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setCreatedtime(LocalDateTime createdtime) {
		this.createdtime = createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.DeletedBy
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.DeletedBy
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public String getDeletedby() {
		return deletedby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.DeletedBy
	 * @param deletedby  the value for D_CDMS_ElectricityCarbonFactor.DeletedBy
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setDeletedby(String deletedby) {
		this.deletedby = deletedby == null ? null : deletedby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.DeletedTime
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.DeletedTime
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public LocalDateTime getDeletedtime() {
		return deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.DeletedTime
	 * @param deletedtime  the value for D_CDMS_ElectricityCarbonFactor.DeletedTime
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setDeletedtime(LocalDateTime deletedtime) {
		this.deletedtime = deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_ElectricityCarbonFactor.IsDeleted
	 * @return  the value of D_CDMS_ElectricityCarbonFactor.IsDeleted
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public Boolean getIsdeleted() {
		return isdeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_ElectricityCarbonFactor.IsDeleted
	 * @param isdeleted  the value for D_CDMS_ElectricityCarbonFactor.IsDeleted
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	public void setIsdeleted(Boolean isdeleted) {
		this.isdeleted = isdeleted;
	}
}