package com.csci.tzh.model;

import java.time.LocalDateTime;

public class TzhEmissionReductionHead {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.Id
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.SiteName
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private String sitename;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.ProtocolCategoryId
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private String protocolcategoryid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.Title
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private String title;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.TitleSC
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private String titlesc;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.TitleEN
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private String titleen;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.CarbonEmissionLocationId
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private String carbonemissionlocationid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.MethodDescription
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private String methoddescription;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.MethodDescriptionSC
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private String methoddescriptionsc;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.MethodDescriptionEN
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private String methoddescriptionen;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.CalculationDescription
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private String calculationdescription;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.CalculationDescriptionSC
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private String calculationdescriptionsc;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.CalculationDescriptionEN
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private String calculationdescriptionen;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.CreatedTime
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private LocalDateTime createdtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.CreatedBy
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private String createdby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.DeletedTime
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private LocalDateTime deletedtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.DeletedBy
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private String deletedby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReductionHead.IsDeleted
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	private Boolean isdeleted;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.Id
	 * @return  the value of Tzh_EmissionReductionHead.Id
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.Id
	 * @param id  the value for Tzh_EmissionReductionHead.Id
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.SiteName
	 * @return  the value of Tzh_EmissionReductionHead.SiteName
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public String getSitename() {
		return sitename;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.SiteName
	 * @param sitename  the value for Tzh_EmissionReductionHead.SiteName
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setSitename(String sitename) {
		this.sitename = sitename == null ? null : sitename.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.ProtocolCategoryId
	 * @return  the value of Tzh_EmissionReductionHead.ProtocolCategoryId
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public String getProtocolcategoryid() {
		return protocolcategoryid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.ProtocolCategoryId
	 * @param protocolcategoryid  the value for Tzh_EmissionReductionHead.ProtocolCategoryId
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setProtocolcategoryid(String protocolcategoryid) {
		this.protocolcategoryid = protocolcategoryid == null ? null : protocolcategoryid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.Title
	 * @return  the value of Tzh_EmissionReductionHead.Title
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public String getTitle() {
		return title;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.Title
	 * @param title  the value for Tzh_EmissionReductionHead.Title
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setTitle(String title) {
		this.title = title == null ? null : title.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.TitleSC
	 * @return  the value of Tzh_EmissionReductionHead.TitleSC
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public String getTitlesc() {
		return titlesc;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.TitleSC
	 * @param titlesc  the value for Tzh_EmissionReductionHead.TitleSC
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setTitlesc(String titlesc) {
		this.titlesc = titlesc == null ? null : titlesc.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.TitleEN
	 * @return  the value of Tzh_EmissionReductionHead.TitleEN
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public String getTitleen() {
		return titleen;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.TitleEN
	 * @param titleen  the value for Tzh_EmissionReductionHead.TitleEN
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setTitleen(String titleen) {
		this.titleen = titleen == null ? null : titleen.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.CarbonEmissionLocationId
	 * @return  the value of Tzh_EmissionReductionHead.CarbonEmissionLocationId
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public String getCarbonemissionlocationid() {
		return carbonemissionlocationid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.CarbonEmissionLocationId
	 * @param carbonemissionlocationid  the value for Tzh_EmissionReductionHead.CarbonEmissionLocationId
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setCarbonemissionlocationid(String carbonemissionlocationid) {
		this.carbonemissionlocationid = carbonemissionlocationid == null ? null : carbonemissionlocationid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.MethodDescription
	 * @return  the value of Tzh_EmissionReductionHead.MethodDescription
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public String getMethoddescription() {
		return methoddescription;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.MethodDescription
	 * @param methoddescription  the value for Tzh_EmissionReductionHead.MethodDescription
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setMethoddescription(String methoddescription) {
		this.methoddescription = methoddescription == null ? null : methoddescription.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.MethodDescriptionSC
	 * @return  the value of Tzh_EmissionReductionHead.MethodDescriptionSC
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public String getMethoddescriptionsc() {
		return methoddescriptionsc;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.MethodDescriptionSC
	 * @param methoddescriptionsc  the value for Tzh_EmissionReductionHead.MethodDescriptionSC
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setMethoddescriptionsc(String methoddescriptionsc) {
		this.methoddescriptionsc = methoddescriptionsc == null ? null : methoddescriptionsc.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.MethodDescriptionEN
	 * @return  the value of Tzh_EmissionReductionHead.MethodDescriptionEN
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public String getMethoddescriptionen() {
		return methoddescriptionen;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.MethodDescriptionEN
	 * @param methoddescriptionen  the value for Tzh_EmissionReductionHead.MethodDescriptionEN
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setMethoddescriptionen(String methoddescriptionen) {
		this.methoddescriptionen = methoddescriptionen == null ? null : methoddescriptionen.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.CalculationDescription
	 * @return  the value of Tzh_EmissionReductionHead.CalculationDescription
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public String getCalculationdescription() {
		return calculationdescription;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.CalculationDescription
	 * @param calculationdescription  the value for Tzh_EmissionReductionHead.CalculationDescription
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setCalculationdescription(String calculationdescription) {
		this.calculationdescription = calculationdescription == null ? null : calculationdescription.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.CalculationDescriptionSC
	 * @return  the value of Tzh_EmissionReductionHead.CalculationDescriptionSC
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public String getCalculationdescriptionsc() {
		return calculationdescriptionsc;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.CalculationDescriptionSC
	 * @param calculationdescriptionsc  the value for Tzh_EmissionReductionHead.CalculationDescriptionSC
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setCalculationdescriptionsc(String calculationdescriptionsc) {
		this.calculationdescriptionsc = calculationdescriptionsc == null ? null : calculationdescriptionsc.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.CalculationDescriptionEN
	 * @return  the value of Tzh_EmissionReductionHead.CalculationDescriptionEN
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public String getCalculationdescriptionen() {
		return calculationdescriptionen;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.CalculationDescriptionEN
	 * @param calculationdescriptionen  the value for Tzh_EmissionReductionHead.CalculationDescriptionEN
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setCalculationdescriptionen(String calculationdescriptionen) {
		this.calculationdescriptionen = calculationdescriptionen == null ? null : calculationdescriptionen.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.CreatedTime
	 * @return  the value of Tzh_EmissionReductionHead.CreatedTime
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public LocalDateTime getCreatedtime() {
		return createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.CreatedTime
	 * @param createdtime  the value for Tzh_EmissionReductionHead.CreatedTime
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setCreatedtime(LocalDateTime createdtime) {
		this.createdtime = createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.CreatedBy
	 * @return  the value of Tzh_EmissionReductionHead.CreatedBy
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public String getCreatedby() {
		return createdby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.CreatedBy
	 * @param createdby  the value for Tzh_EmissionReductionHead.CreatedBy
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setCreatedby(String createdby) {
		this.createdby = createdby == null ? null : createdby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.DeletedTime
	 * @return  the value of Tzh_EmissionReductionHead.DeletedTime
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public LocalDateTime getDeletedtime() {
		return deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.DeletedTime
	 * @param deletedtime  the value for Tzh_EmissionReductionHead.DeletedTime
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setDeletedtime(LocalDateTime deletedtime) {
		this.deletedtime = deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.DeletedBy
	 * @return  the value of Tzh_EmissionReductionHead.DeletedBy
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public String getDeletedby() {
		return deletedby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.DeletedBy
	 * @param deletedby  the value for Tzh_EmissionReductionHead.DeletedBy
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setDeletedby(String deletedby) {
		this.deletedby = deletedby == null ? null : deletedby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReductionHead.IsDeleted
	 * @return  the value of Tzh_EmissionReductionHead.IsDeleted
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public Boolean getIsdeleted() {
		return isdeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReductionHead.IsDeleted
	 * @param isdeleted  the value for Tzh_EmissionReductionHead.IsDeleted
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	public void setIsdeleted(Boolean isdeleted) {
		this.isdeleted = isdeleted;
	}
}