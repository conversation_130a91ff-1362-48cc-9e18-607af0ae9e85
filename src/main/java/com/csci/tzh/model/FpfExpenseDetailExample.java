package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class FpfExpenseDetailExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public FpfExpenseDetailExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("Id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("Id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("Id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("Id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("Id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("Id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("Id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("Id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("Id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("Id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("Id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("Id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBillidIsNull() {
            addCriterion("BillId is null");
            return (Criteria) this;
        }

        public Criteria andBillidIsNotNull() {
            addCriterion("BillId is not null");
            return (Criteria) this;
        }

        public Criteria andBillidEqualTo(Integer value) {
            addCriterion("BillId =", value, "billid");
            return (Criteria) this;
        }

        public Criteria andBillidNotEqualTo(Integer value) {
            addCriterion("BillId <>", value, "billid");
            return (Criteria) this;
        }

        public Criteria andBillidGreaterThan(Integer value) {
            addCriterion("BillId >", value, "billid");
            return (Criteria) this;
        }

        public Criteria andBillidGreaterThanOrEqualTo(Integer value) {
            addCriterion("BillId >=", value, "billid");
            return (Criteria) this;
        }

        public Criteria andBillidLessThan(Integer value) {
            addCriterion("BillId <", value, "billid");
            return (Criteria) this;
        }

        public Criteria andBillidLessThanOrEqualTo(Integer value) {
            addCriterion("BillId <=", value, "billid");
            return (Criteria) this;
        }

        public Criteria andBillidIn(List<Integer> values) {
            addCriterion("BillId in", values, "billid");
            return (Criteria) this;
        }

        public Criteria andBillidNotIn(List<Integer> values) {
            addCriterion("BillId not in", values, "billid");
            return (Criteria) this;
        }

        public Criteria andBillidBetween(Integer value1, Integer value2) {
            addCriterion("BillId between", value1, value2, "billid");
            return (Criteria) this;
        }

        public Criteria andBillidNotBetween(Integer value1, Integer value2) {
            addCriterion("BillId not between", value1, value2, "billid");
            return (Criteria) this;
        }

        public Criteria andPkDeptdocIsNull() {
            addCriterion("PK_DEPTDOC is null");
            return (Criteria) this;
        }

        public Criteria andPkDeptdocIsNotNull() {
            addCriterion("PK_DEPTDOC is not null");
            return (Criteria) this;
        }

        public Criteria andPkDeptdocEqualTo(String value) {
            addCriterion("PK_DEPTDOC =", value, "pkDeptdoc");
            return (Criteria) this;
        }

        public Criteria andPkDeptdocNotEqualTo(String value) {
            addCriterion("PK_DEPTDOC <>", value, "pkDeptdoc");
            return (Criteria) this;
        }

        public Criteria andPkDeptdocGreaterThan(String value) {
            addCriterion("PK_DEPTDOC >", value, "pkDeptdoc");
            return (Criteria) this;
        }

        public Criteria andPkDeptdocGreaterThanOrEqualTo(String value) {
            addCriterion("PK_DEPTDOC >=", value, "pkDeptdoc");
            return (Criteria) this;
        }

        public Criteria andPkDeptdocLessThan(String value) {
            addCriterion("PK_DEPTDOC <", value, "pkDeptdoc");
            return (Criteria) this;
        }

        public Criteria andPkDeptdocLessThanOrEqualTo(String value) {
            addCriterion("PK_DEPTDOC <=", value, "pkDeptdoc");
            return (Criteria) this;
        }

        public Criteria andPkDeptdocLike(String value) {
            addCriterion("PK_DEPTDOC like", value, "pkDeptdoc");
            return (Criteria) this;
        }

        public Criteria andPkDeptdocNotLike(String value) {
            addCriterion("PK_DEPTDOC not like", value, "pkDeptdoc");
            return (Criteria) this;
        }

        public Criteria andPkDeptdocIn(List<String> values) {
            addCriterion("PK_DEPTDOC in", values, "pkDeptdoc");
            return (Criteria) this;
        }

        public Criteria andPkDeptdocNotIn(List<String> values) {
            addCriterion("PK_DEPTDOC not in", values, "pkDeptdoc");
            return (Criteria) this;
        }

        public Criteria andPkDeptdocBetween(String value1, String value2) {
            addCriterion("PK_DEPTDOC between", value1, value2, "pkDeptdoc");
            return (Criteria) this;
        }

        public Criteria andPkDeptdocNotBetween(String value1, String value2) {
            addCriterion("PK_DEPTDOC not between", value1, value2, "pkDeptdoc");
            return (Criteria) this;
        }

        public Criteria andInvoicenoIsNull() {
            addCriterion("InvoiceNo is null");
            return (Criteria) this;
        }

        public Criteria andInvoicenoIsNotNull() {
            addCriterion("InvoiceNo is not null");
            return (Criteria) this;
        }

        public Criteria andInvoicenoEqualTo(String value) {
            addCriterion("InvoiceNo =", value, "invoiceno");
            return (Criteria) this;
        }

        public Criteria andInvoicenoNotEqualTo(String value) {
            addCriterion("InvoiceNo <>", value, "invoiceno");
            return (Criteria) this;
        }

        public Criteria andInvoicenoGreaterThan(String value) {
            addCriterion("InvoiceNo >", value, "invoiceno");
            return (Criteria) this;
        }

        public Criteria andInvoicenoGreaterThanOrEqualTo(String value) {
            addCriterion("InvoiceNo >=", value, "invoiceno");
            return (Criteria) this;
        }

        public Criteria andInvoicenoLessThan(String value) {
            addCriterion("InvoiceNo <", value, "invoiceno");
            return (Criteria) this;
        }

        public Criteria andInvoicenoLessThanOrEqualTo(String value) {
            addCriterion("InvoiceNo <=", value, "invoiceno");
            return (Criteria) this;
        }

        public Criteria andInvoicenoLike(String value) {
            addCriterion("InvoiceNo like", value, "invoiceno");
            return (Criteria) this;
        }

        public Criteria andInvoicenoNotLike(String value) {
            addCriterion("InvoiceNo not like", value, "invoiceno");
            return (Criteria) this;
        }

        public Criteria andInvoicenoIn(List<String> values) {
            addCriterion("InvoiceNo in", values, "invoiceno");
            return (Criteria) this;
        }

        public Criteria andInvoicenoNotIn(List<String> values) {
            addCriterion("InvoiceNo not in", values, "invoiceno");
            return (Criteria) this;
        }

        public Criteria andInvoicenoBetween(String value1, String value2) {
            addCriterion("InvoiceNo between", value1, value2, "invoiceno");
            return (Criteria) this;
        }

        public Criteria andInvoicenoNotBetween(String value1, String value2) {
            addCriterion("InvoiceNo not between", value1, value2, "invoiceno");
            return (Criteria) this;
        }

        public Criteria andInvoicedateIsNull() {
            addCriterion("InvoiceDate is null");
            return (Criteria) this;
        }

        public Criteria andInvoicedateIsNotNull() {
            addCriterion("InvoiceDate is not null");
            return (Criteria) this;
        }

        public Criteria andInvoicedateEqualTo(LocalDateTime value) {
            addCriterion("InvoiceDate =", value, "invoicedate");
            return (Criteria) this;
        }

        public Criteria andInvoicedateNotEqualTo(LocalDateTime value) {
            addCriterion("InvoiceDate <>", value, "invoicedate");
            return (Criteria) this;
        }

        public Criteria andInvoicedateGreaterThan(LocalDateTime value) {
            addCriterion("InvoiceDate >", value, "invoicedate");
            return (Criteria) this;
        }

        public Criteria andInvoicedateGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("InvoiceDate >=", value, "invoicedate");
            return (Criteria) this;
        }

        public Criteria andInvoicedateLessThan(LocalDateTime value) {
            addCriterion("InvoiceDate <", value, "invoicedate");
            return (Criteria) this;
        }

        public Criteria andInvoicedateLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("InvoiceDate <=", value, "invoicedate");
            return (Criteria) this;
        }

        public Criteria andInvoicedateIn(List<LocalDateTime> values) {
            addCriterion("InvoiceDate in", values, "invoicedate");
            return (Criteria) this;
        }

        public Criteria andInvoicedateNotIn(List<LocalDateTime> values) {
            addCriterion("InvoiceDate not in", values, "invoicedate");
            return (Criteria) this;
        }

        public Criteria andInvoicedateBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("InvoiceDate between", value1, value2, "invoicedate");
            return (Criteria) this;
        }

        public Criteria andInvoicedateNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("InvoiceDate not between", value1, value2, "invoicedate");
            return (Criteria) this;
        }

        public Criteria andDigestIsNull() {
            addCriterion("Digest is null");
            return (Criteria) this;
        }

        public Criteria andDigestIsNotNull() {
            addCriterion("Digest is not null");
            return (Criteria) this;
        }

        public Criteria andDigestEqualTo(String value) {
            addCriterion("Digest =", value, "digest");
            return (Criteria) this;
        }

        public Criteria andDigestNotEqualTo(String value) {
            addCriterion("Digest <>", value, "digest");
            return (Criteria) this;
        }

        public Criteria andDigestGreaterThan(String value) {
            addCriterion("Digest >", value, "digest");
            return (Criteria) this;
        }

        public Criteria andDigestGreaterThanOrEqualTo(String value) {
            addCriterion("Digest >=", value, "digest");
            return (Criteria) this;
        }

        public Criteria andDigestLessThan(String value) {
            addCriterion("Digest <", value, "digest");
            return (Criteria) this;
        }

        public Criteria andDigestLessThanOrEqualTo(String value) {
            addCriterion("Digest <=", value, "digest");
            return (Criteria) this;
        }

        public Criteria andDigestLike(String value) {
            addCriterion("Digest like", value, "digest");
            return (Criteria) this;
        }

        public Criteria andDigestNotLike(String value) {
            addCriterion("Digest not like", value, "digest");
            return (Criteria) this;
        }

        public Criteria andDigestIn(List<String> values) {
            addCriterion("Digest in", values, "digest");
            return (Criteria) this;
        }

        public Criteria andDigestNotIn(List<String> values) {
            addCriterion("Digest not in", values, "digest");
            return (Criteria) this;
        }

        public Criteria andDigestBetween(String value1, String value2) {
            addCriterion("Digest between", value1, value2, "digest");
            return (Criteria) this;
        }

        public Criteria andDigestNotBetween(String value1, String value2) {
            addCriterion("Digest not between", value1, value2, "digest");
            return (Criteria) this;
        }

        public Criteria andPriceIsNull() {
            addCriterion("Price is null");
            return (Criteria) this;
        }

        public Criteria andPriceIsNotNull() {
            addCriterion("Price is not null");
            return (Criteria) this;
        }

        public Criteria andPriceEqualTo(BigDecimal value) {
            addCriterion("Price =", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualTo(BigDecimal value) {
            addCriterion("Price <>", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThan(BigDecimal value) {
            addCriterion("Price >", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("Price >=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThan(BigDecimal value) {
            addCriterion("Price <", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("Price <=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceIn(List<BigDecimal> values) {
            addCriterion("Price in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotIn(List<BigDecimal> values) {
            addCriterion("Price not in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("Price between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("Price not between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPkPaytermIsNull() {
            addCriterion("PK_PAYTERM is null");
            return (Criteria) this;
        }

        public Criteria andPkPaytermIsNotNull() {
            addCriterion("PK_PAYTERM is not null");
            return (Criteria) this;
        }

        public Criteria andPkPaytermEqualTo(String value) {
            addCriterion("PK_PAYTERM =", value, "pkPayterm");
            return (Criteria) this;
        }

        public Criteria andPkPaytermNotEqualTo(String value) {
            addCriterion("PK_PAYTERM <>", value, "pkPayterm");
            return (Criteria) this;
        }

        public Criteria andPkPaytermGreaterThan(String value) {
            addCriterion("PK_PAYTERM >", value, "pkPayterm");
            return (Criteria) this;
        }

        public Criteria andPkPaytermGreaterThanOrEqualTo(String value) {
            addCriterion("PK_PAYTERM >=", value, "pkPayterm");
            return (Criteria) this;
        }

        public Criteria andPkPaytermLessThan(String value) {
            addCriterion("PK_PAYTERM <", value, "pkPayterm");
            return (Criteria) this;
        }

        public Criteria andPkPaytermLessThanOrEqualTo(String value) {
            addCriterion("PK_PAYTERM <=", value, "pkPayterm");
            return (Criteria) this;
        }

        public Criteria andPkPaytermLike(String value) {
            addCriterion("PK_PAYTERM like", value, "pkPayterm");
            return (Criteria) this;
        }

        public Criteria andPkPaytermNotLike(String value) {
            addCriterion("PK_PAYTERM not like", value, "pkPayterm");
            return (Criteria) this;
        }

        public Criteria andPkPaytermIn(List<String> values) {
            addCriterion("PK_PAYTERM in", values, "pkPayterm");
            return (Criteria) this;
        }

        public Criteria andPkPaytermNotIn(List<String> values) {
            addCriterion("PK_PAYTERM not in", values, "pkPayterm");
            return (Criteria) this;
        }

        public Criteria andPkPaytermBetween(String value1, String value2) {
            addCriterion("PK_PAYTERM between", value1, value2, "pkPayterm");
            return (Criteria) this;
        }

        public Criteria andPkPaytermNotBetween(String value1, String value2) {
            addCriterion("PK_PAYTERM not between", value1, value2, "pkPayterm");
            return (Criteria) this;
        }

        public Criteria andAccountingcodeIsNull() {
            addCriterion("AccountingCode is null");
            return (Criteria) this;
        }

        public Criteria andAccountingcodeIsNotNull() {
            addCriterion("AccountingCode is not null");
            return (Criteria) this;
        }

        public Criteria andAccountingcodeEqualTo(String value) {
            addCriterion("AccountingCode =", value, "accountingcode");
            return (Criteria) this;
        }

        public Criteria andAccountingcodeNotEqualTo(String value) {
            addCriterion("AccountingCode <>", value, "accountingcode");
            return (Criteria) this;
        }

        public Criteria andAccountingcodeGreaterThan(String value) {
            addCriterion("AccountingCode >", value, "accountingcode");
            return (Criteria) this;
        }

        public Criteria andAccountingcodeGreaterThanOrEqualTo(String value) {
            addCriterion("AccountingCode >=", value, "accountingcode");
            return (Criteria) this;
        }

        public Criteria andAccountingcodeLessThan(String value) {
            addCriterion("AccountingCode <", value, "accountingcode");
            return (Criteria) this;
        }

        public Criteria andAccountingcodeLessThanOrEqualTo(String value) {
            addCriterion("AccountingCode <=", value, "accountingcode");
            return (Criteria) this;
        }

        public Criteria andAccountingcodeLike(String value) {
            addCriterion("AccountingCode like", value, "accountingcode");
            return (Criteria) this;
        }

        public Criteria andAccountingcodeNotLike(String value) {
            addCriterion("AccountingCode not like", value, "accountingcode");
            return (Criteria) this;
        }

        public Criteria andAccountingcodeIn(List<String> values) {
            addCriterion("AccountingCode in", values, "accountingcode");
            return (Criteria) this;
        }

        public Criteria andAccountingcodeNotIn(List<String> values) {
            addCriterion("AccountingCode not in", values, "accountingcode");
            return (Criteria) this;
        }

        public Criteria andAccountingcodeBetween(String value1, String value2) {
            addCriterion("AccountingCode between", value1, value2, "accountingcode");
            return (Criteria) this;
        }

        public Criteria andAccountingcodeNotBetween(String value1, String value2) {
            addCriterion("AccountingCode not between", value1, value2, "accountingcode");
            return (Criteria) this;
        }

        public Criteria andCreationtimeIsNull() {
            addCriterion("CreationTime is null");
            return (Criteria) this;
        }

        public Criteria andCreationtimeIsNotNull() {
            addCriterion("CreationTime is not null");
            return (Criteria) this;
        }

        public Criteria andCreationtimeEqualTo(LocalDateTime value) {
            addCriterion("CreationTime =", value, "creationtime");
            return (Criteria) this;
        }

        public Criteria andCreationtimeNotEqualTo(LocalDateTime value) {
            addCriterion("CreationTime <>", value, "creationtime");
            return (Criteria) this;
        }

        public Criteria andCreationtimeGreaterThan(LocalDateTime value) {
            addCriterion("CreationTime >", value, "creationtime");
            return (Criteria) this;
        }

        public Criteria andCreationtimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("CreationTime >=", value, "creationtime");
            return (Criteria) this;
        }

        public Criteria andCreationtimeLessThan(LocalDateTime value) {
            addCriterion("CreationTime <", value, "creationtime");
            return (Criteria) this;
        }

        public Criteria andCreationtimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("CreationTime <=", value, "creationtime");
            return (Criteria) this;
        }

        public Criteria andCreationtimeIn(List<LocalDateTime> values) {
            addCriterion("CreationTime in", values, "creationtime");
            return (Criteria) this;
        }

        public Criteria andCreationtimeNotIn(List<LocalDateTime> values) {
            addCriterion("CreationTime not in", values, "creationtime");
            return (Criteria) this;
        }

        public Criteria andCreationtimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("CreationTime between", value1, value2, "creationtime");
            return (Criteria) this;
        }

        public Criteria andCreationtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("CreationTime not between", value1, value2, "creationtime");
            return (Criteria) this;
        }

        public Criteria andIsdeletedIsNull() {
            addCriterion("IsDeleted is null");
            return (Criteria) this;
        }

        public Criteria andIsdeletedIsNotNull() {
            addCriterion("IsDeleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsdeletedEqualTo(Boolean value) {
            addCriterion("IsDeleted =", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedNotEqualTo(Boolean value) {
            addCriterion("IsDeleted <>", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedGreaterThan(Boolean value) {
            addCriterion("IsDeleted >", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("IsDeleted >=", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedLessThan(Boolean value) {
            addCriterion("IsDeleted <", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("IsDeleted <=", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedIn(List<Boolean> values) {
            addCriterion("IsDeleted in", values, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedNotIn(List<Boolean> values) {
            addCriterion("IsDeleted not in", values, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("IsDeleted between", value1, value2, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("IsDeleted not between", value1, value2, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andSourceidIsNull() {
            addCriterion("SourceId is null");
            return (Criteria) this;
        }

        public Criteria andSourceidIsNotNull() {
            addCriterion("SourceId is not null");
            return (Criteria) this;
        }

        public Criteria andSourceidEqualTo(Integer value) {
            addCriterion("SourceId =", value, "sourceid");
            return (Criteria) this;
        }

        public Criteria andSourceidNotEqualTo(Integer value) {
            addCriterion("SourceId <>", value, "sourceid");
            return (Criteria) this;
        }

        public Criteria andSourceidGreaterThan(Integer value) {
            addCriterion("SourceId >", value, "sourceid");
            return (Criteria) this;
        }

        public Criteria andSourceidGreaterThanOrEqualTo(Integer value) {
            addCriterion("SourceId >=", value, "sourceid");
            return (Criteria) this;
        }

        public Criteria andSourceidLessThan(Integer value) {
            addCriterion("SourceId <", value, "sourceid");
            return (Criteria) this;
        }

        public Criteria andSourceidLessThanOrEqualTo(Integer value) {
            addCriterion("SourceId <=", value, "sourceid");
            return (Criteria) this;
        }

        public Criteria andSourceidIn(List<Integer> values) {
            addCriterion("SourceId in", values, "sourceid");
            return (Criteria) this;
        }

        public Criteria andSourceidNotIn(List<Integer> values) {
            addCriterion("SourceId not in", values, "sourceid");
            return (Criteria) this;
        }

        public Criteria andSourceidBetween(Integer value1, Integer value2) {
            addCriterion("SourceId between", value1, value2, "sourceid");
            return (Criteria) this;
        }

        public Criteria andSourceidNotBetween(Integer value1, Integer value2) {
            addCriterion("SourceId not between", value1, value2, "sourceid");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated do_not_delete_during_merge Mon May 30 11:24:32 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}