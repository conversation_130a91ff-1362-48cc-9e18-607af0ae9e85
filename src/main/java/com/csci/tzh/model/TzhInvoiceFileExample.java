package com.csci.tzh.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class TzhInvoiceFileExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_Invoice_File
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_Invoice_File
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_Invoice_File
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Invoice_File
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public TzhInvoiceFileExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Invoice_File
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Invoice_File
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Invoice_File
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Invoice_File
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Invoice_File
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Invoice_File
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Invoice_File
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Invoice_File
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Invoice_File
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Invoice_File
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_Invoice_File
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("Id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("Id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("Id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("Id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("Id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("Id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("Id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("Id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("Id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("Id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("Id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("Id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("Id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("Id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andParentidIsNull() {
			addCriterion("ParentId is null");
			return (Criteria) this;
		}

		public Criteria andParentidIsNotNull() {
			addCriterion("ParentId is not null");
			return (Criteria) this;
		}

		public Criteria andParentidEqualTo(String value) {
			addCriterion("ParentId =", value, "parentid");
			return (Criteria) this;
		}

		public Criteria andParentidNotEqualTo(String value) {
			addCriterion("ParentId <>", value, "parentid");
			return (Criteria) this;
		}

		public Criteria andParentidGreaterThan(String value) {
			addCriterion("ParentId >", value, "parentid");
			return (Criteria) this;
		}

		public Criteria andParentidGreaterThanOrEqualTo(String value) {
			addCriterion("ParentId >=", value, "parentid");
			return (Criteria) this;
		}

		public Criteria andParentidLessThan(String value) {
			addCriterion("ParentId <", value, "parentid");
			return (Criteria) this;
		}

		public Criteria andParentidLessThanOrEqualTo(String value) {
			addCriterion("ParentId <=", value, "parentid");
			return (Criteria) this;
		}

		public Criteria andParentidLike(String value) {
			addCriterion("ParentId like", value, "parentid");
			return (Criteria) this;
		}

		public Criteria andParentidNotLike(String value) {
			addCriterion("ParentId not like", value, "parentid");
			return (Criteria) this;
		}

		public Criteria andParentidIn(List<String> values) {
			addCriterion("ParentId in", values, "parentid");
			return (Criteria) this;
		}

		public Criteria andParentidNotIn(List<String> values) {
			addCriterion("ParentId not in", values, "parentid");
			return (Criteria) this;
		}

		public Criteria andParentidBetween(String value1, String value2) {
			addCriterion("ParentId between", value1, value2, "parentid");
			return (Criteria) this;
		}

		public Criteria andParentidNotBetween(String value1, String value2) {
			addCriterion("ParentId not between", value1, value2, "parentid");
			return (Criteria) this;
		}

		public Criteria andSitenameIsNull() {
			addCriterion("SiteName is null");
			return (Criteria) this;
		}

		public Criteria andSitenameIsNotNull() {
			addCriterion("SiteName is not null");
			return (Criteria) this;
		}

		public Criteria andSitenameEqualTo(String value) {
			addCriterion("SiteName =", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotEqualTo(String value) {
			addCriterion("SiteName <>", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameGreaterThan(String value) {
			addCriterion("SiteName >", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameGreaterThanOrEqualTo(String value) {
			addCriterion("SiteName >=", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLessThan(String value) {
			addCriterion("SiteName <", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLessThanOrEqualTo(String value) {
			addCriterion("SiteName <=", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLike(String value) {
			addCriterion("SiteName like", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotLike(String value) {
			addCriterion("SiteName not like", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameIn(List<String> values) {
			addCriterion("SiteName in", values, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotIn(List<String> values) {
			addCriterion("SiteName not in", values, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameBetween(String value1, String value2) {
			addCriterion("SiteName between", value1, value2, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotBetween(String value1, String value2) {
			addCriterion("SiteName not between", value1, value2, "sitename");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthIsNull() {
			addCriterion("RecordYearMonth is null");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthIsNotNull() {
			addCriterion("RecordYearMonth is not null");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthEqualTo(Integer value) {
			addCriterion("RecordYearMonth =", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthNotEqualTo(Integer value) {
			addCriterion("RecordYearMonth <>", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthGreaterThan(Integer value) {
			addCriterion("RecordYearMonth >", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthGreaterThanOrEqualTo(Integer value) {
			addCriterion("RecordYearMonth >=", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthLessThan(Integer value) {
			addCriterion("RecordYearMonth <", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthLessThanOrEqualTo(Integer value) {
			addCriterion("RecordYearMonth <=", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthIn(List<Integer> values) {
			addCriterion("RecordYearMonth in", values, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthNotIn(List<Integer> values) {
			addCriterion("RecordYearMonth not in", values, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthBetween(Integer value1, Integer value2) {
			addCriterion("RecordYearMonth between", value1, value2, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthNotBetween(Integer value1, Integer value2) {
			addCriterion("RecordYearMonth not between", value1, value2, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationIsNull() {
			addCriterion("CarbonEmissionLocation is null");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationIsNotNull() {
			addCriterion("CarbonEmissionLocation is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationEqualTo(String value) {
			addCriterion("CarbonEmissionLocation =", value, "carbonemissionlocation");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationNotEqualTo(String value) {
			addCriterion("CarbonEmissionLocation <>", value, "carbonemissionlocation");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationGreaterThan(String value) {
			addCriterion("CarbonEmissionLocation >", value, "carbonemissionlocation");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationGreaterThanOrEqualTo(String value) {
			addCriterion("CarbonEmissionLocation >=", value, "carbonemissionlocation");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationLessThan(String value) {
			addCriterion("CarbonEmissionLocation <", value, "carbonemissionlocation");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationLessThanOrEqualTo(String value) {
			addCriterion("CarbonEmissionLocation <=", value, "carbonemissionlocation");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationLike(String value) {
			addCriterion("CarbonEmissionLocation like", value, "carbonemissionlocation");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationNotLike(String value) {
			addCriterion("CarbonEmissionLocation not like", value, "carbonemissionlocation");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationIn(List<String> values) {
			addCriterion("CarbonEmissionLocation in", values, "carbonemissionlocation");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationNotIn(List<String> values) {
			addCriterion("CarbonEmissionLocation not in", values, "carbonemissionlocation");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationBetween(String value1, String value2) {
			addCriterion("CarbonEmissionLocation between", value1, value2, "carbonemissionlocation");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationNotBetween(String value1, String value2) {
			addCriterion("CarbonEmissionLocation not between", value1, value2, "carbonemissionlocation");
			return (Criteria) this;
		}

		public Criteria andSectionIsNull() {
			addCriterion("Section is null");
			return (Criteria) this;
		}

		public Criteria andSectionIsNotNull() {
			addCriterion("Section is not null");
			return (Criteria) this;
		}

		public Criteria andSectionEqualTo(String value) {
			addCriterion("Section =", value, "section");
			return (Criteria) this;
		}

		public Criteria andSectionNotEqualTo(String value) {
			addCriterion("Section <>", value, "section");
			return (Criteria) this;
		}

		public Criteria andSectionGreaterThan(String value) {
			addCriterion("Section >", value, "section");
			return (Criteria) this;
		}

		public Criteria andSectionGreaterThanOrEqualTo(String value) {
			addCriterion("Section >=", value, "section");
			return (Criteria) this;
		}

		public Criteria andSectionLessThan(String value) {
			addCriterion("Section <", value, "section");
			return (Criteria) this;
		}

		public Criteria andSectionLessThanOrEqualTo(String value) {
			addCriterion("Section <=", value, "section");
			return (Criteria) this;
		}

		public Criteria andSectionLike(String value) {
			addCriterion("Section like", value, "section");
			return (Criteria) this;
		}

		public Criteria andSectionNotLike(String value) {
			addCriterion("Section not like", value, "section");
			return (Criteria) this;
		}

		public Criteria andSectionIn(List<String> values) {
			addCriterion("Section in", values, "section");
			return (Criteria) this;
		}

		public Criteria andSectionNotIn(List<String> values) {
			addCriterion("Section not in", values, "section");
			return (Criteria) this;
		}

		public Criteria andSectionBetween(String value1, String value2) {
			addCriterion("Section between", value1, value2, "section");
			return (Criteria) this;
		}

		public Criteria andSectionNotBetween(String value1, String value2) {
			addCriterion("Section not between", value1, value2, "section");
			return (Criteria) this;
		}

		public Criteria andMaterialnameIsNull() {
			addCriterion("MaterialName is null");
			return (Criteria) this;
		}

		public Criteria andMaterialnameIsNotNull() {
			addCriterion("MaterialName is not null");
			return (Criteria) this;
		}

		public Criteria andMaterialnameEqualTo(String value) {
			addCriterion("MaterialName =", value, "materialname");
			return (Criteria) this;
		}

		public Criteria andMaterialnameNotEqualTo(String value) {
			addCriterion("MaterialName <>", value, "materialname");
			return (Criteria) this;
		}

		public Criteria andMaterialnameGreaterThan(String value) {
			addCriterion("MaterialName >", value, "materialname");
			return (Criteria) this;
		}

		public Criteria andMaterialnameGreaterThanOrEqualTo(String value) {
			addCriterion("MaterialName >=", value, "materialname");
			return (Criteria) this;
		}

		public Criteria andMaterialnameLessThan(String value) {
			addCriterion("MaterialName <", value, "materialname");
			return (Criteria) this;
		}

		public Criteria andMaterialnameLessThanOrEqualTo(String value) {
			addCriterion("MaterialName <=", value, "materialname");
			return (Criteria) this;
		}

		public Criteria andMaterialnameLike(String value) {
			addCriterion("MaterialName like", value, "materialname");
			return (Criteria) this;
		}

		public Criteria andMaterialnameNotLike(String value) {
			addCriterion("MaterialName not like", value, "materialname");
			return (Criteria) this;
		}

		public Criteria andMaterialnameIn(List<String> values) {
			addCriterion("MaterialName in", values, "materialname");
			return (Criteria) this;
		}

		public Criteria andMaterialnameNotIn(List<String> values) {
			addCriterion("MaterialName not in", values, "materialname");
			return (Criteria) this;
		}

		public Criteria andMaterialnameBetween(String value1, String value2) {
			addCriterion("MaterialName between", value1, value2, "materialname");
			return (Criteria) this;
		}

		public Criteria andMaterialnameNotBetween(String value1, String value2) {
			addCriterion("MaterialName not between", value1, value2, "materialname");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeIsNull() {
			addCriterion("MaterialCode is null");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeIsNotNull() {
			addCriterion("MaterialCode is not null");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeEqualTo(String value) {
			addCriterion("MaterialCode =", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeNotEqualTo(String value) {
			addCriterion("MaterialCode <>", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeGreaterThan(String value) {
			addCriterion("MaterialCode >", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeGreaterThanOrEqualTo(String value) {
			addCriterion("MaterialCode >=", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeLessThan(String value) {
			addCriterion("MaterialCode <", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeLessThanOrEqualTo(String value) {
			addCriterion("MaterialCode <=", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeLike(String value) {
			addCriterion("MaterialCode like", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeNotLike(String value) {
			addCriterion("MaterialCode not like", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeIn(List<String> values) {
			addCriterion("MaterialCode in", values, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeNotIn(List<String> values) {
			addCriterion("MaterialCode not in", values, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeBetween(String value1, String value2) {
			addCriterion("MaterialCode between", value1, value2, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeNotBetween(String value1, String value2) {
			addCriterion("MaterialCode not between", value1, value2, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeIsNull() {
			addCriterion("MaterialAttribute is null");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeIsNotNull() {
			addCriterion("MaterialAttribute is not null");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeEqualTo(String value) {
			addCriterion("MaterialAttribute =", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeNotEqualTo(String value) {
			addCriterion("MaterialAttribute <>", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeGreaterThan(String value) {
			addCriterion("MaterialAttribute >", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeGreaterThanOrEqualTo(String value) {
			addCriterion("MaterialAttribute >=", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeLessThan(String value) {
			addCriterion("MaterialAttribute <", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeLessThanOrEqualTo(String value) {
			addCriterion("MaterialAttribute <=", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeLike(String value) {
			addCriterion("MaterialAttribute like", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeNotLike(String value) {
			addCriterion("MaterialAttribute not like", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeIn(List<String> values) {
			addCriterion("MaterialAttribute in", values, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeNotIn(List<String> values) {
			addCriterion("MaterialAttribute not in", values, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeBetween(String value1, String value2) {
			addCriterion("MaterialAttribute between", value1, value2, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeNotBetween(String value1, String value2) {
			addCriterion("MaterialAttribute not between", value1, value2, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andBillnoIsNull() {
			addCriterion("BillNo is null");
			return (Criteria) this;
		}

		public Criteria andBillnoIsNotNull() {
			addCriterion("BillNo is not null");
			return (Criteria) this;
		}

		public Criteria andBillnoEqualTo(String value) {
			addCriterion("BillNo =", value, "billno");
			return (Criteria) this;
		}

		public Criteria andBillnoNotEqualTo(String value) {
			addCriterion("BillNo <>", value, "billno");
			return (Criteria) this;
		}

		public Criteria andBillnoGreaterThan(String value) {
			addCriterion("BillNo >", value, "billno");
			return (Criteria) this;
		}

		public Criteria andBillnoGreaterThanOrEqualTo(String value) {
			addCriterion("BillNo >=", value, "billno");
			return (Criteria) this;
		}

		public Criteria andBillnoLessThan(String value) {
			addCriterion("BillNo <", value, "billno");
			return (Criteria) this;
		}

		public Criteria andBillnoLessThanOrEqualTo(String value) {
			addCriterion("BillNo <=", value, "billno");
			return (Criteria) this;
		}

		public Criteria andBillnoLike(String value) {
			addCriterion("BillNo like", value, "billno");
			return (Criteria) this;
		}

		public Criteria andBillnoNotLike(String value) {
			addCriterion("BillNo not like", value, "billno");
			return (Criteria) this;
		}

		public Criteria andBillnoIn(List<String> values) {
			addCriterion("BillNo in", values, "billno");
			return (Criteria) this;
		}

		public Criteria andBillnoNotIn(List<String> values) {
			addCriterion("BillNo not in", values, "billno");
			return (Criteria) this;
		}

		public Criteria andBillnoBetween(String value1, String value2) {
			addCriterion("BillNo between", value1, value2, "billno");
			return (Criteria) this;
		}

		public Criteria andBillnoNotBetween(String value1, String value2) {
			addCriterion("BillNo not between", value1, value2, "billno");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNull() {
			addCriterion("CreatedBy is null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNotNull() {
			addCriterion("CreatedBy is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyEqualTo(String value) {
			addCriterion("CreatedBy =", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotEqualTo(String value) {
			addCriterion("CreatedBy <>", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThan(String value) {
			addCriterion("CreatedBy >", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
			addCriterion("CreatedBy >=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThan(String value) {
			addCriterion("CreatedBy <", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThanOrEqualTo(String value) {
			addCriterion("CreatedBy <=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLike(String value) {
			addCriterion("CreatedBy like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotLike(String value) {
			addCriterion("CreatedBy not like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIn(List<String> values) {
			addCriterion("CreatedBy in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotIn(List<String> values) {
			addCriterion("CreatedBy not in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyBetween(String value1, String value2) {
			addCriterion("CreatedBy between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotBetween(String value1, String value2) {
			addCriterion("CreatedBy not between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNull() {
			addCriterion("CreatedTime is null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNotNull() {
			addCriterion("CreatedTime is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime =", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <>", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThan(LocalDateTime value) {
			addCriterion("CreatedTime >", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime >=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThan(LocalDateTime value) {
			addCriterion("CreatedTime <", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime not in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime not between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNull() {
			addCriterion("DeletedBy is null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNotNull() {
			addCriterion("DeletedBy is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyEqualTo(String value) {
			addCriterion("DeletedBy =", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotEqualTo(String value) {
			addCriterion("DeletedBy <>", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThan(String value) {
			addCriterion("DeletedBy >", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThanOrEqualTo(String value) {
			addCriterion("DeletedBy >=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThan(String value) {
			addCriterion("DeletedBy <", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThanOrEqualTo(String value) {
			addCriterion("DeletedBy <=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLike(String value) {
			addCriterion("DeletedBy like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotLike(String value) {
			addCriterion("DeletedBy not like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIn(List<String> values) {
			addCriterion("DeletedBy in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotIn(List<String> values) {
			addCriterion("DeletedBy not in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyBetween(String value1, String value2) {
			addCriterion("DeletedBy between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotBetween(String value1, String value2) {
			addCriterion("DeletedBy not between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNull() {
			addCriterion("DeletedTime is null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNotNull() {
			addCriterion("DeletedTime is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime =", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <>", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThan(LocalDateTime value) {
			addCriterion("DeletedTime >", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime >=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThan(LocalDateTime value) {
			addCriterion("DeletedTime <", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime not in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime not between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNull() {
			addCriterion("IsDeleted is null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNotNull() {
			addCriterion("IsDeleted is not null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedEqualTo(Boolean value) {
			addCriterion("IsDeleted =", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotEqualTo(Boolean value) {
			addCriterion("IsDeleted <>", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThan(Boolean value) {
			addCriterion("IsDeleted >", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted >=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThan(Boolean value) {
			addCriterion("IsDeleted <", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted <=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIn(List<Boolean> values) {
			addCriterion("IsDeleted in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotIn(List<Boolean> values) {
			addCriterion("IsDeleted not in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted between", value1, value2, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted not between", value1, value2, "isdeleted");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_Invoice_File
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_Invoice_File
     *
     * @mbg.generated do_not_delete_during_merge Thu Oct 20 12:33:42 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}