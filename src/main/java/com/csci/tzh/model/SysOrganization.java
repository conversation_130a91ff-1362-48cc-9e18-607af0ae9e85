package com.csci.tzh.model;

import java.time.LocalDateTime;

public class SysOrganization {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.Id
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private Integer id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.InnerNo
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private String innerno;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.Code
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private String code;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.Name
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private String name;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.Type
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private Integer type;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.RelevantId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private Integer relevantid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.SortKey
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private Integer sortkey;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.TenantId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private Integer tenantid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.IsDeleted
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private Boolean isdeleted;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.DeleterUserId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private Long deleteruserid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.DeletionTime
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private LocalDateTime deletiontime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.LastModificationTime
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private LocalDateTime lastmodificationtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.LastModifierUserId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private Long lastmodifieruserid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.CreationTime
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private LocalDateTime creationtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.CreatorUserId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private Long creatoruserid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.SourceId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private String sourceid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.KeyInMdm
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private String keyinmdm;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column sysOrganization.TimeStamp
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	private byte[] timestamp;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.Id
	 * @return  the value of sysOrganization.Id
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.Id
	 * @param id  the value for sysOrganization.Id
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setId(Integer id) {
		this.id = id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.InnerNo
	 * @return  the value of sysOrganization.InnerNo
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public String getInnerno() {
		return innerno;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.InnerNo
	 * @param innerno  the value for sysOrganization.InnerNo
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setInnerno(String innerno) {
		this.innerno = innerno == null ? null : innerno.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.Code
	 * @return  the value of sysOrganization.Code
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public String getCode() {
		return code;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.Code
	 * @param code  the value for sysOrganization.Code
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setCode(String code) {
		this.code = code == null ? null : code.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.Name
	 * @return  the value of sysOrganization.Name
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public String getName() {
		return name;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.Name
	 * @param name  the value for sysOrganization.Name
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setName(String name) {
		this.name = name == null ? null : name.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.Type
	 * @return  the value of sysOrganization.Type
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public Integer getType() {
		return type;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.Type
	 * @param type  the value for sysOrganization.Type
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setType(Integer type) {
		this.type = type;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.RelevantId
	 * @return  the value of sysOrganization.RelevantId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public Integer getRelevantid() {
		return relevantid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.RelevantId
	 * @param relevantid  the value for sysOrganization.RelevantId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setRelevantid(Integer relevantid) {
		this.relevantid = relevantid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.SortKey
	 * @return  the value of sysOrganization.SortKey
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public Integer getSortkey() {
		return sortkey;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.SortKey
	 * @param sortkey  the value for sysOrganization.SortKey
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setSortkey(Integer sortkey) {
		this.sortkey = sortkey;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.TenantId
	 * @return  the value of sysOrganization.TenantId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public Integer getTenantid() {
		return tenantid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.TenantId
	 * @param tenantid  the value for sysOrganization.TenantId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setTenantid(Integer tenantid) {
		this.tenantid = tenantid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.IsDeleted
	 * @return  the value of sysOrganization.IsDeleted
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public Boolean getIsdeleted() {
		return isdeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.IsDeleted
	 * @param isdeleted  the value for sysOrganization.IsDeleted
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setIsdeleted(Boolean isdeleted) {
		this.isdeleted = isdeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.DeleterUserId
	 * @return  the value of sysOrganization.DeleterUserId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public Long getDeleteruserid() {
		return deleteruserid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.DeleterUserId
	 * @param deleteruserid  the value for sysOrganization.DeleterUserId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setDeleteruserid(Long deleteruserid) {
		this.deleteruserid = deleteruserid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.DeletionTime
	 * @return  the value of sysOrganization.DeletionTime
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public LocalDateTime getDeletiontime() {
		return deletiontime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.DeletionTime
	 * @param deletiontime  the value for sysOrganization.DeletionTime
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setDeletiontime(LocalDateTime deletiontime) {
		this.deletiontime = deletiontime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.LastModificationTime
	 * @return  the value of sysOrganization.LastModificationTime
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public LocalDateTime getLastmodificationtime() {
		return lastmodificationtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.LastModificationTime
	 * @param lastmodificationtime  the value for sysOrganization.LastModificationTime
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setLastmodificationtime(LocalDateTime lastmodificationtime) {
		this.lastmodificationtime = lastmodificationtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.LastModifierUserId
	 * @return  the value of sysOrganization.LastModifierUserId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public Long getLastmodifieruserid() {
		return lastmodifieruserid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.LastModifierUserId
	 * @param lastmodifieruserid  the value for sysOrganization.LastModifierUserId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setLastmodifieruserid(Long lastmodifieruserid) {
		this.lastmodifieruserid = lastmodifieruserid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.CreationTime
	 * @return  the value of sysOrganization.CreationTime
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public LocalDateTime getCreationtime() {
		return creationtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.CreationTime
	 * @param creationtime  the value for sysOrganization.CreationTime
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setCreationtime(LocalDateTime creationtime) {
		this.creationtime = creationtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.CreatorUserId
	 * @return  the value of sysOrganization.CreatorUserId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public Long getCreatoruserid() {
		return creatoruserid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.CreatorUserId
	 * @param creatoruserid  the value for sysOrganization.CreatorUserId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setCreatoruserid(Long creatoruserid) {
		this.creatoruserid = creatoruserid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.SourceId
	 * @return  the value of sysOrganization.SourceId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public String getSourceid() {
		return sourceid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.SourceId
	 * @param sourceid  the value for sysOrganization.SourceId
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setSourceid(String sourceid) {
		this.sourceid = sourceid == null ? null : sourceid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.KeyInMdm
	 * @return  the value of sysOrganization.KeyInMdm
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public String getKeyinmdm() {
		return keyinmdm;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.KeyInMdm
	 * @param keyinmdm  the value for sysOrganization.KeyInMdm
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setKeyinmdm(String keyinmdm) {
		this.keyinmdm = keyinmdm == null ? null : keyinmdm.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column sysOrganization.TimeStamp
	 * @return  the value of sysOrganization.TimeStamp
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public byte[] getTimestamp() {
		return timestamp;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column sysOrganization.TimeStamp
	 * @param timestamp  the value for sysOrganization.TimeStamp
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setTimestamp(byte[] timestamp) {
		this.timestamp = timestamp;
	}
}