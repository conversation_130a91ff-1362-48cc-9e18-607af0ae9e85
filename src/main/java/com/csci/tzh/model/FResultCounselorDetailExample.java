package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class FResultCounselorDetailExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public FResultCounselorDetailExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andHeadIdIsNull() {
            addCriterion("head_id is null");
            return (Criteria) this;
        }

        public Criteria andHeadIdIsNotNull() {
            addCriterion("head_id is not null");
            return (Criteria) this;
        }

        public Criteria andHeadIdEqualTo(String value) {
            addCriterion("head_id =", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotEqualTo(String value) {
            addCriterion("head_id <>", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdGreaterThan(String value) {
            addCriterion("head_id >", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdGreaterThanOrEqualTo(String value) {
            addCriterion("head_id >=", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLessThan(String value) {
            addCriterion("head_id <", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLessThanOrEqualTo(String value) {
            addCriterion("head_id <=", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLike(String value) {
            addCriterion("head_id like", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotLike(String value) {
            addCriterion("head_id not like", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdIn(List<String> values) {
            addCriterion("head_id in", values, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotIn(List<String> values) {
            addCriterion("head_id not in", values, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdBetween(String value1, String value2) {
            addCriterion("head_id between", value1, value2, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotBetween(String value1, String value2) {
            addCriterion("head_id not between", value1, value2, "headId");
            return (Criteria) this;
        }

        public Criteria andCarbonEmissionLocationIsNull() {
            addCriterion("carbon_emission_location is null");
            return (Criteria) this;
        }

        public Criteria andCarbonEmissionLocationIsNotNull() {
            addCriterion("carbon_emission_location is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonEmissionLocationEqualTo(String value) {
            addCriterion("carbon_emission_location =", value, "carbonEmissionLocation");
            return (Criteria) this;
        }

        public Criteria andCarbonEmissionLocationNotEqualTo(String value) {
            addCriterion("carbon_emission_location <>", value, "carbonEmissionLocation");
            return (Criteria) this;
        }

        public Criteria andCarbonEmissionLocationGreaterThan(String value) {
            addCriterion("carbon_emission_location >", value, "carbonEmissionLocation");
            return (Criteria) this;
        }

        public Criteria andCarbonEmissionLocationGreaterThanOrEqualTo(String value) {
            addCriterion("carbon_emission_location >=", value, "carbonEmissionLocation");
            return (Criteria) this;
        }

        public Criteria andCarbonEmissionLocationLessThan(String value) {
            addCriterion("carbon_emission_location <", value, "carbonEmissionLocation");
            return (Criteria) this;
        }

        public Criteria andCarbonEmissionLocationLessThanOrEqualTo(String value) {
            addCriterion("carbon_emission_location <=", value, "carbonEmissionLocation");
            return (Criteria) this;
        }

        public Criteria andCarbonEmissionLocationLike(String value) {
            addCriterion("carbon_emission_location like", value, "carbonEmissionLocation");
            return (Criteria) this;
        }

        public Criteria andCarbonEmissionLocationNotLike(String value) {
            addCriterion("carbon_emission_location not like", value, "carbonEmissionLocation");
            return (Criteria) this;
        }

        public Criteria andCarbonEmissionLocationIn(List<String> values) {
            addCriterion("carbon_emission_location in", values, "carbonEmissionLocation");
            return (Criteria) this;
        }

        public Criteria andCarbonEmissionLocationNotIn(List<String> values) {
            addCriterion("carbon_emission_location not in", values, "carbonEmissionLocation");
            return (Criteria) this;
        }

        public Criteria andCarbonEmissionLocationBetween(String value1, String value2) {
            addCriterion("carbon_emission_location between", value1, value2, "carbonEmissionLocation");
            return (Criteria) this;
        }

        public Criteria andCarbonEmissionLocationNotBetween(String value1, String value2) {
            addCriterion("carbon_emission_location not between", value1, value2, "carbonEmissionLocation");
            return (Criteria) this;
        }

        public Criteria andScopeMainIsNull() {
            addCriterion("scope_main is null");
            return (Criteria) this;
        }

        public Criteria andScopeMainIsNotNull() {
            addCriterion("scope_main is not null");
            return (Criteria) this;
        }

        public Criteria andScopeMainEqualTo(String value) {
            addCriterion("scope_main =", value, "scopeMain");
            return (Criteria) this;
        }

        public Criteria andScopeMainNotEqualTo(String value) {
            addCriterion("scope_main <>", value, "scopeMain");
            return (Criteria) this;
        }

        public Criteria andScopeMainGreaterThan(String value) {
            addCriterion("scope_main >", value, "scopeMain");
            return (Criteria) this;
        }

        public Criteria andScopeMainGreaterThanOrEqualTo(String value) {
            addCriterion("scope_main >=", value, "scopeMain");
            return (Criteria) this;
        }

        public Criteria andScopeMainLessThan(String value) {
            addCriterion("scope_main <", value, "scopeMain");
            return (Criteria) this;
        }

        public Criteria andScopeMainLessThanOrEqualTo(String value) {
            addCriterion("scope_main <=", value, "scopeMain");
            return (Criteria) this;
        }

        public Criteria andScopeMainLike(String value) {
            addCriterion("scope_main like", value, "scopeMain");
            return (Criteria) this;
        }

        public Criteria andScopeMainNotLike(String value) {
            addCriterion("scope_main not like", value, "scopeMain");
            return (Criteria) this;
        }

        public Criteria andScopeMainIn(List<String> values) {
            addCriterion("scope_main in", values, "scopeMain");
            return (Criteria) this;
        }

        public Criteria andScopeMainNotIn(List<String> values) {
            addCriterion("scope_main not in", values, "scopeMain");
            return (Criteria) this;
        }

        public Criteria andScopeMainBetween(String value1, String value2) {
            addCriterion("scope_main between", value1, value2, "scopeMain");
            return (Criteria) this;
        }

        public Criteria andScopeMainNotBetween(String value1, String value2) {
            addCriterion("scope_main not between", value1, value2, "scopeMain");
            return (Criteria) this;
        }

        public Criteria andScopeDetailIsNull() {
            addCriterion("scope_detail is null");
            return (Criteria) this;
        }

        public Criteria andScopeDetailIsNotNull() {
            addCriterion("scope_detail is not null");
            return (Criteria) this;
        }

        public Criteria andScopeDetailEqualTo(String value) {
            addCriterion("scope_detail =", value, "scopeDetail");
            return (Criteria) this;
        }

        public Criteria andScopeDetailNotEqualTo(String value) {
            addCriterion("scope_detail <>", value, "scopeDetail");
            return (Criteria) this;
        }

        public Criteria andScopeDetailGreaterThan(String value) {
            addCriterion("scope_detail >", value, "scopeDetail");
            return (Criteria) this;
        }

        public Criteria andScopeDetailGreaterThanOrEqualTo(String value) {
            addCriterion("scope_detail >=", value, "scopeDetail");
            return (Criteria) this;
        }

        public Criteria andScopeDetailLessThan(String value) {
            addCriterion("scope_detail <", value, "scopeDetail");
            return (Criteria) this;
        }

        public Criteria andScopeDetailLessThanOrEqualTo(String value) {
            addCriterion("scope_detail <=", value, "scopeDetail");
            return (Criteria) this;
        }

        public Criteria andScopeDetailLike(String value) {
            addCriterion("scope_detail like", value, "scopeDetail");
            return (Criteria) this;
        }

        public Criteria andScopeDetailNotLike(String value) {
            addCriterion("scope_detail not like", value, "scopeDetail");
            return (Criteria) this;
        }

        public Criteria andScopeDetailIn(List<String> values) {
            addCriterion("scope_detail in", values, "scopeDetail");
            return (Criteria) this;
        }

        public Criteria andScopeDetailNotIn(List<String> values) {
            addCriterion("scope_detail not in", values, "scopeDetail");
            return (Criteria) this;
        }

        public Criteria andScopeDetailBetween(String value1, String value2) {
            addCriterion("scope_detail between", value1, value2, "scopeDetail");
            return (Criteria) this;
        }

        public Criteria andScopeDetailNotBetween(String value1, String value2) {
            addCriterion("scope_detail not between", value1, value2, "scopeDetail");
            return (Criteria) this;
        }

        public Criteria andCarbonAmountIsNull() {
            addCriterion("carbon_amount is null");
            return (Criteria) this;
        }

        public Criteria andCarbonAmountIsNotNull() {
            addCriterion("carbon_amount is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonAmountEqualTo(BigDecimal value) {
            addCriterion("carbon_amount =", value, "carbonAmount");
            return (Criteria) this;
        }

        public Criteria andCarbonAmountNotEqualTo(BigDecimal value) {
            addCriterion("carbon_amount <>", value, "carbonAmount");
            return (Criteria) this;
        }

        public Criteria andCarbonAmountGreaterThan(BigDecimal value) {
            addCriterion("carbon_amount >", value, "carbonAmount");
            return (Criteria) this;
        }

        public Criteria andCarbonAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("carbon_amount >=", value, "carbonAmount");
            return (Criteria) this;
        }

        public Criteria andCarbonAmountLessThan(BigDecimal value) {
            addCriterion("carbon_amount <", value, "carbonAmount");
            return (Criteria) this;
        }

        public Criteria andCarbonAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("carbon_amount <=", value, "carbonAmount");
            return (Criteria) this;
        }

        public Criteria andCarbonAmountIn(List<BigDecimal> values) {
            addCriterion("carbon_amount in", values, "carbonAmount");
            return (Criteria) this;
        }

        public Criteria andCarbonAmountNotIn(List<BigDecimal> values) {
            addCriterion("carbon_amount not in", values, "carbonAmount");
            return (Criteria) this;
        }

        public Criteria andCarbonAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carbon_amount between", value1, value2, "carbonAmount");
            return (Criteria) this;
        }

        public Criteria andCarbonAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carbon_amount not between", value1, value2, "carbonAmount");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNull() {
            addCriterion("creation_time is null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNotNull() {
            addCriterion("creation_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeEqualTo(LocalDateTime value) {
            addCriterion("creation_time =", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
            addCriterion("creation_time <>", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
            addCriterion("creation_time >", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time >=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThan(LocalDateTime value) {
            addCriterion("creation_time <", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time <=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIn(List<LocalDateTime> values) {
            addCriterion("creation_time in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
            addCriterion("creation_time not in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time not between", value1, value2, "creationTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated do_not_delete_during_merge Mon Jan 06 11:53:51 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table f_result_counselor_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}