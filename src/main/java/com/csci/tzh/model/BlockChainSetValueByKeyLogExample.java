package com.csci.tzh.model;

import java.util.ArrayList;
import java.util.List;

public class BlockChainSetValueByKeyLogExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public BlockChainSetValueByKeyLogExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andLogdatetimeIsNull() {
            addCriterion("Logdatetime is null");
            return (Criteria) this;
        }

        public Criteria andLogdatetimeIsNotNull() {
            addCriterion("Logdatetime is not null");
            return (Criteria) this;
        }

        public Criteria andLogdatetimeEqualTo(String value) {
            addCriterion("Logdatetime =", value, "logdatetime");
            return (Criteria) this;
        }

        public Criteria andLogdatetimeNotEqualTo(String value) {
            addCriterion("Logdatetime <>", value, "logdatetime");
            return (Criteria) this;
        }

        public Criteria andLogdatetimeGreaterThan(String value) {
            addCriterion("Logdatetime >", value, "logdatetime");
            return (Criteria) this;
        }

        public Criteria andLogdatetimeGreaterThanOrEqualTo(String value) {
            addCriterion("Logdatetime >=", value, "logdatetime");
            return (Criteria) this;
        }

        public Criteria andLogdatetimeLessThan(String value) {
            addCriterion("Logdatetime <", value, "logdatetime");
            return (Criteria) this;
        }

        public Criteria andLogdatetimeLessThanOrEqualTo(String value) {
            addCriterion("Logdatetime <=", value, "logdatetime");
            return (Criteria) this;
        }

        public Criteria andLogdatetimeLike(String value) {
            addCriterion("Logdatetime like", value, "logdatetime");
            return (Criteria) this;
        }

        public Criteria andLogdatetimeNotLike(String value) {
            addCriterion("Logdatetime not like", value, "logdatetime");
            return (Criteria) this;
        }

        public Criteria andLogdatetimeIn(List<String> values) {
            addCriterion("Logdatetime in", values, "logdatetime");
            return (Criteria) this;
        }

        public Criteria andLogdatetimeNotIn(List<String> values) {
            addCriterion("Logdatetime not in", values, "logdatetime");
            return (Criteria) this;
        }

        public Criteria andLogdatetimeBetween(String value1, String value2) {
            addCriterion("Logdatetime between", value1, value2, "logdatetime");
            return (Criteria) this;
        }

        public Criteria andLogdatetimeNotBetween(String value1, String value2) {
            addCriterion("Logdatetime not between", value1, value2, "logdatetime");
            return (Criteria) this;
        }

        public Criteria andFilepathIsNull() {
            addCriterion("FilePath is null");
            return (Criteria) this;
        }

        public Criteria andFilepathIsNotNull() {
            addCriterion("FilePath is not null");
            return (Criteria) this;
        }

        public Criteria andFilepathEqualTo(String value) {
            addCriterion("FilePath =", value, "filepath");
            return (Criteria) this;
        }

        public Criteria andFilepathNotEqualTo(String value) {
            addCriterion("FilePath <>", value, "filepath");
            return (Criteria) this;
        }

        public Criteria andFilepathGreaterThan(String value) {
            addCriterion("FilePath >", value, "filepath");
            return (Criteria) this;
        }

        public Criteria andFilepathGreaterThanOrEqualTo(String value) {
            addCriterion("FilePath >=", value, "filepath");
            return (Criteria) this;
        }

        public Criteria andFilepathLessThan(String value) {
            addCriterion("FilePath <", value, "filepath");
            return (Criteria) this;
        }

        public Criteria andFilepathLessThanOrEqualTo(String value) {
            addCriterion("FilePath <=", value, "filepath");
            return (Criteria) this;
        }

        public Criteria andFilepathLike(String value) {
            addCriterion("FilePath like", value, "filepath");
            return (Criteria) this;
        }

        public Criteria andFilepathNotLike(String value) {
            addCriterion("FilePath not like", value, "filepath");
            return (Criteria) this;
        }

        public Criteria andFilepathIn(List<String> values) {
            addCriterion("FilePath in", values, "filepath");
            return (Criteria) this;
        }

        public Criteria andFilepathNotIn(List<String> values) {
            addCriterion("FilePath not in", values, "filepath");
            return (Criteria) this;
        }

        public Criteria andFilepathBetween(String value1, String value2) {
            addCriterion("FilePath between", value1, value2, "filepath");
            return (Criteria) this;
        }

        public Criteria andFilepathNotBetween(String value1, String value2) {
            addCriterion("FilePath not between", value1, value2, "filepath");
            return (Criteria) this;
        }

        public Criteria andFilenameIsNull() {
            addCriterion("FileName is null");
            return (Criteria) this;
        }

        public Criteria andFilenameIsNotNull() {
            addCriterion("FileName is not null");
            return (Criteria) this;
        }

        public Criteria andFilenameEqualTo(String value) {
            addCriterion("FileName =", value, "filename");
            return (Criteria) this;
        }

        public Criteria andFilenameNotEqualTo(String value) {
            addCriterion("FileName <>", value, "filename");
            return (Criteria) this;
        }

        public Criteria andFilenameGreaterThan(String value) {
            addCriterion("FileName >", value, "filename");
            return (Criteria) this;
        }

        public Criteria andFilenameGreaterThanOrEqualTo(String value) {
            addCriterion("FileName >=", value, "filename");
            return (Criteria) this;
        }

        public Criteria andFilenameLessThan(String value) {
            addCriterion("FileName <", value, "filename");
            return (Criteria) this;
        }

        public Criteria andFilenameLessThanOrEqualTo(String value) {
            addCriterion("FileName <=", value, "filename");
            return (Criteria) this;
        }

        public Criteria andFilenameLike(String value) {
            addCriterion("FileName like", value, "filename");
            return (Criteria) this;
        }

        public Criteria andFilenameNotLike(String value) {
            addCriterion("FileName not like", value, "filename");
            return (Criteria) this;
        }

        public Criteria andFilenameIn(List<String> values) {
            addCriterion("FileName in", values, "filename");
            return (Criteria) this;
        }

        public Criteria andFilenameNotIn(List<String> values) {
            addCriterion("FileName not in", values, "filename");
            return (Criteria) this;
        }

        public Criteria andFilenameBetween(String value1, String value2) {
            addCriterion("FileName between", value1, value2, "filename");
            return (Criteria) this;
        }

        public Criteria andFilenameNotBetween(String value1, String value2) {
            addCriterion("FileName not between", value1, value2, "filename");
            return (Criteria) this;
        }

        public Criteria andMd5codeIsNull() {
            addCriterion("MD5Code is null");
            return (Criteria) this;
        }

        public Criteria andMd5codeIsNotNull() {
            addCriterion("MD5Code is not null");
            return (Criteria) this;
        }

        public Criteria andMd5codeEqualTo(String value) {
            addCriterion("MD5Code =", value, "md5code");
            return (Criteria) this;
        }

        public Criteria andMd5codeNotEqualTo(String value) {
            addCriterion("MD5Code <>", value, "md5code");
            return (Criteria) this;
        }

        public Criteria andMd5codeGreaterThan(String value) {
            addCriterion("MD5Code >", value, "md5code");
            return (Criteria) this;
        }

        public Criteria andMd5codeGreaterThanOrEqualTo(String value) {
            addCriterion("MD5Code >=", value, "md5code");
            return (Criteria) this;
        }

        public Criteria andMd5codeLessThan(String value) {
            addCriterion("MD5Code <", value, "md5code");
            return (Criteria) this;
        }

        public Criteria andMd5codeLessThanOrEqualTo(String value) {
            addCriterion("MD5Code <=", value, "md5code");
            return (Criteria) this;
        }

        public Criteria andMd5codeLike(String value) {
            addCriterion("MD5Code like", value, "md5code");
            return (Criteria) this;
        }

        public Criteria andMd5codeNotLike(String value) {
            addCriterion("MD5Code not like", value, "md5code");
            return (Criteria) this;
        }

        public Criteria andMd5codeIn(List<String> values) {
            addCriterion("MD5Code in", values, "md5code");
            return (Criteria) this;
        }

        public Criteria andMd5codeNotIn(List<String> values) {
            addCriterion("MD5Code not in", values, "md5code");
            return (Criteria) this;
        }

        public Criteria andMd5codeBetween(String value1, String value2) {
            addCriterion("MD5Code between", value1, value2, "md5code");
            return (Criteria) this;
        }

        public Criteria andMd5codeNotBetween(String value1, String value2) {
            addCriterion("MD5Code not between", value1, value2, "md5code");
            return (Criteria) this;
        }

        public Criteria andRequeststatuscodeIsNull() {
            addCriterion("RequestStatusCode is null");
            return (Criteria) this;
        }

        public Criteria andRequeststatuscodeIsNotNull() {
            addCriterion("RequestStatusCode is not null");
            return (Criteria) this;
        }

        public Criteria andRequeststatuscodeEqualTo(String value) {
            addCriterion("RequestStatusCode =", value, "requeststatuscode");
            return (Criteria) this;
        }

        public Criteria andRequeststatuscodeNotEqualTo(String value) {
            addCriterion("RequestStatusCode <>", value, "requeststatuscode");
            return (Criteria) this;
        }

        public Criteria andRequeststatuscodeGreaterThan(String value) {
            addCriterion("RequestStatusCode >", value, "requeststatuscode");
            return (Criteria) this;
        }

        public Criteria andRequeststatuscodeGreaterThanOrEqualTo(String value) {
            addCriterion("RequestStatusCode >=", value, "requeststatuscode");
            return (Criteria) this;
        }

        public Criteria andRequeststatuscodeLessThan(String value) {
            addCriterion("RequestStatusCode <", value, "requeststatuscode");
            return (Criteria) this;
        }

        public Criteria andRequeststatuscodeLessThanOrEqualTo(String value) {
            addCriterion("RequestStatusCode <=", value, "requeststatuscode");
            return (Criteria) this;
        }

        public Criteria andRequeststatuscodeLike(String value) {
            addCriterion("RequestStatusCode like", value, "requeststatuscode");
            return (Criteria) this;
        }

        public Criteria andRequeststatuscodeNotLike(String value) {
            addCriterion("RequestStatusCode not like", value, "requeststatuscode");
            return (Criteria) this;
        }

        public Criteria andRequeststatuscodeIn(List<String> values) {
            addCriterion("RequestStatusCode in", values, "requeststatuscode");
            return (Criteria) this;
        }

        public Criteria andRequeststatuscodeNotIn(List<String> values) {
            addCriterion("RequestStatusCode not in", values, "requeststatuscode");
            return (Criteria) this;
        }

        public Criteria andRequeststatuscodeBetween(String value1, String value2) {
            addCriterion("RequestStatusCode between", value1, value2, "requeststatuscode");
            return (Criteria) this;
        }

        public Criteria andRequeststatuscodeNotBetween(String value1, String value2) {
            addCriterion("RequestStatusCode not between", value1, value2, "requeststatuscode");
            return (Criteria) this;
        }

        public Criteria andRequesttextIsNull() {
            addCriterion("RequestText is null");
            return (Criteria) this;
        }

        public Criteria andRequesttextIsNotNull() {
            addCriterion("RequestText is not null");
            return (Criteria) this;
        }

        public Criteria andRequesttextEqualTo(String value) {
            addCriterion("RequestText =", value, "requesttext");
            return (Criteria) this;
        }

        public Criteria andRequesttextNotEqualTo(String value) {
            addCriterion("RequestText <>", value, "requesttext");
            return (Criteria) this;
        }

        public Criteria andRequesttextGreaterThan(String value) {
            addCriterion("RequestText >", value, "requesttext");
            return (Criteria) this;
        }

        public Criteria andRequesttextGreaterThanOrEqualTo(String value) {
            addCriterion("RequestText >=", value, "requesttext");
            return (Criteria) this;
        }

        public Criteria andRequesttextLessThan(String value) {
            addCriterion("RequestText <", value, "requesttext");
            return (Criteria) this;
        }

        public Criteria andRequesttextLessThanOrEqualTo(String value) {
            addCriterion("RequestText <=", value, "requesttext");
            return (Criteria) this;
        }

        public Criteria andRequesttextLike(String value) {
            addCriterion("RequestText like", value, "requesttext");
            return (Criteria) this;
        }

        public Criteria andRequesttextNotLike(String value) {
            addCriterion("RequestText not like", value, "requesttext");
            return (Criteria) this;
        }

        public Criteria andRequesttextIn(List<String> values) {
            addCriterion("RequestText in", values, "requesttext");
            return (Criteria) this;
        }

        public Criteria andRequesttextNotIn(List<String> values) {
            addCriterion("RequestText not in", values, "requesttext");
            return (Criteria) this;
        }

        public Criteria andRequesttextBetween(String value1, String value2) {
            addCriterion("RequestText between", value1, value2, "requesttext");
            return (Criteria) this;
        }

        public Criteria andRequesttextNotBetween(String value1, String value2) {
            addCriterion("RequestText not between", value1, value2, "requesttext");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated do_not_delete_during_merge Thu May 26 11:23:15 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table BlockChain_SetValueByKey_Log
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}