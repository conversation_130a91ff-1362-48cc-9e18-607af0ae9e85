package com.csci.tzh.model;

public class BlockChainSetValueByKeyLog {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column BlockChain_SetValueByKey_Log.Logdatetime
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    private String logdatetime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column BlockChain_SetValueByKey_Log.FilePath
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    private String filepath;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column BlockChain_SetValueByKey_Log.FileName
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    private String filename;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column BlockChain_SetValueByKey_Log.MD5Code
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    private String md5code;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column BlockChain_SetValueByKey_Log.RequestStatusCode
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    private String requeststatuscode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column BlockChain_SetValueByKey_Log.RequestText
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    private String requesttext;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BlockChain_SetValueByKey_Log.Logdatetime
     *
     * @return the value of BlockChain_SetValueByKey_Log.Logdatetime
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public String getLogdatetime() {
        return logdatetime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BlockChain_SetValueByKey_Log.Logdatetime
     *
     * @param logdatetime the value for BlockChain_SetValueByKey_Log.Logdatetime
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public void setLogdatetime(String logdatetime) {
        this.logdatetime = logdatetime == null ? null : logdatetime.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BlockChain_SetValueByKey_Log.FilePath
     *
     * @return the value of BlockChain_SetValueByKey_Log.FilePath
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public String getFilepath() {
        return filepath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BlockChain_SetValueByKey_Log.FilePath
     *
     * @param filepath the value for BlockChain_SetValueByKey_Log.FilePath
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public void setFilepath(String filepath) {
        this.filepath = filepath == null ? null : filepath.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BlockChain_SetValueByKey_Log.FileName
     *
     * @return the value of BlockChain_SetValueByKey_Log.FileName
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public String getFilename() {
        return filename;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BlockChain_SetValueByKey_Log.FileName
     *
     * @param filename the value for BlockChain_SetValueByKey_Log.FileName
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public void setFilename(String filename) {
        this.filename = filename == null ? null : filename.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BlockChain_SetValueByKey_Log.MD5Code
     *
     * @return the value of BlockChain_SetValueByKey_Log.MD5Code
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public String getMd5code() {
        return md5code;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BlockChain_SetValueByKey_Log.MD5Code
     *
     * @param md5code the value for BlockChain_SetValueByKey_Log.MD5Code
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public void setMd5code(String md5code) {
        this.md5code = md5code == null ? null : md5code.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BlockChain_SetValueByKey_Log.RequestStatusCode
     *
     * @return the value of BlockChain_SetValueByKey_Log.RequestStatusCode
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public String getRequeststatuscode() {
        return requeststatuscode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BlockChain_SetValueByKey_Log.RequestStatusCode
     *
     * @param requeststatuscode the value for BlockChain_SetValueByKey_Log.RequestStatusCode
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public void setRequeststatuscode(String requeststatuscode) {
        this.requeststatuscode = requeststatuscode == null ? null : requeststatuscode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column BlockChain_SetValueByKey_Log.RequestText
     *
     * @return the value of BlockChain_SetValueByKey_Log.RequestText
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public String getRequesttext() {
        return requesttext;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column BlockChain_SetValueByKey_Log.RequestText
     *
     * @param requesttext the value for BlockChain_SetValueByKey_Log.RequestText
     *
     * @mbg.generated Thu May 26 11:23:15 HKT 2022
     */
    public void setRequesttext(String requesttext) {
        this.requesttext = requesttext == null ? null : requesttext.trim();
    }
}