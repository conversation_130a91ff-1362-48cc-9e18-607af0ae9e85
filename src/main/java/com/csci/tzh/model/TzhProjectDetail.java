package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class TzhProjectDetail {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.Id
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.SiteName
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private String sitename;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.ProtocolId
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private String protocolid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.CarbonEmissionLocationId
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private String carbonemissionlocationid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.EmissionReductionTarget
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private BigDecimal emissionreductiontarget;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.EpdWasteAccount
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private String epdwasteaccount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.EpdWasteAccountPwd
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private String epdwasteaccountpwd;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.HasFoodCourt
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private String hasfoodcourt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.DataSourceId
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private String datasourceid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.WasterWaterCarbonFactor
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private BigDecimal wasterwatercarbonfactor;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.WasterWaterCarbonFactorUnit
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private String wasterwatercarbonfactorunit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.WaterElectricityBillSource
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private String waterelectricitybillsource;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.AccountingCenterNo
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private String accountingcenterno;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.CreatedBy
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private String createdby;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.CreatedTime
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private LocalDateTime createdtime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.DeletedBy
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private String deletedby;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.DeletedTime
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private LocalDateTime deletedtime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.IsDeleted
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private Boolean isdeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_ProjectDetail.SiteId
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    private String siteid;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.Id
     *
     * @return the value of Tzh_ProjectDetail.Id
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.Id
     *
     * @param id the value for Tzh_ProjectDetail.Id
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.SiteName
     *
     * @return the value of Tzh_ProjectDetail.SiteName
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public String getSitename() {
        return sitename;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.SiteName
     *
     * @param sitename the value for Tzh_ProjectDetail.SiteName
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setSitename(String sitename) {
        this.sitename = sitename == null ? null : sitename.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.ProtocolId
     *
     * @return the value of Tzh_ProjectDetail.ProtocolId
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public String getProtocolid() {
        return protocolid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.ProtocolId
     *
     * @param protocolid the value for Tzh_ProjectDetail.ProtocolId
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setProtocolid(String protocolid) {
        this.protocolid = protocolid == null ? null : protocolid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.CarbonEmissionLocationId
     *
     * @return the value of Tzh_ProjectDetail.CarbonEmissionLocationId
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public String getCarbonemissionlocationid() {
        return carbonemissionlocationid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.CarbonEmissionLocationId
     *
     * @param carbonemissionlocationid the value for Tzh_ProjectDetail.CarbonEmissionLocationId
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setCarbonemissionlocationid(String carbonemissionlocationid) {
        this.carbonemissionlocationid = carbonemissionlocationid == null ? null : carbonemissionlocationid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.EmissionReductionTarget
     *
     * @return the value of Tzh_ProjectDetail.EmissionReductionTarget
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public BigDecimal getEmissionreductiontarget() {
        return emissionreductiontarget;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.EmissionReductionTarget
     *
     * @param emissionreductiontarget the value for Tzh_ProjectDetail.EmissionReductionTarget
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setEmissionreductiontarget(BigDecimal emissionreductiontarget) {
        this.emissionreductiontarget = emissionreductiontarget;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.EpdWasteAccount
     *
     * @return the value of Tzh_ProjectDetail.EpdWasteAccount
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public String getEpdwasteaccount() {
        return epdwasteaccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.EpdWasteAccount
     *
     * @param epdwasteaccount the value for Tzh_ProjectDetail.EpdWasteAccount
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setEpdwasteaccount(String epdwasteaccount) {
        this.epdwasteaccount = epdwasteaccount == null ? null : epdwasteaccount.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.EpdWasteAccountPwd
     *
     * @return the value of Tzh_ProjectDetail.EpdWasteAccountPwd
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public String getEpdwasteaccountpwd() {
        return epdwasteaccountpwd;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.EpdWasteAccountPwd
     *
     * @param epdwasteaccountpwd the value for Tzh_ProjectDetail.EpdWasteAccountPwd
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setEpdwasteaccountpwd(String epdwasteaccountpwd) {
        this.epdwasteaccountpwd = epdwasteaccountpwd == null ? null : epdwasteaccountpwd.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.HasFoodCourt
     *
     * @return the value of Tzh_ProjectDetail.HasFoodCourt
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public String getHasfoodcourt() {
        return hasfoodcourt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.HasFoodCourt
     *
     * @param hasfoodcourt the value for Tzh_ProjectDetail.HasFoodCourt
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setHasfoodcourt(String hasfoodcourt) {
        this.hasfoodcourt = hasfoodcourt == null ? null : hasfoodcourt.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.DataSourceId
     *
     * @return the value of Tzh_ProjectDetail.DataSourceId
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public String getDatasourceid() {
        return datasourceid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.DataSourceId
     *
     * @param datasourceid the value for Tzh_ProjectDetail.DataSourceId
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setDatasourceid(String datasourceid) {
        this.datasourceid = datasourceid == null ? null : datasourceid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.WasterWaterCarbonFactor
     *
     * @return the value of Tzh_ProjectDetail.WasterWaterCarbonFactor
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public BigDecimal getWasterwatercarbonfactor() {
        return wasterwatercarbonfactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.WasterWaterCarbonFactor
     *
     * @param wasterwatercarbonfactor the value for Tzh_ProjectDetail.WasterWaterCarbonFactor
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setWasterwatercarbonfactor(BigDecimal wasterwatercarbonfactor) {
        this.wasterwatercarbonfactor = wasterwatercarbonfactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.WasterWaterCarbonFactorUnit
     *
     * @return the value of Tzh_ProjectDetail.WasterWaterCarbonFactorUnit
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public String getWasterwatercarbonfactorunit() {
        return wasterwatercarbonfactorunit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.WasterWaterCarbonFactorUnit
     *
     * @param wasterwatercarbonfactorunit the value for Tzh_ProjectDetail.WasterWaterCarbonFactorUnit
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setWasterwatercarbonfactorunit(String wasterwatercarbonfactorunit) {
        this.wasterwatercarbonfactorunit = wasterwatercarbonfactorunit == null ? null : wasterwatercarbonfactorunit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.WaterElectricityBillSource
     *
     * @return the value of Tzh_ProjectDetail.WaterElectricityBillSource
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public String getWaterelectricitybillsource() {
        return waterelectricitybillsource;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.WaterElectricityBillSource
     *
     * @param waterelectricitybillsource the value for Tzh_ProjectDetail.WaterElectricityBillSource
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setWaterelectricitybillsource(String waterelectricitybillsource) {
        this.waterelectricitybillsource = waterelectricitybillsource == null ? null : waterelectricitybillsource.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.AccountingCenterNo
     *
     * @return the value of Tzh_ProjectDetail.AccountingCenterNo
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public String getAccountingcenterno() {
        return accountingcenterno;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.AccountingCenterNo
     *
     * @param accountingcenterno the value for Tzh_ProjectDetail.AccountingCenterNo
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setAccountingcenterno(String accountingcenterno) {
        this.accountingcenterno = accountingcenterno == null ? null : accountingcenterno.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.CreatedBy
     *
     * @return the value of Tzh_ProjectDetail.CreatedBy
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public String getCreatedby() {
        return createdby;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.CreatedBy
     *
     * @param createdby the value for Tzh_ProjectDetail.CreatedBy
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setCreatedby(String createdby) {
        this.createdby = createdby == null ? null : createdby.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.CreatedTime
     *
     * @return the value of Tzh_ProjectDetail.CreatedTime
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public LocalDateTime getCreatedtime() {
        return createdtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.CreatedTime
     *
     * @param createdtime the value for Tzh_ProjectDetail.CreatedTime
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setCreatedtime(LocalDateTime createdtime) {
        this.createdtime = createdtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.DeletedBy
     *
     * @return the value of Tzh_ProjectDetail.DeletedBy
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public String getDeletedby() {
        return deletedby;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.DeletedBy
     *
     * @param deletedby the value for Tzh_ProjectDetail.DeletedBy
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setDeletedby(String deletedby) {
        this.deletedby = deletedby == null ? null : deletedby.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.DeletedTime
     *
     * @return the value of Tzh_ProjectDetail.DeletedTime
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public LocalDateTime getDeletedtime() {
        return deletedtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.DeletedTime
     *
     * @param deletedtime the value for Tzh_ProjectDetail.DeletedTime
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setDeletedtime(LocalDateTime deletedtime) {
        this.deletedtime = deletedtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.IsDeleted
     *
     * @return the value of Tzh_ProjectDetail.IsDeleted
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public Boolean getIsdeleted() {
        return isdeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.IsDeleted
     *
     * @param isdeleted the value for Tzh_ProjectDetail.IsDeleted
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setIsdeleted(Boolean isdeleted) {
        this.isdeleted = isdeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_ProjectDetail.SiteId
     *
     * @return the value of Tzh_ProjectDetail.SiteId
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public String getSiteid() {
        return siteid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_ProjectDetail.SiteId
     *
     * @param siteid the value for Tzh_ProjectDetail.SiteId
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setSiteid(String siteid) {
        this.siteid = siteid == null ? null : siteid.trim();
    }
}