package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FpfExpenseDetail {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fpfExpenseDetail.Id
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fpfExpenseDetail.BillId
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    private Integer billid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fpfExpenseDetail.PK_DEPTDOC
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    private String pkDeptdoc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fpfExpenseDetail.InvoiceNo
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    private String invoiceno;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fpfExpenseDetail.InvoiceDate
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    private LocalDateTime invoicedate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fpfExpenseDetail.Digest
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    private String digest;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fpfExpenseDetail.Price
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    private BigDecimal price;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fpfExpenseDetail.PK_PAYTERM
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    private String pkPayterm;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fpfExpenseDetail.AccountingCode
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    private String accountingcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fpfExpenseDetail.CreationTime
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    private LocalDateTime creationtime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fpfExpenseDetail.IsDeleted
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    private Boolean isdeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column fpfExpenseDetail.SourceId
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    private Integer sourceid;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column fpfExpenseDetail.Id
     *
     * @return the value of fpfExpenseDetail.Id
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column fpfExpenseDetail.Id
     *
     * @param id the value for fpfExpenseDetail.Id
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column fpfExpenseDetail.BillId
     *
     * @return the value of fpfExpenseDetail.BillId
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public Integer getBillid() {
        return billid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column fpfExpenseDetail.BillId
     *
     * @param billid the value for fpfExpenseDetail.BillId
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public void setBillid(Integer billid) {
        this.billid = billid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column fpfExpenseDetail.PK_DEPTDOC
     *
     * @return the value of fpfExpenseDetail.PK_DEPTDOC
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public String getPkDeptdoc() {
        return pkDeptdoc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column fpfExpenseDetail.PK_DEPTDOC
     *
     * @param pkDeptdoc the value for fpfExpenseDetail.PK_DEPTDOC
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public void setPkDeptdoc(String pkDeptdoc) {
        this.pkDeptdoc = pkDeptdoc == null ? null : pkDeptdoc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column fpfExpenseDetail.InvoiceNo
     *
     * @return the value of fpfExpenseDetail.InvoiceNo
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public String getInvoiceno() {
        return invoiceno;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column fpfExpenseDetail.InvoiceNo
     *
     * @param invoiceno the value for fpfExpenseDetail.InvoiceNo
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public void setInvoiceno(String invoiceno) {
        this.invoiceno = invoiceno == null ? null : invoiceno.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column fpfExpenseDetail.InvoiceDate
     *
     * @return the value of fpfExpenseDetail.InvoiceDate
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public LocalDateTime getInvoicedate() {
        return invoicedate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column fpfExpenseDetail.InvoiceDate
     *
     * @param invoicedate the value for fpfExpenseDetail.InvoiceDate
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public void setInvoicedate(LocalDateTime invoicedate) {
        this.invoicedate = invoicedate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column fpfExpenseDetail.Digest
     *
     * @return the value of fpfExpenseDetail.Digest
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public String getDigest() {
        return digest;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column fpfExpenseDetail.Digest
     *
     * @param digest the value for fpfExpenseDetail.Digest
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public void setDigest(String digest) {
        this.digest = digest == null ? null : digest.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column fpfExpenseDetail.Price
     *
     * @return the value of fpfExpenseDetail.Price
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column fpfExpenseDetail.Price
     *
     * @param price the value for fpfExpenseDetail.Price
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column fpfExpenseDetail.PK_PAYTERM
     *
     * @return the value of fpfExpenseDetail.PK_PAYTERM
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public String getPkPayterm() {
        return pkPayterm;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column fpfExpenseDetail.PK_PAYTERM
     *
     * @param pkPayterm the value for fpfExpenseDetail.PK_PAYTERM
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public void setPkPayterm(String pkPayterm) {
        this.pkPayterm = pkPayterm == null ? null : pkPayterm.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column fpfExpenseDetail.AccountingCode
     *
     * @return the value of fpfExpenseDetail.AccountingCode
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public String getAccountingcode() {
        return accountingcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column fpfExpenseDetail.AccountingCode
     *
     * @param accountingcode the value for fpfExpenseDetail.AccountingCode
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public void setAccountingcode(String accountingcode) {
        this.accountingcode = accountingcode == null ? null : accountingcode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column fpfExpenseDetail.CreationTime
     *
     * @return the value of fpfExpenseDetail.CreationTime
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public LocalDateTime getCreationtime() {
        return creationtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column fpfExpenseDetail.CreationTime
     *
     * @param creationtime the value for fpfExpenseDetail.CreationTime
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public void setCreationtime(LocalDateTime creationtime) {
        this.creationtime = creationtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column fpfExpenseDetail.IsDeleted
     *
     * @return the value of fpfExpenseDetail.IsDeleted
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public Boolean getIsdeleted() {
        return isdeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column fpfExpenseDetail.IsDeleted
     *
     * @param isdeleted the value for fpfExpenseDetail.IsDeleted
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public void setIsdeleted(Boolean isdeleted) {
        this.isdeleted = isdeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column fpfExpenseDetail.SourceId
     *
     * @return the value of fpfExpenseDetail.SourceId
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public Integer getSourceid() {
        return sourceid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column fpfExpenseDetail.SourceId
     *
     * @param sourceid the value for fpfExpenseDetail.SourceId
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    public void setSourceid(Integer sourceid) {
        this.sourceid = sourceid;
    }
}