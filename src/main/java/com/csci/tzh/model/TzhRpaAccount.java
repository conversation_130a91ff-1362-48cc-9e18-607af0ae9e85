package com.csci.tzh.model;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class TzhRpaAccount {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_RpaAccount.Id
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_RpaAccount.ProjectName
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	private String projectname;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_RpaAccount.ProtocolId
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	private String protocolid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_RpaAccount.AccountType
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	private String accounttype;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_RpaAccount.AccountNo
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	private String accountno;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_RpaAccount.Username
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	private String username;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_RpaAccount.Password
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	private String password;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_RpaAccount.Supplier
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	private String supplier;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_RpaAccount.CarbonEmissionLocationId
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	private String carbonemissionlocationid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_RpaAccount.StartDate
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	private LocalDate startdate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_RpaAccount.EndDate
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	private LocalDate enddate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_RpaAccount.CreatedBy
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	private String createdby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_RpaAccount.CreatedTime
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	private LocalDateTime createdtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_RpaAccount.DeletedBy
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	private String deletedby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_RpaAccount.DeletedTime
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	private LocalDateTime deletedtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_RpaAccount.IsDeleted
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	private Boolean isdeleted;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_RpaAccount.Id
	 * @return  the value of Tzh_RpaAccount.Id
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_RpaAccount.Id
	 * @param id  the value for Tzh_RpaAccount.Id
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_RpaAccount.ProjectName
	 * @return  the value of Tzh_RpaAccount.ProjectName
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public String getProjectname() {
		return projectname;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_RpaAccount.ProjectName
	 * @param projectname  the value for Tzh_RpaAccount.ProjectName
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setProjectname(String projectname) {
		this.projectname = projectname == null ? null : projectname.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_RpaAccount.ProtocolId
	 * @return  the value of Tzh_RpaAccount.ProtocolId
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public String getProtocolid() {
		return protocolid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_RpaAccount.ProtocolId
	 * @param protocolid  the value for Tzh_RpaAccount.ProtocolId
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setProtocolid(String protocolid) {
		this.protocolid = protocolid == null ? null : protocolid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_RpaAccount.AccountType
	 * @return  the value of Tzh_RpaAccount.AccountType
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public String getAccounttype() {
		return accounttype;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_RpaAccount.AccountType
	 * @param accounttype  the value for Tzh_RpaAccount.AccountType
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setAccounttype(String accounttype) {
		this.accounttype = accounttype == null ? null : accounttype.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_RpaAccount.AccountNo
	 * @return  the value of Tzh_RpaAccount.AccountNo
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public String getAccountno() {
		return accountno;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_RpaAccount.AccountNo
	 * @param accountno  the value for Tzh_RpaAccount.AccountNo
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setAccountno(String accountno) {
		this.accountno = accountno == null ? null : accountno.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_RpaAccount.Username
	 * @return  the value of Tzh_RpaAccount.Username
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public String getUsername() {
		return username;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_RpaAccount.Username
	 * @param username  the value for Tzh_RpaAccount.Username
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setUsername(String username) {
		this.username = username == null ? null : username.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_RpaAccount.Password
	 * @return  the value of Tzh_RpaAccount.Password
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public String getPassword() {
		return password;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_RpaAccount.Password
	 * @param password  the value for Tzh_RpaAccount.Password
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setPassword(String password) {
		this.password = password == null ? null : password.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_RpaAccount.Supplier
	 * @return  the value of Tzh_RpaAccount.Supplier
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public String getSupplier() {
		return supplier;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_RpaAccount.Supplier
	 * @param supplier  the value for Tzh_RpaAccount.Supplier
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setSupplier(String supplier) {
		this.supplier = supplier == null ? null : supplier.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_RpaAccount.CarbonEmissionLocationId
	 * @return  the value of Tzh_RpaAccount.CarbonEmissionLocationId
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public String getCarbonemissionlocationid() {
		return carbonemissionlocationid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_RpaAccount.CarbonEmissionLocationId
	 * @param carbonemissionlocationid  the value for Tzh_RpaAccount.CarbonEmissionLocationId
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setCarbonemissionlocationid(String carbonemissionlocationid) {
		this.carbonemissionlocationid = carbonemissionlocationid == null ? null : carbonemissionlocationid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_RpaAccount.StartDate
	 * @return  the value of Tzh_RpaAccount.StartDate
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public LocalDate getStartdate() {
		return startdate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_RpaAccount.StartDate
	 * @param startdate  the value for Tzh_RpaAccount.StartDate
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setStartdate(LocalDate startdate) {
		this.startdate = startdate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_RpaAccount.EndDate
	 * @return  the value of Tzh_RpaAccount.EndDate
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public LocalDate getEnddate() {
		return enddate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_RpaAccount.EndDate
	 * @param enddate  the value for Tzh_RpaAccount.EndDate
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setEnddate(LocalDate enddate) {
		this.enddate = enddate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_RpaAccount.CreatedBy
	 * @return  the value of Tzh_RpaAccount.CreatedBy
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public String getCreatedby() {
		return createdby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_RpaAccount.CreatedBy
	 * @param createdby  the value for Tzh_RpaAccount.CreatedBy
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setCreatedby(String createdby) {
		this.createdby = createdby == null ? null : createdby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_RpaAccount.CreatedTime
	 * @return  the value of Tzh_RpaAccount.CreatedTime
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public LocalDateTime getCreatedtime() {
		return createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_RpaAccount.CreatedTime
	 * @param createdtime  the value for Tzh_RpaAccount.CreatedTime
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setCreatedtime(LocalDateTime createdtime) {
		this.createdtime = createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_RpaAccount.DeletedBy
	 * @return  the value of Tzh_RpaAccount.DeletedBy
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public String getDeletedby() {
		return deletedby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_RpaAccount.DeletedBy
	 * @param deletedby  the value for Tzh_RpaAccount.DeletedBy
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setDeletedby(String deletedby) {
		this.deletedby = deletedby == null ? null : deletedby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_RpaAccount.DeletedTime
	 * @return  the value of Tzh_RpaAccount.DeletedTime
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public LocalDateTime getDeletedtime() {
		return deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_RpaAccount.DeletedTime
	 * @param deletedtime  the value for Tzh_RpaAccount.DeletedTime
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setDeletedtime(LocalDateTime deletedtime) {
		this.deletedtime = deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_RpaAccount.IsDeleted
	 * @return  the value of Tzh_RpaAccount.IsDeleted
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public Boolean getIsdeleted() {
		return isdeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_RpaAccount.IsDeleted
	 * @param isdeleted  the value for Tzh_RpaAccount.IsDeleted
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setIsdeleted(Boolean isdeleted) {
		this.isdeleted = isdeleted;
	}
}