package com.csci.tzh.model;

import java.util.ArrayList;
import java.util.List;

public class TzhCarbonEmissionLocationSubCategoryExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public TzhCarbonEmissionLocationSubCategoryExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("Id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("Id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("Id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("Id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("Id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("Id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("Id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("Id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("Id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("Id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("Id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("Id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("Id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("Id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidIsNull() {
            addCriterion("CarbonEmissionLocationId is null");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidIsNotNull() {
            addCriterion("CarbonEmissionLocationId is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidEqualTo(String value) {
            addCriterion("CarbonEmissionLocationId =", value, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidNotEqualTo(String value) {
            addCriterion("CarbonEmissionLocationId <>", value, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidGreaterThan(String value) {
            addCriterion("CarbonEmissionLocationId >", value, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidGreaterThanOrEqualTo(String value) {
            addCriterion("CarbonEmissionLocationId >=", value, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidLessThan(String value) {
            addCriterion("CarbonEmissionLocationId <", value, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidLessThanOrEqualTo(String value) {
            addCriterion("CarbonEmissionLocationId <=", value, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidLike(String value) {
            addCriterion("CarbonEmissionLocationId like", value, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidNotLike(String value) {
            addCriterion("CarbonEmissionLocationId not like", value, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidIn(List<String> values) {
            addCriterion("CarbonEmissionLocationId in", values, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidNotIn(List<String> values) {
            addCriterion("CarbonEmissionLocationId not in", values, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidBetween(String value1, String value2) {
            addCriterion("CarbonEmissionLocationId between", value1, value2, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidNotBetween(String value1, String value2) {
            addCriterion("CarbonEmissionLocationId not between", value1, value2, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andSubcategoryidIsNull() {
            addCriterion("SubCategoryId is null");
            return (Criteria) this;
        }

        public Criteria andSubcategoryidIsNotNull() {
            addCriterion("SubCategoryId is not null");
            return (Criteria) this;
        }

        public Criteria andSubcategoryidEqualTo(String value) {
            addCriterion("SubCategoryId =", value, "subcategoryid");
            return (Criteria) this;
        }

        public Criteria andSubcategoryidNotEqualTo(String value) {
            addCriterion("SubCategoryId <>", value, "subcategoryid");
            return (Criteria) this;
        }

        public Criteria andSubcategoryidGreaterThan(String value) {
            addCriterion("SubCategoryId >", value, "subcategoryid");
            return (Criteria) this;
        }

        public Criteria andSubcategoryidGreaterThanOrEqualTo(String value) {
            addCriterion("SubCategoryId >=", value, "subcategoryid");
            return (Criteria) this;
        }

        public Criteria andSubcategoryidLessThan(String value) {
            addCriterion("SubCategoryId <", value, "subcategoryid");
            return (Criteria) this;
        }

        public Criteria andSubcategoryidLessThanOrEqualTo(String value) {
            addCriterion("SubCategoryId <=", value, "subcategoryid");
            return (Criteria) this;
        }

        public Criteria andSubcategoryidLike(String value) {
            addCriterion("SubCategoryId like", value, "subcategoryid");
            return (Criteria) this;
        }

        public Criteria andSubcategoryidNotLike(String value) {
            addCriterion("SubCategoryId not like", value, "subcategoryid");
            return (Criteria) this;
        }

        public Criteria andSubcategoryidIn(List<String> values) {
            addCriterion("SubCategoryId in", values, "subcategoryid");
            return (Criteria) this;
        }

        public Criteria andSubcategoryidNotIn(List<String> values) {
            addCriterion("SubCategoryId not in", values, "subcategoryid");
            return (Criteria) this;
        }

        public Criteria andSubcategoryidBetween(String value1, String value2) {
            addCriterion("SubCategoryId between", value1, value2, "subcategoryid");
            return (Criteria) this;
        }

        public Criteria andSubcategoryidNotBetween(String value1, String value2) {
            addCriterion("SubCategoryId not between", value1, value2, "subcategoryid");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated do_not_delete_during_merge Tue Mar 28 17:54:24 HKT 2023
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}