package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class DCdmsWasterWaterCarbonFactorExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    public DCdmsWasterWaterCarbonFactorExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andRegionIsNull() {
            addCriterion("Region is null");
            return (Criteria) this;
        }

        public Criteria andRegionIsNotNull() {
            addCriterion("Region is not null");
            return (Criteria) this;
        }

        public Criteria andRegionEqualTo(String value) {
            addCriterion("Region =", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotEqualTo(String value) {
            addCriterion("Region <>", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionGreaterThan(String value) {
            addCriterion("Region >", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionGreaterThanOrEqualTo(String value) {
            addCriterion("Region >=", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionLessThan(String value) {
            addCriterion("Region <", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionLessThanOrEqualTo(String value) {
            addCriterion("Region <=", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionLike(String value) {
            addCriterion("Region like", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotLike(String value) {
            addCriterion("Region not like", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionIn(List<String> values) {
            addCriterion("Region in", values, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotIn(List<String> values) {
            addCriterion("Region not in", values, "region");
            return (Criteria) this;
        }

        public Criteria andRegionBetween(String value1, String value2) {
            addCriterion("Region between", value1, value2, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotBetween(String value1, String value2) {
            addCriterion("Region not between", value1, value2, "region");
            return (Criteria) this;
        }

        public Criteria andSiteidIsNull() {
            addCriterion("SiteId is null");
            return (Criteria) this;
        }

        public Criteria andSiteidIsNotNull() {
            addCriterion("SiteId is not null");
            return (Criteria) this;
        }

        public Criteria andSiteidEqualTo(Integer value) {
            addCriterion("SiteId =", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidNotEqualTo(Integer value) {
            addCriterion("SiteId <>", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidGreaterThan(Integer value) {
            addCriterion("SiteId >", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidGreaterThanOrEqualTo(Integer value) {
            addCriterion("SiteId >=", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidLessThan(Integer value) {
            addCriterion("SiteId <", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidLessThanOrEqualTo(Integer value) {
            addCriterion("SiteId <=", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidIn(List<Integer> values) {
            addCriterion("SiteId in", values, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidNotIn(List<Integer> values) {
            addCriterion("SiteId not in", values, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidBetween(Integer value1, Integer value2) {
            addCriterion("SiteId between", value1, value2, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidNotBetween(Integer value1, Integer value2) {
            addCriterion("SiteId not between", value1, value2, "siteid");
            return (Criteria) this;
        }

        public Criteria andSitenameIsNull() {
            addCriterion("SiteName is null");
            return (Criteria) this;
        }

        public Criteria andSitenameIsNotNull() {
            addCriterion("SiteName is not null");
            return (Criteria) this;
        }

        public Criteria andSitenameEqualTo(String value) {
            addCriterion("SiteName =", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameNotEqualTo(String value) {
            addCriterion("SiteName <>", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameGreaterThan(String value) {
            addCriterion("SiteName >", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameGreaterThanOrEqualTo(String value) {
            addCriterion("SiteName >=", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameLessThan(String value) {
            addCriterion("SiteName <", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameLessThanOrEqualTo(String value) {
            addCriterion("SiteName <=", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameLike(String value) {
            addCriterion("SiteName like", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameNotLike(String value) {
            addCriterion("SiteName not like", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameIn(List<String> values) {
            addCriterion("SiteName in", values, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameNotIn(List<String> values) {
            addCriterion("SiteName not in", values, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameBetween(String value1, String value2) {
            addCriterion("SiteName between", value1, value2, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameNotBetween(String value1, String value2) {
            addCriterion("SiteName not between", value1, value2, "sitename");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationIsNull() {
            addCriterion("CarbonEmissionLocation is null");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationIsNotNull() {
            addCriterion("CarbonEmissionLocation is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationEqualTo(String value) {
            addCriterion("CarbonEmissionLocation =", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationNotEqualTo(String value) {
            addCriterion("CarbonEmissionLocation <>", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationGreaterThan(String value) {
            addCriterion("CarbonEmissionLocation >", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationGreaterThanOrEqualTo(String value) {
            addCriterion("CarbonEmissionLocation >=", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationLessThan(String value) {
            addCriterion("CarbonEmissionLocation <", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationLessThanOrEqualTo(String value) {
            addCriterion("CarbonEmissionLocation <=", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationLike(String value) {
            addCriterion("CarbonEmissionLocation like", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationNotLike(String value) {
            addCriterion("CarbonEmissionLocation not like", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationIn(List<String> values) {
            addCriterion("CarbonEmissionLocation in", values, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationNotIn(List<String> values) {
            addCriterion("CarbonEmissionLocation not in", values, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationBetween(String value1, String value2) {
            addCriterion("CarbonEmissionLocation between", value1, value2, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationNotBetween(String value1, String value2) {
            addCriterion("CarbonEmissionLocation not between", value1, value2, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorrecordyearmonthIsNull() {
            addCriterion("CarbonFactorRecordYearMonth is null");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorrecordyearmonthIsNotNull() {
            addCriterion("CarbonFactorRecordYearMonth is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorrecordyearmonthEqualTo(Integer value) {
            addCriterion("CarbonFactorRecordYearMonth =", value, "carbonfactorrecordyearmonth");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorrecordyearmonthNotEqualTo(Integer value) {
            addCriterion("CarbonFactorRecordYearMonth <>", value, "carbonfactorrecordyearmonth");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorrecordyearmonthGreaterThan(Integer value) {
            addCriterion("CarbonFactorRecordYearMonth >", value, "carbonfactorrecordyearmonth");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorrecordyearmonthGreaterThanOrEqualTo(Integer value) {
            addCriterion("CarbonFactorRecordYearMonth >=", value, "carbonfactorrecordyearmonth");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorrecordyearmonthLessThan(Integer value) {
            addCriterion("CarbonFactorRecordYearMonth <", value, "carbonfactorrecordyearmonth");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorrecordyearmonthLessThanOrEqualTo(Integer value) {
            addCriterion("CarbonFactorRecordYearMonth <=", value, "carbonfactorrecordyearmonth");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorrecordyearmonthIn(List<Integer> values) {
            addCriterion("CarbonFactorRecordYearMonth in", values, "carbonfactorrecordyearmonth");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorrecordyearmonthNotIn(List<Integer> values) {
            addCriterion("CarbonFactorRecordYearMonth not in", values, "carbonfactorrecordyearmonth");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorrecordyearmonthBetween(Integer value1, Integer value2) {
            addCriterion("CarbonFactorRecordYearMonth between", value1, value2, "carbonfactorrecordyearmonth");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorrecordyearmonthNotBetween(Integer value1, Integer value2) {
            addCriterion("CarbonFactorRecordYearMonth not between", value1, value2, "carbonfactorrecordyearmonth");
            return (Criteria) this;
        }

        public Criteria andVolumeIsNull() {
            addCriterion("Volume is null");
            return (Criteria) this;
        }

        public Criteria andVolumeIsNotNull() {
            addCriterion("Volume is not null");
            return (Criteria) this;
        }

        public Criteria andVolumeEqualTo(BigDecimal value) {
            addCriterion("Volume =", value, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeNotEqualTo(BigDecimal value) {
            addCriterion("Volume <>", value, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeGreaterThan(BigDecimal value) {
            addCriterion("Volume >", value, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("Volume >=", value, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeLessThan(BigDecimal value) {
            addCriterion("Volume <", value, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("Volume <=", value, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeIn(List<BigDecimal> values) {
            addCriterion("Volume in", values, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeNotIn(List<BigDecimal> values) {
            addCriterion("Volume not in", values, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("Volume between", value1, value2, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("Volume not between", value1, value2, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeUnitIsNull() {
            addCriterion("Volume_Unit is null");
            return (Criteria) this;
        }

        public Criteria andVolumeUnitIsNotNull() {
            addCriterion("Volume_Unit is not null");
            return (Criteria) this;
        }

        public Criteria andVolumeUnitEqualTo(String value) {
            addCriterion("Volume_Unit =", value, "volumeUnit");
            return (Criteria) this;
        }

        public Criteria andVolumeUnitNotEqualTo(String value) {
            addCriterion("Volume_Unit <>", value, "volumeUnit");
            return (Criteria) this;
        }

        public Criteria andVolumeUnitGreaterThan(String value) {
            addCriterion("Volume_Unit >", value, "volumeUnit");
            return (Criteria) this;
        }

        public Criteria andVolumeUnitGreaterThanOrEqualTo(String value) {
            addCriterion("Volume_Unit >=", value, "volumeUnit");
            return (Criteria) this;
        }

        public Criteria andVolumeUnitLessThan(String value) {
            addCriterion("Volume_Unit <", value, "volumeUnit");
            return (Criteria) this;
        }

        public Criteria andVolumeUnitLessThanOrEqualTo(String value) {
            addCriterion("Volume_Unit <=", value, "volumeUnit");
            return (Criteria) this;
        }

        public Criteria andVolumeUnitLike(String value) {
            addCriterion("Volume_Unit like", value, "volumeUnit");
            return (Criteria) this;
        }

        public Criteria andVolumeUnitNotLike(String value) {
            addCriterion("Volume_Unit not like", value, "volumeUnit");
            return (Criteria) this;
        }

        public Criteria andVolumeUnitIn(List<String> values) {
            addCriterion("Volume_Unit in", values, "volumeUnit");
            return (Criteria) this;
        }

        public Criteria andVolumeUnitNotIn(List<String> values) {
            addCriterion("Volume_Unit not in", values, "volumeUnit");
            return (Criteria) this;
        }

        public Criteria andVolumeUnitBetween(String value1, String value2) {
            addCriterion("Volume_Unit between", value1, value2, "volumeUnit");
            return (Criteria) this;
        }

        public Criteria andVolumeUnitNotBetween(String value1, String value2) {
            addCriterion("Volume_Unit not between", value1, value2, "volumeUnit");
            return (Criteria) this;
        }

        public Criteria andProportionindexIsNull() {
            addCriterion("ProportionIndex is null");
            return (Criteria) this;
        }

        public Criteria andProportionindexIsNotNull() {
            addCriterion("ProportionIndex is not null");
            return (Criteria) this;
        }

        public Criteria andProportionindexEqualTo(BigDecimal value) {
            addCriterion("ProportionIndex =", value, "proportionindex");
            return (Criteria) this;
        }

        public Criteria andProportionindexNotEqualTo(BigDecimal value) {
            addCriterion("ProportionIndex <>", value, "proportionindex");
            return (Criteria) this;
        }

        public Criteria andProportionindexGreaterThan(BigDecimal value) {
            addCriterion("ProportionIndex >", value, "proportionindex");
            return (Criteria) this;
        }

        public Criteria andProportionindexGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ProportionIndex >=", value, "proportionindex");
            return (Criteria) this;
        }

        public Criteria andProportionindexLessThan(BigDecimal value) {
            addCriterion("ProportionIndex <", value, "proportionindex");
            return (Criteria) this;
        }

        public Criteria andProportionindexLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ProportionIndex <=", value, "proportionindex");
            return (Criteria) this;
        }

        public Criteria andProportionindexIn(List<BigDecimal> values) {
            addCriterion("ProportionIndex in", values, "proportionindex");
            return (Criteria) this;
        }

        public Criteria andProportionindexNotIn(List<BigDecimal> values) {
            addCriterion("ProportionIndex not in", values, "proportionindex");
            return (Criteria) this;
        }

        public Criteria andProportionindexBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ProportionIndex between", value1, value2, "proportionindex");
            return (Criteria) this;
        }

        public Criteria andProportionindexNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ProportionIndex not between", value1, value2, "proportionindex");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorIsNull() {
            addCriterion("CarbonFactor is null");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorIsNotNull() {
            addCriterion("CarbonFactor is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorEqualTo(BigDecimal value) {
            addCriterion("CarbonFactor =", value, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorNotEqualTo(BigDecimal value) {
            addCriterion("CarbonFactor <>", value, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorGreaterThan(BigDecimal value) {
            addCriterion("CarbonFactor >", value, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("CarbonFactor >=", value, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorLessThan(BigDecimal value) {
            addCriterion("CarbonFactor <", value, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorLessThanOrEqualTo(BigDecimal value) {
            addCriterion("CarbonFactor <=", value, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorIn(List<BigDecimal> values) {
            addCriterion("CarbonFactor in", values, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorNotIn(List<BigDecimal> values) {
            addCriterion("CarbonFactor not in", values, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("CarbonFactor between", value1, value2, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("CarbonFactor not between", value1, value2, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitIsNull() {
            addCriterion("CarbonFactorUnit is null");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitIsNotNull() {
            addCriterion("CarbonFactorUnit is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitEqualTo(String value) {
            addCriterion("CarbonFactorUnit =", value, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitNotEqualTo(String value) {
            addCriterion("CarbonFactorUnit <>", value, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitGreaterThan(String value) {
            addCriterion("CarbonFactorUnit >", value, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitGreaterThanOrEqualTo(String value) {
            addCriterion("CarbonFactorUnit >=", value, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitLessThan(String value) {
            addCriterion("CarbonFactorUnit <", value, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitLessThanOrEqualTo(String value) {
            addCriterion("CarbonFactorUnit <=", value, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitLike(String value) {
            addCriterion("CarbonFactorUnit like", value, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitNotLike(String value) {
            addCriterion("CarbonFactorUnit not like", value, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitIn(List<String> values) {
            addCriterion("CarbonFactorUnit in", values, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitNotIn(List<String> values) {
            addCriterion("CarbonFactorUnit not in", values, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitBetween(String value1, String value2) {
            addCriterion("CarbonFactorUnit between", value1, value2, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitNotBetween(String value1, String value2) {
            addCriterion("CarbonFactorUnit not between", value1, value2, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andScopeIsNull() {
            addCriterion("Scope is null");
            return (Criteria) this;
        }

        public Criteria andScopeIsNotNull() {
            addCriterion("Scope is not null");
            return (Criteria) this;
        }

        public Criteria andScopeEqualTo(String value) {
            addCriterion("Scope =", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeNotEqualTo(String value) {
            addCriterion("Scope <>", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeGreaterThan(String value) {
            addCriterion("Scope >", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeGreaterThanOrEqualTo(String value) {
            addCriterion("Scope >=", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeLessThan(String value) {
            addCriterion("Scope <", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeLessThanOrEqualTo(String value) {
            addCriterion("Scope <=", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeLike(String value) {
            addCriterion("Scope like", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeNotLike(String value) {
            addCriterion("Scope not like", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeIn(List<String> values) {
            addCriterion("Scope in", values, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeNotIn(List<String> values) {
            addCriterion("Scope not in", values, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeBetween(String value1, String value2) {
            addCriterion("Scope between", value1, value2, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeNotBetween(String value1, String value2) {
            addCriterion("Scope not between", value1, value2, "scope");
            return (Criteria) this;
        }

        public Criteria andInputdatetimeIsNull() {
            addCriterion("InputDatetime is null");
            return (Criteria) this;
        }

        public Criteria andInputdatetimeIsNotNull() {
            addCriterion("InputDatetime is not null");
            return (Criteria) this;
        }

        public Criteria andInputdatetimeEqualTo(LocalDateTime value) {
            addCriterion("InputDatetime =", value, "inputdatetime");
            return (Criteria) this;
        }

        public Criteria andInputdatetimeNotEqualTo(LocalDateTime value) {
            addCriterion("InputDatetime <>", value, "inputdatetime");
            return (Criteria) this;
        }

        public Criteria andInputdatetimeGreaterThan(LocalDateTime value) {
            addCriterion("InputDatetime >", value, "inputdatetime");
            return (Criteria) this;
        }

        public Criteria andInputdatetimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("InputDatetime >=", value, "inputdatetime");
            return (Criteria) this;
        }

        public Criteria andInputdatetimeLessThan(LocalDateTime value) {
            addCriterion("InputDatetime <", value, "inputdatetime");
            return (Criteria) this;
        }

        public Criteria andInputdatetimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("InputDatetime <=", value, "inputdatetime");
            return (Criteria) this;
        }

        public Criteria andInputdatetimeIn(List<LocalDateTime> values) {
            addCriterion("InputDatetime in", values, "inputdatetime");
            return (Criteria) this;
        }

        public Criteria andInputdatetimeNotIn(List<LocalDateTime> values) {
            addCriterion("InputDatetime not in", values, "inputdatetime");
            return (Criteria) this;
        }

        public Criteria andInputdatetimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("InputDatetime between", value1, value2, "inputdatetime");
            return (Criteria) this;
        }

        public Criteria andInputdatetimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("InputDatetime not between", value1, value2, "inputdatetime");
            return (Criteria) this;
        }

        public Criteria andSyncdatetimeIsNull() {
            addCriterion("SyncDatetime is null");
            return (Criteria) this;
        }

        public Criteria andSyncdatetimeIsNotNull() {
            addCriterion("SyncDatetime is not null");
            return (Criteria) this;
        }

        public Criteria andSyncdatetimeEqualTo(LocalDateTime value) {
            addCriterion("SyncDatetime =", value, "syncdatetime");
            return (Criteria) this;
        }

        public Criteria andSyncdatetimeNotEqualTo(LocalDateTime value) {
            addCriterion("SyncDatetime <>", value, "syncdatetime");
            return (Criteria) this;
        }

        public Criteria andSyncdatetimeGreaterThan(LocalDateTime value) {
            addCriterion("SyncDatetime >", value, "syncdatetime");
            return (Criteria) this;
        }

        public Criteria andSyncdatetimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("SyncDatetime >=", value, "syncdatetime");
            return (Criteria) this;
        }

        public Criteria andSyncdatetimeLessThan(LocalDateTime value) {
            addCriterion("SyncDatetime <", value, "syncdatetime");
            return (Criteria) this;
        }

        public Criteria andSyncdatetimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("SyncDatetime <=", value, "syncdatetime");
            return (Criteria) this;
        }

        public Criteria andSyncdatetimeIn(List<LocalDateTime> values) {
            addCriterion("SyncDatetime in", values, "syncdatetime");
            return (Criteria) this;
        }

        public Criteria andSyncdatetimeNotIn(List<LocalDateTime> values) {
            addCriterion("SyncDatetime not in", values, "syncdatetime");
            return (Criteria) this;
        }

        public Criteria andSyncdatetimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("SyncDatetime between", value1, value2, "syncdatetime");
            return (Criteria) this;
        }

        public Criteria andSyncdatetimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("SyncDatetime not between", value1, value2, "syncdatetime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated do_not_delete_during_merge Tue Dec 06 14:43:53 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}