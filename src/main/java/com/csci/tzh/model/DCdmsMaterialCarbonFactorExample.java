package com.csci.tzh.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.time.LocalDateTime;

public class DCdmsMaterialCarbonFactorExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	public DCdmsMaterialCarbonFactorExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("Id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("Id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("Id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("Id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("Id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("Id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("Id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("Id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("Id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("Id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("Id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("Id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("Id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("Id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andChinesenameIsNull() {
			addCriterion("ChineseName is null");
			return (Criteria) this;
		}

		public Criteria andChinesenameIsNotNull() {
			addCriterion("ChineseName is not null");
			return (Criteria) this;
		}

		public Criteria andChinesenameEqualTo(String value) {
			addCriterion("ChineseName =", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameNotEqualTo(String value) {
			addCriterion("ChineseName <>", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameGreaterThan(String value) {
			addCriterion("ChineseName >", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameGreaterThanOrEqualTo(String value) {
			addCriterion("ChineseName >=", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameLessThan(String value) {
			addCriterion("ChineseName <", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameLessThanOrEqualTo(String value) {
			addCriterion("ChineseName <=", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameLike(String value) {
			addCriterion("ChineseName like", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameNotLike(String value) {
			addCriterion("ChineseName not like", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameIn(List<String> values) {
			addCriterion("ChineseName in", values, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameNotIn(List<String> values) {
			addCriterion("ChineseName not in", values, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameBetween(String value1, String value2) {
			addCriterion("ChineseName between", value1, value2, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameNotBetween(String value1, String value2) {
			addCriterion("ChineseName not between", value1, value2, "chinesename");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeIsNull() {
			addCriterion("MaterialCode is null");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeIsNotNull() {
			addCriterion("MaterialCode is not null");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeEqualTo(String value) {
			addCriterion("MaterialCode =", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeNotEqualTo(String value) {
			addCriterion("MaterialCode <>", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeGreaterThan(String value) {
			addCriterion("MaterialCode >", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeGreaterThanOrEqualTo(String value) {
			addCriterion("MaterialCode >=", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeLessThan(String value) {
			addCriterion("MaterialCode <", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeLessThanOrEqualTo(String value) {
			addCriterion("MaterialCode <=", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeLike(String value) {
			addCriterion("MaterialCode like", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeNotLike(String value) {
			addCriterion("MaterialCode not like", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeIn(List<String> values) {
			addCriterion("MaterialCode in", values, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeNotIn(List<String> values) {
			addCriterion("MaterialCode not in", values, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeBetween(String value1, String value2) {
			addCriterion("MaterialCode between", value1, value2, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeNotBetween(String value1, String value2) {
			addCriterion("MaterialCode not between", value1, value2, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeIsNull() {
			addCriterion("MaterialAttribute is null");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeIsNotNull() {
			addCriterion("MaterialAttribute is not null");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeEqualTo(String value) {
			addCriterion("MaterialAttribute =", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeNotEqualTo(String value) {
			addCriterion("MaterialAttribute <>", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeGreaterThan(String value) {
			addCriterion("MaterialAttribute >", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeGreaterThanOrEqualTo(String value) {
			addCriterion("MaterialAttribute >=", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeLessThan(String value) {
			addCriterion("MaterialAttribute <", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeLessThanOrEqualTo(String value) {
			addCriterion("MaterialAttribute <=", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeLike(String value) {
			addCriterion("MaterialAttribute like", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeNotLike(String value) {
			addCriterion("MaterialAttribute not like", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeIn(List<String> values) {
			addCriterion("MaterialAttribute in", values, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeNotIn(List<String> values) {
			addCriterion("MaterialAttribute not in", values, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeBetween(String value1, String value2) {
			addCriterion("MaterialAttribute between", value1, value2, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeNotBetween(String value1, String value2) {
			addCriterion("MaterialAttribute not between", value1, value2, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialtypeIsNull() {
			addCriterion("MaterialType is null");
			return (Criteria) this;
		}

		public Criteria andMaterialtypeIsNotNull() {
			addCriterion("MaterialType is not null");
			return (Criteria) this;
		}

		public Criteria andMaterialtypeEqualTo(String value) {
			addCriterion("MaterialType =", value, "materialtype");
			return (Criteria) this;
		}

		public Criteria andMaterialtypeNotEqualTo(String value) {
			addCriterion("MaterialType <>", value, "materialtype");
			return (Criteria) this;
		}

		public Criteria andMaterialtypeGreaterThan(String value) {
			addCriterion("MaterialType >", value, "materialtype");
			return (Criteria) this;
		}

		public Criteria andMaterialtypeGreaterThanOrEqualTo(String value) {
			addCriterion("MaterialType >=", value, "materialtype");
			return (Criteria) this;
		}

		public Criteria andMaterialtypeLessThan(String value) {
			addCriterion("MaterialType <", value, "materialtype");
			return (Criteria) this;
		}

		public Criteria andMaterialtypeLessThanOrEqualTo(String value) {
			addCriterion("MaterialType <=", value, "materialtype");
			return (Criteria) this;
		}

		public Criteria andMaterialtypeLike(String value) {
			addCriterion("MaterialType like", value, "materialtype");
			return (Criteria) this;
		}

		public Criteria andMaterialtypeNotLike(String value) {
			addCriterion("MaterialType not like", value, "materialtype");
			return (Criteria) this;
		}

		public Criteria andMaterialtypeIn(List<String> values) {
			addCriterion("MaterialType in", values, "materialtype");
			return (Criteria) this;
		}

		public Criteria andMaterialtypeNotIn(List<String> values) {
			addCriterion("MaterialType not in", values, "materialtype");
			return (Criteria) this;
		}

		public Criteria andMaterialtypeBetween(String value1, String value2) {
			addCriterion("MaterialType between", value1, value2, "materialtype");
			return (Criteria) this;
		}

		public Criteria andMaterialtypeNotBetween(String value1, String value2) {
			addCriterion("MaterialType not between", value1, value2, "materialtype");
			return (Criteria) this;
		}

		public Criteria andSitenameIsNull() {
			addCriterion("SiteName is null");
			return (Criteria) this;
		}

		public Criteria andSitenameIsNotNull() {
			addCriterion("SiteName is not null");
			return (Criteria) this;
		}

		public Criteria andSitenameEqualTo(String value) {
			addCriterion("SiteName =", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotEqualTo(String value) {
			addCriterion("SiteName <>", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameGreaterThan(String value) {
			addCriterion("SiteName >", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameGreaterThanOrEqualTo(String value) {
			addCriterion("SiteName >=", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLessThan(String value) {
			addCriterion("SiteName <", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLessThanOrEqualTo(String value) {
			addCriterion("SiteName <=", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLike(String value) {
			addCriterion("SiteName like", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotLike(String value) {
			addCriterion("SiteName not like", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameIn(List<String> values) {
			addCriterion("SiteName in", values, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotIn(List<String> values) {
			addCriterion("SiteName not in", values, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameBetween(String value1, String value2) {
			addCriterion("SiteName between", value1, value2, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotBetween(String value1, String value2) {
			addCriterion("SiteName not between", value1, value2, "sitename");
			return (Criteria) this;
		}

		public Criteria andUnitIsNull() {
			addCriterion("Unit is null");
			return (Criteria) this;
		}

		public Criteria andUnitIsNotNull() {
			addCriterion("Unit is not null");
			return (Criteria) this;
		}

		public Criteria andUnitEqualTo(String value) {
			addCriterion("Unit =", value, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitNotEqualTo(String value) {
			addCriterion("Unit <>", value, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitGreaterThan(String value) {
			addCriterion("Unit >", value, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitGreaterThanOrEqualTo(String value) {
			addCriterion("Unit >=", value, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitLessThan(String value) {
			addCriterion("Unit <", value, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitLessThanOrEqualTo(String value) {
			addCriterion("Unit <=", value, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitLike(String value) {
			addCriterion("Unit like", value, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitNotLike(String value) {
			addCriterion("Unit not like", value, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitIn(List<String> values) {
			addCriterion("Unit in", values, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitNotIn(List<String> values) {
			addCriterion("Unit not in", values, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitBetween(String value1, String value2) {
			addCriterion("Unit between", value1, value2, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitNotBetween(String value1, String value2) {
			addCriterion("Unit not between", value1, value2, "unit");
			return (Criteria) this;
		}

		public Criteria andDescriptionIsNull() {
			addCriterion("Description is null");
			return (Criteria) this;
		}

		public Criteria andDescriptionIsNotNull() {
			addCriterion("Description is not null");
			return (Criteria) this;
		}

		public Criteria andDescriptionEqualTo(String value) {
			addCriterion("Description =", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotEqualTo(String value) {
			addCriterion("Description <>", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionGreaterThan(String value) {
			addCriterion("Description >", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
			addCriterion("Description >=", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionLessThan(String value) {
			addCriterion("Description <", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionLessThanOrEqualTo(String value) {
			addCriterion("Description <=", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionLike(String value) {
			addCriterion("Description like", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotLike(String value) {
			addCriterion("Description not like", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionIn(List<String> values) {
			addCriterion("Description in", values, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotIn(List<String> values) {
			addCriterion("Description not in", values, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionBetween(String value1, String value2) {
			addCriterion("Description between", value1, value2, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotBetween(String value1, String value2) {
			addCriterion("Description not between", value1, value2, "description");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidIsNull() {
			addCriterion("CarbonEmissionLocationId is null");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidIsNotNull() {
			addCriterion("CarbonEmissionLocationId is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId =", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId <>", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidGreaterThan(String value) {
			addCriterion("CarbonEmissionLocationId >", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidGreaterThanOrEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId >=", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidLessThan(String value) {
			addCriterion("CarbonEmissionLocationId <", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidLessThanOrEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId <=", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidLike(String value) {
			addCriterion("CarbonEmissionLocationId like", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotLike(String value) {
			addCriterion("CarbonEmissionLocationId not like", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidIn(List<String> values) {
			addCriterion("CarbonEmissionLocationId in", values, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotIn(List<String> values) {
			addCriterion("CarbonEmissionLocationId not in", values, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidBetween(String value1, String value2) {
			addCriterion("CarbonEmissionLocationId between", value1, value2, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotBetween(String value1, String value2) {
			addCriterion("CarbonEmissionLocationId not between", value1, value2, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorIsNull() {
			addCriterion("CarbonFactor is null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorIsNotNull() {
			addCriterion("CarbonFactor is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor =", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorNotEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor <>", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorGreaterThan(BigDecimal value) {
			addCriterion("CarbonFactor >", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor >=", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorLessThan(BigDecimal value) {
			addCriterion("CarbonFactor <", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorLessThanOrEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor <=", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorIn(List<BigDecimal> values) {
			addCriterion("CarbonFactor in", values, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorNotIn(List<BigDecimal> values) {
			addCriterion("CarbonFactor not in", values, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("CarbonFactor between", value1, value2, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("CarbonFactor not between", value1, value2, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitIsNull() {
			addCriterion("CarbonFactorUnit is null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitIsNotNull() {
			addCriterion("CarbonFactorUnit is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitEqualTo(String value) {
			addCriterion("CarbonFactorUnit =", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotEqualTo(String value) {
			addCriterion("CarbonFactorUnit <>", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitGreaterThan(String value) {
			addCriterion("CarbonFactorUnit >", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitGreaterThanOrEqualTo(String value) {
			addCriterion("CarbonFactorUnit >=", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitLessThan(String value) {
			addCriterion("CarbonFactorUnit <", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitLessThanOrEqualTo(String value) {
			addCriterion("CarbonFactorUnit <=", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitLike(String value) {
			addCriterion("CarbonFactorUnit like", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotLike(String value) {
			addCriterion("CarbonFactorUnit not like", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitIn(List<String> values) {
			addCriterion("CarbonFactorUnit in", values, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotIn(List<String> values) {
			addCriterion("CarbonFactorUnit not in", values, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitBetween(String value1, String value2) {
			addCriterion("CarbonFactorUnit between", value1, value2, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotBetween(String value1, String value2) {
			addCriterion("CarbonFactorUnit not between", value1, value2, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorIsNull() {
			addCriterion("TransportFactor is null");
			return (Criteria) this;
		}

		public Criteria andTransportfactorIsNotNull() {
			addCriterion("TransportFactor is not null");
			return (Criteria) this;
		}

		public Criteria andTransportfactorEqualTo(BigDecimal value) {
			addCriterion("TransportFactor =", value, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorNotEqualTo(BigDecimal value) {
			addCriterion("TransportFactor <>", value, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorGreaterThan(BigDecimal value) {
			addCriterion("TransportFactor >", value, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("TransportFactor >=", value, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorLessThan(BigDecimal value) {
			addCriterion("TransportFactor <", value, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorLessThanOrEqualTo(BigDecimal value) {
			addCriterion("TransportFactor <=", value, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorIn(List<BigDecimal> values) {
			addCriterion("TransportFactor in", values, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorNotIn(List<BigDecimal> values) {
			addCriterion("TransportFactor not in", values, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("TransportFactor between", value1, value2, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("TransportFactor not between", value1, value2, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitIsNull() {
			addCriterion("TransportFactorUnit is null");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitIsNotNull() {
			addCriterion("TransportFactorUnit is not null");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitEqualTo(String value) {
			addCriterion("TransportFactorUnit =", value, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitNotEqualTo(String value) {
			addCriterion("TransportFactorUnit <>", value, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitGreaterThan(String value) {
			addCriterion("TransportFactorUnit >", value, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitGreaterThanOrEqualTo(String value) {
			addCriterion("TransportFactorUnit >=", value, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitLessThan(String value) {
			addCriterion("TransportFactorUnit <", value, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitLessThanOrEqualTo(String value) {
			addCriterion("TransportFactorUnit <=", value, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitLike(String value) {
			addCriterion("TransportFactorUnit like", value, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitNotLike(String value) {
			addCriterion("TransportFactorUnit not like", value, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitIn(List<String> values) {
			addCriterion("TransportFactorUnit in", values, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitNotIn(List<String> values) {
			addCriterion("TransportFactorUnit not in", values, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitBetween(String value1, String value2) {
			addCriterion("TransportFactorUnit between", value1, value2, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitNotBetween(String value1, String value2) {
			addCriterion("TransportFactorUnit not between", value1, value2, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceIsNull() {
			addCriterion("TransportDistance is null");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceIsNotNull() {
			addCriterion("TransportDistance is not null");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceEqualTo(BigDecimal value) {
			addCriterion("TransportDistance =", value, "transportdistance");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceNotEqualTo(BigDecimal value) {
			addCriterion("TransportDistance <>", value, "transportdistance");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceGreaterThan(BigDecimal value) {
			addCriterion("TransportDistance >", value, "transportdistance");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("TransportDistance >=", value, "transportdistance");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceLessThan(BigDecimal value) {
			addCriterion("TransportDistance <", value, "transportdistance");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceLessThanOrEqualTo(BigDecimal value) {
			addCriterion("TransportDistance <=", value, "transportdistance");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceIn(List<BigDecimal> values) {
			addCriterion("TransportDistance in", values, "transportdistance");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceNotIn(List<BigDecimal> values) {
			addCriterion("TransportDistance not in", values, "transportdistance");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("TransportDistance between", value1, value2, "transportdistance");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("TransportDistance not between", value1, value2, "transportdistance");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceunitIsNull() {
			addCriterion("TransportDistanceUnit is null");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceunitIsNotNull() {
			addCriterion("TransportDistanceUnit is not null");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceunitEqualTo(String value) {
			addCriterion("TransportDistanceUnit =", value, "transportdistanceunit");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceunitNotEqualTo(String value) {
			addCriterion("TransportDistanceUnit <>", value, "transportdistanceunit");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceunitGreaterThan(String value) {
			addCriterion("TransportDistanceUnit >", value, "transportdistanceunit");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceunitGreaterThanOrEqualTo(String value) {
			addCriterion("TransportDistanceUnit >=", value, "transportdistanceunit");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceunitLessThan(String value) {
			addCriterion("TransportDistanceUnit <", value, "transportdistanceunit");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceunitLessThanOrEqualTo(String value) {
			addCriterion("TransportDistanceUnit <=", value, "transportdistanceunit");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceunitLike(String value) {
			addCriterion("TransportDistanceUnit like", value, "transportdistanceunit");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceunitNotLike(String value) {
			addCriterion("TransportDistanceUnit not like", value, "transportdistanceunit");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceunitIn(List<String> values) {
			addCriterion("TransportDistanceUnit in", values, "transportdistanceunit");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceunitNotIn(List<String> values) {
			addCriterion("TransportDistanceUnit not in", values, "transportdistanceunit");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceunitBetween(String value1, String value2) {
			addCriterion("TransportDistanceUnit between", value1, value2, "transportdistanceunit");
			return (Criteria) this;
		}

		public Criteria andTransportdistanceunitNotBetween(String value1, String value2) {
			addCriterion("TransportDistanceUnit not between", value1, value2, "transportdistanceunit");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidIsNull() {
			addCriterion("ProtocolSubCategoryId is null");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidIsNotNull() {
			addCriterion("ProtocolSubCategoryId is not null");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidEqualTo(String value) {
			addCriterion("ProtocolSubCategoryId =", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidNotEqualTo(String value) {
			addCriterion("ProtocolSubCategoryId <>", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidGreaterThan(String value) {
			addCriterion("ProtocolSubCategoryId >", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidGreaterThanOrEqualTo(String value) {
			addCriterion("ProtocolSubCategoryId >=", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidLessThan(String value) {
			addCriterion("ProtocolSubCategoryId <", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidLessThanOrEqualTo(String value) {
			addCriterion("ProtocolSubCategoryId <=", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidLike(String value) {
			addCriterion("ProtocolSubCategoryId like", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidNotLike(String value) {
			addCriterion("ProtocolSubCategoryId not like", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidIn(List<String> values) {
			addCriterion("ProtocolSubCategoryId in", values, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidNotIn(List<String> values) {
			addCriterion("ProtocolSubCategoryId not in", values, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidBetween(String value1, String value2) {
			addCriterion("ProtocolSubCategoryId between", value1, value2, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidNotBetween(String value1, String value2) {
			addCriterion("ProtocolSubCategoryId not between", value1, value2, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionIsNull() {
			addCriterion("SourceDescription is null");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionIsNotNull() {
			addCriterion("SourceDescription is not null");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionEqualTo(String value) {
			addCriterion("SourceDescription =", value, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionNotEqualTo(String value) {
			addCriterion("SourceDescription <>", value, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionGreaterThan(String value) {
			addCriterion("SourceDescription >", value, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionGreaterThanOrEqualTo(String value) {
			addCriterion("SourceDescription >=", value, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionLessThan(String value) {
			addCriterion("SourceDescription <", value, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionLessThanOrEqualTo(String value) {
			addCriterion("SourceDescription <=", value, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionLike(String value) {
			addCriterion("SourceDescription like", value, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionNotLike(String value) {
			addCriterion("SourceDescription not like", value, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionIn(List<String> values) {
			addCriterion("SourceDescription in", values, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionNotIn(List<String> values) {
			addCriterion("SourceDescription not in", values, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionBetween(String value1, String value2) {
			addCriterion("SourceDescription between", value1, value2, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionNotBetween(String value1, String value2) {
			addCriterion("SourceDescription not between", value1, value2, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNull() {
			addCriterion("CreatedBy is null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNotNull() {
			addCriterion("CreatedBy is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyEqualTo(String value) {
			addCriterion("CreatedBy =", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotEqualTo(String value) {
			addCriterion("CreatedBy <>", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThan(String value) {
			addCriterion("CreatedBy >", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
			addCriterion("CreatedBy >=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThan(String value) {
			addCriterion("CreatedBy <", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThanOrEqualTo(String value) {
			addCriterion("CreatedBy <=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLike(String value) {
			addCriterion("CreatedBy like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotLike(String value) {
			addCriterion("CreatedBy not like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIn(List<String> values) {
			addCriterion("CreatedBy in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotIn(List<String> values) {
			addCriterion("CreatedBy not in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyBetween(String value1, String value2) {
			addCriterion("CreatedBy between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotBetween(String value1, String value2) {
			addCriterion("CreatedBy not between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNull() {
			addCriterion("CreatedTime is null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNotNull() {
			addCriterion("CreatedTime is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime =", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <>", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThan(LocalDateTime value) {
			addCriterion("CreatedTime >", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime >=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThan(LocalDateTime value) {
			addCriterion("CreatedTime <", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime not in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime not between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNull() {
			addCriterion("DeletedBy is null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNotNull() {
			addCriterion("DeletedBy is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyEqualTo(String value) {
			addCriterion("DeletedBy =", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotEqualTo(String value) {
			addCriterion("DeletedBy <>", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThan(String value) {
			addCriterion("DeletedBy >", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThanOrEqualTo(String value) {
			addCriterion("DeletedBy >=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThan(String value) {
			addCriterion("DeletedBy <", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThanOrEqualTo(String value) {
			addCriterion("DeletedBy <=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLike(String value) {
			addCriterion("DeletedBy like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotLike(String value) {
			addCriterion("DeletedBy not like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIn(List<String> values) {
			addCriterion("DeletedBy in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotIn(List<String> values) {
			addCriterion("DeletedBy not in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyBetween(String value1, String value2) {
			addCriterion("DeletedBy between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotBetween(String value1, String value2) {
			addCriterion("DeletedBy not between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNull() {
			addCriterion("DeletedTime is null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNotNull() {
			addCriterion("DeletedTime is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime =", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <>", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThan(LocalDateTime value) {
			addCriterion("DeletedTime >", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime >=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThan(LocalDateTime value) {
			addCriterion("DeletedTime <", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime not in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime not between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNull() {
			addCriterion("IsDeleted is null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNotNull() {
			addCriterion("IsDeleted is not null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedEqualTo(Boolean value) {
			addCriterion("IsDeleted =", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotEqualTo(Boolean value) {
			addCriterion("IsDeleted <>", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThan(Boolean value) {
			addCriterion("IsDeleted >", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted >=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThan(Boolean value) {
			addCriterion("IsDeleted <", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted <=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIn(List<Boolean> values) {
			addCriterion("IsDeleted in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotIn(List<Boolean> values) {
			addCriterion("IsDeleted not in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted between", value1, value2, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted not between", value1, value2, "isdeleted");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table D_CDMS_MaterialCarbonFactor
     *
     * @mbg.generated do_not_delete_during_merge Mon May 23 14:32:15 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}