package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FResultCounselorDetail {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column f_result_counselor_detail.id
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column f_result_counselor_detail.head_id
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    private String headId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column f_result_counselor_detail.carbon_emission_location
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    private String carbonEmissionLocation;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column f_result_counselor_detail.scope_main
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    private String scopeMain;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column f_result_counselor_detail.scope_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    private String scopeDetail;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column f_result_counselor_detail.carbon_amount
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    private BigDecimal carbonAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column f_result_counselor_detail.creation_time
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    private LocalDateTime creationTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column f_result_counselor_detail.id
     *
     * @return the value of f_result_counselor_detail.id
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column f_result_counselor_detail.id
     *
     * @param id the value for f_result_counselor_detail.id
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column f_result_counselor_detail.head_id
     *
     * @return the value of f_result_counselor_detail.head_id
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public String getHeadId() {
        return headId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column f_result_counselor_detail.head_id
     *
     * @param headId the value for f_result_counselor_detail.head_id
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public void setHeadId(String headId) {
        this.headId = headId == null ? null : headId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column f_result_counselor_detail.carbon_emission_location
     *
     * @return the value of f_result_counselor_detail.carbon_emission_location
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public String getCarbonEmissionLocation() {
        return carbonEmissionLocation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column f_result_counselor_detail.carbon_emission_location
     *
     * @param carbonEmissionLocation the value for f_result_counselor_detail.carbon_emission_location
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public void setCarbonEmissionLocation(String carbonEmissionLocation) {
        this.carbonEmissionLocation = carbonEmissionLocation == null ? null : carbonEmissionLocation.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column f_result_counselor_detail.scope_main
     *
     * @return the value of f_result_counselor_detail.scope_main
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public String getScopeMain() {
        return scopeMain;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column f_result_counselor_detail.scope_main
     *
     * @param scopeMain the value for f_result_counselor_detail.scope_main
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public void setScopeMain(String scopeMain) {
        this.scopeMain = scopeMain == null ? null : scopeMain.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column f_result_counselor_detail.scope_detail
     *
     * @return the value of f_result_counselor_detail.scope_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public String getScopeDetail() {
        return scopeDetail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column f_result_counselor_detail.scope_detail
     *
     * @param scopeDetail the value for f_result_counselor_detail.scope_detail
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public void setScopeDetail(String scopeDetail) {
        this.scopeDetail = scopeDetail == null ? null : scopeDetail.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column f_result_counselor_detail.carbon_amount
     *
     * @return the value of f_result_counselor_detail.carbon_amount
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public BigDecimal getCarbonAmount() {
        return carbonAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column f_result_counselor_detail.carbon_amount
     *
     * @param carbonAmount the value for f_result_counselor_detail.carbon_amount
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public void setCarbonAmount(BigDecimal carbonAmount) {
        this.carbonAmount = carbonAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column f_result_counselor_detail.creation_time
     *
     * @return the value of f_result_counselor_detail.creation_time
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column f_result_counselor_detail.creation_time
     *
     * @param creationTime the value for f_result_counselor_detail.creation_time
     *
     * @mbg.generated Mon Jan 06 11:53:51 CST 2025
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }
}