package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class TzhOrgMaterialCarbonFactorExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_Org_MaterialCarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_Org_MaterialCarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_Org_MaterialCarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public TzhOrgMaterialCarbonFactorExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_Org_MaterialCarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_Org_MaterialCarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("Id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("Id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("Id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("Id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("Id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("Id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("Id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("Id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("Id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("Id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("Id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("Id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("Id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("Id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andChinesenameIsNull() {
			addCriterion("ChineseName is null");
			return (Criteria) this;
		}

		public Criteria andChinesenameIsNotNull() {
			addCriterion("ChineseName is not null");
			return (Criteria) this;
		}

		public Criteria andChinesenameEqualTo(String value) {
			addCriterion("ChineseName =", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameNotEqualTo(String value) {
			addCriterion("ChineseName <>", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameGreaterThan(String value) {
			addCriterion("ChineseName >", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameGreaterThanOrEqualTo(String value) {
			addCriterion("ChineseName >=", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameLessThan(String value) {
			addCriterion("ChineseName <", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameLessThanOrEqualTo(String value) {
			addCriterion("ChineseName <=", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameLike(String value) {
			addCriterion("ChineseName like", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameNotLike(String value) {
			addCriterion("ChineseName not like", value, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameIn(List<String> values) {
			addCriterion("ChineseName in", values, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameNotIn(List<String> values) {
			addCriterion("ChineseName not in", values, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameBetween(String value1, String value2) {
			addCriterion("ChineseName between", value1, value2, "chinesename");
			return (Criteria) this;
		}

		public Criteria andChinesenameNotBetween(String value1, String value2) {
			addCriterion("ChineseName not between", value1, value2, "chinesename");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeIsNull() {
			addCriterion("MaterialCode is null");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeIsNotNull() {
			addCriterion("MaterialCode is not null");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeEqualTo(String value) {
			addCriterion("MaterialCode =", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeNotEqualTo(String value) {
			addCriterion("MaterialCode <>", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeGreaterThan(String value) {
			addCriterion("MaterialCode >", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeGreaterThanOrEqualTo(String value) {
			addCriterion("MaterialCode >=", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeLessThan(String value) {
			addCriterion("MaterialCode <", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeLessThanOrEqualTo(String value) {
			addCriterion("MaterialCode <=", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeLike(String value) {
			addCriterion("MaterialCode like", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeNotLike(String value) {
			addCriterion("MaterialCode not like", value, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeIn(List<String> values) {
			addCriterion("MaterialCode in", values, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeNotIn(List<String> values) {
			addCriterion("MaterialCode not in", values, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeBetween(String value1, String value2) {
			addCriterion("MaterialCode between", value1, value2, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialcodeNotBetween(String value1, String value2) {
			addCriterion("MaterialCode not between", value1, value2, "materialcode");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeIsNull() {
			addCriterion("MaterialAttribute is null");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeIsNotNull() {
			addCriterion("MaterialAttribute is not null");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeEqualTo(String value) {
			addCriterion("MaterialAttribute =", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeNotEqualTo(String value) {
			addCriterion("MaterialAttribute <>", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeGreaterThan(String value) {
			addCriterion("MaterialAttribute >", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeGreaterThanOrEqualTo(String value) {
			addCriterion("MaterialAttribute >=", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeLessThan(String value) {
			addCriterion("MaterialAttribute <", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeLessThanOrEqualTo(String value) {
			addCriterion("MaterialAttribute <=", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeLike(String value) {
			addCriterion("MaterialAttribute like", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeNotLike(String value) {
			addCriterion("MaterialAttribute not like", value, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeIn(List<String> values) {
			addCriterion("MaterialAttribute in", values, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeNotIn(List<String> values) {
			addCriterion("MaterialAttribute not in", values, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeBetween(String value1, String value2) {
			addCriterion("MaterialAttribute between", value1, value2, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andMaterialattributeNotBetween(String value1, String value2) {
			addCriterion("MaterialAttribute not between", value1, value2, "materialattribute");
			return (Criteria) this;
		}

		public Criteria andSupplierIsNull() {
			addCriterion("Supplier is null");
			return (Criteria) this;
		}

		public Criteria andSupplierIsNotNull() {
			addCriterion("Supplier is not null");
			return (Criteria) this;
		}

		public Criteria andSupplierEqualTo(String value) {
			addCriterion("Supplier =", value, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierNotEqualTo(String value) {
			addCriterion("Supplier <>", value, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierGreaterThan(String value) {
			addCriterion("Supplier >", value, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierGreaterThanOrEqualTo(String value) {
			addCriterion("Supplier >=", value, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierLessThan(String value) {
			addCriterion("Supplier <", value, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierLessThanOrEqualTo(String value) {
			addCriterion("Supplier <=", value, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierLike(String value) {
			addCriterion("Supplier like", value, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierNotLike(String value) {
			addCriterion("Supplier not like", value, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierIn(List<String> values) {
			addCriterion("Supplier in", values, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierNotIn(List<String> values) {
			addCriterion("Supplier not in", values, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierBetween(String value1, String value2) {
			addCriterion("Supplier between", value1, value2, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierNotBetween(String value1, String value2) {
			addCriterion("Supplier not between", value1, value2, "supplier");
			return (Criteria) this;
		}

		public Criteria andProductionprocessIsNull() {
			addCriterion("ProductionProcess is null");
			return (Criteria) this;
		}

		public Criteria andProductionprocessIsNotNull() {
			addCriterion("ProductionProcess is not null");
			return (Criteria) this;
		}

		public Criteria andProductionprocessEqualTo(String value) {
			addCriterion("ProductionProcess =", value, "productionprocess");
			return (Criteria) this;
		}

		public Criteria andProductionprocessNotEqualTo(String value) {
			addCriterion("ProductionProcess <>", value, "productionprocess");
			return (Criteria) this;
		}

		public Criteria andProductionprocessGreaterThan(String value) {
			addCriterion("ProductionProcess >", value, "productionprocess");
			return (Criteria) this;
		}

		public Criteria andProductionprocessGreaterThanOrEqualTo(String value) {
			addCriterion("ProductionProcess >=", value, "productionprocess");
			return (Criteria) this;
		}

		public Criteria andProductionprocessLessThan(String value) {
			addCriterion("ProductionProcess <", value, "productionprocess");
			return (Criteria) this;
		}

		public Criteria andProductionprocessLessThanOrEqualTo(String value) {
			addCriterion("ProductionProcess <=", value, "productionprocess");
			return (Criteria) this;
		}

		public Criteria andProductionprocessLike(String value) {
			addCriterion("ProductionProcess like", value, "productionprocess");
			return (Criteria) this;
		}

		public Criteria andProductionprocessNotLike(String value) {
			addCriterion("ProductionProcess not like", value, "productionprocess");
			return (Criteria) this;
		}

		public Criteria andProductionprocessIn(List<String> values) {
			addCriterion("ProductionProcess in", values, "productionprocess");
			return (Criteria) this;
		}

		public Criteria andProductionprocessNotIn(List<String> values) {
			addCriterion("ProductionProcess not in", values, "productionprocess");
			return (Criteria) this;
		}

		public Criteria andProductionprocessBetween(String value1, String value2) {
			addCriterion("ProductionProcess between", value1, value2, "productionprocess");
			return (Criteria) this;
		}

		public Criteria andProductionprocessNotBetween(String value1, String value2) {
			addCriterion("ProductionProcess not between", value1, value2, "productionprocess");
			return (Criteria) this;
		}

		public Criteria andUnitIsNull() {
			addCriterion("Unit is null");
			return (Criteria) this;
		}

		public Criteria andUnitIsNotNull() {
			addCriterion("Unit is not null");
			return (Criteria) this;
		}

		public Criteria andUnitEqualTo(String value) {
			addCriterion("Unit =", value, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitNotEqualTo(String value) {
			addCriterion("Unit <>", value, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitGreaterThan(String value) {
			addCriterion("Unit >", value, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitGreaterThanOrEqualTo(String value) {
			addCriterion("Unit >=", value, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitLessThan(String value) {
			addCriterion("Unit <", value, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitLessThanOrEqualTo(String value) {
			addCriterion("Unit <=", value, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitLike(String value) {
			addCriterion("Unit like", value, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitNotLike(String value) {
			addCriterion("Unit not like", value, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitIn(List<String> values) {
			addCriterion("Unit in", values, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitNotIn(List<String> values) {
			addCriterion("Unit not in", values, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitBetween(String value1, String value2) {
			addCriterion("Unit between", value1, value2, "unit");
			return (Criteria) this;
		}

		public Criteria andUnitNotBetween(String value1, String value2) {
			addCriterion("Unit not between", value1, value2, "unit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorIsNull() {
			addCriterion("CarbonFactor is null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorIsNotNull() {
			addCriterion("CarbonFactor is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor =", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorNotEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor <>", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorGreaterThan(BigDecimal value) {
			addCriterion("CarbonFactor >", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor >=", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorLessThan(BigDecimal value) {
			addCriterion("CarbonFactor <", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorLessThanOrEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor <=", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorIn(List<BigDecimal> values) {
			addCriterion("CarbonFactor in", values, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorNotIn(List<BigDecimal> values) {
			addCriterion("CarbonFactor not in", values, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("CarbonFactor between", value1, value2, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("CarbonFactor not between", value1, value2, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitIsNull() {
			addCriterion("CarbonFactorUnit is null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitIsNotNull() {
			addCriterion("CarbonFactorUnit is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitEqualTo(String value) {
			addCriterion("CarbonFactorUnit =", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotEqualTo(String value) {
			addCriterion("CarbonFactorUnit <>", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitGreaterThan(String value) {
			addCriterion("CarbonFactorUnit >", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitGreaterThanOrEqualTo(String value) {
			addCriterion("CarbonFactorUnit >=", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitLessThan(String value) {
			addCriterion("CarbonFactorUnit <", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitLessThanOrEqualTo(String value) {
			addCriterion("CarbonFactorUnit <=", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitLike(String value) {
			addCriterion("CarbonFactorUnit like", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotLike(String value) {
			addCriterion("CarbonFactorUnit not like", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitIn(List<String> values) {
			addCriterion("CarbonFactorUnit in", values, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotIn(List<String> values) {
			addCriterion("CarbonFactorUnit not in", values, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitBetween(String value1, String value2) {
			addCriterion("CarbonFactorUnit between", value1, value2, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotBetween(String value1, String value2) {
			addCriterion("CarbonFactorUnit not between", value1, value2, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidIsNull() {
			addCriterion("ProtocolSubCategoryId is null");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidIsNotNull() {
			addCriterion("ProtocolSubCategoryId is not null");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidEqualTo(String value) {
			addCriterion("ProtocolSubCategoryId =", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidNotEqualTo(String value) {
			addCriterion("ProtocolSubCategoryId <>", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidGreaterThan(String value) {
			addCriterion("ProtocolSubCategoryId >", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidGreaterThanOrEqualTo(String value) {
			addCriterion("ProtocolSubCategoryId >=", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidLessThan(String value) {
			addCriterion("ProtocolSubCategoryId <", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidLessThanOrEqualTo(String value) {
			addCriterion("ProtocolSubCategoryId <=", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidLike(String value) {
			addCriterion("ProtocolSubCategoryId like", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidNotLike(String value) {
			addCriterion("ProtocolSubCategoryId not like", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidIn(List<String> values) {
			addCriterion("ProtocolSubCategoryId in", values, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidNotIn(List<String> values) {
			addCriterion("ProtocolSubCategoryId not in", values, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidBetween(String value1, String value2) {
			addCriterion("ProtocolSubCategoryId between", value1, value2, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidNotBetween(String value1, String value2) {
			addCriterion("ProtocolSubCategoryId not between", value1, value2, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andDescriptionIsNull() {
			addCriterion("Description is null");
			return (Criteria) this;
		}

		public Criteria andDescriptionIsNotNull() {
			addCriterion("Description is not null");
			return (Criteria) this;
		}

		public Criteria andDescriptionEqualTo(String value) {
			addCriterion("Description =", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotEqualTo(String value) {
			addCriterion("Description <>", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionGreaterThan(String value) {
			addCriterion("Description >", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
			addCriterion("Description >=", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionLessThan(String value) {
			addCriterion("Description <", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionLessThanOrEqualTo(String value) {
			addCriterion("Description <=", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionLike(String value) {
			addCriterion("Description like", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotLike(String value) {
			addCriterion("Description not like", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionIn(List<String> values) {
			addCriterion("Description in", values, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotIn(List<String> values) {
			addCriterion("Description not in", values, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionBetween(String value1, String value2) {
			addCriterion("Description between", value1, value2, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotBetween(String value1, String value2) {
			addCriterion("Description not between", value1, value2, "description");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionIsNull() {
			addCriterion("SourceDescription is null");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionIsNotNull() {
			addCriterion("SourceDescription is not null");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionEqualTo(String value) {
			addCriterion("SourceDescription =", value, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionNotEqualTo(String value) {
			addCriterion("SourceDescription <>", value, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionGreaterThan(String value) {
			addCriterion("SourceDescription >", value, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionGreaterThanOrEqualTo(String value) {
			addCriterion("SourceDescription >=", value, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionLessThan(String value) {
			addCriterion("SourceDescription <", value, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionLessThanOrEqualTo(String value) {
			addCriterion("SourceDescription <=", value, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionLike(String value) {
			addCriterion("SourceDescription like", value, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionNotLike(String value) {
			addCriterion("SourceDescription not like", value, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionIn(List<String> values) {
			addCriterion("SourceDescription in", values, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionNotIn(List<String> values) {
			addCriterion("SourceDescription not in", values, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionBetween(String value1, String value2) {
			addCriterion("SourceDescription between", value1, value2, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andSourcedescriptionNotBetween(String value1, String value2) {
			addCriterion("SourceDescription not between", value1, value2, "sourcedescription");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNull() {
			addCriterion("CreatedBy is null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNotNull() {
			addCriterion("CreatedBy is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyEqualTo(String value) {
			addCriterion("CreatedBy =", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotEqualTo(String value) {
			addCriterion("CreatedBy <>", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThan(String value) {
			addCriterion("CreatedBy >", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
			addCriterion("CreatedBy >=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThan(String value) {
			addCriterion("CreatedBy <", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThanOrEqualTo(String value) {
			addCriterion("CreatedBy <=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLike(String value) {
			addCriterion("CreatedBy like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotLike(String value) {
			addCriterion("CreatedBy not like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIn(List<String> values) {
			addCriterion("CreatedBy in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotIn(List<String> values) {
			addCriterion("CreatedBy not in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyBetween(String value1, String value2) {
			addCriterion("CreatedBy between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotBetween(String value1, String value2) {
			addCriterion("CreatedBy not between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNull() {
			addCriterion("CreatedTime is null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNotNull() {
			addCriterion("CreatedTime is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime =", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <>", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThan(LocalDateTime value) {
			addCriterion("CreatedTime >", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime >=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThan(LocalDateTime value) {
			addCriterion("CreatedTime <", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime not in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime not between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNull() {
			addCriterion("DeletedBy is null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNotNull() {
			addCriterion("DeletedBy is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyEqualTo(String value) {
			addCriterion("DeletedBy =", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotEqualTo(String value) {
			addCriterion("DeletedBy <>", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThan(String value) {
			addCriterion("DeletedBy >", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThanOrEqualTo(String value) {
			addCriterion("DeletedBy >=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThan(String value) {
			addCriterion("DeletedBy <", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThanOrEqualTo(String value) {
			addCriterion("DeletedBy <=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLike(String value) {
			addCriterion("DeletedBy like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotLike(String value) {
			addCriterion("DeletedBy not like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIn(List<String> values) {
			addCriterion("DeletedBy in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotIn(List<String> values) {
			addCriterion("DeletedBy not in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyBetween(String value1, String value2) {
			addCriterion("DeletedBy between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotBetween(String value1, String value2) {
			addCriterion("DeletedBy not between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNull() {
			addCriterion("DeletedTime is null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNotNull() {
			addCriterion("DeletedTime is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime =", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <>", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThan(LocalDateTime value) {
			addCriterion("DeletedTime >", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime >=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThan(LocalDateTime value) {
			addCriterion("DeletedTime <", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime not in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime not between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNull() {
			addCriterion("IsDeleted is null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNotNull() {
			addCriterion("IsDeleted is not null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedEqualTo(Boolean value) {
			addCriterion("IsDeleted =", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotEqualTo(Boolean value) {
			addCriterion("IsDeleted <>", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThan(Boolean value) {
			addCriterion("IsDeleted >", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted >=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThan(Boolean value) {
			addCriterion("IsDeleted <", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted <=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIn(List<Boolean> values) {
			addCriterion("IsDeleted in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotIn(List<Boolean> values) {
			addCriterion("IsDeleted not in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted between", value1, value2, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted not between", value1, value2, "isdeleted");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_Org_MaterialCarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_Org_MaterialCarbonFactor
     *
     * @mbg.generated do_not_delete_during_merge Wed Jan 11 16:54:58 HKT 2023
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}