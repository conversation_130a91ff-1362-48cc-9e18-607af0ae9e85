package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class FCdmsGasCarbonFactorExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	public FCdmsGasCarbonFactorExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("Id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("Id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("Id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("Id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("Id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("Id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("Id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("Id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("Id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("Id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("Id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("Id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("Id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("Id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andSitenameIsNull() {
			addCriterion("SiteName is null");
			return (Criteria) this;
		}

		public Criteria andSitenameIsNotNull() {
			addCriterion("SiteName is not null");
			return (Criteria) this;
		}

		public Criteria andSitenameEqualTo(String value) {
			addCriterion("SiteName =", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotEqualTo(String value) {
			addCriterion("SiteName <>", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameGreaterThan(String value) {
			addCriterion("SiteName >", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameGreaterThanOrEqualTo(String value) {
			addCriterion("SiteName >=", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLessThan(String value) {
			addCriterion("SiteName <", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLessThanOrEqualTo(String value) {
			addCriterion("SiteName <=", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLike(String value) {
			addCriterion("SiteName like", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotLike(String value) {
			addCriterion("SiteName not like", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameIn(List<String> values) {
			addCriterion("SiteName in", values, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotIn(List<String> values) {
			addCriterion("SiteName not in", values, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameBetween(String value1, String value2) {
			addCriterion("SiteName between", value1, value2, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotBetween(String value1, String value2) {
			addCriterion("SiteName not between", value1, value2, "sitename");
			return (Criteria) this;
		}

		public Criteria andProtocolidIsNull() {
			addCriterion("ProtocolId is null");
			return (Criteria) this;
		}

		public Criteria andProtocolidIsNotNull() {
			addCriterion("ProtocolId is not null");
			return (Criteria) this;
		}

		public Criteria andProtocolidEqualTo(String value) {
			addCriterion("ProtocolId =", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotEqualTo(String value) {
			addCriterion("ProtocolId <>", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidGreaterThan(String value) {
			addCriterion("ProtocolId >", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidGreaterThanOrEqualTo(String value) {
			addCriterion("ProtocolId >=", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidLessThan(String value) {
			addCriterion("ProtocolId <", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidLessThanOrEqualTo(String value) {
			addCriterion("ProtocolId <=", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidLike(String value) {
			addCriterion("ProtocolId like", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotLike(String value) {
			addCriterion("ProtocolId not like", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidIn(List<String> values) {
			addCriterion("ProtocolId in", values, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotIn(List<String> values) {
			addCriterion("ProtocolId not in", values, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidBetween(String value1, String value2) {
			addCriterion("ProtocolId between", value1, value2, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotBetween(String value1, String value2) {
			addCriterion("ProtocolId not between", value1, value2, "protocolid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidIsNull() {
			addCriterion("CarbonEmissionLocationId is null");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidIsNotNull() {
			addCriterion("CarbonEmissionLocationId is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId =", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId <>", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidGreaterThan(String value) {
			addCriterion("CarbonEmissionLocationId >", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidGreaterThanOrEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId >=", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidLessThan(String value) {
			addCriterion("CarbonEmissionLocationId <", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidLessThanOrEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId <=", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidLike(String value) {
			addCriterion("CarbonEmissionLocationId like", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotLike(String value) {
			addCriterion("CarbonEmissionLocationId not like", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidIn(List<String> values) {
			addCriterion("CarbonEmissionLocationId in", values, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotIn(List<String> values) {
			addCriterion("CarbonEmissionLocationId not in", values, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidBetween(String value1, String value2) {
			addCriterion("CarbonEmissionLocationId between", value1, value2, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotBetween(String value1, String value2) {
			addCriterion("CarbonEmissionLocationId not between", value1, value2, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthIsNull() {
			addCriterion("RecordYearMonth is null");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthIsNotNull() {
			addCriterion("RecordYearMonth is not null");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthEqualTo(Integer value) {
			addCriterion("RecordYearMonth =", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthNotEqualTo(Integer value) {
			addCriterion("RecordYearMonth <>", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthGreaterThan(Integer value) {
			addCriterion("RecordYearMonth >", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthGreaterThanOrEqualTo(Integer value) {
			addCriterion("RecordYearMonth >=", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthLessThan(Integer value) {
			addCriterion("RecordYearMonth <", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthLessThanOrEqualTo(Integer value) {
			addCriterion("RecordYearMonth <=", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthIn(List<Integer> values) {
			addCriterion("RecordYearMonth in", values, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthNotIn(List<Integer> values) {
			addCriterion("RecordYearMonth not in", values, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthBetween(Integer value1, Integer value2) {
			addCriterion("RecordYearMonth between", value1, value2, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthNotBetween(Integer value1, Integer value2) {
			addCriterion("RecordYearMonth not between", value1, value2, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andInvoicenoIsNull() {
			addCriterion("InvoiceNo is null");
			return (Criteria) this;
		}

		public Criteria andInvoicenoIsNotNull() {
			addCriterion("InvoiceNo is not null");
			return (Criteria) this;
		}

		public Criteria andInvoicenoEqualTo(String value) {
			addCriterion("InvoiceNo =", value, "invoiceno");
			return (Criteria) this;
		}

		public Criteria andInvoicenoNotEqualTo(String value) {
			addCriterion("InvoiceNo <>", value, "invoiceno");
			return (Criteria) this;
		}

		public Criteria andInvoicenoGreaterThan(String value) {
			addCriterion("InvoiceNo >", value, "invoiceno");
			return (Criteria) this;
		}

		public Criteria andInvoicenoGreaterThanOrEqualTo(String value) {
			addCriterion("InvoiceNo >=", value, "invoiceno");
			return (Criteria) this;
		}

		public Criteria andInvoicenoLessThan(String value) {
			addCriterion("InvoiceNo <", value, "invoiceno");
			return (Criteria) this;
		}

		public Criteria andInvoicenoLessThanOrEqualTo(String value) {
			addCriterion("InvoiceNo <=", value, "invoiceno");
			return (Criteria) this;
		}

		public Criteria andInvoicenoLike(String value) {
			addCriterion("InvoiceNo like", value, "invoiceno");
			return (Criteria) this;
		}

		public Criteria andInvoicenoNotLike(String value) {
			addCriterion("InvoiceNo not like", value, "invoiceno");
			return (Criteria) this;
		}

		public Criteria andInvoicenoIn(List<String> values) {
			addCriterion("InvoiceNo in", values, "invoiceno");
			return (Criteria) this;
		}

		public Criteria andInvoicenoNotIn(List<String> values) {
			addCriterion("InvoiceNo not in", values, "invoiceno");
			return (Criteria) this;
		}

		public Criteria andInvoicenoBetween(String value1, String value2) {
			addCriterion("InvoiceNo between", value1, value2, "invoiceno");
			return (Criteria) this;
		}

		public Criteria andInvoicenoNotBetween(String value1, String value2) {
			addCriterion("InvoiceNo not between", value1, value2, "invoiceno");
			return (Criteria) this;
		}

		public Criteria andGasamountIsNull() {
			addCriterion("GasAmount is null");
			return (Criteria) this;
		}

		public Criteria andGasamountIsNotNull() {
			addCriterion("GasAmount is not null");
			return (Criteria) this;
		}

		public Criteria andGasamountEqualTo(BigDecimal value) {
			addCriterion("GasAmount =", value, "gasamount");
			return (Criteria) this;
		}

		public Criteria andGasamountNotEqualTo(BigDecimal value) {
			addCriterion("GasAmount <>", value, "gasamount");
			return (Criteria) this;
		}

		public Criteria andGasamountGreaterThan(BigDecimal value) {
			addCriterion("GasAmount >", value, "gasamount");
			return (Criteria) this;
		}

		public Criteria andGasamountGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("GasAmount >=", value, "gasamount");
			return (Criteria) this;
		}

		public Criteria andGasamountLessThan(BigDecimal value) {
			addCriterion("GasAmount <", value, "gasamount");
			return (Criteria) this;
		}

		public Criteria andGasamountLessThanOrEqualTo(BigDecimal value) {
			addCriterion("GasAmount <=", value, "gasamount");
			return (Criteria) this;
		}

		public Criteria andGasamountIn(List<BigDecimal> values) {
			addCriterion("GasAmount in", values, "gasamount");
			return (Criteria) this;
		}

		public Criteria andGasamountNotIn(List<BigDecimal> values) {
			addCriterion("GasAmount not in", values, "gasamount");
			return (Criteria) this;
		}

		public Criteria andGasamountBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("GasAmount between", value1, value2, "gasamount");
			return (Criteria) this;
		}

		public Criteria andGasamountNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("GasAmount not between", value1, value2, "gasamount");
			return (Criteria) this;
		}

		public Criteria andGasquantityIsNull() {
			addCriterion("GasQuantity is null");
			return (Criteria) this;
		}

		public Criteria andGasquantityIsNotNull() {
			addCriterion("GasQuantity is not null");
			return (Criteria) this;
		}

		public Criteria andGasquantityEqualTo(BigDecimal value) {
			addCriterion("GasQuantity =", value, "gasquantity");
			return (Criteria) this;
		}

		public Criteria andGasquantityNotEqualTo(BigDecimal value) {
			addCriterion("GasQuantity <>", value, "gasquantity");
			return (Criteria) this;
		}

		public Criteria andGasquantityGreaterThan(BigDecimal value) {
			addCriterion("GasQuantity >", value, "gasquantity");
			return (Criteria) this;
		}

		public Criteria andGasquantityGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("GasQuantity >=", value, "gasquantity");
			return (Criteria) this;
		}

		public Criteria andGasquantityLessThan(BigDecimal value) {
			addCriterion("GasQuantity <", value, "gasquantity");
			return (Criteria) this;
		}

		public Criteria andGasquantityLessThanOrEqualTo(BigDecimal value) {
			addCriterion("GasQuantity <=", value, "gasquantity");
			return (Criteria) this;
		}

		public Criteria andGasquantityIn(List<BigDecimal> values) {
			addCriterion("GasQuantity in", values, "gasquantity");
			return (Criteria) this;
		}

		public Criteria andGasquantityNotIn(List<BigDecimal> values) {
			addCriterion("GasQuantity not in", values, "gasquantity");
			return (Criteria) this;
		}

		public Criteria andGasquantityBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("GasQuantity between", value1, value2, "gasquantity");
			return (Criteria) this;
		}

		public Criteria andGasquantityNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("GasQuantity not between", value1, value2, "gasquantity");
			return (Criteria) this;
		}

		public Criteria andGasquantityUnitIsNull() {
			addCriterion("GasQuantity_Unit is null");
			return (Criteria) this;
		}

		public Criteria andGasquantityUnitIsNotNull() {
			addCriterion("GasQuantity_Unit is not null");
			return (Criteria) this;
		}

		public Criteria andGasquantityUnitEqualTo(String value) {
			addCriterion("GasQuantity_Unit =", value, "gasquantityUnit");
			return (Criteria) this;
		}

		public Criteria andGasquantityUnitNotEqualTo(String value) {
			addCriterion("GasQuantity_Unit <>", value, "gasquantityUnit");
			return (Criteria) this;
		}

		public Criteria andGasquantityUnitGreaterThan(String value) {
			addCriterion("GasQuantity_Unit >", value, "gasquantityUnit");
			return (Criteria) this;
		}

		public Criteria andGasquantityUnitGreaterThanOrEqualTo(String value) {
			addCriterion("GasQuantity_Unit >=", value, "gasquantityUnit");
			return (Criteria) this;
		}

		public Criteria andGasquantityUnitLessThan(String value) {
			addCriterion("GasQuantity_Unit <", value, "gasquantityUnit");
			return (Criteria) this;
		}

		public Criteria andGasquantityUnitLessThanOrEqualTo(String value) {
			addCriterion("GasQuantity_Unit <=", value, "gasquantityUnit");
			return (Criteria) this;
		}

		public Criteria andGasquantityUnitLike(String value) {
			addCriterion("GasQuantity_Unit like", value, "gasquantityUnit");
			return (Criteria) this;
		}

		public Criteria andGasquantityUnitNotLike(String value) {
			addCriterion("GasQuantity_Unit not like", value, "gasquantityUnit");
			return (Criteria) this;
		}

		public Criteria andGasquantityUnitIn(List<String> values) {
			addCriterion("GasQuantity_Unit in", values, "gasquantityUnit");
			return (Criteria) this;
		}

		public Criteria andGasquantityUnitNotIn(List<String> values) {
			addCriterion("GasQuantity_Unit not in", values, "gasquantityUnit");
			return (Criteria) this;
		}

		public Criteria andGasquantityUnitBetween(String value1, String value2) {
			addCriterion("GasQuantity_Unit between", value1, value2, "gasquantityUnit");
			return (Criteria) this;
		}

		public Criteria andGasquantityUnitNotBetween(String value1, String value2) {
			addCriterion("GasQuantity_Unit not between", value1, value2, "gasquantityUnit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorIsNull() {
			addCriterion("CarbonFactor is null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorIsNotNull() {
			addCriterion("CarbonFactor is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor =", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorNotEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor <>", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorGreaterThan(BigDecimal value) {
			addCriterion("CarbonFactor >", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor >=", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorLessThan(BigDecimal value) {
			addCriterion("CarbonFactor <", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorLessThanOrEqualTo(BigDecimal value) {
			addCriterion("CarbonFactor <=", value, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorIn(List<BigDecimal> values) {
			addCriterion("CarbonFactor in", values, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorNotIn(List<BigDecimal> values) {
			addCriterion("CarbonFactor not in", values, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("CarbonFactor between", value1, value2, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("CarbonFactor not between", value1, value2, "carbonfactor");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitIsNull() {
			addCriterion("CarbonFactorUnit is null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitIsNotNull() {
			addCriterion("CarbonFactorUnit is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitEqualTo(String value) {
			addCriterion("CarbonFactorUnit =", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotEqualTo(String value) {
			addCriterion("CarbonFactorUnit <>", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitGreaterThan(String value) {
			addCriterion("CarbonFactorUnit >", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitGreaterThanOrEqualTo(String value) {
			addCriterion("CarbonFactorUnit >=", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitLessThan(String value) {
			addCriterion("CarbonFactorUnit <", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitLessThanOrEqualTo(String value) {
			addCriterion("CarbonFactorUnit <=", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitLike(String value) {
			addCriterion("CarbonFactorUnit like", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotLike(String value) {
			addCriterion("CarbonFactorUnit not like", value, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitIn(List<String> values) {
			addCriterion("CarbonFactorUnit in", values, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotIn(List<String> values) {
			addCriterion("CarbonFactorUnit not in", values, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitBetween(String value1, String value2) {
			addCriterion("CarbonFactorUnit between", value1, value2, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCarbonfactorunitNotBetween(String value1, String value2) {
			addCriterion("CarbonFactorUnit not between", value1, value2, "carbonfactorunit");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNull() {
			addCriterion("CreatedBy is null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNotNull() {
			addCriterion("CreatedBy is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyEqualTo(String value) {
			addCriterion("CreatedBy =", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotEqualTo(String value) {
			addCriterion("CreatedBy <>", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThan(String value) {
			addCriterion("CreatedBy >", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
			addCriterion("CreatedBy >=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThan(String value) {
			addCriterion("CreatedBy <", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThanOrEqualTo(String value) {
			addCriterion("CreatedBy <=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLike(String value) {
			addCriterion("CreatedBy like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotLike(String value) {
			addCriterion("CreatedBy not like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIn(List<String> values) {
			addCriterion("CreatedBy in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotIn(List<String> values) {
			addCriterion("CreatedBy not in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyBetween(String value1, String value2) {
			addCriterion("CreatedBy between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotBetween(String value1, String value2) {
			addCriterion("CreatedBy not between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNull() {
			addCriterion("CreatedTime is null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNotNull() {
			addCriterion("CreatedTime is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime =", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <>", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThan(LocalDateTime value) {
			addCriterion("CreatedTime >", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime >=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThan(LocalDateTime value) {
			addCriterion("CreatedTime <", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime not in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime not between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNull() {
			addCriterion("DeletedBy is null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNotNull() {
			addCriterion("DeletedBy is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyEqualTo(String value) {
			addCriterion("DeletedBy =", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotEqualTo(String value) {
			addCriterion("DeletedBy <>", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThan(String value) {
			addCriterion("DeletedBy >", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThanOrEqualTo(String value) {
			addCriterion("DeletedBy >=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThan(String value) {
			addCriterion("DeletedBy <", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThanOrEqualTo(String value) {
			addCriterion("DeletedBy <=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLike(String value) {
			addCriterion("DeletedBy like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotLike(String value) {
			addCriterion("DeletedBy not like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIn(List<String> values) {
			addCriterion("DeletedBy in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotIn(List<String> values) {
			addCriterion("DeletedBy not in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyBetween(String value1, String value2) {
			addCriterion("DeletedBy between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotBetween(String value1, String value2) {
			addCriterion("DeletedBy not between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNull() {
			addCriterion("DeletedTime is null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNotNull() {
			addCriterion("DeletedTime is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime =", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <>", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThan(LocalDateTime value) {
			addCriterion("DeletedTime >", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime >=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThan(LocalDateTime value) {
			addCriterion("DeletedTime <", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime not in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime not between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNull() {
			addCriterion("IsDeleted is null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNotNull() {
			addCriterion("IsDeleted is not null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedEqualTo(Boolean value) {
			addCriterion("IsDeleted =", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotEqualTo(Boolean value) {
			addCriterion("IsDeleted <>", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThan(Boolean value) {
			addCriterion("IsDeleted >", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted >=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThan(Boolean value) {
			addCriterion("IsDeleted <", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted <=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIn(List<Boolean> values) {
			addCriterion("IsDeleted in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotIn(List<Boolean> values) {
			addCriterion("IsDeleted not in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted between", value1, value2, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted not between", value1, value2, "isdeleted");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table F_CDMS_GasCarbonFactor
     *
     * @mbg.generated do_not_delete_during_merge Mon May 30 10:29:06 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}