package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class TzhOrgMaterialCarbonFactor {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.Id
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.ChineseName
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private String chinesename;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.MaterialCode
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private String materialcode;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.MaterialAttribute
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private String materialattribute;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.Supplier
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private String supplier;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.ProductionProcess
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private String productionprocess;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.Unit
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private String unit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.CarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private BigDecimal carbonfactor;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.CarbonFactorUnit
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private String carbonfactorunit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.ProtocolSubCategoryId
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private String protocolsubcategoryid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.Description
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private String description;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.SourceDescription
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private String sourcedescription;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.CreatedBy
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private String createdby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.CreatedTime
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private LocalDateTime createdtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.DeletedBy
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private String deletedby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.DeletedTime
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private LocalDateTime deletedtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor.IsDeleted
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	private Boolean isdeleted;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.Id
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.Id
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.Id
	 * @param id  the value for Tzh_Org_MaterialCarbonFactor.Id
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.ChineseName
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.ChineseName
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public String getChinesename() {
		return chinesename;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.ChineseName
	 * @param chinesename  the value for Tzh_Org_MaterialCarbonFactor.ChineseName
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setChinesename(String chinesename) {
		this.chinesename = chinesename == null ? null : chinesename.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.MaterialCode
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.MaterialCode
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public String getMaterialcode() {
		return materialcode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.MaterialCode
	 * @param materialcode  the value for Tzh_Org_MaterialCarbonFactor.MaterialCode
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setMaterialcode(String materialcode) {
		this.materialcode = materialcode == null ? null : materialcode.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.MaterialAttribute
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.MaterialAttribute
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public String getMaterialattribute() {
		return materialattribute;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.MaterialAttribute
	 * @param materialattribute  the value for Tzh_Org_MaterialCarbonFactor.MaterialAttribute
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setMaterialattribute(String materialattribute) {
		this.materialattribute = materialattribute == null ? null : materialattribute.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.Supplier
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.Supplier
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public String getSupplier() {
		return supplier;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.Supplier
	 * @param supplier  the value for Tzh_Org_MaterialCarbonFactor.Supplier
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setSupplier(String supplier) {
		this.supplier = supplier == null ? null : supplier.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.ProductionProcess
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.ProductionProcess
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public String getProductionprocess() {
		return productionprocess;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.ProductionProcess
	 * @param productionprocess  the value for Tzh_Org_MaterialCarbonFactor.ProductionProcess
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setProductionprocess(String productionprocess) {
		this.productionprocess = productionprocess == null ? null : productionprocess.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.Unit
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.Unit
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public String getUnit() {
		return unit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.Unit
	 * @param unit  the value for Tzh_Org_MaterialCarbonFactor.Unit
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setUnit(String unit) {
		this.unit = unit == null ? null : unit.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.CarbonFactor
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.CarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public BigDecimal getCarbonfactor() {
		return carbonfactor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.CarbonFactor
	 * @param carbonfactor  the value for Tzh_Org_MaterialCarbonFactor.CarbonFactor
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setCarbonfactor(BigDecimal carbonfactor) {
		this.carbonfactor = carbonfactor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.CarbonFactorUnit
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.CarbonFactorUnit
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public String getCarbonfactorunit() {
		return carbonfactorunit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.CarbonFactorUnit
	 * @param carbonfactorunit  the value for Tzh_Org_MaterialCarbonFactor.CarbonFactorUnit
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setCarbonfactorunit(String carbonfactorunit) {
		this.carbonfactorunit = carbonfactorunit == null ? null : carbonfactorunit.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.ProtocolSubCategoryId
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.ProtocolSubCategoryId
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public String getProtocolsubcategoryid() {
		return protocolsubcategoryid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.ProtocolSubCategoryId
	 * @param protocolsubcategoryid  the value for Tzh_Org_MaterialCarbonFactor.ProtocolSubCategoryId
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setProtocolsubcategoryid(String protocolsubcategoryid) {
		this.protocolsubcategoryid = protocolsubcategoryid == null ? null : protocolsubcategoryid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.Description
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.Description
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.Description
	 * @param description  the value for Tzh_Org_MaterialCarbonFactor.Description
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setDescription(String description) {
		this.description = description == null ? null : description.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.SourceDescription
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.SourceDescription
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public String getSourcedescription() {
		return sourcedescription;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.SourceDescription
	 * @param sourcedescription  the value for Tzh_Org_MaterialCarbonFactor.SourceDescription
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setSourcedescription(String sourcedescription) {
		this.sourcedescription = sourcedescription == null ? null : sourcedescription.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.CreatedBy
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.CreatedBy
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public String getCreatedby() {
		return createdby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.CreatedBy
	 * @param createdby  the value for Tzh_Org_MaterialCarbonFactor.CreatedBy
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setCreatedby(String createdby) {
		this.createdby = createdby == null ? null : createdby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.CreatedTime
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.CreatedTime
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public LocalDateTime getCreatedtime() {
		return createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.CreatedTime
	 * @param createdtime  the value for Tzh_Org_MaterialCarbonFactor.CreatedTime
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setCreatedtime(LocalDateTime createdtime) {
		this.createdtime = createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.DeletedBy
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.DeletedBy
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public String getDeletedby() {
		return deletedby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.DeletedBy
	 * @param deletedby  the value for Tzh_Org_MaterialCarbonFactor.DeletedBy
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setDeletedby(String deletedby) {
		this.deletedby = deletedby == null ? null : deletedby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.DeletedTime
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.DeletedTime
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public LocalDateTime getDeletedtime() {
		return deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.DeletedTime
	 * @param deletedtime  the value for Tzh_Org_MaterialCarbonFactor.DeletedTime
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setDeletedtime(LocalDateTime deletedtime) {
		this.deletedtime = deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor.IsDeleted
	 * @return  the value of Tzh_Org_MaterialCarbonFactor.IsDeleted
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public Boolean getIsdeleted() {
		return isdeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor.IsDeleted
	 * @param isdeleted  the value for Tzh_Org_MaterialCarbonFactor.IsDeleted
	 * @mbg.generated  Thu Feb 01 17:29:32 HKT 2024
	 */
	public void setIsdeleted(Boolean isdeleted) {
		this.isdeleted = isdeleted;
	}
}