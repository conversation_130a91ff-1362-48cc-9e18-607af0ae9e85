package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class FMaterialExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table F_Material
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table F_Material
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table F_Material
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public FMaterialExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table F_Material
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andRegionIsNull() {
            addCriterion("Region is null");
            return (Criteria) this;
        }

        public Criteria andRegionIsNotNull() {
            addCriterion("Region is not null");
            return (Criteria) this;
        }

        public Criteria andRegionEqualTo(String value) {
            addCriterion("Region =", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotEqualTo(String value) {
            addCriterion("Region <>", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionGreaterThan(String value) {
            addCriterion("Region >", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionGreaterThanOrEqualTo(String value) {
            addCriterion("Region >=", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionLessThan(String value) {
            addCriterion("Region <", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionLessThanOrEqualTo(String value) {
            addCriterion("Region <=", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionLike(String value) {
            addCriterion("Region like", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotLike(String value) {
            addCriterion("Region not like", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionIn(List<String> values) {
            addCriterion("Region in", values, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotIn(List<String> values) {
            addCriterion("Region not in", values, "region");
            return (Criteria) this;
        }

        public Criteria andRegionBetween(String value1, String value2) {
            addCriterion("Region between", value1, value2, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotBetween(String value1, String value2) {
            addCriterion("Region not between", value1, value2, "region");
            return (Criteria) this;
        }

        public Criteria andSiteidIsNull() {
            addCriterion("SiteId is null");
            return (Criteria) this;
        }

        public Criteria andSiteidIsNotNull() {
            addCriterion("SiteId is not null");
            return (Criteria) this;
        }

        public Criteria andSiteidEqualTo(Integer value) {
            addCriterion("SiteId =", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidNotEqualTo(Integer value) {
            addCriterion("SiteId <>", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidGreaterThan(Integer value) {
            addCriterion("SiteId >", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidGreaterThanOrEqualTo(Integer value) {
            addCriterion("SiteId >=", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidLessThan(Integer value) {
            addCriterion("SiteId <", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidLessThanOrEqualTo(Integer value) {
            addCriterion("SiteId <=", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidIn(List<Integer> values) {
            addCriterion("SiteId in", values, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidNotIn(List<Integer> values) {
            addCriterion("SiteId not in", values, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidBetween(Integer value1, Integer value2) {
            addCriterion("SiteId between", value1, value2, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidNotBetween(Integer value1, Integer value2) {
            addCriterion("SiteId not between", value1, value2, "siteid");
            return (Criteria) this;
        }

        public Criteria andSitenameIsNull() {
            addCriterion("SiteName is null");
            return (Criteria) this;
        }

        public Criteria andSitenameIsNotNull() {
            addCriterion("SiteName is not null");
            return (Criteria) this;
        }

        public Criteria andSitenameEqualTo(String value) {
            addCriterion("SiteName =", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameNotEqualTo(String value) {
            addCriterion("SiteName <>", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameGreaterThan(String value) {
            addCriterion("SiteName >", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameGreaterThanOrEqualTo(String value) {
            addCriterion("SiteName >=", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameLessThan(String value) {
            addCriterion("SiteName <", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameLessThanOrEqualTo(String value) {
            addCriterion("SiteName <=", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameLike(String value) {
            addCriterion("SiteName like", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameNotLike(String value) {
            addCriterion("SiteName not like", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameIn(List<String> values) {
            addCriterion("SiteName in", values, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameNotIn(List<String> values) {
            addCriterion("SiteName not in", values, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameBetween(String value1, String value2) {
            addCriterion("SiteName between", value1, value2, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameNotBetween(String value1, String value2) {
            addCriterion("SiteName not between", value1, value2, "sitename");
            return (Criteria) this;
        }

        public Criteria andRecordyearmonthIsNull() {
            addCriterion("RecordYearMonth is null");
            return (Criteria) this;
        }

        public Criteria andRecordyearmonthIsNotNull() {
            addCriterion("RecordYearMonth is not null");
            return (Criteria) this;
        }

        public Criteria andRecordyearmonthEqualTo(Integer value) {
            addCriterion("RecordYearMonth =", value, "recordyearmonth");
            return (Criteria) this;
        }

        public Criteria andRecordyearmonthNotEqualTo(Integer value) {
            addCriterion("RecordYearMonth <>", value, "recordyearmonth");
            return (Criteria) this;
        }

        public Criteria andRecordyearmonthGreaterThan(Integer value) {
            addCriterion("RecordYearMonth >", value, "recordyearmonth");
            return (Criteria) this;
        }

        public Criteria andRecordyearmonthGreaterThanOrEqualTo(Integer value) {
            addCriterion("RecordYearMonth >=", value, "recordyearmonth");
            return (Criteria) this;
        }

        public Criteria andRecordyearmonthLessThan(Integer value) {
            addCriterion("RecordYearMonth <", value, "recordyearmonth");
            return (Criteria) this;
        }

        public Criteria andRecordyearmonthLessThanOrEqualTo(Integer value) {
            addCriterion("RecordYearMonth <=", value, "recordyearmonth");
            return (Criteria) this;
        }

        public Criteria andRecordyearmonthIn(List<Integer> values) {
            addCriterion("RecordYearMonth in", values, "recordyearmonth");
            return (Criteria) this;
        }

        public Criteria andRecordyearmonthNotIn(List<Integer> values) {
            addCriterion("RecordYearMonth not in", values, "recordyearmonth");
            return (Criteria) this;
        }

        public Criteria andRecordyearmonthBetween(Integer value1, Integer value2) {
            addCriterion("RecordYearMonth between", value1, value2, "recordyearmonth");
            return (Criteria) this;
        }

        public Criteria andRecordyearmonthNotBetween(Integer value1, Integer value2) {
            addCriterion("RecordYearMonth not between", value1, value2, "recordyearmonth");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationIsNull() {
            addCriterion("CarbonEmissionLocation is null");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationIsNotNull() {
            addCriterion("CarbonEmissionLocation is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationEqualTo(String value) {
            addCriterion("CarbonEmissionLocation =", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationNotEqualTo(String value) {
            addCriterion("CarbonEmissionLocation <>", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationGreaterThan(String value) {
            addCriterion("CarbonEmissionLocation >", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationGreaterThanOrEqualTo(String value) {
            addCriterion("CarbonEmissionLocation >=", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationLessThan(String value) {
            addCriterion("CarbonEmissionLocation <", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationLessThanOrEqualTo(String value) {
            addCriterion("CarbonEmissionLocation <=", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationLike(String value) {
            addCriterion("CarbonEmissionLocation like", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationNotLike(String value) {
            addCriterion("CarbonEmissionLocation not like", value, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationIn(List<String> values) {
            addCriterion("CarbonEmissionLocation in", values, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationNotIn(List<String> values) {
            addCriterion("CarbonEmissionLocation not in", values, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationBetween(String value1, String value2) {
            addCriterion("CarbonEmissionLocation between", value1, value2, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationNotBetween(String value1, String value2) {
            addCriterion("CarbonEmissionLocation not between", value1, value2, "carbonemissionlocation");
            return (Criteria) this;
        }

        public Criteria andMaterialcodeIsNull() {
            addCriterion("MaterialCode is null");
            return (Criteria) this;
        }

        public Criteria andMaterialcodeIsNotNull() {
            addCriterion("MaterialCode is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialcodeEqualTo(String value) {
            addCriterion("MaterialCode =", value, "materialcode");
            return (Criteria) this;
        }

        public Criteria andMaterialcodeNotEqualTo(String value) {
            addCriterion("MaterialCode <>", value, "materialcode");
            return (Criteria) this;
        }

        public Criteria andMaterialcodeGreaterThan(String value) {
            addCriterion("MaterialCode >", value, "materialcode");
            return (Criteria) this;
        }

        public Criteria andMaterialcodeGreaterThanOrEqualTo(String value) {
            addCriterion("MaterialCode >=", value, "materialcode");
            return (Criteria) this;
        }

        public Criteria andMaterialcodeLessThan(String value) {
            addCriterion("MaterialCode <", value, "materialcode");
            return (Criteria) this;
        }

        public Criteria andMaterialcodeLessThanOrEqualTo(String value) {
            addCriterion("MaterialCode <=", value, "materialcode");
            return (Criteria) this;
        }

        public Criteria andMaterialcodeLike(String value) {
            addCriterion("MaterialCode like", value, "materialcode");
            return (Criteria) this;
        }

        public Criteria andMaterialcodeNotLike(String value) {
            addCriterion("MaterialCode not like", value, "materialcode");
            return (Criteria) this;
        }

        public Criteria andMaterialcodeIn(List<String> values) {
            addCriterion("MaterialCode in", values, "materialcode");
            return (Criteria) this;
        }

        public Criteria andMaterialcodeNotIn(List<String> values) {
            addCriterion("MaterialCode not in", values, "materialcode");
            return (Criteria) this;
        }

        public Criteria andMaterialcodeBetween(String value1, String value2) {
            addCriterion("MaterialCode between", value1, value2, "materialcode");
            return (Criteria) this;
        }

        public Criteria andMaterialcodeNotBetween(String value1, String value2) {
            addCriterion("MaterialCode not between", value1, value2, "materialcode");
            return (Criteria) this;
        }

        public Criteria andChinesenameIsNull() {
            addCriterion("ChineseName is null");
            return (Criteria) this;
        }

        public Criteria andChinesenameIsNotNull() {
            addCriterion("ChineseName is not null");
            return (Criteria) this;
        }

        public Criteria andChinesenameEqualTo(String value) {
            addCriterion("ChineseName =", value, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameNotEqualTo(String value) {
            addCriterion("ChineseName <>", value, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameGreaterThan(String value) {
            addCriterion("ChineseName >", value, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameGreaterThanOrEqualTo(String value) {
            addCriterion("ChineseName >=", value, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameLessThan(String value) {
            addCriterion("ChineseName <", value, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameLessThanOrEqualTo(String value) {
            addCriterion("ChineseName <=", value, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameLike(String value) {
            addCriterion("ChineseName like", value, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameNotLike(String value) {
            addCriterion("ChineseName not like", value, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameIn(List<String> values) {
            addCriterion("ChineseName in", values, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameNotIn(List<String> values) {
            addCriterion("ChineseName not in", values, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameBetween(String value1, String value2) {
            addCriterion("ChineseName between", value1, value2, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameNotBetween(String value1, String value2) {
            addCriterion("ChineseName not between", value1, value2, "chinesename");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("Unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("Unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("Unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("Unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("Unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("Unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("Unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("Unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("Unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("Unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("Unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("Unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("Unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("Unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("Description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("Description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("Description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("Description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("Description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("Description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("Description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("Description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("Description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("Description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("Description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("Description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("Description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("Description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorIsNull() {
            addCriterion("CarbonFactor is null");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorIsNotNull() {
            addCriterion("CarbonFactor is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorEqualTo(BigDecimal value) {
            addCriterion("CarbonFactor =", value, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorNotEqualTo(BigDecimal value) {
            addCriterion("CarbonFactor <>", value, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorGreaterThan(BigDecimal value) {
            addCriterion("CarbonFactor >", value, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("CarbonFactor >=", value, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorLessThan(BigDecimal value) {
            addCriterion("CarbonFactor <", value, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorLessThanOrEqualTo(BigDecimal value) {
            addCriterion("CarbonFactor <=", value, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorIn(List<BigDecimal> values) {
            addCriterion("CarbonFactor in", values, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorNotIn(List<BigDecimal> values) {
            addCriterion("CarbonFactor not in", values, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("CarbonFactor between", value1, value2, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("CarbonFactor not between", value1, value2, "carbonfactor");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitIsNull() {
            addCriterion("CarbonFactorUnit is null");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitIsNotNull() {
            addCriterion("CarbonFactorUnit is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitEqualTo(String value) {
            addCriterion("CarbonFactorUnit =", value, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitNotEqualTo(String value) {
            addCriterion("CarbonFactorUnit <>", value, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitGreaterThan(String value) {
            addCriterion("CarbonFactorUnit >", value, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitGreaterThanOrEqualTo(String value) {
            addCriterion("CarbonFactorUnit >=", value, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitLessThan(String value) {
            addCriterion("CarbonFactorUnit <", value, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitLessThanOrEqualTo(String value) {
            addCriterion("CarbonFactorUnit <=", value, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitLike(String value) {
            addCriterion("CarbonFactorUnit like", value, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitNotLike(String value) {
            addCriterion("CarbonFactorUnit not like", value, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitIn(List<String> values) {
            addCriterion("CarbonFactorUnit in", values, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitNotIn(List<String> values) {
            addCriterion("CarbonFactorUnit not in", values, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitBetween(String value1, String value2) {
            addCriterion("CarbonFactorUnit between", value1, value2, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andCarbonfactorunitNotBetween(String value1, String value2) {
            addCriterion("CarbonFactorUnit not between", value1, value2, "carbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andTransportfactorIsNull() {
            addCriterion("TransportFactor is null");
            return (Criteria) this;
        }

        public Criteria andTransportfactorIsNotNull() {
            addCriterion("TransportFactor is not null");
            return (Criteria) this;
        }

        public Criteria andTransportfactorEqualTo(BigDecimal value) {
            addCriterion("TransportFactor =", value, "transportfactor");
            return (Criteria) this;
        }

        public Criteria andTransportfactorNotEqualTo(BigDecimal value) {
            addCriterion("TransportFactor <>", value, "transportfactor");
            return (Criteria) this;
        }

        public Criteria andTransportfactorGreaterThan(BigDecimal value) {
            addCriterion("TransportFactor >", value, "transportfactor");
            return (Criteria) this;
        }

        public Criteria andTransportfactorGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("TransportFactor >=", value, "transportfactor");
            return (Criteria) this;
        }

        public Criteria andTransportfactorLessThan(BigDecimal value) {
            addCriterion("TransportFactor <", value, "transportfactor");
            return (Criteria) this;
        }

        public Criteria andTransportfactorLessThanOrEqualTo(BigDecimal value) {
            addCriterion("TransportFactor <=", value, "transportfactor");
            return (Criteria) this;
        }

        public Criteria andTransportfactorIn(List<BigDecimal> values) {
            addCriterion("TransportFactor in", values, "transportfactor");
            return (Criteria) this;
        }

        public Criteria andTransportfactorNotIn(List<BigDecimal> values) {
            addCriterion("TransportFactor not in", values, "transportfactor");
            return (Criteria) this;
        }

        public Criteria andTransportfactorBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("TransportFactor between", value1, value2, "transportfactor");
            return (Criteria) this;
        }

        public Criteria andTransportfactorNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("TransportFactor not between", value1, value2, "transportfactor");
            return (Criteria) this;
        }

        public Criteria andTransportfactorunitIsNull() {
            addCriterion("TransportFactorUnit is null");
            return (Criteria) this;
        }

        public Criteria andTransportfactorunitIsNotNull() {
            addCriterion("TransportFactorUnit is not null");
            return (Criteria) this;
        }

        public Criteria andTransportfactorunitEqualTo(String value) {
            addCriterion("TransportFactorUnit =", value, "transportfactorunit");
            return (Criteria) this;
        }

        public Criteria andTransportfactorunitNotEqualTo(String value) {
            addCriterion("TransportFactorUnit <>", value, "transportfactorunit");
            return (Criteria) this;
        }

        public Criteria andTransportfactorunitGreaterThan(String value) {
            addCriterion("TransportFactorUnit >", value, "transportfactorunit");
            return (Criteria) this;
        }

        public Criteria andTransportfactorunitGreaterThanOrEqualTo(String value) {
            addCriterion("TransportFactorUnit >=", value, "transportfactorunit");
            return (Criteria) this;
        }

        public Criteria andTransportfactorunitLessThan(String value) {
            addCriterion("TransportFactorUnit <", value, "transportfactorunit");
            return (Criteria) this;
        }

        public Criteria andTransportfactorunitLessThanOrEqualTo(String value) {
            addCriterion("TransportFactorUnit <=", value, "transportfactorunit");
            return (Criteria) this;
        }

        public Criteria andTransportfactorunitLike(String value) {
            addCriterion("TransportFactorUnit like", value, "transportfactorunit");
            return (Criteria) this;
        }

        public Criteria andTransportfactorunitNotLike(String value) {
            addCriterion("TransportFactorUnit not like", value, "transportfactorunit");
            return (Criteria) this;
        }

        public Criteria andTransportfactorunitIn(List<String> values) {
            addCriterion("TransportFactorUnit in", values, "transportfactorunit");
            return (Criteria) this;
        }

        public Criteria andTransportfactorunitNotIn(List<String> values) {
            addCriterion("TransportFactorUnit not in", values, "transportfactorunit");
            return (Criteria) this;
        }

        public Criteria andTransportfactorunitBetween(String value1, String value2) {
            addCriterion("TransportFactorUnit between", value1, value2, "transportfactorunit");
            return (Criteria) this;
        }

        public Criteria andTransportfactorunitNotBetween(String value1, String value2) {
            addCriterion("TransportFactorUnit not between", value1, value2, "transportfactorunit");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceIsNull() {
            addCriterion("TransportDistance is null");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceIsNotNull() {
            addCriterion("TransportDistance is not null");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceEqualTo(BigDecimal value) {
            addCriterion("TransportDistance =", value, "transportdistance");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceNotEqualTo(BigDecimal value) {
            addCriterion("TransportDistance <>", value, "transportdistance");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceGreaterThan(BigDecimal value) {
            addCriterion("TransportDistance >", value, "transportdistance");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("TransportDistance >=", value, "transportdistance");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceLessThan(BigDecimal value) {
            addCriterion("TransportDistance <", value, "transportdistance");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("TransportDistance <=", value, "transportdistance");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceIn(List<BigDecimal> values) {
            addCriterion("TransportDistance in", values, "transportdistance");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceNotIn(List<BigDecimal> values) {
            addCriterion("TransportDistance not in", values, "transportdistance");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("TransportDistance between", value1, value2, "transportdistance");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("TransportDistance not between", value1, value2, "transportdistance");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceunitIsNull() {
            addCriterion("TransportDistanceUnit is null");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceunitIsNotNull() {
            addCriterion("TransportDistanceUnit is not null");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceunitEqualTo(String value) {
            addCriterion("TransportDistanceUnit =", value, "transportdistanceunit");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceunitNotEqualTo(String value) {
            addCriterion("TransportDistanceUnit <>", value, "transportdistanceunit");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceunitGreaterThan(String value) {
            addCriterion("TransportDistanceUnit >", value, "transportdistanceunit");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceunitGreaterThanOrEqualTo(String value) {
            addCriterion("TransportDistanceUnit >=", value, "transportdistanceunit");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceunitLessThan(String value) {
            addCriterion("TransportDistanceUnit <", value, "transportdistanceunit");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceunitLessThanOrEqualTo(String value) {
            addCriterion("TransportDistanceUnit <=", value, "transportdistanceunit");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceunitLike(String value) {
            addCriterion("TransportDistanceUnit like", value, "transportdistanceunit");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceunitNotLike(String value) {
            addCriterion("TransportDistanceUnit not like", value, "transportdistanceunit");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceunitIn(List<String> values) {
            addCriterion("TransportDistanceUnit in", values, "transportdistanceunit");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceunitNotIn(List<String> values) {
            addCriterion("TransportDistanceUnit not in", values, "transportdistanceunit");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceunitBetween(String value1, String value2) {
            addCriterion("TransportDistanceUnit between", value1, value2, "transportdistanceunit");
            return (Criteria) this;
        }

        public Criteria andTransportdistanceunitNotBetween(String value1, String value2) {
            addCriterion("TransportDistanceUnit not between", value1, value2, "transportdistanceunit");
            return (Criteria) this;
        }

        public Criteria andQtyIsNull() {
            addCriterion("Qty is null");
            return (Criteria) this;
        }

        public Criteria andQtyIsNotNull() {
            addCriterion("Qty is not null");
            return (Criteria) this;
        }

        public Criteria andQtyEqualTo(BigDecimal value) {
            addCriterion("Qty =", value, "qty");
            return (Criteria) this;
        }

        public Criteria andQtyNotEqualTo(BigDecimal value) {
            addCriterion("Qty <>", value, "qty");
            return (Criteria) this;
        }

        public Criteria andQtyGreaterThan(BigDecimal value) {
            addCriterion("Qty >", value, "qty");
            return (Criteria) this;
        }

        public Criteria andQtyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("Qty >=", value, "qty");
            return (Criteria) this;
        }

        public Criteria andQtyLessThan(BigDecimal value) {
            addCriterion("Qty <", value, "qty");
            return (Criteria) this;
        }

        public Criteria andQtyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("Qty <=", value, "qty");
            return (Criteria) this;
        }

        public Criteria andQtyIn(List<BigDecimal> values) {
            addCriterion("Qty in", values, "qty");
            return (Criteria) this;
        }

        public Criteria andQtyNotIn(List<BigDecimal> values) {
            addCriterion("Qty not in", values, "qty");
            return (Criteria) this;
        }

        public Criteria andQtyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("Qty between", value1, value2, "qty");
            return (Criteria) this;
        }

        public Criteria andQtyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("Qty not between", value1, value2, "qty");
            return (Criteria) this;
        }

        public Criteria andCarbonamountIsNull() {
            addCriterion("CarbonAmount is null");
            return (Criteria) this;
        }

        public Criteria andCarbonamountIsNotNull() {
            addCriterion("CarbonAmount is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonamountEqualTo(BigDecimal value) {
            addCriterion("CarbonAmount =", value, "carbonamount");
            return (Criteria) this;
        }

        public Criteria andCarbonamountNotEqualTo(BigDecimal value) {
            addCriterion("CarbonAmount <>", value, "carbonamount");
            return (Criteria) this;
        }

        public Criteria andCarbonamountGreaterThan(BigDecimal value) {
            addCriterion("CarbonAmount >", value, "carbonamount");
            return (Criteria) this;
        }

        public Criteria andCarbonamountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("CarbonAmount >=", value, "carbonamount");
            return (Criteria) this;
        }

        public Criteria andCarbonamountLessThan(BigDecimal value) {
            addCriterion("CarbonAmount <", value, "carbonamount");
            return (Criteria) this;
        }

        public Criteria andCarbonamountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("CarbonAmount <=", value, "carbonamount");
            return (Criteria) this;
        }

        public Criteria andCarbonamountIn(List<BigDecimal> values) {
            addCriterion("CarbonAmount in", values, "carbonamount");
            return (Criteria) this;
        }

        public Criteria andCarbonamountNotIn(List<BigDecimal> values) {
            addCriterion("CarbonAmount not in", values, "carbonamount");
            return (Criteria) this;
        }

        public Criteria andCarbonamountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("CarbonAmount between", value1, value2, "carbonamount");
            return (Criteria) this;
        }

        public Criteria andCarbonamountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("CarbonAmount not between", value1, value2, "carbonamount");
            return (Criteria) this;
        }

        public Criteria andTransportcarbonamountIsNull() {
            addCriterion("TransportCarbonAmount is null");
            return (Criteria) this;
        }

        public Criteria andTransportcarbonamountIsNotNull() {
            addCriterion("TransportCarbonAmount is not null");
            return (Criteria) this;
        }

        public Criteria andTransportcarbonamountEqualTo(BigDecimal value) {
            addCriterion("TransportCarbonAmount =", value, "transportcarbonamount");
            return (Criteria) this;
        }

        public Criteria andTransportcarbonamountNotEqualTo(BigDecimal value) {
            addCriterion("TransportCarbonAmount <>", value, "transportcarbonamount");
            return (Criteria) this;
        }

        public Criteria andTransportcarbonamountGreaterThan(BigDecimal value) {
            addCriterion("TransportCarbonAmount >", value, "transportcarbonamount");
            return (Criteria) this;
        }

        public Criteria andTransportcarbonamountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("TransportCarbonAmount >=", value, "transportcarbonamount");
            return (Criteria) this;
        }

        public Criteria andTransportcarbonamountLessThan(BigDecimal value) {
            addCriterion("TransportCarbonAmount <", value, "transportcarbonamount");
            return (Criteria) this;
        }

        public Criteria andTransportcarbonamountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("TransportCarbonAmount <=", value, "transportcarbonamount");
            return (Criteria) this;
        }

        public Criteria andTransportcarbonamountIn(List<BigDecimal> values) {
            addCriterion("TransportCarbonAmount in", values, "transportcarbonamount");
            return (Criteria) this;
        }

        public Criteria andTransportcarbonamountNotIn(List<BigDecimal> values) {
            addCriterion("TransportCarbonAmount not in", values, "transportcarbonamount");
            return (Criteria) this;
        }

        public Criteria andTransportcarbonamountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("TransportCarbonAmount between", value1, value2, "transportcarbonamount");
            return (Criteria) this;
        }

        public Criteria andTransportcarbonamountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("TransportCarbonAmount not between", value1, value2, "transportcarbonamount");
            return (Criteria) this;
        }

        public Criteria andScopeIsNull() {
            addCriterion("Scope is null");
            return (Criteria) this;
        }

        public Criteria andScopeIsNotNull() {
            addCriterion("Scope is not null");
            return (Criteria) this;
        }

        public Criteria andScopeEqualTo(String value) {
            addCriterion("Scope =", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeNotEqualTo(String value) {
            addCriterion("Scope <>", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeGreaterThan(String value) {
            addCriterion("Scope >", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeGreaterThanOrEqualTo(String value) {
            addCriterion("Scope >=", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeLessThan(String value) {
            addCriterion("Scope <", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeLessThanOrEqualTo(String value) {
            addCriterion("Scope <=", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeLike(String value) {
            addCriterion("Scope like", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeNotLike(String value) {
            addCriterion("Scope not like", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeIn(List<String> values) {
            addCriterion("Scope in", values, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeNotIn(List<String> values) {
            addCriterion("Scope not in", values, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeBetween(String value1, String value2) {
            addCriterion("Scope between", value1, value2, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeNotBetween(String value1, String value2) {
            addCriterion("Scope not between", value1, value2, "scope");
            return (Criteria) this;
        }

        public Criteria andTransportscopeIsNull() {
            addCriterion("TransportScope is null");
            return (Criteria) this;
        }

        public Criteria andTransportscopeIsNotNull() {
            addCriterion("TransportScope is not null");
            return (Criteria) this;
        }

        public Criteria andTransportscopeEqualTo(String value) {
            addCriterion("TransportScope =", value, "transportscope");
            return (Criteria) this;
        }

        public Criteria andTransportscopeNotEqualTo(String value) {
            addCriterion("TransportScope <>", value, "transportscope");
            return (Criteria) this;
        }

        public Criteria andTransportscopeGreaterThan(String value) {
            addCriterion("TransportScope >", value, "transportscope");
            return (Criteria) this;
        }

        public Criteria andTransportscopeGreaterThanOrEqualTo(String value) {
            addCriterion("TransportScope >=", value, "transportscope");
            return (Criteria) this;
        }

        public Criteria andTransportscopeLessThan(String value) {
            addCriterion("TransportScope <", value, "transportscope");
            return (Criteria) this;
        }

        public Criteria andTransportscopeLessThanOrEqualTo(String value) {
            addCriterion("TransportScope <=", value, "transportscope");
            return (Criteria) this;
        }

        public Criteria andTransportscopeLike(String value) {
            addCriterion("TransportScope like", value, "transportscope");
            return (Criteria) this;
        }

        public Criteria andTransportscopeNotLike(String value) {
            addCriterion("TransportScope not like", value, "transportscope");
            return (Criteria) this;
        }

        public Criteria andTransportscopeIn(List<String> values) {
            addCriterion("TransportScope in", values, "transportscope");
            return (Criteria) this;
        }

        public Criteria andTransportscopeNotIn(List<String> values) {
            addCriterion("TransportScope not in", values, "transportscope");
            return (Criteria) this;
        }

        public Criteria andTransportscopeBetween(String value1, String value2) {
            addCriterion("TransportScope between", value1, value2, "transportscope");
            return (Criteria) this;
        }

        public Criteria andTransportscopeNotBetween(String value1, String value2) {
            addCriterion("TransportScope not between", value1, value2, "transportscope");
            return (Criteria) this;
        }

        public Criteria andCalculatedateIsNull() {
            addCriterion("CalculateDate is null");
            return (Criteria) this;
        }

        public Criteria andCalculatedateIsNotNull() {
            addCriterion("CalculateDate is not null");
            return (Criteria) this;
        }

        public Criteria andCalculatedateEqualTo(LocalDate value) {
            addCriterion("CalculateDate =", value, "calculatedate");
            return (Criteria) this;
        }

        public Criteria andCalculatedateNotEqualTo(LocalDate value) {
            addCriterion("CalculateDate <>", value, "calculatedate");
            return (Criteria) this;
        }

        public Criteria andCalculatedateGreaterThan(LocalDate value) {
            addCriterion("CalculateDate >", value, "calculatedate");
            return (Criteria) this;
        }

        public Criteria andCalculatedateGreaterThanOrEqualTo(LocalDate value) {
            addCriterion("CalculateDate >=", value, "calculatedate");
            return (Criteria) this;
        }

        public Criteria andCalculatedateLessThan(LocalDate value) {
            addCriterion("CalculateDate <", value, "calculatedate");
            return (Criteria) this;
        }

        public Criteria andCalculatedateLessThanOrEqualTo(LocalDate value) {
            addCriterion("CalculateDate <=", value, "calculatedate");
            return (Criteria) this;
        }

        public Criteria andCalculatedateIn(List<LocalDate> values) {
            addCriterion("CalculateDate in", values, "calculatedate");
            return (Criteria) this;
        }

        public Criteria andCalculatedateNotIn(List<LocalDate> values) {
            addCriterion("CalculateDate not in", values, "calculatedate");
            return (Criteria) this;
        }

        public Criteria andCalculatedateBetween(LocalDate value1, LocalDate value2) {
            addCriterion("CalculateDate between", value1, value2, "calculatedate");
            return (Criteria) this;
        }

        public Criteria andCalculatedateNotBetween(LocalDate value1, LocalDate value2) {
            addCriterion("CalculateDate not between", value1, value2, "calculatedate");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table F_Material
     *
     * @mbg.generated do_not_delete_during_merge Wed Oct 19 17:12:17 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table F_Material
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}