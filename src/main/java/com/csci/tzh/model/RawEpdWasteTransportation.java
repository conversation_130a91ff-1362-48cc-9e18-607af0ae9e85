package com.csci.tzh.model;

import java.time.LocalDateTime;

public class RawEpdWasteTransportation {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.Id
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.Facility
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private String facility;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.TransactionDate
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private String transactiondate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.VehicleNo
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private String vehicleno;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.AccountNo
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private String accountno;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.ChitNo
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private String chitno;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.TimeIn
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private String timein;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.TimeOut
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private String timeout;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.WasteDepth
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private String wastedepth;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.WeightIn
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private String weightin;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.WeightOut
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private String weightout;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.NetWeight
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private String netweight;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.InputDatetime
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private LocalDateTime inputdatetime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.SyncDatetime
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private LocalDateTime syncdatetime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.SyncLogId
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private String synclogid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.ProcessDatetime
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private LocalDateTime processdatetime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation.ProcessErrorLog
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	private String processerrorlog;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.Id
	 * @return  the value of RawEpdWasteTransportation.Id
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.Id
	 * @param id  the value for RawEpdWasteTransportation.Id
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.Facility
	 * @return  the value of RawEpdWasteTransportation.Facility
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public String getFacility() {
		return facility;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.Facility
	 * @param facility  the value for RawEpdWasteTransportation.Facility
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setFacility(String facility) {
		this.facility = facility == null ? null : facility.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.TransactionDate
	 * @return  the value of RawEpdWasteTransportation.TransactionDate
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public String getTransactiondate() {
		return transactiondate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.TransactionDate
	 * @param transactiondate  the value for RawEpdWasteTransportation.TransactionDate
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setTransactiondate(String transactiondate) {
		this.transactiondate = transactiondate == null ? null : transactiondate.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.VehicleNo
	 * @return  the value of RawEpdWasteTransportation.VehicleNo
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public String getVehicleno() {
		return vehicleno;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.VehicleNo
	 * @param vehicleno  the value for RawEpdWasteTransportation.VehicleNo
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setVehicleno(String vehicleno) {
		this.vehicleno = vehicleno == null ? null : vehicleno.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.AccountNo
	 * @return  the value of RawEpdWasteTransportation.AccountNo
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public String getAccountno() {
		return accountno;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.AccountNo
	 * @param accountno  the value for RawEpdWasteTransportation.AccountNo
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setAccountno(String accountno) {
		this.accountno = accountno == null ? null : accountno.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.ChitNo
	 * @return  the value of RawEpdWasteTransportation.ChitNo
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public String getChitno() {
		return chitno;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.ChitNo
	 * @param chitno  the value for RawEpdWasteTransportation.ChitNo
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setChitno(String chitno) {
		this.chitno = chitno == null ? null : chitno.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.TimeIn
	 * @return  the value of RawEpdWasteTransportation.TimeIn
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public String getTimein() {
		return timein;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.TimeIn
	 * @param timein  the value for RawEpdWasteTransportation.TimeIn
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setTimein(String timein) {
		this.timein = timein == null ? null : timein.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.TimeOut
	 * @return  the value of RawEpdWasteTransportation.TimeOut
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public String getTimeout() {
		return timeout;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.TimeOut
	 * @param timeout  the value for RawEpdWasteTransportation.TimeOut
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setTimeout(String timeout) {
		this.timeout = timeout == null ? null : timeout.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.WasteDepth
	 * @return  the value of RawEpdWasteTransportation.WasteDepth
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public String getWastedepth() {
		return wastedepth;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.WasteDepth
	 * @param wastedepth  the value for RawEpdWasteTransportation.WasteDepth
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setWastedepth(String wastedepth) {
		this.wastedepth = wastedepth == null ? null : wastedepth.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.WeightIn
	 * @return  the value of RawEpdWasteTransportation.WeightIn
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public String getWeightin() {
		return weightin;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.WeightIn
	 * @param weightin  the value for RawEpdWasteTransportation.WeightIn
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setWeightin(String weightin) {
		this.weightin = weightin == null ? null : weightin.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.WeightOut
	 * @return  the value of RawEpdWasteTransportation.WeightOut
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public String getWeightout() {
		return weightout;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.WeightOut
	 * @param weightout  the value for RawEpdWasteTransportation.WeightOut
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setWeightout(String weightout) {
		this.weightout = weightout == null ? null : weightout.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.NetWeight
	 * @return  the value of RawEpdWasteTransportation.NetWeight
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public String getNetweight() {
		return netweight;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.NetWeight
	 * @param netweight  the value for RawEpdWasteTransportation.NetWeight
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setNetweight(String netweight) {
		this.netweight = netweight == null ? null : netweight.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.InputDatetime
	 * @return  the value of RawEpdWasteTransportation.InputDatetime
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public LocalDateTime getInputdatetime() {
		return inputdatetime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.InputDatetime
	 * @param inputdatetime  the value for RawEpdWasteTransportation.InputDatetime
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setInputdatetime(LocalDateTime inputdatetime) {
		this.inputdatetime = inputdatetime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.SyncDatetime
	 * @return  the value of RawEpdWasteTransportation.SyncDatetime
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public LocalDateTime getSyncdatetime() {
		return syncdatetime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.SyncDatetime
	 * @param syncdatetime  the value for RawEpdWasteTransportation.SyncDatetime
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setSyncdatetime(LocalDateTime syncdatetime) {
		this.syncdatetime = syncdatetime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.SyncLogId
	 * @return  the value of RawEpdWasteTransportation.SyncLogId
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public String getSynclogid() {
		return synclogid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.SyncLogId
	 * @param synclogid  the value for RawEpdWasteTransportation.SyncLogId
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setSynclogid(String synclogid) {
		this.synclogid = synclogid == null ? null : synclogid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.ProcessDatetime
	 * @return  the value of RawEpdWasteTransportation.ProcessDatetime
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public LocalDateTime getProcessdatetime() {
		return processdatetime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.ProcessDatetime
	 * @param processdatetime  the value for RawEpdWasteTransportation.ProcessDatetime
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setProcessdatetime(LocalDateTime processdatetime) {
		this.processdatetime = processdatetime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation.ProcessErrorLog
	 * @return  the value of RawEpdWasteTransportation.ProcessErrorLog
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public String getProcesserrorlog() {
		return processerrorlog;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation.ProcessErrorLog
	 * @param processerrorlog  the value for RawEpdWasteTransportation.ProcessErrorLog
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	public void setProcesserrorlog(String processerrorlog) {
		this.processerrorlog = processerrorlog == null ? null : processerrorlog.trim();
	}
}