package com.csci.tzh.model;

public class DScope {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column D_Scope.Protocol
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    private String protocol;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column D_Scope.CarbonEmissionLocation
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    private String carbonemissionlocation;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column D_Scope.ScopeMain
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    private String scopemain;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column D_Scope.ScopeDetail
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    private String scopedetail;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column D_Scope.Protocol
     *
     * @return the value of D_Scope.Protocol
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public String getProtocol() {
        return protocol;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column D_Scope.Protocol
     *
     * @param protocol the value for D_Scope.Protocol
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public void setProtocol(String protocol) {
        this.protocol = protocol == null ? null : protocol.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column D_Scope.CarbonEmissionLocation
     *
     * @return the value of D_Scope.CarbonEmissionLocation
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public String getCarbonemissionlocation() {
        return carbonemissionlocation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column D_Scope.CarbonEmissionLocation
     *
     * @param carbonemissionlocation the value for D_Scope.CarbonEmissionLocation
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public void setCarbonemissionlocation(String carbonemissionlocation) {
        this.carbonemissionlocation = carbonemissionlocation == null ? null : carbonemissionlocation.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column D_Scope.ScopeMain
     *
     * @return the value of D_Scope.ScopeMain
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public String getScopemain() {
        return scopemain;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column D_Scope.ScopeMain
     *
     * @param scopemain the value for D_Scope.ScopeMain
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public void setScopemain(String scopemain) {
        this.scopemain = scopemain == null ? null : scopemain.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column D_Scope.ScopeDetail
     *
     * @return the value of D_Scope.ScopeDetail
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public String getScopedetail() {
        return scopedetail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column D_Scope.ScopeDetail
     *
     * @param scopedetail the value for D_Scope.ScopeDetail
     *
     * @mbg.generated Mon Jan 09 11:17:37 HKT 2023
     */
    public void setScopedetail(String scopedetail) {
        this.scopedetail = scopedetail == null ? null : scopedetail.trim();
    }
}