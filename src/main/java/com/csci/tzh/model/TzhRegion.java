package com.csci.tzh.model;

public class TzhRegion {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Region.Id
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Region.Name
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Region.NameSC
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    private String namesc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Region.NameEN
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    private String nameen;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Region.Id
     *
     * @return the value of Tzh_Region.Id
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Region.Id
     *
     * @param id the value for Tzh_Region.Id
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Region.Name
     *
     * @return the value of Tzh_Region.Name
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Region.Name
     *
     * @param name the value for Tzh_Region.Name
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Region.NameSC
     *
     * @return the value of Tzh_Region.NameSC
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    public String getNamesc() {
        return namesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Region.NameSC
     *
     * @param namesc the value for Tzh_Region.NameSC
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    public void setNamesc(String namesc) {
        this.namesc = namesc == null ? null : namesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Region.NameEN
     *
     * @return the value of Tzh_Region.NameEN
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    public String getNameen() {
        return nameen;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Region.NameEN
     *
     * @param nameen the value for Tzh_Region.NameEN
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    public void setNameen(String nameen) {
        this.nameen = nameen == null ? null : nameen.trim();
    }
}