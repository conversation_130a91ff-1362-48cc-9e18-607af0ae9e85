package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class TzhOrgMaterialCarbonFactorW50Example {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public TzhOrgMaterialCarbonFactorW50Example() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("Id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("Id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("Id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("Id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("Id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("Id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("Id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("Id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("Id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("Id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("Id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("Id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("Id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("Id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andChinesenameIsNull() {
            addCriterion("ChineseName is null");
            return (Criteria) this;
        }

        public Criteria andChinesenameIsNotNull() {
            addCriterion("ChineseName is not null");
            return (Criteria) this;
        }

        public Criteria andChinesenameEqualTo(String value) {
            addCriterion("ChineseName =", value, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameNotEqualTo(String value) {
            addCriterion("ChineseName <>", value, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameGreaterThan(String value) {
            addCriterion("ChineseName >", value, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameGreaterThanOrEqualTo(String value) {
            addCriterion("ChineseName >=", value, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameLessThan(String value) {
            addCriterion("ChineseName <", value, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameLessThanOrEqualTo(String value) {
            addCriterion("ChineseName <=", value, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameLike(String value) {
            addCriterion("ChineseName like", value, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameNotLike(String value) {
            addCriterion("ChineseName not like", value, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameIn(List<String> values) {
            addCriterion("ChineseName in", values, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameNotIn(List<String> values) {
            addCriterion("ChineseName not in", values, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameBetween(String value1, String value2) {
            addCriterion("ChineseName between", value1, value2, "chinesename");
            return (Criteria) this;
        }

        public Criteria andChinesenameNotBetween(String value1, String value2) {
            addCriterion("ChineseName not between", value1, value2, "chinesename");
            return (Criteria) this;
        }

        public Criteria andUpstreamemissionIsNull() {
            addCriterion("UpstreamEmission is null");
            return (Criteria) this;
        }

        public Criteria andUpstreamemissionIsNotNull() {
            addCriterion("UpstreamEmission is not null");
            return (Criteria) this;
        }

        public Criteria andUpstreamemissionEqualTo(BigDecimal value) {
            addCriterion("UpstreamEmission =", value, "upstreamemission");
            return (Criteria) this;
        }

        public Criteria andUpstreamemissionNotEqualTo(BigDecimal value) {
            addCriterion("UpstreamEmission <>", value, "upstreamemission");
            return (Criteria) this;
        }

        public Criteria andUpstreamemissionGreaterThan(BigDecimal value) {
            addCriterion("UpstreamEmission >", value, "upstreamemission");
            return (Criteria) this;
        }

        public Criteria andUpstreamemissionGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("UpstreamEmission >=", value, "upstreamemission");
            return (Criteria) this;
        }

        public Criteria andUpstreamemissionLessThan(BigDecimal value) {
            addCriterion("UpstreamEmission <", value, "upstreamemission");
            return (Criteria) this;
        }

        public Criteria andUpstreamemissionLessThanOrEqualTo(BigDecimal value) {
            addCriterion("UpstreamEmission <=", value, "upstreamemission");
            return (Criteria) this;
        }

        public Criteria andUpstreamemissionIn(List<BigDecimal> values) {
            addCriterion("UpstreamEmission in", values, "upstreamemission");
            return (Criteria) this;
        }

        public Criteria andUpstreamemissionNotIn(List<BigDecimal> values) {
            addCriterion("UpstreamEmission not in", values, "upstreamemission");
            return (Criteria) this;
        }

        public Criteria andUpstreamemissionBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("UpstreamEmission between", value1, value2, "upstreamemission");
            return (Criteria) this;
        }

        public Criteria andUpstreamemissionNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("UpstreamEmission not between", value1, value2, "upstreamemission");
            return (Criteria) this;
        }

        public Criteria andDownstreamemissionIsNull() {
            addCriterion("DownstreamEmission is null");
            return (Criteria) this;
        }

        public Criteria andDownstreamemissionIsNotNull() {
            addCriterion("DownstreamEmission is not null");
            return (Criteria) this;
        }

        public Criteria andDownstreamemissionEqualTo(BigDecimal value) {
            addCriterion("DownstreamEmission =", value, "downstreamemission");
            return (Criteria) this;
        }

        public Criteria andDownstreamemissionNotEqualTo(BigDecimal value) {
            addCriterion("DownstreamEmission <>", value, "downstreamemission");
            return (Criteria) this;
        }

        public Criteria andDownstreamemissionGreaterThan(BigDecimal value) {
            addCriterion("DownstreamEmission >", value, "downstreamemission");
            return (Criteria) this;
        }

        public Criteria andDownstreamemissionGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("DownstreamEmission >=", value, "downstreamemission");
            return (Criteria) this;
        }

        public Criteria andDownstreamemissionLessThan(BigDecimal value) {
            addCriterion("DownstreamEmission <", value, "downstreamemission");
            return (Criteria) this;
        }

        public Criteria andDownstreamemissionLessThanOrEqualTo(BigDecimal value) {
            addCriterion("DownstreamEmission <=", value, "downstreamemission");
            return (Criteria) this;
        }

        public Criteria andDownstreamemissionIn(List<BigDecimal> values) {
            addCriterion("DownstreamEmission in", values, "downstreamemission");
            return (Criteria) this;
        }

        public Criteria andDownstreamemissionNotIn(List<BigDecimal> values) {
            addCriterion("DownstreamEmission not in", values, "downstreamemission");
            return (Criteria) this;
        }

        public Criteria andDownstreamemissionBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("DownstreamEmission between", value1, value2, "downstreamemission");
            return (Criteria) this;
        }

        public Criteria andDownstreamemissionNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("DownstreamEmission not between", value1, value2, "downstreamemission");
            return (Criteria) this;
        }

        public Criteria andEmissionunitIsNull() {
            addCriterion("EmissionUnit is null");
            return (Criteria) this;
        }

        public Criteria andEmissionunitIsNotNull() {
            addCriterion("EmissionUnit is not null");
            return (Criteria) this;
        }

        public Criteria andEmissionunitEqualTo(String value) {
            addCriterion("EmissionUnit =", value, "emissionunit");
            return (Criteria) this;
        }

        public Criteria andEmissionunitNotEqualTo(String value) {
            addCriterion("EmissionUnit <>", value, "emissionunit");
            return (Criteria) this;
        }

        public Criteria andEmissionunitGreaterThan(String value) {
            addCriterion("EmissionUnit >", value, "emissionunit");
            return (Criteria) this;
        }

        public Criteria andEmissionunitGreaterThanOrEqualTo(String value) {
            addCriterion("EmissionUnit >=", value, "emissionunit");
            return (Criteria) this;
        }

        public Criteria andEmissionunitLessThan(String value) {
            addCriterion("EmissionUnit <", value, "emissionunit");
            return (Criteria) this;
        }

        public Criteria andEmissionunitLessThanOrEqualTo(String value) {
            addCriterion("EmissionUnit <=", value, "emissionunit");
            return (Criteria) this;
        }

        public Criteria andEmissionunitLike(String value) {
            addCriterion("EmissionUnit like", value, "emissionunit");
            return (Criteria) this;
        }

        public Criteria andEmissionunitNotLike(String value) {
            addCriterion("EmissionUnit not like", value, "emissionunit");
            return (Criteria) this;
        }

        public Criteria andEmissionunitIn(List<String> values) {
            addCriterion("EmissionUnit in", values, "emissionunit");
            return (Criteria) this;
        }

        public Criteria andEmissionunitNotIn(List<String> values) {
            addCriterion("EmissionUnit not in", values, "emissionunit");
            return (Criteria) this;
        }

        public Criteria andEmissionunitBetween(String value1, String value2) {
            addCriterion("EmissionUnit between", value1, value2, "emissionunit");
            return (Criteria) this;
        }

        public Criteria andEmissionunitNotBetween(String value1, String value2) {
            addCriterion("EmissionUnit not between", value1, value2, "emissionunit");
            return (Criteria) this;
        }

        public Criteria andEmissionstageIsNull() {
            addCriterion("EmissionStage is null");
            return (Criteria) this;
        }

        public Criteria andEmissionstageIsNotNull() {
            addCriterion("EmissionStage is not null");
            return (Criteria) this;
        }

        public Criteria andEmissionstageEqualTo(String value) {
            addCriterion("EmissionStage =", value, "emissionstage");
            return (Criteria) this;
        }

        public Criteria andEmissionstageNotEqualTo(String value) {
            addCriterion("EmissionStage <>", value, "emissionstage");
            return (Criteria) this;
        }

        public Criteria andEmissionstageGreaterThan(String value) {
            addCriterion("EmissionStage >", value, "emissionstage");
            return (Criteria) this;
        }

        public Criteria andEmissionstageGreaterThanOrEqualTo(String value) {
            addCriterion("EmissionStage >=", value, "emissionstage");
            return (Criteria) this;
        }

        public Criteria andEmissionstageLessThan(String value) {
            addCriterion("EmissionStage <", value, "emissionstage");
            return (Criteria) this;
        }

        public Criteria andEmissionstageLessThanOrEqualTo(String value) {
            addCriterion("EmissionStage <=", value, "emissionstage");
            return (Criteria) this;
        }

        public Criteria andEmissionstageLike(String value) {
            addCriterion("EmissionStage like", value, "emissionstage");
            return (Criteria) this;
        }

        public Criteria andEmissionstageNotLike(String value) {
            addCriterion("EmissionStage not like", value, "emissionstage");
            return (Criteria) this;
        }

        public Criteria andEmissionstageIn(List<String> values) {
            addCriterion("EmissionStage in", values, "emissionstage");
            return (Criteria) this;
        }

        public Criteria andEmissionstageNotIn(List<String> values) {
            addCriterion("EmissionStage not in", values, "emissionstage");
            return (Criteria) this;
        }

        public Criteria andEmissionstageBetween(String value1, String value2) {
            addCriterion("EmissionStage between", value1, value2, "emissionstage");
            return (Criteria) this;
        }

        public Criteria andEmissionstageNotBetween(String value1, String value2) {
            addCriterion("EmissionStage not between", value1, value2, "emissionstage");
            return (Criteria) this;
        }

        public Criteria andUncertaintyIsNull() {
            addCriterion("Uncertainty is null");
            return (Criteria) this;
        }

        public Criteria andUncertaintyIsNotNull() {
            addCriterion("Uncertainty is not null");
            return (Criteria) this;
        }

        public Criteria andUncertaintyEqualTo(String value) {
            addCriterion("Uncertainty =", value, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyNotEqualTo(String value) {
            addCriterion("Uncertainty <>", value, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyGreaterThan(String value) {
            addCriterion("Uncertainty >", value, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyGreaterThanOrEqualTo(String value) {
            addCriterion("Uncertainty >=", value, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyLessThan(String value) {
            addCriterion("Uncertainty <", value, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyLessThanOrEqualTo(String value) {
            addCriterion("Uncertainty <=", value, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyLike(String value) {
            addCriterion("Uncertainty like", value, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyNotLike(String value) {
            addCriterion("Uncertainty not like", value, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyIn(List<String> values) {
            addCriterion("Uncertainty in", values, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyNotIn(List<String> values) {
            addCriterion("Uncertainty not in", values, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyBetween(String value1, String value2) {
            addCriterion("Uncertainty between", value1, value2, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyNotBetween(String value1, String value2) {
            addCriterion("Uncertainty not between", value1, value2, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andOthersIsNull() {
            addCriterion("Others is null");
            return (Criteria) this;
        }

        public Criteria andOthersIsNotNull() {
            addCriterion("Others is not null");
            return (Criteria) this;
        }

        public Criteria andOthersEqualTo(String value) {
            addCriterion("Others =", value, "others");
            return (Criteria) this;
        }

        public Criteria andOthersNotEqualTo(String value) {
            addCriterion("Others <>", value, "others");
            return (Criteria) this;
        }

        public Criteria andOthersGreaterThan(String value) {
            addCriterion("Others >", value, "others");
            return (Criteria) this;
        }

        public Criteria andOthersGreaterThanOrEqualTo(String value) {
            addCriterion("Others >=", value, "others");
            return (Criteria) this;
        }

        public Criteria andOthersLessThan(String value) {
            addCriterion("Others <", value, "others");
            return (Criteria) this;
        }

        public Criteria andOthersLessThanOrEqualTo(String value) {
            addCriterion("Others <=", value, "others");
            return (Criteria) this;
        }

        public Criteria andOthersLike(String value) {
            addCriterion("Others like", value, "others");
            return (Criteria) this;
        }

        public Criteria andOthersNotLike(String value) {
            addCriterion("Others not like", value, "others");
            return (Criteria) this;
        }

        public Criteria andOthersIn(List<String> values) {
            addCriterion("Others in", values, "others");
            return (Criteria) this;
        }

        public Criteria andOthersNotIn(List<String> values) {
            addCriterion("Others not in", values, "others");
            return (Criteria) this;
        }

        public Criteria andOthersBetween(String value1, String value2) {
            addCriterion("Others between", value1, value2, "others");
            return (Criteria) this;
        }

        public Criteria andOthersNotBetween(String value1, String value2) {
            addCriterion("Others not between", value1, value2, "others");
            return (Criteria) this;
        }

        public Criteria andDatatimeIsNull() {
            addCriterion("DataTime is null");
            return (Criteria) this;
        }

        public Criteria andDatatimeIsNotNull() {
            addCriterion("DataTime is not null");
            return (Criteria) this;
        }

        public Criteria andDatatimeEqualTo(String value) {
            addCriterion("DataTime =", value, "datatime");
            return (Criteria) this;
        }

        public Criteria andDatatimeNotEqualTo(String value) {
            addCriterion("DataTime <>", value, "datatime");
            return (Criteria) this;
        }

        public Criteria andDatatimeGreaterThan(String value) {
            addCriterion("DataTime >", value, "datatime");
            return (Criteria) this;
        }

        public Criteria andDatatimeGreaterThanOrEqualTo(String value) {
            addCriterion("DataTime >=", value, "datatime");
            return (Criteria) this;
        }

        public Criteria andDatatimeLessThan(String value) {
            addCriterion("DataTime <", value, "datatime");
            return (Criteria) this;
        }

        public Criteria andDatatimeLessThanOrEqualTo(String value) {
            addCriterion("DataTime <=", value, "datatime");
            return (Criteria) this;
        }

        public Criteria andDatatimeLike(String value) {
            addCriterion("DataTime like", value, "datatime");
            return (Criteria) this;
        }

        public Criteria andDatatimeNotLike(String value) {
            addCriterion("DataTime not like", value, "datatime");
            return (Criteria) this;
        }

        public Criteria andDatatimeIn(List<String> values) {
            addCriterion("DataTime in", values, "datatime");
            return (Criteria) this;
        }

        public Criteria andDatatimeNotIn(List<String> values) {
            addCriterion("DataTime not in", values, "datatime");
            return (Criteria) this;
        }

        public Criteria andDatatimeBetween(String value1, String value2) {
            addCriterion("DataTime between", value1, value2, "datatime");
            return (Criteria) this;
        }

        public Criteria andDatatimeNotBetween(String value1, String value2) {
            addCriterion("DataTime not between", value1, value2, "datatime");
            return (Criteria) this;
        }

        public Criteria andDatasourceIsNull() {
            addCriterion("Datasource is null");
            return (Criteria) this;
        }

        public Criteria andDatasourceIsNotNull() {
            addCriterion("Datasource is not null");
            return (Criteria) this;
        }

        public Criteria andDatasourceEqualTo(String value) {
            addCriterion("Datasource =", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceNotEqualTo(String value) {
            addCriterion("Datasource <>", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceGreaterThan(String value) {
            addCriterion("Datasource >", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceGreaterThanOrEqualTo(String value) {
            addCriterion("Datasource >=", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceLessThan(String value) {
            addCriterion("Datasource <", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceLessThanOrEqualTo(String value) {
            addCriterion("Datasource <=", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceLike(String value) {
            addCriterion("Datasource like", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceNotLike(String value) {
            addCriterion("Datasource not like", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceIn(List<String> values) {
            addCriterion("Datasource in", values, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceNotIn(List<String> values) {
            addCriterion("Datasource not in", values, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceBetween(String value1, String value2) {
            addCriterion("Datasource between", value1, value2, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceNotBetween(String value1, String value2) {
            addCriterion("Datasource not between", value1, value2, "datasource");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIsNull() {
            addCriterion("CreatedBy is null");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIsNotNull() {
            addCriterion("CreatedBy is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedbyEqualTo(String value) {
            addCriterion("CreatedBy =", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotEqualTo(String value) {
            addCriterion("CreatedBy <>", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyGreaterThan(String value) {
            addCriterion("CreatedBy >", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
            addCriterion("CreatedBy >=", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLessThan(String value) {
            addCriterion("CreatedBy <", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLessThanOrEqualTo(String value) {
            addCriterion("CreatedBy <=", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLike(String value) {
            addCriterion("CreatedBy like", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotLike(String value) {
            addCriterion("CreatedBy not like", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIn(List<String> values) {
            addCriterion("CreatedBy in", values, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotIn(List<String> values) {
            addCriterion("CreatedBy not in", values, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyBetween(String value1, String value2) {
            addCriterion("CreatedBy between", value1, value2, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotBetween(String value1, String value2) {
            addCriterion("CreatedBy not between", value1, value2, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeIsNull() {
            addCriterion("CreatedTime is null");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeIsNotNull() {
            addCriterion("CreatedTime is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeEqualTo(LocalDateTime value) {
            addCriterion("CreatedTime =", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeNotEqualTo(LocalDateTime value) {
            addCriterion("CreatedTime <>", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeGreaterThan(LocalDateTime value) {
            addCriterion("CreatedTime >", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("CreatedTime >=", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeLessThan(LocalDateTime value) {
            addCriterion("CreatedTime <", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("CreatedTime <=", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeIn(List<LocalDateTime> values) {
            addCriterion("CreatedTime in", values, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeNotIn(List<LocalDateTime> values) {
            addCriterion("CreatedTime not in", values, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("CreatedTime between", value1, value2, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("CreatedTime not between", value1, value2, "createdtime");
            return (Criteria) this;
        }

        public Criteria andDeletedbyIsNull() {
            addCriterion("DeletedBy is null");
            return (Criteria) this;
        }

        public Criteria andDeletedbyIsNotNull() {
            addCriterion("DeletedBy is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedbyEqualTo(String value) {
            addCriterion("DeletedBy =", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyNotEqualTo(String value) {
            addCriterion("DeletedBy <>", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyGreaterThan(String value) {
            addCriterion("DeletedBy >", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyGreaterThanOrEqualTo(String value) {
            addCriterion("DeletedBy >=", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyLessThan(String value) {
            addCriterion("DeletedBy <", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyLessThanOrEqualTo(String value) {
            addCriterion("DeletedBy <=", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyLike(String value) {
            addCriterion("DeletedBy like", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyNotLike(String value) {
            addCriterion("DeletedBy not like", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyIn(List<String> values) {
            addCriterion("DeletedBy in", values, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyNotIn(List<String> values) {
            addCriterion("DeletedBy not in", values, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyBetween(String value1, String value2) {
            addCriterion("DeletedBy between", value1, value2, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyNotBetween(String value1, String value2) {
            addCriterion("DeletedBy not between", value1, value2, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeIsNull() {
            addCriterion("DeletedTime is null");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeIsNotNull() {
            addCriterion("DeletedTime is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeEqualTo(LocalDateTime value) {
            addCriterion("DeletedTime =", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeNotEqualTo(LocalDateTime value) {
            addCriterion("DeletedTime <>", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeGreaterThan(LocalDateTime value) {
            addCriterion("DeletedTime >", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("DeletedTime >=", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeLessThan(LocalDateTime value) {
            addCriterion("DeletedTime <", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("DeletedTime <=", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeIn(List<LocalDateTime> values) {
            addCriterion("DeletedTime in", values, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeNotIn(List<LocalDateTime> values) {
            addCriterion("DeletedTime not in", values, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("DeletedTime between", value1, value2, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("DeletedTime not between", value1, value2, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andIsdeletedIsNull() {
            addCriterion("IsDeleted is null");
            return (Criteria) this;
        }

        public Criteria andIsdeletedIsNotNull() {
            addCriterion("IsDeleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsdeletedEqualTo(Boolean value) {
            addCriterion("IsDeleted =", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedNotEqualTo(Boolean value) {
            addCriterion("IsDeleted <>", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedGreaterThan(Boolean value) {
            addCriterion("IsDeleted >", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("IsDeleted >=", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedLessThan(Boolean value) {
            addCriterion("IsDeleted <", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("IsDeleted <=", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedIn(List<Boolean> values) {
            addCriterion("IsDeleted in", values, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedNotIn(List<Boolean> values) {
            addCriterion("IsDeleted not in", values, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("IsDeleted between", value1, value2, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("IsDeleted not between", value1, value2, "isdeleted");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated do_not_delete_during_merge Fri Aug 11 15:13:14 HKT 2023
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}