package com.csci.tzh.model;

import java.util.ArrayList;
import java.util.List;

public class TzhDataSourceExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_DataSource
	 * @mbg.generated  Tue Mar 28 17:54:41 HKT 2023
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_DataSource
	 * @mbg.generated  Tue Mar 28 17:54:41 HKT 2023
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_DataSource
	 * @mbg.generated  Tue Mar 28 17:54:41 HKT 2023
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_DataSource
	 * @mbg.generated  Tue Mar 28 17:54:41 HKT 2023
	 */
	public TzhDataSourceExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_DataSource
	 * @mbg.generated  Tue Mar 28 17:54:41 HKT 2023
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_DataSource
	 * @mbg.generated  Tue Mar 28 17:54:41 HKT 2023
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_DataSource
	 * @mbg.generated  Tue Mar 28 17:54:41 HKT 2023
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_DataSource
	 * @mbg.generated  Tue Mar 28 17:54:41 HKT 2023
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_DataSource
	 * @mbg.generated  Tue Mar 28 17:54:41 HKT 2023
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_DataSource
	 * @mbg.generated  Tue Mar 28 17:54:41 HKT 2023
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_DataSource
	 * @mbg.generated  Tue Mar 28 17:54:41 HKT 2023
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_DataSource
	 * @mbg.generated  Tue Mar 28 17:54:41 HKT 2023
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_DataSource
	 * @mbg.generated  Tue Mar 28 17:54:41 HKT 2023
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_DataSource
	 * @mbg.generated  Tue Mar 28 17:54:41 HKT 2023
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_DataSource
	 * @mbg.generated  Tue Mar 28 17:54:41 HKT 2023
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("Id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("Id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("Id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("Id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("Id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("Id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("Id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("Id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("Id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("Id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("Id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("Id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("Id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("Id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andNameIsNull() {
			addCriterion("Name is null");
			return (Criteria) this;
		}

		public Criteria andNameIsNotNull() {
			addCriterion("Name is not null");
			return (Criteria) this;
		}

		public Criteria andNameEqualTo(String value) {
			addCriterion("Name =", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotEqualTo(String value) {
			addCriterion("Name <>", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameGreaterThan(String value) {
			addCriterion("Name >", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameGreaterThanOrEqualTo(String value) {
			addCriterion("Name >=", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLessThan(String value) {
			addCriterion("Name <", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLessThanOrEqualTo(String value) {
			addCriterion("Name <=", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLike(String value) {
			addCriterion("Name like", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotLike(String value) {
			addCriterion("Name not like", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameIn(List<String> values) {
			addCriterion("Name in", values, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotIn(List<String> values) {
			addCriterion("Name not in", values, "name");
			return (Criteria) this;
		}

		public Criteria andNameBetween(String value1, String value2) {
			addCriterion("Name between", value1, value2, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotBetween(String value1, String value2) {
			addCriterion("Name not between", value1, value2, "name");
			return (Criteria) this;
		}

		public Criteria andNamescIsNull() {
			addCriterion("NameSC is null");
			return (Criteria) this;
		}

		public Criteria andNamescIsNotNull() {
			addCriterion("NameSC is not null");
			return (Criteria) this;
		}

		public Criteria andNamescEqualTo(String value) {
			addCriterion("NameSC =", value, "namesc");
			return (Criteria) this;
		}

		public Criteria andNamescNotEqualTo(String value) {
			addCriterion("NameSC <>", value, "namesc");
			return (Criteria) this;
		}

		public Criteria andNamescGreaterThan(String value) {
			addCriterion("NameSC >", value, "namesc");
			return (Criteria) this;
		}

		public Criteria andNamescGreaterThanOrEqualTo(String value) {
			addCriterion("NameSC >=", value, "namesc");
			return (Criteria) this;
		}

		public Criteria andNamescLessThan(String value) {
			addCriterion("NameSC <", value, "namesc");
			return (Criteria) this;
		}

		public Criteria andNamescLessThanOrEqualTo(String value) {
			addCriterion("NameSC <=", value, "namesc");
			return (Criteria) this;
		}

		public Criteria andNamescLike(String value) {
			addCriterion("NameSC like", value, "namesc");
			return (Criteria) this;
		}

		public Criteria andNamescNotLike(String value) {
			addCriterion("NameSC not like", value, "namesc");
			return (Criteria) this;
		}

		public Criteria andNamescIn(List<String> values) {
			addCriterion("NameSC in", values, "namesc");
			return (Criteria) this;
		}

		public Criteria andNamescNotIn(List<String> values) {
			addCriterion("NameSC not in", values, "namesc");
			return (Criteria) this;
		}

		public Criteria andNamescBetween(String value1, String value2) {
			addCriterion("NameSC between", value1, value2, "namesc");
			return (Criteria) this;
		}

		public Criteria andNamescNotBetween(String value1, String value2) {
			addCriterion("NameSC not between", value1, value2, "namesc");
			return (Criteria) this;
		}

		public Criteria andNameenIsNull() {
			addCriterion("NameEN is null");
			return (Criteria) this;
		}

		public Criteria andNameenIsNotNull() {
			addCriterion("NameEN is not null");
			return (Criteria) this;
		}

		public Criteria andNameenEqualTo(String value) {
			addCriterion("NameEN =", value, "nameen");
			return (Criteria) this;
		}

		public Criteria andNameenNotEqualTo(String value) {
			addCriterion("NameEN <>", value, "nameen");
			return (Criteria) this;
		}

		public Criteria andNameenGreaterThan(String value) {
			addCriterion("NameEN >", value, "nameen");
			return (Criteria) this;
		}

		public Criteria andNameenGreaterThanOrEqualTo(String value) {
			addCriterion("NameEN >=", value, "nameen");
			return (Criteria) this;
		}

		public Criteria andNameenLessThan(String value) {
			addCriterion("NameEN <", value, "nameen");
			return (Criteria) this;
		}

		public Criteria andNameenLessThanOrEqualTo(String value) {
			addCriterion("NameEN <=", value, "nameen");
			return (Criteria) this;
		}

		public Criteria andNameenLike(String value) {
			addCriterion("NameEN like", value, "nameen");
			return (Criteria) this;
		}

		public Criteria andNameenNotLike(String value) {
			addCriterion("NameEN not like", value, "nameen");
			return (Criteria) this;
		}

		public Criteria andNameenIn(List<String> values) {
			addCriterion("NameEN in", values, "nameen");
			return (Criteria) this;
		}

		public Criteria andNameenNotIn(List<String> values) {
			addCriterion("NameEN not in", values, "nameen");
			return (Criteria) this;
		}

		public Criteria andNameenBetween(String value1, String value2) {
			addCriterion("NameEN between", value1, value2, "nameen");
			return (Criteria) this;
		}

		public Criteria andNameenNotBetween(String value1, String value2) {
			addCriterion("NameEN not between", value1, value2, "nameen");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_DataSource
	 * @mbg.generated  Tue Mar 28 17:54:41 HKT 2023
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_DataSource
     *
     * @mbg.generated do_not_delete_during_merge Tue Mar 28 15:49:14 HKT 2023
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}