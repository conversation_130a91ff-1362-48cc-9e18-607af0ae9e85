package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class TzhProjectInfoExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated Thu Feb 06 09:02:18 CST 2025
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated Thu Feb 06 09:02:18 CST 2025
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated Thu Feb 06 09:02:18 CST 2025
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated Thu Feb 06 09:02:18 CST 2025
     */
    public TzhProjectInfoExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated Thu Feb 06 09:02:18 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated Thu Feb 06 09:02:18 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated Thu Feb 06 09:02:18 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated Thu Feb 06 09:02:18 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated Thu Feb 06 09:02:18 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated Thu Feb 06 09:02:18 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated Thu Feb 06 09:02:18 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated Thu Feb 06 09:02:18 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated Thu Feb 06 09:02:18 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated Thu Feb 06 09:02:18 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated Thu Feb 06 09:02:18 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("Id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("Id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("Id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("Id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("Id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("Id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("Id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("Id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("Id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("Id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("Id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("Id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("Id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("Id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("Code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("Code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("Code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("Code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("Code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("Code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("Code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("Code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("Code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("Code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("Code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("Code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("Code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("Code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("Name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("Name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("Name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("Name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("Name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("Name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("Name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("Name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("Name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("Name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("Name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("Name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("Name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("Name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("Type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("Type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("Type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("Type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("Type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("Type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("Type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("Type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("Type like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("Type not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("Type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("Type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("Type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("Type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andIsconsumptionmeasurableIsNull() {
            addCriterion("IsConsumptionMeasurable is null");
            return (Criteria) this;
        }

        public Criteria andIsconsumptionmeasurableIsNotNull() {
            addCriterion("IsConsumptionMeasurable is not null");
            return (Criteria) this;
        }

        public Criteria andIsconsumptionmeasurableEqualTo(String value) {
            addCriterion("IsConsumptionMeasurable =", value, "isconsumptionmeasurable");
            return (Criteria) this;
        }

        public Criteria andIsconsumptionmeasurableNotEqualTo(String value) {
            addCriterion("IsConsumptionMeasurable <>", value, "isconsumptionmeasurable");
            return (Criteria) this;
        }

        public Criteria andIsconsumptionmeasurableGreaterThan(String value) {
            addCriterion("IsConsumptionMeasurable >", value, "isconsumptionmeasurable");
            return (Criteria) this;
        }

        public Criteria andIsconsumptionmeasurableGreaterThanOrEqualTo(String value) {
            addCriterion("IsConsumptionMeasurable >=", value, "isconsumptionmeasurable");
            return (Criteria) this;
        }

        public Criteria andIsconsumptionmeasurableLessThan(String value) {
            addCriterion("IsConsumptionMeasurable <", value, "isconsumptionmeasurable");
            return (Criteria) this;
        }

        public Criteria andIsconsumptionmeasurableLessThanOrEqualTo(String value) {
            addCriterion("IsConsumptionMeasurable <=", value, "isconsumptionmeasurable");
            return (Criteria) this;
        }

        public Criteria andIsconsumptionmeasurableLike(String value) {
            addCriterion("IsConsumptionMeasurable like", value, "isconsumptionmeasurable");
            return (Criteria) this;
        }

        public Criteria andIsconsumptionmeasurableNotLike(String value) {
            addCriterion("IsConsumptionMeasurable not like", value, "isconsumptionmeasurable");
            return (Criteria) this;
        }

        public Criteria andIsconsumptionmeasurableIn(List<String> values) {
            addCriterion("IsConsumptionMeasurable in", values, "isconsumptionmeasurable");
            return (Criteria) this;
        }

        public Criteria andIsconsumptionmeasurableNotIn(List<String> values) {
            addCriterion("IsConsumptionMeasurable not in", values, "isconsumptionmeasurable");
            return (Criteria) this;
        }

        public Criteria andIsconsumptionmeasurableBetween(String value1, String value2) {
            addCriterion("IsConsumptionMeasurable between", value1, value2, "isconsumptionmeasurable");
            return (Criteria) this;
        }

        public Criteria andIsconsumptionmeasurableNotBetween(String value1, String value2) {
            addCriterion("IsConsumptionMeasurable not between", value1, value2, "isconsumptionmeasurable");
            return (Criteria) this;
        }

        public Criteria andIsperiodicallyreportableIsNull() {
            addCriterion("IsPeriodicallyReportable is null");
            return (Criteria) this;
        }

        public Criteria andIsperiodicallyreportableIsNotNull() {
            addCriterion("IsPeriodicallyReportable is not null");
            return (Criteria) this;
        }

        public Criteria andIsperiodicallyreportableEqualTo(String value) {
            addCriterion("IsPeriodicallyReportable =", value, "isperiodicallyreportable");
            return (Criteria) this;
        }

        public Criteria andIsperiodicallyreportableNotEqualTo(String value) {
            addCriterion("IsPeriodicallyReportable <>", value, "isperiodicallyreportable");
            return (Criteria) this;
        }

        public Criteria andIsperiodicallyreportableGreaterThan(String value) {
            addCriterion("IsPeriodicallyReportable >", value, "isperiodicallyreportable");
            return (Criteria) this;
        }

        public Criteria andIsperiodicallyreportableGreaterThanOrEqualTo(String value) {
            addCriterion("IsPeriodicallyReportable >=", value, "isperiodicallyreportable");
            return (Criteria) this;
        }

        public Criteria andIsperiodicallyreportableLessThan(String value) {
            addCriterion("IsPeriodicallyReportable <", value, "isperiodicallyreportable");
            return (Criteria) this;
        }

        public Criteria andIsperiodicallyreportableLessThanOrEqualTo(String value) {
            addCriterion("IsPeriodicallyReportable <=", value, "isperiodicallyreportable");
            return (Criteria) this;
        }

        public Criteria andIsperiodicallyreportableLike(String value) {
            addCriterion("IsPeriodicallyReportable like", value, "isperiodicallyreportable");
            return (Criteria) this;
        }

        public Criteria andIsperiodicallyreportableNotLike(String value) {
            addCriterion("IsPeriodicallyReportable not like", value, "isperiodicallyreportable");
            return (Criteria) this;
        }

        public Criteria andIsperiodicallyreportableIn(List<String> values) {
            addCriterion("IsPeriodicallyReportable in", values, "isperiodicallyreportable");
            return (Criteria) this;
        }

        public Criteria andIsperiodicallyreportableNotIn(List<String> values) {
            addCriterion("IsPeriodicallyReportable not in", values, "isperiodicallyreportable");
            return (Criteria) this;
        }

        public Criteria andIsperiodicallyreportableBetween(String value1, String value2) {
            addCriterion("IsPeriodicallyReportable between", value1, value2, "isperiodicallyreportable");
            return (Criteria) this;
        }

        public Criteria andIsperiodicallyreportableNotBetween(String value1, String value2) {
            addCriterion("IsPeriodicallyReportable not between", value1, value2, "isperiodicallyreportable");
            return (Criteria) this;
        }

        public Criteria andInvestmenttotalIsNull() {
            addCriterion("InvestmentTotal is null");
            return (Criteria) this;
        }

        public Criteria andInvestmenttotalIsNotNull() {
            addCriterion("InvestmentTotal is not null");
            return (Criteria) this;
        }

        public Criteria andInvestmenttotalEqualTo(BigDecimal value) {
            addCriterion("InvestmentTotal =", value, "investmenttotal");
            return (Criteria) this;
        }

        public Criteria andInvestmenttotalNotEqualTo(BigDecimal value) {
            addCriterion("InvestmentTotal <>", value, "investmenttotal");
            return (Criteria) this;
        }

        public Criteria andInvestmenttotalGreaterThan(BigDecimal value) {
            addCriterion("InvestmentTotal >", value, "investmenttotal");
            return (Criteria) this;
        }

        public Criteria andInvestmenttotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("InvestmentTotal >=", value, "investmenttotal");
            return (Criteria) this;
        }

        public Criteria andInvestmenttotalLessThan(BigDecimal value) {
            addCriterion("InvestmentTotal <", value, "investmenttotal");
            return (Criteria) this;
        }

        public Criteria andInvestmenttotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("InvestmentTotal <=", value, "investmenttotal");
            return (Criteria) this;
        }

        public Criteria andInvestmenttotalIn(List<BigDecimal> values) {
            addCriterion("InvestmentTotal in", values, "investmenttotal");
            return (Criteria) this;
        }

        public Criteria andInvestmenttotalNotIn(List<BigDecimal> values) {
            addCriterion("InvestmentTotal not in", values, "investmenttotal");
            return (Criteria) this;
        }

        public Criteria andInvestmenttotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("InvestmentTotal between", value1, value2, "investmenttotal");
            return (Criteria) this;
        }

        public Criteria andInvestmenttotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("InvestmentTotal not between", value1, value2, "investmenttotal");
            return (Criteria) this;
        }

        public Criteria andContractamountIsNull() {
            addCriterion("ContractAmount is null");
            return (Criteria) this;
        }

        public Criteria andContractamountIsNotNull() {
            addCriterion("ContractAmount is not null");
            return (Criteria) this;
        }

        public Criteria andContractamountEqualTo(BigDecimal value) {
            addCriterion("ContractAmount =", value, "contractamount");
            return (Criteria) this;
        }

        public Criteria andContractamountNotEqualTo(BigDecimal value) {
            addCriterion("ContractAmount <>", value, "contractamount");
            return (Criteria) this;
        }

        public Criteria andContractamountGreaterThan(BigDecimal value) {
            addCriterion("ContractAmount >", value, "contractamount");
            return (Criteria) this;
        }

        public Criteria andContractamountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ContractAmount >=", value, "contractamount");
            return (Criteria) this;
        }

        public Criteria andContractamountLessThan(BigDecimal value) {
            addCriterion("ContractAmount <", value, "contractamount");
            return (Criteria) this;
        }

        public Criteria andContractamountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ContractAmount <=", value, "contractamount");
            return (Criteria) this;
        }

        public Criteria andContractamountIn(List<BigDecimal> values) {
            addCriterion("ContractAmount in", values, "contractamount");
            return (Criteria) this;
        }

        public Criteria andContractamountNotIn(List<BigDecimal> values) {
            addCriterion("ContractAmount not in", values, "contractamount");
            return (Criteria) this;
        }

        public Criteria andContractamountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ContractAmount between", value1, value2, "contractamount");
            return (Criteria) this;
        }

        public Criteria andContractamountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ContractAmount not between", value1, value2, "contractamount");
            return (Criteria) this;
        }

        public Criteria andAreaIsNull() {
            addCriterion("Area is null");
            return (Criteria) this;
        }

        public Criteria andAreaIsNotNull() {
            addCriterion("Area is not null");
            return (Criteria) this;
        }

        public Criteria andAreaEqualTo(BigDecimal value) {
            addCriterion("Area =", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotEqualTo(BigDecimal value) {
            addCriterion("Area <>", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaGreaterThan(BigDecimal value) {
            addCriterion("Area >", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("Area >=", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLessThan(BigDecimal value) {
            addCriterion("Area <", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLessThanOrEqualTo(BigDecimal value) {
            addCriterion("Area <=", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaIn(List<BigDecimal> values) {
            addCriterion("Area in", values, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotIn(List<BigDecimal> values) {
            addCriterion("Area not in", values, "area");
            return (Criteria) this;
        }

        public Criteria andAreaBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("Area between", value1, value2, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("Area not between", value1, value2, "area");
            return (Criteria) this;
        }

        public Criteria andStartdateIsNull() {
            addCriterion("StartDate is null");
            return (Criteria) this;
        }

        public Criteria andStartdateIsNotNull() {
            addCriterion("StartDate is not null");
            return (Criteria) this;
        }

        public Criteria andStartdateEqualTo(LocalDate value) {
            addCriterion("StartDate =", value, "startdate");
            return (Criteria) this;
        }

        public Criteria andStartdateNotEqualTo(LocalDate value) {
            addCriterion("StartDate <>", value, "startdate");
            return (Criteria) this;
        }

        public Criteria andStartdateGreaterThan(LocalDate value) {
            addCriterion("StartDate >", value, "startdate");
            return (Criteria) this;
        }

        public Criteria andStartdateGreaterThanOrEqualTo(LocalDate value) {
            addCriterion("StartDate >=", value, "startdate");
            return (Criteria) this;
        }

        public Criteria andStartdateLessThan(LocalDate value) {
            addCriterion("StartDate <", value, "startdate");
            return (Criteria) this;
        }

        public Criteria andStartdateLessThanOrEqualTo(LocalDate value) {
            addCriterion("StartDate <=", value, "startdate");
            return (Criteria) this;
        }

        public Criteria andStartdateIn(List<LocalDate> values) {
            addCriterion("StartDate in", values, "startdate");
            return (Criteria) this;
        }

        public Criteria andStartdateNotIn(List<LocalDate> values) {
            addCriterion("StartDate not in", values, "startdate");
            return (Criteria) this;
        }

        public Criteria andStartdateBetween(LocalDate value1, LocalDate value2) {
            addCriterion("StartDate between", value1, value2, "startdate");
            return (Criteria) this;
        }

        public Criteria andStartdateNotBetween(LocalDate value1, LocalDate value2) {
            addCriterion("StartDate not between", value1, value2, "startdate");
            return (Criteria) this;
        }

        public Criteria andEnddateIsNull() {
            addCriterion("EndDate is null");
            return (Criteria) this;
        }

        public Criteria andEnddateIsNotNull() {
            addCriterion("EndDate is not null");
            return (Criteria) this;
        }

        public Criteria andEnddateEqualTo(LocalDate value) {
            addCriterion("EndDate =", value, "enddate");
            return (Criteria) this;
        }

        public Criteria andEnddateNotEqualTo(LocalDate value) {
            addCriterion("EndDate <>", value, "enddate");
            return (Criteria) this;
        }

        public Criteria andEnddateGreaterThan(LocalDate value) {
            addCriterion("EndDate >", value, "enddate");
            return (Criteria) this;
        }

        public Criteria andEnddateGreaterThanOrEqualTo(LocalDate value) {
            addCriterion("EndDate >=", value, "enddate");
            return (Criteria) this;
        }

        public Criteria andEnddateLessThan(LocalDate value) {
            addCriterion("EndDate <", value, "enddate");
            return (Criteria) this;
        }

        public Criteria andEnddateLessThanOrEqualTo(LocalDate value) {
            addCriterion("EndDate <=", value, "enddate");
            return (Criteria) this;
        }

        public Criteria andEnddateIn(List<LocalDate> values) {
            addCriterion("EndDate in", values, "enddate");
            return (Criteria) this;
        }

        public Criteria andEnddateNotIn(List<LocalDate> values) {
            addCriterion("EndDate not in", values, "enddate");
            return (Criteria) this;
        }

        public Criteria andEnddateBetween(LocalDate value1, LocalDate value2) {
            addCriterion("EndDate between", value1, value2, "enddate");
            return (Criteria) this;
        }

        public Criteria andEnddateNotBetween(LocalDate value1, LocalDate value2) {
            addCriterion("EndDate not between", value1, value2, "enddate");
            return (Criteria) this;
        }

        public Criteria andAddressIsNull() {
            addCriterion("Address is null");
            return (Criteria) this;
        }

        public Criteria andAddressIsNotNull() {
            addCriterion("Address is not null");
            return (Criteria) this;
        }

        public Criteria andAddressEqualTo(String value) {
            addCriterion("Address =", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotEqualTo(String value) {
            addCriterion("Address <>", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThan(String value) {
            addCriterion("Address >", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThanOrEqualTo(String value) {
            addCriterion("Address >=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThan(String value) {
            addCriterion("Address <", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThanOrEqualTo(String value) {
            addCriterion("Address <=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLike(String value) {
            addCriterion("Address like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotLike(String value) {
            addCriterion("Address not like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressIn(List<String> values) {
            addCriterion("Address in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotIn(List<String> values) {
            addCriterion("Address not in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressBetween(String value1, String value2) {
            addCriterion("Address between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotBetween(String value1, String value2) {
            addCriterion("Address not between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andContractorIsNull() {
            addCriterion("Contractor is null");
            return (Criteria) this;
        }

        public Criteria andContractorIsNotNull() {
            addCriterion("Contractor is not null");
            return (Criteria) this;
        }

        public Criteria andContractorEqualTo(String value) {
            addCriterion("Contractor =", value, "contractor");
            return (Criteria) this;
        }

        public Criteria andContractorNotEqualTo(String value) {
            addCriterion("Contractor <>", value, "contractor");
            return (Criteria) this;
        }

        public Criteria andContractorGreaterThan(String value) {
            addCriterion("Contractor >", value, "contractor");
            return (Criteria) this;
        }

        public Criteria andContractorGreaterThanOrEqualTo(String value) {
            addCriterion("Contractor >=", value, "contractor");
            return (Criteria) this;
        }

        public Criteria andContractorLessThan(String value) {
            addCriterion("Contractor <", value, "contractor");
            return (Criteria) this;
        }

        public Criteria andContractorLessThanOrEqualTo(String value) {
            addCriterion("Contractor <=", value, "contractor");
            return (Criteria) this;
        }

        public Criteria andContractorLike(String value) {
            addCriterion("Contractor like", value, "contractor");
            return (Criteria) this;
        }

        public Criteria andContractorNotLike(String value) {
            addCriterion("Contractor not like", value, "contractor");
            return (Criteria) this;
        }

        public Criteria andContractorIn(List<String> values) {
            addCriterion("Contractor in", values, "contractor");
            return (Criteria) this;
        }

        public Criteria andContractorNotIn(List<String> values) {
            addCriterion("Contractor not in", values, "contractor");
            return (Criteria) this;
        }

        public Criteria andContractorBetween(String value1, String value2) {
            addCriterion("Contractor between", value1, value2, "contractor");
            return (Criteria) this;
        }

        public Criteria andContractorNotBetween(String value1, String value2) {
            addCriterion("Contractor not between", value1, value2, "contractor");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNull() {
            addCriterion("Owner is null");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNotNull() {
            addCriterion("Owner is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerEqualTo(String value) {
            addCriterion("Owner =", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotEqualTo(String value) {
            addCriterion("Owner <>", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThan(String value) {
            addCriterion("Owner >", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("Owner >=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThan(String value) {
            addCriterion("Owner <", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThanOrEqualTo(String value) {
            addCriterion("Owner <=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLike(String value) {
            addCriterion("Owner like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotLike(String value) {
            addCriterion("Owner not like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerIn(List<String> values) {
            addCriterion("Owner in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotIn(List<String> values) {
            addCriterion("Owner not in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerBetween(String value1, String value2) {
            addCriterion("Owner between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotBetween(String value1, String value2) {
            addCriterion("Owner not between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andArchitectIsNull() {
            addCriterion("Architect is null");
            return (Criteria) this;
        }

        public Criteria andArchitectIsNotNull() {
            addCriterion("Architect is not null");
            return (Criteria) this;
        }

        public Criteria andArchitectEqualTo(String value) {
            addCriterion("Architect =", value, "architect");
            return (Criteria) this;
        }

        public Criteria andArchitectNotEqualTo(String value) {
            addCriterion("Architect <>", value, "architect");
            return (Criteria) this;
        }

        public Criteria andArchitectGreaterThan(String value) {
            addCriterion("Architect >", value, "architect");
            return (Criteria) this;
        }

        public Criteria andArchitectGreaterThanOrEqualTo(String value) {
            addCriterion("Architect >=", value, "architect");
            return (Criteria) this;
        }

        public Criteria andArchitectLessThan(String value) {
            addCriterion("Architect <", value, "architect");
            return (Criteria) this;
        }

        public Criteria andArchitectLessThanOrEqualTo(String value) {
            addCriterion("Architect <=", value, "architect");
            return (Criteria) this;
        }

        public Criteria andArchitectLike(String value) {
            addCriterion("Architect like", value, "architect");
            return (Criteria) this;
        }

        public Criteria andArchitectNotLike(String value) {
            addCriterion("Architect not like", value, "architect");
            return (Criteria) this;
        }

        public Criteria andArchitectIn(List<String> values) {
            addCriterion("Architect in", values, "architect");
            return (Criteria) this;
        }

        public Criteria andArchitectNotIn(List<String> values) {
            addCriterion("Architect not in", values, "architect");
            return (Criteria) this;
        }

        public Criteria andArchitectBetween(String value1, String value2) {
            addCriterion("Architect between", value1, value2, "architect");
            return (Criteria) this;
        }

        public Criteria andArchitectNotBetween(String value1, String value2) {
            addCriterion("Architect not between", value1, value2, "architect");
            return (Criteria) this;
        }

        public Criteria andSupervisorIsNull() {
            addCriterion("Supervisor is null");
            return (Criteria) this;
        }

        public Criteria andSupervisorIsNotNull() {
            addCriterion("Supervisor is not null");
            return (Criteria) this;
        }

        public Criteria andSupervisorEqualTo(String value) {
            addCriterion("Supervisor =", value, "supervisor");
            return (Criteria) this;
        }

        public Criteria andSupervisorNotEqualTo(String value) {
            addCriterion("Supervisor <>", value, "supervisor");
            return (Criteria) this;
        }

        public Criteria andSupervisorGreaterThan(String value) {
            addCriterion("Supervisor >", value, "supervisor");
            return (Criteria) this;
        }

        public Criteria andSupervisorGreaterThanOrEqualTo(String value) {
            addCriterion("Supervisor >=", value, "supervisor");
            return (Criteria) this;
        }

        public Criteria andSupervisorLessThan(String value) {
            addCriterion("Supervisor <", value, "supervisor");
            return (Criteria) this;
        }

        public Criteria andSupervisorLessThanOrEqualTo(String value) {
            addCriterion("Supervisor <=", value, "supervisor");
            return (Criteria) this;
        }

        public Criteria andSupervisorLike(String value) {
            addCriterion("Supervisor like", value, "supervisor");
            return (Criteria) this;
        }

        public Criteria andSupervisorNotLike(String value) {
            addCriterion("Supervisor not like", value, "supervisor");
            return (Criteria) this;
        }

        public Criteria andSupervisorIn(List<String> values) {
            addCriterion("Supervisor in", values, "supervisor");
            return (Criteria) this;
        }

        public Criteria andSupervisorNotIn(List<String> values) {
            addCriterion("Supervisor not in", values, "supervisor");
            return (Criteria) this;
        }

        public Criteria andSupervisorBetween(String value1, String value2) {
            addCriterion("Supervisor between", value1, value2, "supervisor");
            return (Criteria) this;
        }

        public Criteria andSupervisorNotBetween(String value1, String value2) {
            addCriterion("Supervisor not between", value1, value2, "supervisor");
            return (Criteria) this;
        }

        public Criteria andManagerzhtaccountIsNull() {
            addCriterion("ManagerZhtAccount is null");
            return (Criteria) this;
        }

        public Criteria andManagerzhtaccountIsNotNull() {
            addCriterion("ManagerZhtAccount is not null");
            return (Criteria) this;
        }

        public Criteria andManagerzhtaccountEqualTo(String value) {
            addCriterion("ManagerZhtAccount =", value, "managerzhtaccount");
            return (Criteria) this;
        }

        public Criteria andManagerzhtaccountNotEqualTo(String value) {
            addCriterion("ManagerZhtAccount <>", value, "managerzhtaccount");
            return (Criteria) this;
        }

        public Criteria andManagerzhtaccountGreaterThan(String value) {
            addCriterion("ManagerZhtAccount >", value, "managerzhtaccount");
            return (Criteria) this;
        }

        public Criteria andManagerzhtaccountGreaterThanOrEqualTo(String value) {
            addCriterion("ManagerZhtAccount >=", value, "managerzhtaccount");
            return (Criteria) this;
        }

        public Criteria andManagerzhtaccountLessThan(String value) {
            addCriterion("ManagerZhtAccount <", value, "managerzhtaccount");
            return (Criteria) this;
        }

        public Criteria andManagerzhtaccountLessThanOrEqualTo(String value) {
            addCriterion("ManagerZhtAccount <=", value, "managerzhtaccount");
            return (Criteria) this;
        }

        public Criteria andManagerzhtaccountLike(String value) {
            addCriterion("ManagerZhtAccount like", value, "managerzhtaccount");
            return (Criteria) this;
        }

        public Criteria andManagerzhtaccountNotLike(String value) {
            addCriterion("ManagerZhtAccount not like", value, "managerzhtaccount");
            return (Criteria) this;
        }

        public Criteria andManagerzhtaccountIn(List<String> values) {
            addCriterion("ManagerZhtAccount in", values, "managerzhtaccount");
            return (Criteria) this;
        }

        public Criteria andManagerzhtaccountNotIn(List<String> values) {
            addCriterion("ManagerZhtAccount not in", values, "managerzhtaccount");
            return (Criteria) this;
        }

        public Criteria andManagerzhtaccountBetween(String value1, String value2) {
            addCriterion("ManagerZhtAccount between", value1, value2, "managerzhtaccount");
            return (Criteria) this;
        }

        public Criteria andManagerzhtaccountNotBetween(String value1, String value2) {
            addCriterion("ManagerZhtAccount not between", value1, value2, "managerzhtaccount");
            return (Criteria) this;
        }

        public Criteria andLongitudeIsNull() {
            addCriterion("Longitude is null");
            return (Criteria) this;
        }

        public Criteria andLongitudeIsNotNull() {
            addCriterion("Longitude is not null");
            return (Criteria) this;
        }

        public Criteria andLongitudeEqualTo(String value) {
            addCriterion("Longitude =", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeNotEqualTo(String value) {
            addCriterion("Longitude <>", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeGreaterThan(String value) {
            addCriterion("Longitude >", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeGreaterThanOrEqualTo(String value) {
            addCriterion("Longitude >=", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeLessThan(String value) {
            addCriterion("Longitude <", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeLessThanOrEqualTo(String value) {
            addCriterion("Longitude <=", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeLike(String value) {
            addCriterion("Longitude like", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeNotLike(String value) {
            addCriterion("Longitude not like", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeIn(List<String> values) {
            addCriterion("Longitude in", values, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeNotIn(List<String> values) {
            addCriterion("Longitude not in", values, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeBetween(String value1, String value2) {
            addCriterion("Longitude between", value1, value2, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeNotBetween(String value1, String value2) {
            addCriterion("Longitude not between", value1, value2, "longitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeIsNull() {
            addCriterion("Latitude is null");
            return (Criteria) this;
        }

        public Criteria andLatitudeIsNotNull() {
            addCriterion("Latitude is not null");
            return (Criteria) this;
        }

        public Criteria andLatitudeEqualTo(String value) {
            addCriterion("Latitude =", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeNotEqualTo(String value) {
            addCriterion("Latitude <>", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeGreaterThan(String value) {
            addCriterion("Latitude >", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeGreaterThanOrEqualTo(String value) {
            addCriterion("Latitude >=", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeLessThan(String value) {
            addCriterion("Latitude <", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeLessThanOrEqualTo(String value) {
            addCriterion("Latitude <=", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeLike(String value) {
            addCriterion("Latitude like", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeNotLike(String value) {
            addCriterion("Latitude not like", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeIn(List<String> values) {
            addCriterion("Latitude in", values, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeNotIn(List<String> values) {
            addCriterion("Latitude not in", values, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeBetween(String value1, String value2) {
            addCriterion("Latitude between", value1, value2, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeNotBetween(String value1, String value2) {
            addCriterion("Latitude not between", value1, value2, "latitude");
            return (Criteria) this;
        }

        public Criteria andRegionidIsNull() {
            addCriterion("RegionId is null");
            return (Criteria) this;
        }

        public Criteria andRegionidIsNotNull() {
            addCriterion("RegionId is not null");
            return (Criteria) this;
        }

        public Criteria andRegionidEqualTo(String value) {
            addCriterion("RegionId =", value, "regionid");
            return (Criteria) this;
        }

        public Criteria andRegionidNotEqualTo(String value) {
            addCriterion("RegionId <>", value, "regionid");
            return (Criteria) this;
        }

        public Criteria andRegionidGreaterThan(String value) {
            addCriterion("RegionId >", value, "regionid");
            return (Criteria) this;
        }

        public Criteria andRegionidGreaterThanOrEqualTo(String value) {
            addCriterion("RegionId >=", value, "regionid");
            return (Criteria) this;
        }

        public Criteria andRegionidLessThan(String value) {
            addCriterion("RegionId <", value, "regionid");
            return (Criteria) this;
        }

        public Criteria andRegionidLessThanOrEqualTo(String value) {
            addCriterion("RegionId <=", value, "regionid");
            return (Criteria) this;
        }

        public Criteria andRegionidLike(String value) {
            addCriterion("RegionId like", value, "regionid");
            return (Criteria) this;
        }

        public Criteria andRegionidNotLike(String value) {
            addCriterion("RegionId not like", value, "regionid");
            return (Criteria) this;
        }

        public Criteria andRegionidIn(List<String> values) {
            addCriterion("RegionId in", values, "regionid");
            return (Criteria) this;
        }

        public Criteria andRegionidNotIn(List<String> values) {
            addCriterion("RegionId not in", values, "regionid");
            return (Criteria) this;
        }

        public Criteria andRegionidBetween(String value1, String value2) {
            addCriterion("RegionId between", value1, value2, "regionid");
            return (Criteria) this;
        }

        public Criteria andRegionidNotBetween(String value1, String value2) {
            addCriterion("RegionId not between", value1, value2, "regionid");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIsNull() {
            addCriterion("CreatedBy is null");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIsNotNull() {
            addCriterion("CreatedBy is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedbyEqualTo(String value) {
            addCriterion("CreatedBy =", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotEqualTo(String value) {
            addCriterion("CreatedBy <>", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyGreaterThan(String value) {
            addCriterion("CreatedBy >", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
            addCriterion("CreatedBy >=", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLessThan(String value) {
            addCriterion("CreatedBy <", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLessThanOrEqualTo(String value) {
            addCriterion("CreatedBy <=", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLike(String value) {
            addCriterion("CreatedBy like", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotLike(String value) {
            addCriterion("CreatedBy not like", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIn(List<String> values) {
            addCriterion("CreatedBy in", values, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotIn(List<String> values) {
            addCriterion("CreatedBy not in", values, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyBetween(String value1, String value2) {
            addCriterion("CreatedBy between", value1, value2, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotBetween(String value1, String value2) {
            addCriterion("CreatedBy not between", value1, value2, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeIsNull() {
            addCriterion("CreatedTime is null");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeIsNotNull() {
            addCriterion("CreatedTime is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeEqualTo(LocalDateTime value) {
            addCriterion("CreatedTime =", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeNotEqualTo(LocalDateTime value) {
            addCriterion("CreatedTime <>", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeGreaterThan(LocalDateTime value) {
            addCriterion("CreatedTime >", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("CreatedTime >=", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeLessThan(LocalDateTime value) {
            addCriterion("CreatedTime <", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("CreatedTime <=", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeIn(List<LocalDateTime> values) {
            addCriterion("CreatedTime in", values, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeNotIn(List<LocalDateTime> values) {
            addCriterion("CreatedTime not in", values, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("CreatedTime between", value1, value2, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("CreatedTime not between", value1, value2, "createdtime");
            return (Criteria) this;
        }

        public Criteria andDeletedbyIsNull() {
            addCriterion("DeletedBy is null");
            return (Criteria) this;
        }

        public Criteria andDeletedbyIsNotNull() {
            addCriterion("DeletedBy is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedbyEqualTo(String value) {
            addCriterion("DeletedBy =", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyNotEqualTo(String value) {
            addCriterion("DeletedBy <>", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyGreaterThan(String value) {
            addCriterion("DeletedBy >", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyGreaterThanOrEqualTo(String value) {
            addCriterion("DeletedBy >=", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyLessThan(String value) {
            addCriterion("DeletedBy <", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyLessThanOrEqualTo(String value) {
            addCriterion("DeletedBy <=", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyLike(String value) {
            addCriterion("DeletedBy like", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyNotLike(String value) {
            addCriterion("DeletedBy not like", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyIn(List<String> values) {
            addCriterion("DeletedBy in", values, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyNotIn(List<String> values) {
            addCriterion("DeletedBy not in", values, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyBetween(String value1, String value2) {
            addCriterion("DeletedBy between", value1, value2, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyNotBetween(String value1, String value2) {
            addCriterion("DeletedBy not between", value1, value2, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeIsNull() {
            addCriterion("DeletedTime is null");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeIsNotNull() {
            addCriterion("DeletedTime is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeEqualTo(LocalDateTime value) {
            addCriterion("DeletedTime =", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeNotEqualTo(LocalDateTime value) {
            addCriterion("DeletedTime <>", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeGreaterThan(LocalDateTime value) {
            addCriterion("DeletedTime >", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("DeletedTime >=", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeLessThan(LocalDateTime value) {
            addCriterion("DeletedTime <", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("DeletedTime <=", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeIn(List<LocalDateTime> values) {
            addCriterion("DeletedTime in", values, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeNotIn(List<LocalDateTime> values) {
            addCriterion("DeletedTime not in", values, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("DeletedTime between", value1, value2, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("DeletedTime not between", value1, value2, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andIsdeletedIsNull() {
            addCriterion("IsDeleted is null");
            return (Criteria) this;
        }

        public Criteria andIsdeletedIsNotNull() {
            addCriterion("IsDeleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsdeletedEqualTo(Boolean value) {
            addCriterion("IsDeleted =", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedNotEqualTo(Boolean value) {
            addCriterion("IsDeleted <>", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedGreaterThan(Boolean value) {
            addCriterion("IsDeleted >", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("IsDeleted >=", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedLessThan(Boolean value) {
            addCriterion("IsDeleted <", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("IsDeleted <=", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedIn(List<Boolean> values) {
            addCriterion("IsDeleted in", values, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedNotIn(List<Boolean> values) {
            addCriterion("IsDeleted not in", values, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("IsDeleted between", value1, value2, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("IsDeleted not between", value1, value2, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNull() {
            addCriterion("Province is null");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNotNull() {
            addCriterion("Province is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceEqualTo(String value) {
            addCriterion("Province =", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotEqualTo(String value) {
            addCriterion("Province <>", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThan(String value) {
            addCriterion("Province >", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("Province >=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThan(String value) {
            addCriterion("Province <", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThanOrEqualTo(String value) {
            addCriterion("Province <=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLike(String value) {
            addCriterion("Province like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotLike(String value) {
            addCriterion("Province not like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceIn(List<String> values) {
            addCriterion("Province in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotIn(List<String> values) {
            addCriterion("Province not in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceBetween(String value1, String value2) {
            addCriterion("Province between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotBetween(String value1, String value2) {
            addCriterion("Province not between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("City is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("City is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("City =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("City <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("City >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("City >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("City <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("City <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("City like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("City not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("City in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("City not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("City between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("City not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andDistrictIsNull() {
            addCriterion("District is null");
            return (Criteria) this;
        }

        public Criteria andDistrictIsNotNull() {
            addCriterion("District is not null");
            return (Criteria) this;
        }

        public Criteria andDistrictEqualTo(String value) {
            addCriterion("District =", value, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictNotEqualTo(String value) {
            addCriterion("District <>", value, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictGreaterThan(String value) {
            addCriterion("District >", value, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictGreaterThanOrEqualTo(String value) {
            addCriterion("District >=", value, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictLessThan(String value) {
            addCriterion("District <", value, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictLessThanOrEqualTo(String value) {
            addCriterion("District <=", value, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictLike(String value) {
            addCriterion("District like", value, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictNotLike(String value) {
            addCriterion("District not like", value, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictIn(List<String> values) {
            addCriterion("District in", values, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictNotIn(List<String> values) {
            addCriterion("District not in", values, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictBetween(String value1, String value2) {
            addCriterion("District between", value1, value2, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictNotBetween(String value1, String value2) {
            addCriterion("District not between", value1, value2, "district");
            return (Criteria) this;
        }

        public Criteria andMajorprojectnumIsNull() {
            addCriterion("MajorProjectNum is null");
            return (Criteria) this;
        }

        public Criteria andMajorprojectnumIsNotNull() {
            addCriterion("MajorProjectNum is not null");
            return (Criteria) this;
        }

        public Criteria andMajorprojectnumEqualTo(Integer value) {
            addCriterion("MajorProjectNum =", value, "majorprojectnum");
            return (Criteria) this;
        }

        public Criteria andMajorprojectnumNotEqualTo(Integer value) {
            addCriterion("MajorProjectNum <>", value, "majorprojectnum");
            return (Criteria) this;
        }

        public Criteria andMajorprojectnumGreaterThan(Integer value) {
            addCriterion("MajorProjectNum >", value, "majorprojectnum");
            return (Criteria) this;
        }

        public Criteria andMajorprojectnumGreaterThanOrEqualTo(Integer value) {
            addCriterion("MajorProjectNum >=", value, "majorprojectnum");
            return (Criteria) this;
        }

        public Criteria andMajorprojectnumLessThan(Integer value) {
            addCriterion("MajorProjectNum <", value, "majorprojectnum");
            return (Criteria) this;
        }

        public Criteria andMajorprojectnumLessThanOrEqualTo(Integer value) {
            addCriterion("MajorProjectNum <=", value, "majorprojectnum");
            return (Criteria) this;
        }

        public Criteria andMajorprojectnumIn(List<Integer> values) {
            addCriterion("MajorProjectNum in", values, "majorprojectnum");
            return (Criteria) this;
        }

        public Criteria andMajorprojectnumNotIn(List<Integer> values) {
            addCriterion("MajorProjectNum not in", values, "majorprojectnum");
            return (Criteria) this;
        }

        public Criteria andMajorprojectnumBetween(Integer value1, Integer value2) {
            addCriterion("MajorProjectNum between", value1, value2, "majorprojectnum");
            return (Criteria) this;
        }

        public Criteria andMajorprojectnumNotBetween(Integer value1, Integer value2) {
            addCriterion("MajorProjectNum not between", value1, value2, "majorprojectnum");
            return (Criteria) this;
        }

        public Criteria andSiteidIsNull() {
            addCriterion("SiteId is null");
            return (Criteria) this;
        }

        public Criteria andSiteidIsNotNull() {
            addCriterion("SiteId is not null");
            return (Criteria) this;
        }

        public Criteria andSiteidEqualTo(String value) {
            addCriterion("SiteId =", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidNotEqualTo(String value) {
            addCriterion("SiteId <>", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidGreaterThan(String value) {
            addCriterion("SiteId >", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidGreaterThanOrEqualTo(String value) {
            addCriterion("SiteId >=", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidLessThan(String value) {
            addCriterion("SiteId <", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidLessThanOrEqualTo(String value) {
            addCriterion("SiteId <=", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidLike(String value) {
            addCriterion("SiteId like", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidNotLike(String value) {
            addCriterion("SiteId not like", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidIn(List<String> values) {
            addCriterion("SiteId in", values, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidNotIn(List<String> values) {
            addCriterion("SiteId not in", values, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidBetween(String value1, String value2) {
            addCriterion("SiteId between", value1, value2, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidNotBetween(String value1, String value2) {
            addCriterion("SiteId not between", value1, value2, "siteid");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated do_not_delete_during_merge Thu Feb 06 09:02:18 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_ProjectInfo
     *
     * @mbg.generated Thu Feb 06 09:02:18 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}