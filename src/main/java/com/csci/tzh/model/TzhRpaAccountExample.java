package com.csci.tzh.model;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class TzhRpaAccountExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public TzhRpaAccountExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("Id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("Id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("Id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("Id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("Id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("Id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("Id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("Id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("Id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("Id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("Id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("Id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("Id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("Id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andProjectnameIsNull() {
			addCriterion("ProjectName is null");
			return (Criteria) this;
		}

		public Criteria andProjectnameIsNotNull() {
			addCriterion("ProjectName is not null");
			return (Criteria) this;
		}

		public Criteria andProjectnameEqualTo(String value) {
			addCriterion("ProjectName =", value, "projectname");
			return (Criteria) this;
		}

		public Criteria andProjectnameNotEqualTo(String value) {
			addCriterion("ProjectName <>", value, "projectname");
			return (Criteria) this;
		}

		public Criteria andProjectnameGreaterThan(String value) {
			addCriterion("ProjectName >", value, "projectname");
			return (Criteria) this;
		}

		public Criteria andProjectnameGreaterThanOrEqualTo(String value) {
			addCriterion("ProjectName >=", value, "projectname");
			return (Criteria) this;
		}

		public Criteria andProjectnameLessThan(String value) {
			addCriterion("ProjectName <", value, "projectname");
			return (Criteria) this;
		}

		public Criteria andProjectnameLessThanOrEqualTo(String value) {
			addCriterion("ProjectName <=", value, "projectname");
			return (Criteria) this;
		}

		public Criteria andProjectnameLike(String value) {
			addCriterion("ProjectName like", value, "projectname");
			return (Criteria) this;
		}

		public Criteria andProjectnameNotLike(String value) {
			addCriterion("ProjectName not like", value, "projectname");
			return (Criteria) this;
		}

		public Criteria andProjectnameIn(List<String> values) {
			addCriterion("ProjectName in", values, "projectname");
			return (Criteria) this;
		}

		public Criteria andProjectnameNotIn(List<String> values) {
			addCriterion("ProjectName not in", values, "projectname");
			return (Criteria) this;
		}

		public Criteria andProjectnameBetween(String value1, String value2) {
			addCriterion("ProjectName between", value1, value2, "projectname");
			return (Criteria) this;
		}

		public Criteria andProjectnameNotBetween(String value1, String value2) {
			addCriterion("ProjectName not between", value1, value2, "projectname");
			return (Criteria) this;
		}

		public Criteria andProtocolidIsNull() {
			addCriterion("ProtocolId is null");
			return (Criteria) this;
		}

		public Criteria andProtocolidIsNotNull() {
			addCriterion("ProtocolId is not null");
			return (Criteria) this;
		}

		public Criteria andProtocolidEqualTo(String value) {
			addCriterion("ProtocolId =", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotEqualTo(String value) {
			addCriterion("ProtocolId <>", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidGreaterThan(String value) {
			addCriterion("ProtocolId >", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidGreaterThanOrEqualTo(String value) {
			addCriterion("ProtocolId >=", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidLessThan(String value) {
			addCriterion("ProtocolId <", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidLessThanOrEqualTo(String value) {
			addCriterion("ProtocolId <=", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidLike(String value) {
			addCriterion("ProtocolId like", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotLike(String value) {
			addCriterion("ProtocolId not like", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidIn(List<String> values) {
			addCriterion("ProtocolId in", values, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotIn(List<String> values) {
			addCriterion("ProtocolId not in", values, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidBetween(String value1, String value2) {
			addCriterion("ProtocolId between", value1, value2, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotBetween(String value1, String value2) {
			addCriterion("ProtocolId not between", value1, value2, "protocolid");
			return (Criteria) this;
		}

		public Criteria andAccounttypeIsNull() {
			addCriterion("AccountType is null");
			return (Criteria) this;
		}

		public Criteria andAccounttypeIsNotNull() {
			addCriterion("AccountType is not null");
			return (Criteria) this;
		}

		public Criteria andAccounttypeEqualTo(String value) {
			addCriterion("AccountType =", value, "accounttype");
			return (Criteria) this;
		}

		public Criteria andAccounttypeNotEqualTo(String value) {
			addCriterion("AccountType <>", value, "accounttype");
			return (Criteria) this;
		}

		public Criteria andAccounttypeGreaterThan(String value) {
			addCriterion("AccountType >", value, "accounttype");
			return (Criteria) this;
		}

		public Criteria andAccounttypeGreaterThanOrEqualTo(String value) {
			addCriterion("AccountType >=", value, "accounttype");
			return (Criteria) this;
		}

		public Criteria andAccounttypeLessThan(String value) {
			addCriterion("AccountType <", value, "accounttype");
			return (Criteria) this;
		}

		public Criteria andAccounttypeLessThanOrEqualTo(String value) {
			addCriterion("AccountType <=", value, "accounttype");
			return (Criteria) this;
		}

		public Criteria andAccounttypeLike(String value) {
			addCriterion("AccountType like", value, "accounttype");
			return (Criteria) this;
		}

		public Criteria andAccounttypeNotLike(String value) {
			addCriterion("AccountType not like", value, "accounttype");
			return (Criteria) this;
		}

		public Criteria andAccounttypeIn(List<String> values) {
			addCriterion("AccountType in", values, "accounttype");
			return (Criteria) this;
		}

		public Criteria andAccounttypeNotIn(List<String> values) {
			addCriterion("AccountType not in", values, "accounttype");
			return (Criteria) this;
		}

		public Criteria andAccounttypeBetween(String value1, String value2) {
			addCriterion("AccountType between", value1, value2, "accounttype");
			return (Criteria) this;
		}

		public Criteria andAccounttypeNotBetween(String value1, String value2) {
			addCriterion("AccountType not between", value1, value2, "accounttype");
			return (Criteria) this;
		}

		public Criteria andAccountnoIsNull() {
			addCriterion("AccountNo is null");
			return (Criteria) this;
		}

		public Criteria andAccountnoIsNotNull() {
			addCriterion("AccountNo is not null");
			return (Criteria) this;
		}

		public Criteria andAccountnoEqualTo(String value) {
			addCriterion("AccountNo =", value, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoNotEqualTo(String value) {
			addCriterion("AccountNo <>", value, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoGreaterThan(String value) {
			addCriterion("AccountNo >", value, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoGreaterThanOrEqualTo(String value) {
			addCriterion("AccountNo >=", value, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoLessThan(String value) {
			addCriterion("AccountNo <", value, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoLessThanOrEqualTo(String value) {
			addCriterion("AccountNo <=", value, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoLike(String value) {
			addCriterion("AccountNo like", value, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoNotLike(String value) {
			addCriterion("AccountNo not like", value, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoIn(List<String> values) {
			addCriterion("AccountNo in", values, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoNotIn(List<String> values) {
			addCriterion("AccountNo not in", values, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoBetween(String value1, String value2) {
			addCriterion("AccountNo between", value1, value2, "accountno");
			return (Criteria) this;
		}

		public Criteria andAccountnoNotBetween(String value1, String value2) {
			addCriterion("AccountNo not between", value1, value2, "accountno");
			return (Criteria) this;
		}

		public Criteria andUsernameIsNull() {
			addCriterion("Username is null");
			return (Criteria) this;
		}

		public Criteria andUsernameIsNotNull() {
			addCriterion("Username is not null");
			return (Criteria) this;
		}

		public Criteria andUsernameEqualTo(String value) {
			addCriterion("Username =", value, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameNotEqualTo(String value) {
			addCriterion("Username <>", value, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameGreaterThan(String value) {
			addCriterion("Username >", value, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameGreaterThanOrEqualTo(String value) {
			addCriterion("Username >=", value, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameLessThan(String value) {
			addCriterion("Username <", value, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameLessThanOrEqualTo(String value) {
			addCriterion("Username <=", value, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameLike(String value) {
			addCriterion("Username like", value, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameNotLike(String value) {
			addCriterion("Username not like", value, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameIn(List<String> values) {
			addCriterion("Username in", values, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameNotIn(List<String> values) {
			addCriterion("Username not in", values, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameBetween(String value1, String value2) {
			addCriterion("Username between", value1, value2, "username");
			return (Criteria) this;
		}

		public Criteria andUsernameNotBetween(String value1, String value2) {
			addCriterion("Username not between", value1, value2, "username");
			return (Criteria) this;
		}

		public Criteria andPasswordIsNull() {
			addCriterion("Password is null");
			return (Criteria) this;
		}

		public Criteria andPasswordIsNotNull() {
			addCriterion("Password is not null");
			return (Criteria) this;
		}

		public Criteria andPasswordEqualTo(String value) {
			addCriterion("Password =", value, "password");
			return (Criteria) this;
		}

		public Criteria andPasswordNotEqualTo(String value) {
			addCriterion("Password <>", value, "password");
			return (Criteria) this;
		}

		public Criteria andPasswordGreaterThan(String value) {
			addCriterion("Password >", value, "password");
			return (Criteria) this;
		}

		public Criteria andPasswordGreaterThanOrEqualTo(String value) {
			addCriterion("Password >=", value, "password");
			return (Criteria) this;
		}

		public Criteria andPasswordLessThan(String value) {
			addCriterion("Password <", value, "password");
			return (Criteria) this;
		}

		public Criteria andPasswordLessThanOrEqualTo(String value) {
			addCriterion("Password <=", value, "password");
			return (Criteria) this;
		}

		public Criteria andPasswordLike(String value) {
			addCriterion("Password like", value, "password");
			return (Criteria) this;
		}

		public Criteria andPasswordNotLike(String value) {
			addCriterion("Password not like", value, "password");
			return (Criteria) this;
		}

		public Criteria andPasswordIn(List<String> values) {
			addCriterion("Password in", values, "password");
			return (Criteria) this;
		}

		public Criteria andPasswordNotIn(List<String> values) {
			addCriterion("Password not in", values, "password");
			return (Criteria) this;
		}

		public Criteria andPasswordBetween(String value1, String value2) {
			addCriterion("Password between", value1, value2, "password");
			return (Criteria) this;
		}

		public Criteria andPasswordNotBetween(String value1, String value2) {
			addCriterion("Password not between", value1, value2, "password");
			return (Criteria) this;
		}

		public Criteria andSupplierIsNull() {
			addCriterion("Supplier is null");
			return (Criteria) this;
		}

		public Criteria andSupplierIsNotNull() {
			addCriterion("Supplier is not null");
			return (Criteria) this;
		}

		public Criteria andSupplierEqualTo(String value) {
			addCriterion("Supplier =", value, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierNotEqualTo(String value) {
			addCriterion("Supplier <>", value, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierGreaterThan(String value) {
			addCriterion("Supplier >", value, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierGreaterThanOrEqualTo(String value) {
			addCriterion("Supplier >=", value, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierLessThan(String value) {
			addCriterion("Supplier <", value, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierLessThanOrEqualTo(String value) {
			addCriterion("Supplier <=", value, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierLike(String value) {
			addCriterion("Supplier like", value, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierNotLike(String value) {
			addCriterion("Supplier not like", value, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierIn(List<String> values) {
			addCriterion("Supplier in", values, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierNotIn(List<String> values) {
			addCriterion("Supplier not in", values, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierBetween(String value1, String value2) {
			addCriterion("Supplier between", value1, value2, "supplier");
			return (Criteria) this;
		}

		public Criteria andSupplierNotBetween(String value1, String value2) {
			addCriterion("Supplier not between", value1, value2, "supplier");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidIsNull() {
			addCriterion("CarbonEmissionLocationId is null");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidIsNotNull() {
			addCriterion("CarbonEmissionLocationId is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId =", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId <>", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidGreaterThan(String value) {
			addCriterion("CarbonEmissionLocationId >", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidGreaterThanOrEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId >=", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidLessThan(String value) {
			addCriterion("CarbonEmissionLocationId <", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidLessThanOrEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId <=", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidLike(String value) {
			addCriterion("CarbonEmissionLocationId like", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotLike(String value) {
			addCriterion("CarbonEmissionLocationId not like", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidIn(List<String> values) {
			addCriterion("CarbonEmissionLocationId in", values, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotIn(List<String> values) {
			addCriterion("CarbonEmissionLocationId not in", values, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidBetween(String value1, String value2) {
			addCriterion("CarbonEmissionLocationId between", value1, value2, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotBetween(String value1, String value2) {
			addCriterion("CarbonEmissionLocationId not between", value1, value2, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andStartdateIsNull() {
			addCriterion("StartDate is null");
			return (Criteria) this;
		}

		public Criteria andStartdateIsNotNull() {
			addCriterion("StartDate is not null");
			return (Criteria) this;
		}

		public Criteria andStartdateEqualTo(LocalDate value) {
			addCriterion("StartDate =", value, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateNotEqualTo(LocalDate value) {
			addCriterion("StartDate <>", value, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateGreaterThan(LocalDate value) {
			addCriterion("StartDate >", value, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateGreaterThanOrEqualTo(LocalDate value) {
			addCriterion("StartDate >=", value, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateLessThan(LocalDate value) {
			addCriterion("StartDate <", value, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateLessThanOrEqualTo(LocalDate value) {
			addCriterion("StartDate <=", value, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateIn(List<LocalDate> values) {
			addCriterion("StartDate in", values, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateNotIn(List<LocalDate> values) {
			addCriterion("StartDate not in", values, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateBetween(LocalDate value1, LocalDate value2) {
			addCriterion("StartDate between", value1, value2, "startdate");
			return (Criteria) this;
		}

		public Criteria andStartdateNotBetween(LocalDate value1, LocalDate value2) {
			addCriterion("StartDate not between", value1, value2, "startdate");
			return (Criteria) this;
		}

		public Criteria andEnddateIsNull() {
			addCriterion("EndDate is null");
			return (Criteria) this;
		}

		public Criteria andEnddateIsNotNull() {
			addCriterion("EndDate is not null");
			return (Criteria) this;
		}

		public Criteria andEnddateEqualTo(LocalDate value) {
			addCriterion("EndDate =", value, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateNotEqualTo(LocalDate value) {
			addCriterion("EndDate <>", value, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateGreaterThan(LocalDate value) {
			addCriterion("EndDate >", value, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateGreaterThanOrEqualTo(LocalDate value) {
			addCriterion("EndDate >=", value, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateLessThan(LocalDate value) {
			addCriterion("EndDate <", value, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateLessThanOrEqualTo(LocalDate value) {
			addCriterion("EndDate <=", value, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateIn(List<LocalDate> values) {
			addCriterion("EndDate in", values, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateNotIn(List<LocalDate> values) {
			addCriterion("EndDate not in", values, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateBetween(LocalDate value1, LocalDate value2) {
			addCriterion("EndDate between", value1, value2, "enddate");
			return (Criteria) this;
		}

		public Criteria andEnddateNotBetween(LocalDate value1, LocalDate value2) {
			addCriterion("EndDate not between", value1, value2, "enddate");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNull() {
			addCriterion("CreatedBy is null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNotNull() {
			addCriterion("CreatedBy is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyEqualTo(String value) {
			addCriterion("CreatedBy =", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotEqualTo(String value) {
			addCriterion("CreatedBy <>", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThan(String value) {
			addCriterion("CreatedBy >", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
			addCriterion("CreatedBy >=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThan(String value) {
			addCriterion("CreatedBy <", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThanOrEqualTo(String value) {
			addCriterion("CreatedBy <=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLike(String value) {
			addCriterion("CreatedBy like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotLike(String value) {
			addCriterion("CreatedBy not like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIn(List<String> values) {
			addCriterion("CreatedBy in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotIn(List<String> values) {
			addCriterion("CreatedBy not in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyBetween(String value1, String value2) {
			addCriterion("CreatedBy between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotBetween(String value1, String value2) {
			addCriterion("CreatedBy not between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNull() {
			addCriterion("CreatedTime is null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNotNull() {
			addCriterion("CreatedTime is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime =", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <>", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThan(LocalDateTime value) {
			addCriterion("CreatedTime >", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime >=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThan(LocalDateTime value) {
			addCriterion("CreatedTime <", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime not in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime not between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNull() {
			addCriterion("DeletedBy is null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNotNull() {
			addCriterion("DeletedBy is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyEqualTo(String value) {
			addCriterion("DeletedBy =", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotEqualTo(String value) {
			addCriterion("DeletedBy <>", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThan(String value) {
			addCriterion("DeletedBy >", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThanOrEqualTo(String value) {
			addCriterion("DeletedBy >=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThan(String value) {
			addCriterion("DeletedBy <", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThanOrEqualTo(String value) {
			addCriterion("DeletedBy <=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLike(String value) {
			addCriterion("DeletedBy like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotLike(String value) {
			addCriterion("DeletedBy not like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIn(List<String> values) {
			addCriterion("DeletedBy in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotIn(List<String> values) {
			addCriterion("DeletedBy not in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyBetween(String value1, String value2) {
			addCriterion("DeletedBy between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotBetween(String value1, String value2) {
			addCriterion("DeletedBy not between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNull() {
			addCriterion("DeletedTime is null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNotNull() {
			addCriterion("DeletedTime is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime =", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <>", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThan(LocalDateTime value) {
			addCriterion("DeletedTime >", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime >=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThan(LocalDateTime value) {
			addCriterion("DeletedTime <", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime not in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime not between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNull() {
			addCriterion("IsDeleted is null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNotNull() {
			addCriterion("IsDeleted is not null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedEqualTo(Boolean value) {
			addCriterion("IsDeleted =", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotEqualTo(Boolean value) {
			addCriterion("IsDeleted <>", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThan(Boolean value) {
			addCriterion("IsDeleted >", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted >=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThan(Boolean value) {
			addCriterion("IsDeleted <", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted <=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIn(List<Boolean> values) {
			addCriterion("IsDeleted in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotIn(List<Boolean> values) {
			addCriterion("IsDeleted not in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted between", value1, value2, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted not between", value1, value2, "isdeleted");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_RpaAccount
     *
     * @mbg.generated do_not_delete_during_merge Mon Nov 28 16:44:29 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}