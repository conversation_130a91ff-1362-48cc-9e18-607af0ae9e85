package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class TzhEstimateConstructionWaste {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EstimateConstructionWaste.Id
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EstimateConstructionWaste.SiteName
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	private String sitename;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EstimateConstructionWaste.ProtocolId
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	private String protocolid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EstimateConstructionWaste.SubCategoryId
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	private String subcategoryid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EstimateConstructionWaste.DisposalMethodId
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	private String disposalmethodid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EstimateConstructionWaste.Qty
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	private BigDecimal qty;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EstimateConstructionWaste.QtyUnit
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	private String qtyunit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EstimateConstructionWaste.TurnoverCount
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	private Integer turnovercount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EstimateConstructionWaste.TargetAttritionRate
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	private BigDecimal targetattritionrate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EstimateConstructionWaste.WasteQty
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	private BigDecimal wasteqty;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EstimateConstructionWaste.WasteQtyUnit
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	private String wasteqtyunit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EstimateConstructionWaste.CreatedBy
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	private String createdby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EstimateConstructionWaste.CreatedTime
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	private LocalDateTime createdtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EstimateConstructionWaste.DeletedBy
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	private String deletedby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EstimateConstructionWaste.DeletedTime
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	private LocalDateTime deletedtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EstimateConstructionWaste.IsDeleted
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	private Boolean isdeleted;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EstimateConstructionWaste.Id
	 * @return  the value of Tzh_EstimateConstructionWaste.Id
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EstimateConstructionWaste.Id
	 * @param id  the value for Tzh_EstimateConstructionWaste.Id
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EstimateConstructionWaste.SiteName
	 * @return  the value of Tzh_EstimateConstructionWaste.SiteName
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public String getSitename() {
		return sitename;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EstimateConstructionWaste.SiteName
	 * @param sitename  the value for Tzh_EstimateConstructionWaste.SiteName
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setSitename(String sitename) {
		this.sitename = sitename == null ? null : sitename.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EstimateConstructionWaste.ProtocolId
	 * @return  the value of Tzh_EstimateConstructionWaste.ProtocolId
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public String getProtocolid() {
		return protocolid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EstimateConstructionWaste.ProtocolId
	 * @param protocolid  the value for Tzh_EstimateConstructionWaste.ProtocolId
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setProtocolid(String protocolid) {
		this.protocolid = protocolid == null ? null : protocolid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EstimateConstructionWaste.SubCategoryId
	 * @return  the value of Tzh_EstimateConstructionWaste.SubCategoryId
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public String getSubcategoryid() {
		return subcategoryid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EstimateConstructionWaste.SubCategoryId
	 * @param subcategoryid  the value for Tzh_EstimateConstructionWaste.SubCategoryId
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setSubcategoryid(String subcategoryid) {
		this.subcategoryid = subcategoryid == null ? null : subcategoryid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EstimateConstructionWaste.DisposalMethodId
	 * @return  the value of Tzh_EstimateConstructionWaste.DisposalMethodId
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public String getDisposalmethodid() {
		return disposalmethodid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EstimateConstructionWaste.DisposalMethodId
	 * @param disposalmethodid  the value for Tzh_EstimateConstructionWaste.DisposalMethodId
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setDisposalmethodid(String disposalmethodid) {
		this.disposalmethodid = disposalmethodid == null ? null : disposalmethodid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EstimateConstructionWaste.Qty
	 * @return  the value of Tzh_EstimateConstructionWaste.Qty
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public BigDecimal getQty() {
		return qty;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EstimateConstructionWaste.Qty
	 * @param qty  the value for Tzh_EstimateConstructionWaste.Qty
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setQty(BigDecimal qty) {
		this.qty = qty;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EstimateConstructionWaste.QtyUnit
	 * @return  the value of Tzh_EstimateConstructionWaste.QtyUnit
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public String getQtyunit() {
		return qtyunit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EstimateConstructionWaste.QtyUnit
	 * @param qtyunit  the value for Tzh_EstimateConstructionWaste.QtyUnit
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setQtyunit(String qtyunit) {
		this.qtyunit = qtyunit == null ? null : qtyunit.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EstimateConstructionWaste.TurnoverCount
	 * @return  the value of Tzh_EstimateConstructionWaste.TurnoverCount
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public Integer getTurnovercount() {
		return turnovercount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EstimateConstructionWaste.TurnoverCount
	 * @param turnovercount  the value for Tzh_EstimateConstructionWaste.TurnoverCount
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setTurnovercount(Integer turnovercount) {
		this.turnovercount = turnovercount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EstimateConstructionWaste.TargetAttritionRate
	 * @return  the value of Tzh_EstimateConstructionWaste.TargetAttritionRate
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public BigDecimal getTargetattritionrate() {
		return targetattritionrate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EstimateConstructionWaste.TargetAttritionRate
	 * @param targetattritionrate  the value for Tzh_EstimateConstructionWaste.TargetAttritionRate
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setTargetattritionrate(BigDecimal targetattritionrate) {
		this.targetattritionrate = targetattritionrate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EstimateConstructionWaste.WasteQty
	 * @return  the value of Tzh_EstimateConstructionWaste.WasteQty
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public BigDecimal getWasteqty() {
		return wasteqty;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EstimateConstructionWaste.WasteQty
	 * @param wasteqty  the value for Tzh_EstimateConstructionWaste.WasteQty
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setWasteqty(BigDecimal wasteqty) {
		this.wasteqty = wasteqty;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EstimateConstructionWaste.WasteQtyUnit
	 * @return  the value of Tzh_EstimateConstructionWaste.WasteQtyUnit
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public String getWasteqtyunit() {
		return wasteqtyunit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EstimateConstructionWaste.WasteQtyUnit
	 * @param wasteqtyunit  the value for Tzh_EstimateConstructionWaste.WasteQtyUnit
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setWasteqtyunit(String wasteqtyunit) {
		this.wasteqtyunit = wasteqtyunit == null ? null : wasteqtyunit.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EstimateConstructionWaste.CreatedBy
	 * @return  the value of Tzh_EstimateConstructionWaste.CreatedBy
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public String getCreatedby() {
		return createdby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EstimateConstructionWaste.CreatedBy
	 * @param createdby  the value for Tzh_EstimateConstructionWaste.CreatedBy
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setCreatedby(String createdby) {
		this.createdby = createdby == null ? null : createdby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EstimateConstructionWaste.CreatedTime
	 * @return  the value of Tzh_EstimateConstructionWaste.CreatedTime
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public LocalDateTime getCreatedtime() {
		return createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EstimateConstructionWaste.CreatedTime
	 * @param createdtime  the value for Tzh_EstimateConstructionWaste.CreatedTime
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setCreatedtime(LocalDateTime createdtime) {
		this.createdtime = createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EstimateConstructionWaste.DeletedBy
	 * @return  the value of Tzh_EstimateConstructionWaste.DeletedBy
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public String getDeletedby() {
		return deletedby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EstimateConstructionWaste.DeletedBy
	 * @param deletedby  the value for Tzh_EstimateConstructionWaste.DeletedBy
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setDeletedby(String deletedby) {
		this.deletedby = deletedby == null ? null : deletedby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EstimateConstructionWaste.DeletedTime
	 * @return  the value of Tzh_EstimateConstructionWaste.DeletedTime
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public LocalDateTime getDeletedtime() {
		return deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EstimateConstructionWaste.DeletedTime
	 * @param deletedtime  the value for Tzh_EstimateConstructionWaste.DeletedTime
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setDeletedtime(LocalDateTime deletedtime) {
		this.deletedtime = deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EstimateConstructionWaste.IsDeleted
	 * @return  the value of Tzh_EstimateConstructionWaste.IsDeleted
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public Boolean getIsdeleted() {
		return isdeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EstimateConstructionWaste.IsDeleted
	 * @param isdeleted  the value for Tzh_EstimateConstructionWaste.IsDeleted
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setIsdeleted(Boolean isdeleted) {
		this.isdeleted = isdeleted;
	}
}