package com.csci.tzh.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class TzhEmissionReductionDescriptionExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public TzhEmissionReductionDescriptionExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("Id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("Id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("Id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("Id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("Id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("Id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("Id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("Id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("Id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("Id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("Id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("Id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("Id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("Id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andSitenameIsNull() {
			addCriterion("SiteName is null");
			return (Criteria) this;
		}

		public Criteria andSitenameIsNotNull() {
			addCriterion("SiteName is not null");
			return (Criteria) this;
		}

		public Criteria andSitenameEqualTo(String value) {
			addCriterion("SiteName =", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotEqualTo(String value) {
			addCriterion("SiteName <>", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameGreaterThan(String value) {
			addCriterion("SiteName >", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameGreaterThanOrEqualTo(String value) {
			addCriterion("SiteName >=", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLessThan(String value) {
			addCriterion("SiteName <", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLessThanOrEqualTo(String value) {
			addCriterion("SiteName <=", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLike(String value) {
			addCriterion("SiteName like", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotLike(String value) {
			addCriterion("SiteName not like", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameIn(List<String> values) {
			addCriterion("SiteName in", values, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotIn(List<String> values) {
			addCriterion("SiteName not in", values, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameBetween(String value1, String value2) {
			addCriterion("SiteName between", value1, value2, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotBetween(String value1, String value2) {
			addCriterion("SiteName not between", value1, value2, "sitename");
			return (Criteria) this;
		}

		public Criteria andProtocolidIsNull() {
			addCriterion("ProtocolId is null");
			return (Criteria) this;
		}

		public Criteria andProtocolidIsNotNull() {
			addCriterion("ProtocolId is not null");
			return (Criteria) this;
		}

		public Criteria andProtocolidEqualTo(String value) {
			addCriterion("ProtocolId =", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotEqualTo(String value) {
			addCriterion("ProtocolId <>", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidGreaterThan(String value) {
			addCriterion("ProtocolId >", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidGreaterThanOrEqualTo(String value) {
			addCriterion("ProtocolId >=", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidLessThan(String value) {
			addCriterion("ProtocolId <", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidLessThanOrEqualTo(String value) {
			addCriterion("ProtocolId <=", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidLike(String value) {
			addCriterion("ProtocolId like", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotLike(String value) {
			addCriterion("ProtocolId not like", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidIn(List<String> values) {
			addCriterion("ProtocolId in", values, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotIn(List<String> values) {
			addCriterion("ProtocolId not in", values, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidBetween(String value1, String value2) {
			addCriterion("ProtocolId between", value1, value2, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotBetween(String value1, String value2) {
			addCriterion("ProtocolId not between", value1, value2, "protocolid");
			return (Criteria) this;
		}

		public Criteria andTitleIsNull() {
			addCriterion("Title is null");
			return (Criteria) this;
		}

		public Criteria andTitleIsNotNull() {
			addCriterion("Title is not null");
			return (Criteria) this;
		}

		public Criteria andTitleEqualTo(String value) {
			addCriterion("Title =", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleNotEqualTo(String value) {
			addCriterion("Title <>", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleGreaterThan(String value) {
			addCriterion("Title >", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleGreaterThanOrEqualTo(String value) {
			addCriterion("Title >=", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleLessThan(String value) {
			addCriterion("Title <", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleLessThanOrEqualTo(String value) {
			addCriterion("Title <=", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleLike(String value) {
			addCriterion("Title like", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleNotLike(String value) {
			addCriterion("Title not like", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleIn(List<String> values) {
			addCriterion("Title in", values, "title");
			return (Criteria) this;
		}

		public Criteria andTitleNotIn(List<String> values) {
			addCriterion("Title not in", values, "title");
			return (Criteria) this;
		}

		public Criteria andTitleBetween(String value1, String value2) {
			addCriterion("Title between", value1, value2, "title");
			return (Criteria) this;
		}

		public Criteria andTitleNotBetween(String value1, String value2) {
			addCriterion("Title not between", value1, value2, "title");
			return (Criteria) this;
		}

		public Criteria andTitlescIsNull() {
			addCriterion("TitleSC is null");
			return (Criteria) this;
		}

		public Criteria andTitlescIsNotNull() {
			addCriterion("TitleSC is not null");
			return (Criteria) this;
		}

		public Criteria andTitlescEqualTo(String value) {
			addCriterion("TitleSC =", value, "titlesc");
			return (Criteria) this;
		}

		public Criteria andTitlescNotEqualTo(String value) {
			addCriterion("TitleSC <>", value, "titlesc");
			return (Criteria) this;
		}

		public Criteria andTitlescGreaterThan(String value) {
			addCriterion("TitleSC >", value, "titlesc");
			return (Criteria) this;
		}

		public Criteria andTitlescGreaterThanOrEqualTo(String value) {
			addCriterion("TitleSC >=", value, "titlesc");
			return (Criteria) this;
		}

		public Criteria andTitlescLessThan(String value) {
			addCriterion("TitleSC <", value, "titlesc");
			return (Criteria) this;
		}

		public Criteria andTitlescLessThanOrEqualTo(String value) {
			addCriterion("TitleSC <=", value, "titlesc");
			return (Criteria) this;
		}

		public Criteria andTitlescLike(String value) {
			addCriterion("TitleSC like", value, "titlesc");
			return (Criteria) this;
		}

		public Criteria andTitlescNotLike(String value) {
			addCriterion("TitleSC not like", value, "titlesc");
			return (Criteria) this;
		}

		public Criteria andTitlescIn(List<String> values) {
			addCriterion("TitleSC in", values, "titlesc");
			return (Criteria) this;
		}

		public Criteria andTitlescNotIn(List<String> values) {
			addCriterion("TitleSC not in", values, "titlesc");
			return (Criteria) this;
		}

		public Criteria andTitlescBetween(String value1, String value2) {
			addCriterion("TitleSC between", value1, value2, "titlesc");
			return (Criteria) this;
		}

		public Criteria andTitlescNotBetween(String value1, String value2) {
			addCriterion("TitleSC not between", value1, value2, "titlesc");
			return (Criteria) this;
		}

		public Criteria andTitleenIsNull() {
			addCriterion("TitleEN is null");
			return (Criteria) this;
		}

		public Criteria andTitleenIsNotNull() {
			addCriterion("TitleEN is not null");
			return (Criteria) this;
		}

		public Criteria andTitleenEqualTo(String value) {
			addCriterion("TitleEN =", value, "titleen");
			return (Criteria) this;
		}

		public Criteria andTitleenNotEqualTo(String value) {
			addCriterion("TitleEN <>", value, "titleen");
			return (Criteria) this;
		}

		public Criteria andTitleenGreaterThan(String value) {
			addCriterion("TitleEN >", value, "titleen");
			return (Criteria) this;
		}

		public Criteria andTitleenGreaterThanOrEqualTo(String value) {
			addCriterion("TitleEN >=", value, "titleen");
			return (Criteria) this;
		}

		public Criteria andTitleenLessThan(String value) {
			addCriterion("TitleEN <", value, "titleen");
			return (Criteria) this;
		}

		public Criteria andTitleenLessThanOrEqualTo(String value) {
			addCriterion("TitleEN <=", value, "titleen");
			return (Criteria) this;
		}

		public Criteria andTitleenLike(String value) {
			addCriterion("TitleEN like", value, "titleen");
			return (Criteria) this;
		}

		public Criteria andTitleenNotLike(String value) {
			addCriterion("TitleEN not like", value, "titleen");
			return (Criteria) this;
		}

		public Criteria andTitleenIn(List<String> values) {
			addCriterion("TitleEN in", values, "titleen");
			return (Criteria) this;
		}

		public Criteria andTitleenNotIn(List<String> values) {
			addCriterion("TitleEN not in", values, "titleen");
			return (Criteria) this;
		}

		public Criteria andTitleenBetween(String value1, String value2) {
			addCriterion("TitleEN between", value1, value2, "titleen");
			return (Criteria) this;
		}

		public Criteria andTitleenNotBetween(String value1, String value2) {
			addCriterion("TitleEN not between", value1, value2, "titleen");
			return (Criteria) this;
		}

		public Criteria andSeqIsNull() {
			addCriterion("Seq is null");
			return (Criteria) this;
		}

		public Criteria andSeqIsNotNull() {
			addCriterion("Seq is not null");
			return (Criteria) this;
		}

		public Criteria andSeqEqualTo(Integer value) {
			addCriterion("Seq =", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqNotEqualTo(Integer value) {
			addCriterion("Seq <>", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqGreaterThan(Integer value) {
			addCriterion("Seq >", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqGreaterThanOrEqualTo(Integer value) {
			addCriterion("Seq >=", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqLessThan(Integer value) {
			addCriterion("Seq <", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqLessThanOrEqualTo(Integer value) {
			addCriterion("Seq <=", value, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqIn(List<Integer> values) {
			addCriterion("Seq in", values, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqNotIn(List<Integer> values) {
			addCriterion("Seq not in", values, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqBetween(Integer value1, Integer value2) {
			addCriterion("Seq between", value1, value2, "seq");
			return (Criteria) this;
		}

		public Criteria andSeqNotBetween(Integer value1, Integer value2) {
			addCriterion("Seq not between", value1, value2, "seq");
			return (Criteria) this;
		}

		public Criteria andDescriptionIsNull() {
			addCriterion("Description is null");
			return (Criteria) this;
		}

		public Criteria andDescriptionIsNotNull() {
			addCriterion("Description is not null");
			return (Criteria) this;
		}

		public Criteria andDescriptionEqualTo(String value) {
			addCriterion("Description =", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotEqualTo(String value) {
			addCriterion("Description <>", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionGreaterThan(String value) {
			addCriterion("Description >", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
			addCriterion("Description >=", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionLessThan(String value) {
			addCriterion("Description <", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionLessThanOrEqualTo(String value) {
			addCriterion("Description <=", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionLike(String value) {
			addCriterion("Description like", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotLike(String value) {
			addCriterion("Description not like", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionIn(List<String> values) {
			addCriterion("Description in", values, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotIn(List<String> values) {
			addCriterion("Description not in", values, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionBetween(String value1, String value2) {
			addCriterion("Description between", value1, value2, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotBetween(String value1, String value2) {
			addCriterion("Description not between", value1, value2, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionscIsNull() {
			addCriterion("DescriptionSC is null");
			return (Criteria) this;
		}

		public Criteria andDescriptionscIsNotNull() {
			addCriterion("DescriptionSC is not null");
			return (Criteria) this;
		}

		public Criteria andDescriptionscEqualTo(String value) {
			addCriterion("DescriptionSC =", value, "descriptionsc");
			return (Criteria) this;
		}

		public Criteria andDescriptionscNotEqualTo(String value) {
			addCriterion("DescriptionSC <>", value, "descriptionsc");
			return (Criteria) this;
		}

		public Criteria andDescriptionscGreaterThan(String value) {
			addCriterion("DescriptionSC >", value, "descriptionsc");
			return (Criteria) this;
		}

		public Criteria andDescriptionscGreaterThanOrEqualTo(String value) {
			addCriterion("DescriptionSC >=", value, "descriptionsc");
			return (Criteria) this;
		}

		public Criteria andDescriptionscLessThan(String value) {
			addCriterion("DescriptionSC <", value, "descriptionsc");
			return (Criteria) this;
		}

		public Criteria andDescriptionscLessThanOrEqualTo(String value) {
			addCriterion("DescriptionSC <=", value, "descriptionsc");
			return (Criteria) this;
		}

		public Criteria andDescriptionscLike(String value) {
			addCriterion("DescriptionSC like", value, "descriptionsc");
			return (Criteria) this;
		}

		public Criteria andDescriptionscNotLike(String value) {
			addCriterion("DescriptionSC not like", value, "descriptionsc");
			return (Criteria) this;
		}

		public Criteria andDescriptionscIn(List<String> values) {
			addCriterion("DescriptionSC in", values, "descriptionsc");
			return (Criteria) this;
		}

		public Criteria andDescriptionscNotIn(List<String> values) {
			addCriterion("DescriptionSC not in", values, "descriptionsc");
			return (Criteria) this;
		}

		public Criteria andDescriptionscBetween(String value1, String value2) {
			addCriterion("DescriptionSC between", value1, value2, "descriptionsc");
			return (Criteria) this;
		}

		public Criteria andDescriptionscNotBetween(String value1, String value2) {
			addCriterion("DescriptionSC not between", value1, value2, "descriptionsc");
			return (Criteria) this;
		}

		public Criteria andDescriptionenIsNull() {
			addCriterion("DescriptionEN is null");
			return (Criteria) this;
		}

		public Criteria andDescriptionenIsNotNull() {
			addCriterion("DescriptionEN is not null");
			return (Criteria) this;
		}

		public Criteria andDescriptionenEqualTo(String value) {
			addCriterion("DescriptionEN =", value, "descriptionen");
			return (Criteria) this;
		}

		public Criteria andDescriptionenNotEqualTo(String value) {
			addCriterion("DescriptionEN <>", value, "descriptionen");
			return (Criteria) this;
		}

		public Criteria andDescriptionenGreaterThan(String value) {
			addCriterion("DescriptionEN >", value, "descriptionen");
			return (Criteria) this;
		}

		public Criteria andDescriptionenGreaterThanOrEqualTo(String value) {
			addCriterion("DescriptionEN >=", value, "descriptionen");
			return (Criteria) this;
		}

		public Criteria andDescriptionenLessThan(String value) {
			addCriterion("DescriptionEN <", value, "descriptionen");
			return (Criteria) this;
		}

		public Criteria andDescriptionenLessThanOrEqualTo(String value) {
			addCriterion("DescriptionEN <=", value, "descriptionen");
			return (Criteria) this;
		}

		public Criteria andDescriptionenLike(String value) {
			addCriterion("DescriptionEN like", value, "descriptionen");
			return (Criteria) this;
		}

		public Criteria andDescriptionenNotLike(String value) {
			addCriterion("DescriptionEN not like", value, "descriptionen");
			return (Criteria) this;
		}

		public Criteria andDescriptionenIn(List<String> values) {
			addCriterion("DescriptionEN in", values, "descriptionen");
			return (Criteria) this;
		}

		public Criteria andDescriptionenNotIn(List<String> values) {
			addCriterion("DescriptionEN not in", values, "descriptionen");
			return (Criteria) this;
		}

		public Criteria andDescriptionenBetween(String value1, String value2) {
			addCriterion("DescriptionEN between", value1, value2, "descriptionen");
			return (Criteria) this;
		}

		public Criteria andDescriptionenNotBetween(String value1, String value2) {
			addCriterion("DescriptionEN not between", value1, value2, "descriptionen");
			return (Criteria) this;
		}

		public Criteria andPhotoidIsNull() {
			addCriterion("PhotoId is null");
			return (Criteria) this;
		}

		public Criteria andPhotoidIsNotNull() {
			addCriterion("PhotoId is not null");
			return (Criteria) this;
		}

		public Criteria andPhotoidEqualTo(String value) {
			addCriterion("PhotoId =", value, "photoid");
			return (Criteria) this;
		}

		public Criteria andPhotoidNotEqualTo(String value) {
			addCriterion("PhotoId <>", value, "photoid");
			return (Criteria) this;
		}

		public Criteria andPhotoidGreaterThan(String value) {
			addCriterion("PhotoId >", value, "photoid");
			return (Criteria) this;
		}

		public Criteria andPhotoidGreaterThanOrEqualTo(String value) {
			addCriterion("PhotoId >=", value, "photoid");
			return (Criteria) this;
		}

		public Criteria andPhotoidLessThan(String value) {
			addCriterion("PhotoId <", value, "photoid");
			return (Criteria) this;
		}

		public Criteria andPhotoidLessThanOrEqualTo(String value) {
			addCriterion("PhotoId <=", value, "photoid");
			return (Criteria) this;
		}

		public Criteria andPhotoidLike(String value) {
			addCriterion("PhotoId like", value, "photoid");
			return (Criteria) this;
		}

		public Criteria andPhotoidNotLike(String value) {
			addCriterion("PhotoId not like", value, "photoid");
			return (Criteria) this;
		}

		public Criteria andPhotoidIn(List<String> values) {
			addCriterion("PhotoId in", values, "photoid");
			return (Criteria) this;
		}

		public Criteria andPhotoidNotIn(List<String> values) {
			addCriterion("PhotoId not in", values, "photoid");
			return (Criteria) this;
		}

		public Criteria andPhotoidBetween(String value1, String value2) {
			addCriterion("PhotoId between", value1, value2, "photoid");
			return (Criteria) this;
		}

		public Criteria andPhotoidNotBetween(String value1, String value2) {
			addCriterion("PhotoId not between", value1, value2, "photoid");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNull() {
			addCriterion("CreatedBy is null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNotNull() {
			addCriterion("CreatedBy is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyEqualTo(String value) {
			addCriterion("CreatedBy =", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotEqualTo(String value) {
			addCriterion("CreatedBy <>", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThan(String value) {
			addCriterion("CreatedBy >", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
			addCriterion("CreatedBy >=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThan(String value) {
			addCriterion("CreatedBy <", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThanOrEqualTo(String value) {
			addCriterion("CreatedBy <=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLike(String value) {
			addCriterion("CreatedBy like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotLike(String value) {
			addCriterion("CreatedBy not like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIn(List<String> values) {
			addCriterion("CreatedBy in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotIn(List<String> values) {
			addCriterion("CreatedBy not in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyBetween(String value1, String value2) {
			addCriterion("CreatedBy between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotBetween(String value1, String value2) {
			addCriterion("CreatedBy not between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNull() {
			addCriterion("CreatedTime is null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNotNull() {
			addCriterion("CreatedTime is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime =", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <>", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThan(LocalDateTime value) {
			addCriterion("CreatedTime >", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime >=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThan(LocalDateTime value) {
			addCriterion("CreatedTime <", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime not in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime not between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNull() {
			addCriterion("DeletedBy is null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNotNull() {
			addCriterion("DeletedBy is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyEqualTo(String value) {
			addCriterion("DeletedBy =", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotEqualTo(String value) {
			addCriterion("DeletedBy <>", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThan(String value) {
			addCriterion("DeletedBy >", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThanOrEqualTo(String value) {
			addCriterion("DeletedBy >=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThan(String value) {
			addCriterion("DeletedBy <", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThanOrEqualTo(String value) {
			addCriterion("DeletedBy <=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLike(String value) {
			addCriterion("DeletedBy like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotLike(String value) {
			addCriterion("DeletedBy not like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIn(List<String> values) {
			addCriterion("DeletedBy in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotIn(List<String> values) {
			addCriterion("DeletedBy not in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyBetween(String value1, String value2) {
			addCriterion("DeletedBy between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotBetween(String value1, String value2) {
			addCriterion("DeletedBy not between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNull() {
			addCriterion("DeletedTime is null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNotNull() {
			addCriterion("DeletedTime is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime =", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <>", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThan(LocalDateTime value) {
			addCriterion("DeletedTime >", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime >=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThan(LocalDateTime value) {
			addCriterion("DeletedTime <", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime not in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime not between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNull() {
			addCriterion("IsDeleted is null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNotNull() {
			addCriterion("IsDeleted is not null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedEqualTo(Boolean value) {
			addCriterion("IsDeleted =", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotEqualTo(Boolean value) {
			addCriterion("IsDeleted <>", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThan(Boolean value) {
			addCriterion("IsDeleted >", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted >=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThan(Boolean value) {
			addCriterion("IsDeleted <", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted <=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIn(List<Boolean> values) {
			addCriterion("IsDeleted in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotIn(List<Boolean> values) {
			addCriterion("IsDeleted not in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted between", value1, value2, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted not between", value1, value2, "isdeleted");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_EmissionReductionDescription
     *
     * @mbg.generated do_not_delete_during_merge Mon Mar 27 16:40:47 HKT 2023
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}