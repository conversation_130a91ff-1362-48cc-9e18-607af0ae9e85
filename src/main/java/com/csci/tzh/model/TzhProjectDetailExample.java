package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class TzhProjectDetailExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public TzhProjectDetailExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("Id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("Id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("Id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("Id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("Id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("Id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("Id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("Id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("Id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("Id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("Id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("Id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("Id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("Id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSitenameIsNull() {
            addCriterion("SiteName is null");
            return (Criteria) this;
        }

        public Criteria andSitenameIsNotNull() {
            addCriterion("SiteName is not null");
            return (Criteria) this;
        }

        public Criteria andSitenameEqualTo(String value) {
            addCriterion("SiteName =", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameNotEqualTo(String value) {
            addCriterion("SiteName <>", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameGreaterThan(String value) {
            addCriterion("SiteName >", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameGreaterThanOrEqualTo(String value) {
            addCriterion("SiteName >=", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameLessThan(String value) {
            addCriterion("SiteName <", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameLessThanOrEqualTo(String value) {
            addCriterion("SiteName <=", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameLike(String value) {
            addCriterion("SiteName like", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameNotLike(String value) {
            addCriterion("SiteName not like", value, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameIn(List<String> values) {
            addCriterion("SiteName in", values, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameNotIn(List<String> values) {
            addCriterion("SiteName not in", values, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameBetween(String value1, String value2) {
            addCriterion("SiteName between", value1, value2, "sitename");
            return (Criteria) this;
        }

        public Criteria andSitenameNotBetween(String value1, String value2) {
            addCriterion("SiteName not between", value1, value2, "sitename");
            return (Criteria) this;
        }

        public Criteria andProtocolidIsNull() {
            addCriterion("ProtocolId is null");
            return (Criteria) this;
        }

        public Criteria andProtocolidIsNotNull() {
            addCriterion("ProtocolId is not null");
            return (Criteria) this;
        }

        public Criteria andProtocolidEqualTo(String value) {
            addCriterion("ProtocolId =", value, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidNotEqualTo(String value) {
            addCriterion("ProtocolId <>", value, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidGreaterThan(String value) {
            addCriterion("ProtocolId >", value, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidGreaterThanOrEqualTo(String value) {
            addCriterion("ProtocolId >=", value, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidLessThan(String value) {
            addCriterion("ProtocolId <", value, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidLessThanOrEqualTo(String value) {
            addCriterion("ProtocolId <=", value, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidLike(String value) {
            addCriterion("ProtocolId like", value, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidNotLike(String value) {
            addCriterion("ProtocolId not like", value, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidIn(List<String> values) {
            addCriterion("ProtocolId in", values, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidNotIn(List<String> values) {
            addCriterion("ProtocolId not in", values, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidBetween(String value1, String value2) {
            addCriterion("ProtocolId between", value1, value2, "protocolid");
            return (Criteria) this;
        }

        public Criteria andProtocolidNotBetween(String value1, String value2) {
            addCriterion("ProtocolId not between", value1, value2, "protocolid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidIsNull() {
            addCriterion("CarbonEmissionLocationId is null");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidIsNotNull() {
            addCriterion("CarbonEmissionLocationId is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidEqualTo(String value) {
            addCriterion("CarbonEmissionLocationId =", value, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidNotEqualTo(String value) {
            addCriterion("CarbonEmissionLocationId <>", value, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidGreaterThan(String value) {
            addCriterion("CarbonEmissionLocationId >", value, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidGreaterThanOrEqualTo(String value) {
            addCriterion("CarbonEmissionLocationId >=", value, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidLessThan(String value) {
            addCriterion("CarbonEmissionLocationId <", value, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidLessThanOrEqualTo(String value) {
            addCriterion("CarbonEmissionLocationId <=", value, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidLike(String value) {
            addCriterion("CarbonEmissionLocationId like", value, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidNotLike(String value) {
            addCriterion("CarbonEmissionLocationId not like", value, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidIn(List<String> values) {
            addCriterion("CarbonEmissionLocationId in", values, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidNotIn(List<String> values) {
            addCriterion("CarbonEmissionLocationId not in", values, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidBetween(String value1, String value2) {
            addCriterion("CarbonEmissionLocationId between", value1, value2, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andCarbonemissionlocationidNotBetween(String value1, String value2) {
            addCriterion("CarbonEmissionLocationId not between", value1, value2, "carbonemissionlocationid");
            return (Criteria) this;
        }

        public Criteria andEmissionreductiontargetIsNull() {
            addCriterion("EmissionReductionTarget is null");
            return (Criteria) this;
        }

        public Criteria andEmissionreductiontargetIsNotNull() {
            addCriterion("EmissionReductionTarget is not null");
            return (Criteria) this;
        }

        public Criteria andEmissionreductiontargetEqualTo(BigDecimal value) {
            addCriterion("EmissionReductionTarget =", value, "emissionreductiontarget");
            return (Criteria) this;
        }

        public Criteria andEmissionreductiontargetNotEqualTo(BigDecimal value) {
            addCriterion("EmissionReductionTarget <>", value, "emissionreductiontarget");
            return (Criteria) this;
        }

        public Criteria andEmissionreductiontargetGreaterThan(BigDecimal value) {
            addCriterion("EmissionReductionTarget >", value, "emissionreductiontarget");
            return (Criteria) this;
        }

        public Criteria andEmissionreductiontargetGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("EmissionReductionTarget >=", value, "emissionreductiontarget");
            return (Criteria) this;
        }

        public Criteria andEmissionreductiontargetLessThan(BigDecimal value) {
            addCriterion("EmissionReductionTarget <", value, "emissionreductiontarget");
            return (Criteria) this;
        }

        public Criteria andEmissionreductiontargetLessThanOrEqualTo(BigDecimal value) {
            addCriterion("EmissionReductionTarget <=", value, "emissionreductiontarget");
            return (Criteria) this;
        }

        public Criteria andEmissionreductiontargetIn(List<BigDecimal> values) {
            addCriterion("EmissionReductionTarget in", values, "emissionreductiontarget");
            return (Criteria) this;
        }

        public Criteria andEmissionreductiontargetNotIn(List<BigDecimal> values) {
            addCriterion("EmissionReductionTarget not in", values, "emissionreductiontarget");
            return (Criteria) this;
        }

        public Criteria andEmissionreductiontargetBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("EmissionReductionTarget between", value1, value2, "emissionreductiontarget");
            return (Criteria) this;
        }

        public Criteria andEmissionreductiontargetNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("EmissionReductionTarget not between", value1, value2, "emissionreductiontarget");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountIsNull() {
            addCriterion("EpdWasteAccount is null");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountIsNotNull() {
            addCriterion("EpdWasteAccount is not null");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountEqualTo(String value) {
            addCriterion("EpdWasteAccount =", value, "epdwasteaccount");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountNotEqualTo(String value) {
            addCriterion("EpdWasteAccount <>", value, "epdwasteaccount");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountGreaterThan(String value) {
            addCriterion("EpdWasteAccount >", value, "epdwasteaccount");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountGreaterThanOrEqualTo(String value) {
            addCriterion("EpdWasteAccount >=", value, "epdwasteaccount");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountLessThan(String value) {
            addCriterion("EpdWasteAccount <", value, "epdwasteaccount");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountLessThanOrEqualTo(String value) {
            addCriterion("EpdWasteAccount <=", value, "epdwasteaccount");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountLike(String value) {
            addCriterion("EpdWasteAccount like", value, "epdwasteaccount");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountNotLike(String value) {
            addCriterion("EpdWasteAccount not like", value, "epdwasteaccount");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountIn(List<String> values) {
            addCriterion("EpdWasteAccount in", values, "epdwasteaccount");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountNotIn(List<String> values) {
            addCriterion("EpdWasteAccount not in", values, "epdwasteaccount");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountBetween(String value1, String value2) {
            addCriterion("EpdWasteAccount between", value1, value2, "epdwasteaccount");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountNotBetween(String value1, String value2) {
            addCriterion("EpdWasteAccount not between", value1, value2, "epdwasteaccount");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountpwdIsNull() {
            addCriterion("EpdWasteAccountPwd is null");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountpwdIsNotNull() {
            addCriterion("EpdWasteAccountPwd is not null");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountpwdEqualTo(String value) {
            addCriterion("EpdWasteAccountPwd =", value, "epdwasteaccountpwd");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountpwdNotEqualTo(String value) {
            addCriterion("EpdWasteAccountPwd <>", value, "epdwasteaccountpwd");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountpwdGreaterThan(String value) {
            addCriterion("EpdWasteAccountPwd >", value, "epdwasteaccountpwd");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountpwdGreaterThanOrEqualTo(String value) {
            addCriterion("EpdWasteAccountPwd >=", value, "epdwasteaccountpwd");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountpwdLessThan(String value) {
            addCriterion("EpdWasteAccountPwd <", value, "epdwasteaccountpwd");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountpwdLessThanOrEqualTo(String value) {
            addCriterion("EpdWasteAccountPwd <=", value, "epdwasteaccountpwd");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountpwdLike(String value) {
            addCriterion("EpdWasteAccountPwd like", value, "epdwasteaccountpwd");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountpwdNotLike(String value) {
            addCriterion("EpdWasteAccountPwd not like", value, "epdwasteaccountpwd");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountpwdIn(List<String> values) {
            addCriterion("EpdWasteAccountPwd in", values, "epdwasteaccountpwd");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountpwdNotIn(List<String> values) {
            addCriterion("EpdWasteAccountPwd not in", values, "epdwasteaccountpwd");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountpwdBetween(String value1, String value2) {
            addCriterion("EpdWasteAccountPwd between", value1, value2, "epdwasteaccountpwd");
            return (Criteria) this;
        }

        public Criteria andEpdwasteaccountpwdNotBetween(String value1, String value2) {
            addCriterion("EpdWasteAccountPwd not between", value1, value2, "epdwasteaccountpwd");
            return (Criteria) this;
        }

        public Criteria andHasfoodcourtIsNull() {
            addCriterion("HasFoodCourt is null");
            return (Criteria) this;
        }

        public Criteria andHasfoodcourtIsNotNull() {
            addCriterion("HasFoodCourt is not null");
            return (Criteria) this;
        }

        public Criteria andHasfoodcourtEqualTo(String value) {
            addCriterion("HasFoodCourt =", value, "hasfoodcourt");
            return (Criteria) this;
        }

        public Criteria andHasfoodcourtNotEqualTo(String value) {
            addCriterion("HasFoodCourt <>", value, "hasfoodcourt");
            return (Criteria) this;
        }

        public Criteria andHasfoodcourtGreaterThan(String value) {
            addCriterion("HasFoodCourt >", value, "hasfoodcourt");
            return (Criteria) this;
        }

        public Criteria andHasfoodcourtGreaterThanOrEqualTo(String value) {
            addCriterion("HasFoodCourt >=", value, "hasfoodcourt");
            return (Criteria) this;
        }

        public Criteria andHasfoodcourtLessThan(String value) {
            addCriterion("HasFoodCourt <", value, "hasfoodcourt");
            return (Criteria) this;
        }

        public Criteria andHasfoodcourtLessThanOrEqualTo(String value) {
            addCriterion("HasFoodCourt <=", value, "hasfoodcourt");
            return (Criteria) this;
        }

        public Criteria andHasfoodcourtLike(String value) {
            addCriterion("HasFoodCourt like", value, "hasfoodcourt");
            return (Criteria) this;
        }

        public Criteria andHasfoodcourtNotLike(String value) {
            addCriterion("HasFoodCourt not like", value, "hasfoodcourt");
            return (Criteria) this;
        }

        public Criteria andHasfoodcourtIn(List<String> values) {
            addCriterion("HasFoodCourt in", values, "hasfoodcourt");
            return (Criteria) this;
        }

        public Criteria andHasfoodcourtNotIn(List<String> values) {
            addCriterion("HasFoodCourt not in", values, "hasfoodcourt");
            return (Criteria) this;
        }

        public Criteria andHasfoodcourtBetween(String value1, String value2) {
            addCriterion("HasFoodCourt between", value1, value2, "hasfoodcourt");
            return (Criteria) this;
        }

        public Criteria andHasfoodcourtNotBetween(String value1, String value2) {
            addCriterion("HasFoodCourt not between", value1, value2, "hasfoodcourt");
            return (Criteria) this;
        }

        public Criteria andDatasourceidIsNull() {
            addCriterion("DataSourceId is null");
            return (Criteria) this;
        }

        public Criteria andDatasourceidIsNotNull() {
            addCriterion("DataSourceId is not null");
            return (Criteria) this;
        }

        public Criteria andDatasourceidEqualTo(String value) {
            addCriterion("DataSourceId =", value, "datasourceid");
            return (Criteria) this;
        }

        public Criteria andDatasourceidNotEqualTo(String value) {
            addCriterion("DataSourceId <>", value, "datasourceid");
            return (Criteria) this;
        }

        public Criteria andDatasourceidGreaterThan(String value) {
            addCriterion("DataSourceId >", value, "datasourceid");
            return (Criteria) this;
        }

        public Criteria andDatasourceidGreaterThanOrEqualTo(String value) {
            addCriterion("DataSourceId >=", value, "datasourceid");
            return (Criteria) this;
        }

        public Criteria andDatasourceidLessThan(String value) {
            addCriterion("DataSourceId <", value, "datasourceid");
            return (Criteria) this;
        }

        public Criteria andDatasourceidLessThanOrEqualTo(String value) {
            addCriterion("DataSourceId <=", value, "datasourceid");
            return (Criteria) this;
        }

        public Criteria andDatasourceidLike(String value) {
            addCriterion("DataSourceId like", value, "datasourceid");
            return (Criteria) this;
        }

        public Criteria andDatasourceidNotLike(String value) {
            addCriterion("DataSourceId not like", value, "datasourceid");
            return (Criteria) this;
        }

        public Criteria andDatasourceidIn(List<String> values) {
            addCriterion("DataSourceId in", values, "datasourceid");
            return (Criteria) this;
        }

        public Criteria andDatasourceidNotIn(List<String> values) {
            addCriterion("DataSourceId not in", values, "datasourceid");
            return (Criteria) this;
        }

        public Criteria andDatasourceidBetween(String value1, String value2) {
            addCriterion("DataSourceId between", value1, value2, "datasourceid");
            return (Criteria) this;
        }

        public Criteria andDatasourceidNotBetween(String value1, String value2) {
            addCriterion("DataSourceId not between", value1, value2, "datasourceid");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorIsNull() {
            addCriterion("WasterWaterCarbonFactor is null");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorIsNotNull() {
            addCriterion("WasterWaterCarbonFactor is not null");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorEqualTo(BigDecimal value) {
            addCriterion("WasterWaterCarbonFactor =", value, "wasterwatercarbonfactor");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorNotEqualTo(BigDecimal value) {
            addCriterion("WasterWaterCarbonFactor <>", value, "wasterwatercarbonfactor");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorGreaterThan(BigDecimal value) {
            addCriterion("WasterWaterCarbonFactor >", value, "wasterwatercarbonfactor");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("WasterWaterCarbonFactor >=", value, "wasterwatercarbonfactor");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorLessThan(BigDecimal value) {
            addCriterion("WasterWaterCarbonFactor <", value, "wasterwatercarbonfactor");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorLessThanOrEqualTo(BigDecimal value) {
            addCriterion("WasterWaterCarbonFactor <=", value, "wasterwatercarbonfactor");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorIn(List<BigDecimal> values) {
            addCriterion("WasterWaterCarbonFactor in", values, "wasterwatercarbonfactor");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorNotIn(List<BigDecimal> values) {
            addCriterion("WasterWaterCarbonFactor not in", values, "wasterwatercarbonfactor");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("WasterWaterCarbonFactor between", value1, value2, "wasterwatercarbonfactor");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("WasterWaterCarbonFactor not between", value1, value2, "wasterwatercarbonfactor");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorunitIsNull() {
            addCriterion("WasterWaterCarbonFactorUnit is null");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorunitIsNotNull() {
            addCriterion("WasterWaterCarbonFactorUnit is not null");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorunitEqualTo(String value) {
            addCriterion("WasterWaterCarbonFactorUnit =", value, "wasterwatercarbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorunitNotEqualTo(String value) {
            addCriterion("WasterWaterCarbonFactorUnit <>", value, "wasterwatercarbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorunitGreaterThan(String value) {
            addCriterion("WasterWaterCarbonFactorUnit >", value, "wasterwatercarbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorunitGreaterThanOrEqualTo(String value) {
            addCriterion("WasterWaterCarbonFactorUnit >=", value, "wasterwatercarbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorunitLessThan(String value) {
            addCriterion("WasterWaterCarbonFactorUnit <", value, "wasterwatercarbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorunitLessThanOrEqualTo(String value) {
            addCriterion("WasterWaterCarbonFactorUnit <=", value, "wasterwatercarbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorunitLike(String value) {
            addCriterion("WasterWaterCarbonFactorUnit like", value, "wasterwatercarbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorunitNotLike(String value) {
            addCriterion("WasterWaterCarbonFactorUnit not like", value, "wasterwatercarbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorunitIn(List<String> values) {
            addCriterion("WasterWaterCarbonFactorUnit in", values, "wasterwatercarbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorunitNotIn(List<String> values) {
            addCriterion("WasterWaterCarbonFactorUnit not in", values, "wasterwatercarbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorunitBetween(String value1, String value2) {
            addCriterion("WasterWaterCarbonFactorUnit between", value1, value2, "wasterwatercarbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andWasterwatercarbonfactorunitNotBetween(String value1, String value2) {
            addCriterion("WasterWaterCarbonFactorUnit not between", value1, value2, "wasterwatercarbonfactorunit");
            return (Criteria) this;
        }

        public Criteria andWaterelectricitybillsourceIsNull() {
            addCriterion("WaterElectricityBillSource is null");
            return (Criteria) this;
        }

        public Criteria andWaterelectricitybillsourceIsNotNull() {
            addCriterion("WaterElectricityBillSource is not null");
            return (Criteria) this;
        }

        public Criteria andWaterelectricitybillsourceEqualTo(String value) {
            addCriterion("WaterElectricityBillSource =", value, "waterelectricitybillsource");
            return (Criteria) this;
        }

        public Criteria andWaterelectricitybillsourceNotEqualTo(String value) {
            addCriterion("WaterElectricityBillSource <>", value, "waterelectricitybillsource");
            return (Criteria) this;
        }

        public Criteria andWaterelectricitybillsourceGreaterThan(String value) {
            addCriterion("WaterElectricityBillSource >", value, "waterelectricitybillsource");
            return (Criteria) this;
        }

        public Criteria andWaterelectricitybillsourceGreaterThanOrEqualTo(String value) {
            addCriterion("WaterElectricityBillSource >=", value, "waterelectricitybillsource");
            return (Criteria) this;
        }

        public Criteria andWaterelectricitybillsourceLessThan(String value) {
            addCriterion("WaterElectricityBillSource <", value, "waterelectricitybillsource");
            return (Criteria) this;
        }

        public Criteria andWaterelectricitybillsourceLessThanOrEqualTo(String value) {
            addCriterion("WaterElectricityBillSource <=", value, "waterelectricitybillsource");
            return (Criteria) this;
        }

        public Criteria andWaterelectricitybillsourceLike(String value) {
            addCriterion("WaterElectricityBillSource like", value, "waterelectricitybillsource");
            return (Criteria) this;
        }

        public Criteria andWaterelectricitybillsourceNotLike(String value) {
            addCriterion("WaterElectricityBillSource not like", value, "waterelectricitybillsource");
            return (Criteria) this;
        }

        public Criteria andWaterelectricitybillsourceIn(List<String> values) {
            addCriterion("WaterElectricityBillSource in", values, "waterelectricitybillsource");
            return (Criteria) this;
        }

        public Criteria andWaterelectricitybillsourceNotIn(List<String> values) {
            addCriterion("WaterElectricityBillSource not in", values, "waterelectricitybillsource");
            return (Criteria) this;
        }

        public Criteria andWaterelectricitybillsourceBetween(String value1, String value2) {
            addCriterion("WaterElectricityBillSource between", value1, value2, "waterelectricitybillsource");
            return (Criteria) this;
        }

        public Criteria andWaterelectricitybillsourceNotBetween(String value1, String value2) {
            addCriterion("WaterElectricityBillSource not between", value1, value2, "waterelectricitybillsource");
            return (Criteria) this;
        }

        public Criteria andAccountingcenternoIsNull() {
            addCriterion("AccountingCenterNo is null");
            return (Criteria) this;
        }

        public Criteria andAccountingcenternoIsNotNull() {
            addCriterion("AccountingCenterNo is not null");
            return (Criteria) this;
        }

        public Criteria andAccountingcenternoEqualTo(String value) {
            addCriterion("AccountingCenterNo =", value, "accountingcenterno");
            return (Criteria) this;
        }

        public Criteria andAccountingcenternoNotEqualTo(String value) {
            addCriterion("AccountingCenterNo <>", value, "accountingcenterno");
            return (Criteria) this;
        }

        public Criteria andAccountingcenternoGreaterThan(String value) {
            addCriterion("AccountingCenterNo >", value, "accountingcenterno");
            return (Criteria) this;
        }

        public Criteria andAccountingcenternoGreaterThanOrEqualTo(String value) {
            addCriterion("AccountingCenterNo >=", value, "accountingcenterno");
            return (Criteria) this;
        }

        public Criteria andAccountingcenternoLessThan(String value) {
            addCriterion("AccountingCenterNo <", value, "accountingcenterno");
            return (Criteria) this;
        }

        public Criteria andAccountingcenternoLessThanOrEqualTo(String value) {
            addCriterion("AccountingCenterNo <=", value, "accountingcenterno");
            return (Criteria) this;
        }

        public Criteria andAccountingcenternoLike(String value) {
            addCriterion("AccountingCenterNo like", value, "accountingcenterno");
            return (Criteria) this;
        }

        public Criteria andAccountingcenternoNotLike(String value) {
            addCriterion("AccountingCenterNo not like", value, "accountingcenterno");
            return (Criteria) this;
        }

        public Criteria andAccountingcenternoIn(List<String> values) {
            addCriterion("AccountingCenterNo in", values, "accountingcenterno");
            return (Criteria) this;
        }

        public Criteria andAccountingcenternoNotIn(List<String> values) {
            addCriterion("AccountingCenterNo not in", values, "accountingcenterno");
            return (Criteria) this;
        }

        public Criteria andAccountingcenternoBetween(String value1, String value2) {
            addCriterion("AccountingCenterNo between", value1, value2, "accountingcenterno");
            return (Criteria) this;
        }

        public Criteria andAccountingcenternoNotBetween(String value1, String value2) {
            addCriterion("AccountingCenterNo not between", value1, value2, "accountingcenterno");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIsNull() {
            addCriterion("CreatedBy is null");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIsNotNull() {
            addCriterion("CreatedBy is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedbyEqualTo(String value) {
            addCriterion("CreatedBy =", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotEqualTo(String value) {
            addCriterion("CreatedBy <>", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyGreaterThan(String value) {
            addCriterion("CreatedBy >", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
            addCriterion("CreatedBy >=", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLessThan(String value) {
            addCriterion("CreatedBy <", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLessThanOrEqualTo(String value) {
            addCriterion("CreatedBy <=", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLike(String value) {
            addCriterion("CreatedBy like", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotLike(String value) {
            addCriterion("CreatedBy not like", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIn(List<String> values) {
            addCriterion("CreatedBy in", values, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotIn(List<String> values) {
            addCriterion("CreatedBy not in", values, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyBetween(String value1, String value2) {
            addCriterion("CreatedBy between", value1, value2, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotBetween(String value1, String value2) {
            addCriterion("CreatedBy not between", value1, value2, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeIsNull() {
            addCriterion("CreatedTime is null");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeIsNotNull() {
            addCriterion("CreatedTime is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeEqualTo(LocalDateTime value) {
            addCriterion("CreatedTime =", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeNotEqualTo(LocalDateTime value) {
            addCriterion("CreatedTime <>", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeGreaterThan(LocalDateTime value) {
            addCriterion("CreatedTime >", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("CreatedTime >=", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeLessThan(LocalDateTime value) {
            addCriterion("CreatedTime <", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("CreatedTime <=", value, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeIn(List<LocalDateTime> values) {
            addCriterion("CreatedTime in", values, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeNotIn(List<LocalDateTime> values) {
            addCriterion("CreatedTime not in", values, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("CreatedTime between", value1, value2, "createdtime");
            return (Criteria) this;
        }

        public Criteria andCreatedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("CreatedTime not between", value1, value2, "createdtime");
            return (Criteria) this;
        }

        public Criteria andDeletedbyIsNull() {
            addCriterion("DeletedBy is null");
            return (Criteria) this;
        }

        public Criteria andDeletedbyIsNotNull() {
            addCriterion("DeletedBy is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedbyEqualTo(String value) {
            addCriterion("DeletedBy =", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyNotEqualTo(String value) {
            addCriterion("DeletedBy <>", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyGreaterThan(String value) {
            addCriterion("DeletedBy >", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyGreaterThanOrEqualTo(String value) {
            addCriterion("DeletedBy >=", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyLessThan(String value) {
            addCriterion("DeletedBy <", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyLessThanOrEqualTo(String value) {
            addCriterion("DeletedBy <=", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyLike(String value) {
            addCriterion("DeletedBy like", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyNotLike(String value) {
            addCriterion("DeletedBy not like", value, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyIn(List<String> values) {
            addCriterion("DeletedBy in", values, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyNotIn(List<String> values) {
            addCriterion("DeletedBy not in", values, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyBetween(String value1, String value2) {
            addCriterion("DeletedBy between", value1, value2, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedbyNotBetween(String value1, String value2) {
            addCriterion("DeletedBy not between", value1, value2, "deletedby");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeIsNull() {
            addCriterion("DeletedTime is null");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeIsNotNull() {
            addCriterion("DeletedTime is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeEqualTo(LocalDateTime value) {
            addCriterion("DeletedTime =", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeNotEqualTo(LocalDateTime value) {
            addCriterion("DeletedTime <>", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeGreaterThan(LocalDateTime value) {
            addCriterion("DeletedTime >", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("DeletedTime >=", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeLessThan(LocalDateTime value) {
            addCriterion("DeletedTime <", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("DeletedTime <=", value, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeIn(List<LocalDateTime> values) {
            addCriterion("DeletedTime in", values, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeNotIn(List<LocalDateTime> values) {
            addCriterion("DeletedTime not in", values, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("DeletedTime between", value1, value2, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andDeletedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("DeletedTime not between", value1, value2, "deletedtime");
            return (Criteria) this;
        }

        public Criteria andIsdeletedIsNull() {
            addCriterion("IsDeleted is null");
            return (Criteria) this;
        }

        public Criteria andIsdeletedIsNotNull() {
            addCriterion("IsDeleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsdeletedEqualTo(Boolean value) {
            addCriterion("IsDeleted =", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedNotEqualTo(Boolean value) {
            addCriterion("IsDeleted <>", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedGreaterThan(Boolean value) {
            addCriterion("IsDeleted >", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("IsDeleted >=", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedLessThan(Boolean value) {
            addCriterion("IsDeleted <", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("IsDeleted <=", value, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedIn(List<Boolean> values) {
            addCriterion("IsDeleted in", values, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedNotIn(List<Boolean> values) {
            addCriterion("IsDeleted not in", values, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("IsDeleted between", value1, value2, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andIsdeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("IsDeleted not between", value1, value2, "isdeleted");
            return (Criteria) this;
        }

        public Criteria andSiteidIsNull() {
            addCriterion("SiteId is null");
            return (Criteria) this;
        }

        public Criteria andSiteidIsNotNull() {
            addCriterion("SiteId is not null");
            return (Criteria) this;
        }

        public Criteria andSiteidEqualTo(String value) {
            addCriterion("SiteId =", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidNotEqualTo(String value) {
            addCriterion("SiteId <>", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidGreaterThan(String value) {
            addCriterion("SiteId >", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidGreaterThanOrEqualTo(String value) {
            addCriterion("SiteId >=", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidLessThan(String value) {
            addCriterion("SiteId <", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidLessThanOrEqualTo(String value) {
            addCriterion("SiteId <=", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidLike(String value) {
            addCriterion("SiteId like", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidNotLike(String value) {
            addCriterion("SiteId not like", value, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidIn(List<String> values) {
            addCriterion("SiteId in", values, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidNotIn(List<String> values) {
            addCriterion("SiteId not in", values, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidBetween(String value1, String value2) {
            addCriterion("SiteId between", value1, value2, "siteid");
            return (Criteria) this;
        }

        public Criteria andSiteidNotBetween(String value1, String value2) {
            addCriterion("SiteId not between", value1, value2, "siteid");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated do_not_delete_during_merge Thu Feb 06 09:05:56 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_ProjectDetail
     *
     * @mbg.generated Thu Feb 06 09:05:56 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}