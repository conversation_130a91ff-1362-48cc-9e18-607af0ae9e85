package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.time.LocalDate;

public class FCdmsWasteTransportationCarbonFactorExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
	 * @mbg.generated  Fri Apr 21 10:13:35 HKT 2023
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
	 * @mbg.generated  Fri Apr 21 10:13:35 HKT 2023
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
	 * @mbg.generated  Fri Apr 21 10:13:35 HKT 2023
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
	 * @mbg.generated  Fri Apr 21 10:13:35 HKT 2023
	 */
	public FCdmsWasteTransportationCarbonFactorExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
	 * @mbg.generated  Fri Apr 21 10:13:35 HKT 2023
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
	 * @mbg.generated  Fri Apr 21 10:13:35 HKT 2023
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
	 * @mbg.generated  Fri Apr 21 10:13:35 HKT 2023
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
	 * @mbg.generated  Fri Apr 21 10:13:35 HKT 2023
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
	 * @mbg.generated  Fri Apr 21 10:13:35 HKT 2023
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
	 * @mbg.generated  Fri Apr 21 10:13:35 HKT 2023
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
	 * @mbg.generated  Fri Apr 21 10:13:35 HKT 2023
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
	 * @mbg.generated  Fri Apr 21 10:13:35 HKT 2023
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
	 * @mbg.generated  Fri Apr 21 10:13:35 HKT 2023
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
	 * @mbg.generated  Fri Apr 21 10:13:35 HKT 2023
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
	 * @mbg.generated  Fri Apr 21 10:13:35 HKT 2023
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("Id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("Id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("Id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("Id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("Id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("Id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("Id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("Id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("Id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("Id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("Id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("Id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("Id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("Id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andSitenameIsNull() {
			addCriterion("SiteName is null");
			return (Criteria) this;
		}

		public Criteria andSitenameIsNotNull() {
			addCriterion("SiteName is not null");
			return (Criteria) this;
		}

		public Criteria andSitenameEqualTo(String value) {
			addCriterion("SiteName =", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotEqualTo(String value) {
			addCriterion("SiteName <>", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameGreaterThan(String value) {
			addCriterion("SiteName >", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameGreaterThanOrEqualTo(String value) {
			addCriterion("SiteName >=", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLessThan(String value) {
			addCriterion("SiteName <", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLessThanOrEqualTo(String value) {
			addCriterion("SiteName <=", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLike(String value) {
			addCriterion("SiteName like", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotLike(String value) {
			addCriterion("SiteName not like", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameIn(List<String> values) {
			addCriterion("SiteName in", values, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotIn(List<String> values) {
			addCriterion("SiteName not in", values, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameBetween(String value1, String value2) {
			addCriterion("SiteName between", value1, value2, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotBetween(String value1, String value2) {
			addCriterion("SiteName not between", value1, value2, "sitename");
			return (Criteria) this;
		}

		public Criteria andProtocolidIsNull() {
			addCriterion("ProtocolId is null");
			return (Criteria) this;
		}

		public Criteria andProtocolidIsNotNull() {
			addCriterion("ProtocolId is not null");
			return (Criteria) this;
		}

		public Criteria andProtocolidEqualTo(String value) {
			addCriterion("ProtocolId =", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotEqualTo(String value) {
			addCriterion("ProtocolId <>", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidGreaterThan(String value) {
			addCriterion("ProtocolId >", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidGreaterThanOrEqualTo(String value) {
			addCriterion("ProtocolId >=", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidLessThan(String value) {
			addCriterion("ProtocolId <", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidLessThanOrEqualTo(String value) {
			addCriterion("ProtocolId <=", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidLike(String value) {
			addCriterion("ProtocolId like", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotLike(String value) {
			addCriterion("ProtocolId not like", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidIn(List<String> values) {
			addCriterion("ProtocolId in", values, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotIn(List<String> values) {
			addCriterion("ProtocolId not in", values, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidBetween(String value1, String value2) {
			addCriterion("ProtocolId between", value1, value2, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotBetween(String value1, String value2) {
			addCriterion("ProtocolId not between", value1, value2, "protocolid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidIsNull() {
			addCriterion("CarbonEmissionLocationId is null");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidIsNotNull() {
			addCriterion("CarbonEmissionLocationId is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId =", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId <>", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidGreaterThan(String value) {
			addCriterion("CarbonEmissionLocationId >", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidGreaterThanOrEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId >=", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidLessThan(String value) {
			addCriterion("CarbonEmissionLocationId <", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidLessThanOrEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId <=", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidLike(String value) {
			addCriterion("CarbonEmissionLocationId like", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotLike(String value) {
			addCriterion("CarbonEmissionLocationId not like", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidIn(List<String> values) {
			addCriterion("CarbonEmissionLocationId in", values, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotIn(List<String> values) {
			addCriterion("CarbonEmissionLocationId not in", values, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidBetween(String value1, String value2) {
			addCriterion("CarbonEmissionLocationId between", value1, value2, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotBetween(String value1, String value2) {
			addCriterion("CarbonEmissionLocationId not between", value1, value2, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthIsNull() {
			addCriterion("RecordYearMonth is null");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthIsNotNull() {
			addCriterion("RecordYearMonth is not null");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthEqualTo(Integer value) {
			addCriterion("RecordYearMonth =", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthNotEqualTo(Integer value) {
			addCriterion("RecordYearMonth <>", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthGreaterThan(Integer value) {
			addCriterion("RecordYearMonth >", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthGreaterThanOrEqualTo(Integer value) {
			addCriterion("RecordYearMonth >=", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthLessThan(Integer value) {
			addCriterion("RecordYearMonth <", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthLessThanOrEqualTo(Integer value) {
			addCriterion("RecordYearMonth <=", value, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthIn(List<Integer> values) {
			addCriterion("RecordYearMonth in", values, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthNotIn(List<Integer> values) {
			addCriterion("RecordYearMonth not in", values, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthBetween(Integer value1, Integer value2) {
			addCriterion("RecordYearMonth between", value1, value2, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecordyearmonthNotBetween(Integer value1, Integer value2) {
			addCriterion("RecordYearMonth not between", value1, value2, "recordyearmonth");
			return (Criteria) this;
		}

		public Criteria andRecorddateIsNull() {
			addCriterion("RecordDate is null");
			return (Criteria) this;
		}

		public Criteria andRecorddateIsNotNull() {
			addCriterion("RecordDate is not null");
			return (Criteria) this;
		}

		public Criteria andRecorddateEqualTo(LocalDate value) {
			addCriterion("RecordDate =", value, "recorddate");
			return (Criteria) this;
		}

		public Criteria andRecorddateNotEqualTo(LocalDate value) {
			addCriterion("RecordDate <>", value, "recorddate");
			return (Criteria) this;
		}

		public Criteria andRecorddateGreaterThan(LocalDate value) {
			addCriterion("RecordDate >", value, "recorddate");
			return (Criteria) this;
		}

		public Criteria andRecorddateGreaterThanOrEqualTo(LocalDate value) {
			addCriterion("RecordDate >=", value, "recorddate");
			return (Criteria) this;
		}

		public Criteria andRecorddateLessThan(LocalDate value) {
			addCriterion("RecordDate <", value, "recorddate");
			return (Criteria) this;
		}

		public Criteria andRecorddateLessThanOrEqualTo(LocalDate value) {
			addCriterion("RecordDate <=", value, "recorddate");
			return (Criteria) this;
		}

		public Criteria andRecorddateIn(List<LocalDate> values) {
			addCriterion("RecordDate in", values, "recorddate");
			return (Criteria) this;
		}

		public Criteria andRecorddateNotIn(List<LocalDate> values) {
			addCriterion("RecordDate not in", values, "recorddate");
			return (Criteria) this;
		}

		public Criteria andRecorddateBetween(LocalDate value1, LocalDate value2) {
			addCriterion("RecordDate between", value1, value2, "recorddate");
			return (Criteria) this;
		}

		public Criteria andRecorddateNotBetween(LocalDate value1, LocalDate value2) {
			addCriterion("RecordDate not between", value1, value2, "recorddate");
			return (Criteria) this;
		}

		public Criteria andVehiclenoIsNull() {
			addCriterion("VehicleNo is null");
			return (Criteria) this;
		}

		public Criteria andVehiclenoIsNotNull() {
			addCriterion("VehicleNo is not null");
			return (Criteria) this;
		}

		public Criteria andVehiclenoEqualTo(String value) {
			addCriterion("VehicleNo =", value, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoNotEqualTo(String value) {
			addCriterion("VehicleNo <>", value, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoGreaterThan(String value) {
			addCriterion("VehicleNo >", value, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoGreaterThanOrEqualTo(String value) {
			addCriterion("VehicleNo >=", value, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoLessThan(String value) {
			addCriterion("VehicleNo <", value, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoLessThanOrEqualTo(String value) {
			addCriterion("VehicleNo <=", value, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoLike(String value) {
			addCriterion("VehicleNo like", value, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoNotLike(String value) {
			addCriterion("VehicleNo not like", value, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoIn(List<String> values) {
			addCriterion("VehicleNo in", values, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoNotIn(List<String> values) {
			addCriterion("VehicleNo not in", values, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoBetween(String value1, String value2) {
			addCriterion("VehicleNo between", value1, value2, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andVehiclenoNotBetween(String value1, String value2) {
			addCriterion("VehicleNo not between", value1, value2, "vehicleno");
			return (Criteria) this;
		}

		public Criteria andChitnoIsNull() {
			addCriterion("ChitNo is null");
			return (Criteria) this;
		}

		public Criteria andChitnoIsNotNull() {
			addCriterion("ChitNo is not null");
			return (Criteria) this;
		}

		public Criteria andChitnoEqualTo(String value) {
			addCriterion("ChitNo =", value, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoNotEqualTo(String value) {
			addCriterion("ChitNo <>", value, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoGreaterThan(String value) {
			addCriterion("ChitNo >", value, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoGreaterThanOrEqualTo(String value) {
			addCriterion("ChitNo >=", value, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoLessThan(String value) {
			addCriterion("ChitNo <", value, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoLessThanOrEqualTo(String value) {
			addCriterion("ChitNo <=", value, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoLike(String value) {
			addCriterion("ChitNo like", value, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoNotLike(String value) {
			addCriterion("ChitNo not like", value, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoIn(List<String> values) {
			addCriterion("ChitNo in", values, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoNotIn(List<String> values) {
			addCriterion("ChitNo not in", values, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoBetween(String value1, String value2) {
			addCriterion("ChitNo between", value1, value2, "chitno");
			return (Criteria) this;
		}

		public Criteria andChitnoNotBetween(String value1, String value2) {
			addCriterion("ChitNo not between", value1, value2, "chitno");
			return (Criteria) this;
		}

		public Criteria andWastertypeIsNull() {
			addCriterion("WasterType is null");
			return (Criteria) this;
		}

		public Criteria andWastertypeIsNotNull() {
			addCriterion("WasterType is not null");
			return (Criteria) this;
		}

		public Criteria andWastertypeEqualTo(String value) {
			addCriterion("WasterType =", value, "wastertype");
			return (Criteria) this;
		}

		public Criteria andWastertypeNotEqualTo(String value) {
			addCriterion("WasterType <>", value, "wastertype");
			return (Criteria) this;
		}

		public Criteria andWastertypeGreaterThan(String value) {
			addCriterion("WasterType >", value, "wastertype");
			return (Criteria) this;
		}

		public Criteria andWastertypeGreaterThanOrEqualTo(String value) {
			addCriterion("WasterType >=", value, "wastertype");
			return (Criteria) this;
		}

		public Criteria andWastertypeLessThan(String value) {
			addCriterion("WasterType <", value, "wastertype");
			return (Criteria) this;
		}

		public Criteria andWastertypeLessThanOrEqualTo(String value) {
			addCriterion("WasterType <=", value, "wastertype");
			return (Criteria) this;
		}

		public Criteria andWastertypeLike(String value) {
			addCriterion("WasterType like", value, "wastertype");
			return (Criteria) this;
		}

		public Criteria andWastertypeNotLike(String value) {
			addCriterion("WasterType not like", value, "wastertype");
			return (Criteria) this;
		}

		public Criteria andWastertypeIn(List<String> values) {
			addCriterion("WasterType in", values, "wastertype");
			return (Criteria) this;
		}

		public Criteria andWastertypeNotIn(List<String> values) {
			addCriterion("WasterType not in", values, "wastertype");
			return (Criteria) this;
		}

		public Criteria andWastertypeBetween(String value1, String value2) {
			addCriterion("WasterType between", value1, value2, "wastertype");
			return (Criteria) this;
		}

		public Criteria andWastertypeNotBetween(String value1, String value2) {
			addCriterion("WasterType not between", value1, value2, "wastertype");
			return (Criteria) this;
		}

		public Criteria andHandlemethodIsNull() {
			addCriterion("HandleMethod is null");
			return (Criteria) this;
		}

		public Criteria andHandlemethodIsNotNull() {
			addCriterion("HandleMethod is not null");
			return (Criteria) this;
		}

		public Criteria andHandlemethodEqualTo(String value) {
			addCriterion("HandleMethod =", value, "handlemethod");
			return (Criteria) this;
		}

		public Criteria andHandlemethodNotEqualTo(String value) {
			addCriterion("HandleMethod <>", value, "handlemethod");
			return (Criteria) this;
		}

		public Criteria andHandlemethodGreaterThan(String value) {
			addCriterion("HandleMethod >", value, "handlemethod");
			return (Criteria) this;
		}

		public Criteria andHandlemethodGreaterThanOrEqualTo(String value) {
			addCriterion("HandleMethod >=", value, "handlemethod");
			return (Criteria) this;
		}

		public Criteria andHandlemethodLessThan(String value) {
			addCriterion("HandleMethod <", value, "handlemethod");
			return (Criteria) this;
		}

		public Criteria andHandlemethodLessThanOrEqualTo(String value) {
			addCriterion("HandleMethod <=", value, "handlemethod");
			return (Criteria) this;
		}

		public Criteria andHandlemethodLike(String value) {
			addCriterion("HandleMethod like", value, "handlemethod");
			return (Criteria) this;
		}

		public Criteria andHandlemethodNotLike(String value) {
			addCriterion("HandleMethod not like", value, "handlemethod");
			return (Criteria) this;
		}

		public Criteria andHandlemethodIn(List<String> values) {
			addCriterion("HandleMethod in", values, "handlemethod");
			return (Criteria) this;
		}

		public Criteria andHandlemethodNotIn(List<String> values) {
			addCriterion("HandleMethod not in", values, "handlemethod");
			return (Criteria) this;
		}

		public Criteria andHandlemethodBetween(String value1, String value2) {
			addCriterion("HandleMethod between", value1, value2, "handlemethod");
			return (Criteria) this;
		}

		public Criteria andHandlemethodNotBetween(String value1, String value2) {
			addCriterion("HandleMethod not between", value1, value2, "handlemethod");
			return (Criteria) this;
		}

		public Criteria andHandlelocationIsNull() {
			addCriterion("HandleLocation is null");
			return (Criteria) this;
		}

		public Criteria andHandlelocationIsNotNull() {
			addCriterion("HandleLocation is not null");
			return (Criteria) this;
		}

		public Criteria andHandlelocationEqualTo(String value) {
			addCriterion("HandleLocation =", value, "handlelocation");
			return (Criteria) this;
		}

		public Criteria andHandlelocationNotEqualTo(String value) {
			addCriterion("HandleLocation <>", value, "handlelocation");
			return (Criteria) this;
		}

		public Criteria andHandlelocationGreaterThan(String value) {
			addCriterion("HandleLocation >", value, "handlelocation");
			return (Criteria) this;
		}

		public Criteria andHandlelocationGreaterThanOrEqualTo(String value) {
			addCriterion("HandleLocation >=", value, "handlelocation");
			return (Criteria) this;
		}

		public Criteria andHandlelocationLessThan(String value) {
			addCriterion("HandleLocation <", value, "handlelocation");
			return (Criteria) this;
		}

		public Criteria andHandlelocationLessThanOrEqualTo(String value) {
			addCriterion("HandleLocation <=", value, "handlelocation");
			return (Criteria) this;
		}

		public Criteria andHandlelocationLike(String value) {
			addCriterion("HandleLocation like", value, "handlelocation");
			return (Criteria) this;
		}

		public Criteria andHandlelocationNotLike(String value) {
			addCriterion("HandleLocation not like", value, "handlelocation");
			return (Criteria) this;
		}

		public Criteria andHandlelocationIn(List<String> values) {
			addCriterion("HandleLocation in", values, "handlelocation");
			return (Criteria) this;
		}

		public Criteria andHandlelocationNotIn(List<String> values) {
			addCriterion("HandleLocation not in", values, "handlelocation");
			return (Criteria) this;
		}

		public Criteria andHandlelocationBetween(String value1, String value2) {
			addCriterion("HandleLocation between", value1, value2, "handlelocation");
			return (Criteria) this;
		}

		public Criteria andHandlelocationNotBetween(String value1, String value2) {
			addCriterion("HandleLocation not between", value1, value2, "handlelocation");
			return (Criteria) this;
		}

		public Criteria andTransportationtypeIsNull() {
			addCriterion("TransportationType is null");
			return (Criteria) this;
		}

		public Criteria andTransportationtypeIsNotNull() {
			addCriterion("TransportationType is not null");
			return (Criteria) this;
		}

		public Criteria andTransportationtypeEqualTo(String value) {
			addCriterion("TransportationType =", value, "transportationtype");
			return (Criteria) this;
		}

		public Criteria andTransportationtypeNotEqualTo(String value) {
			addCriterion("TransportationType <>", value, "transportationtype");
			return (Criteria) this;
		}

		public Criteria andTransportationtypeGreaterThan(String value) {
			addCriterion("TransportationType >", value, "transportationtype");
			return (Criteria) this;
		}

		public Criteria andTransportationtypeGreaterThanOrEqualTo(String value) {
			addCriterion("TransportationType >=", value, "transportationtype");
			return (Criteria) this;
		}

		public Criteria andTransportationtypeLessThan(String value) {
			addCriterion("TransportationType <", value, "transportationtype");
			return (Criteria) this;
		}

		public Criteria andTransportationtypeLessThanOrEqualTo(String value) {
			addCriterion("TransportationType <=", value, "transportationtype");
			return (Criteria) this;
		}

		public Criteria andTransportationtypeLike(String value) {
			addCriterion("TransportationType like", value, "transportationtype");
			return (Criteria) this;
		}

		public Criteria andTransportationtypeNotLike(String value) {
			addCriterion("TransportationType not like", value, "transportationtype");
			return (Criteria) this;
		}

		public Criteria andTransportationtypeIn(List<String> values) {
			addCriterion("TransportationType in", values, "transportationtype");
			return (Criteria) this;
		}

		public Criteria andTransportationtypeNotIn(List<String> values) {
			addCriterion("TransportationType not in", values, "transportationtype");
			return (Criteria) this;
		}

		public Criteria andTransportationtypeBetween(String value1, String value2) {
			addCriterion("TransportationType between", value1, value2, "transportationtype");
			return (Criteria) this;
		}

		public Criteria andTransportationtypeNotBetween(String value1, String value2) {
			addCriterion("TransportationType not between", value1, value2, "transportationtype");
			return (Criteria) this;
		}

		public Criteria andQtyIsNull() {
			addCriterion("Qty is null");
			return (Criteria) this;
		}

		public Criteria andQtyIsNotNull() {
			addCriterion("Qty is not null");
			return (Criteria) this;
		}

		public Criteria andQtyEqualTo(BigDecimal value) {
			addCriterion("Qty =", value, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyNotEqualTo(BigDecimal value) {
			addCriterion("Qty <>", value, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyGreaterThan(BigDecimal value) {
			addCriterion("Qty >", value, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("Qty >=", value, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyLessThan(BigDecimal value) {
			addCriterion("Qty <", value, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyLessThanOrEqualTo(BigDecimal value) {
			addCriterion("Qty <=", value, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyIn(List<BigDecimal> values) {
			addCriterion("Qty in", values, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyNotIn(List<BigDecimal> values) {
			addCriterion("Qty not in", values, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("Qty between", value1, value2, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("Qty not between", value1, value2, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyUnitIsNull() {
			addCriterion("Qty_Unit is null");
			return (Criteria) this;
		}

		public Criteria andQtyUnitIsNotNull() {
			addCriterion("Qty_Unit is not null");
			return (Criteria) this;
		}

		public Criteria andQtyUnitEqualTo(String value) {
			addCriterion("Qty_Unit =", value, "qtyUnit");
			return (Criteria) this;
		}

		public Criteria andQtyUnitNotEqualTo(String value) {
			addCriterion("Qty_Unit <>", value, "qtyUnit");
			return (Criteria) this;
		}

		public Criteria andQtyUnitGreaterThan(String value) {
			addCriterion("Qty_Unit >", value, "qtyUnit");
			return (Criteria) this;
		}

		public Criteria andQtyUnitGreaterThanOrEqualTo(String value) {
			addCriterion("Qty_Unit >=", value, "qtyUnit");
			return (Criteria) this;
		}

		public Criteria andQtyUnitLessThan(String value) {
			addCriterion("Qty_Unit <", value, "qtyUnit");
			return (Criteria) this;
		}

		public Criteria andQtyUnitLessThanOrEqualTo(String value) {
			addCriterion("Qty_Unit <=", value, "qtyUnit");
			return (Criteria) this;
		}

		public Criteria andQtyUnitLike(String value) {
			addCriterion("Qty_Unit like", value, "qtyUnit");
			return (Criteria) this;
		}

		public Criteria andQtyUnitNotLike(String value) {
			addCriterion("Qty_Unit not like", value, "qtyUnit");
			return (Criteria) this;
		}

		public Criteria andQtyUnitIn(List<String> values) {
			addCriterion("Qty_Unit in", values, "qtyUnit");
			return (Criteria) this;
		}

		public Criteria andQtyUnitNotIn(List<String> values) {
			addCriterion("Qty_Unit not in", values, "qtyUnit");
			return (Criteria) this;
		}

		public Criteria andQtyUnitBetween(String value1, String value2) {
			addCriterion("Qty_Unit between", value1, value2, "qtyUnit");
			return (Criteria) this;
		}

		public Criteria andQtyUnitNotBetween(String value1, String value2) {
			addCriterion("Qty_Unit not between", value1, value2, "qtyUnit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorIsNull() {
			addCriterion("TransportFactor is null");
			return (Criteria) this;
		}

		public Criteria andTransportfactorIsNotNull() {
			addCriterion("TransportFactor is not null");
			return (Criteria) this;
		}

		public Criteria andTransportfactorEqualTo(BigDecimal value) {
			addCriterion("TransportFactor =", value, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorNotEqualTo(BigDecimal value) {
			addCriterion("TransportFactor <>", value, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorGreaterThan(BigDecimal value) {
			addCriterion("TransportFactor >", value, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("TransportFactor >=", value, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorLessThan(BigDecimal value) {
			addCriterion("TransportFactor <", value, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorLessThanOrEqualTo(BigDecimal value) {
			addCriterion("TransportFactor <=", value, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorIn(List<BigDecimal> values) {
			addCriterion("TransportFactor in", values, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorNotIn(List<BigDecimal> values) {
			addCriterion("TransportFactor not in", values, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("TransportFactor between", value1, value2, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("TransportFactor not between", value1, value2, "transportfactor");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitIsNull() {
			addCriterion("TransportFactorUnit is null");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitIsNotNull() {
			addCriterion("TransportFactorUnit is not null");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitEqualTo(String value) {
			addCriterion("TransportFactorUnit =", value, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitNotEqualTo(String value) {
			addCriterion("TransportFactorUnit <>", value, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitGreaterThan(String value) {
			addCriterion("TransportFactorUnit >", value, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitGreaterThanOrEqualTo(String value) {
			addCriterion("TransportFactorUnit >=", value, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitLessThan(String value) {
			addCriterion("TransportFactorUnit <", value, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitLessThanOrEqualTo(String value) {
			addCriterion("TransportFactorUnit <=", value, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitLike(String value) {
			addCriterion("TransportFactorUnit like", value, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitNotLike(String value) {
			addCriterion("TransportFactorUnit not like", value, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitIn(List<String> values) {
			addCriterion("TransportFactorUnit in", values, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitNotIn(List<String> values) {
			addCriterion("TransportFactorUnit not in", values, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitBetween(String value1, String value2) {
			addCriterion("TransportFactorUnit between", value1, value2, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportfactorunitNotBetween(String value1, String value2) {
			addCriterion("TransportFactorUnit not between", value1, value2, "transportfactorunit");
			return (Criteria) this;
		}

		public Criteria andTransportscopeIsNull() {
			addCriterion("TransportScope is null");
			return (Criteria) this;
		}

		public Criteria andTransportscopeIsNotNull() {
			addCriterion("TransportScope is not null");
			return (Criteria) this;
		}

		public Criteria andTransportscopeEqualTo(String value) {
			addCriterion("TransportScope =", value, "transportscope");
			return (Criteria) this;
		}

		public Criteria andTransportscopeNotEqualTo(String value) {
			addCriterion("TransportScope <>", value, "transportscope");
			return (Criteria) this;
		}

		public Criteria andTransportscopeGreaterThan(String value) {
			addCriterion("TransportScope >", value, "transportscope");
			return (Criteria) this;
		}

		public Criteria andTransportscopeGreaterThanOrEqualTo(String value) {
			addCriterion("TransportScope >=", value, "transportscope");
			return (Criteria) this;
		}

		public Criteria andTransportscopeLessThan(String value) {
			addCriterion("TransportScope <", value, "transportscope");
			return (Criteria) this;
		}

		public Criteria andTransportscopeLessThanOrEqualTo(String value) {
			addCriterion("TransportScope <=", value, "transportscope");
			return (Criteria) this;
		}

		public Criteria andTransportscopeLike(String value) {
			addCriterion("TransportScope like", value, "transportscope");
			return (Criteria) this;
		}

		public Criteria andTransportscopeNotLike(String value) {
			addCriterion("TransportScope not like", value, "transportscope");
			return (Criteria) this;
		}

		public Criteria andTransportscopeIn(List<String> values) {
			addCriterion("TransportScope in", values, "transportscope");
			return (Criteria) this;
		}

		public Criteria andTransportscopeNotIn(List<String> values) {
			addCriterion("TransportScope not in", values, "transportscope");
			return (Criteria) this;
		}

		public Criteria andTransportscopeBetween(String value1, String value2) {
			addCriterion("TransportScope between", value1, value2, "transportscope");
			return (Criteria) this;
		}

		public Criteria andTransportscopeNotBetween(String value1, String value2) {
			addCriterion("TransportScope not between", value1, value2, "transportscope");
			return (Criteria) this;
		}

		public Criteria andDatasourceIsNull() {
			addCriterion("DataSource is null");
			return (Criteria) this;
		}

		public Criteria andDatasourceIsNotNull() {
			addCriterion("DataSource is not null");
			return (Criteria) this;
		}

		public Criteria andDatasourceEqualTo(String value) {
			addCriterion("DataSource =", value, "datasource");
			return (Criteria) this;
		}

		public Criteria andDatasourceNotEqualTo(String value) {
			addCriterion("DataSource <>", value, "datasource");
			return (Criteria) this;
		}

		public Criteria andDatasourceGreaterThan(String value) {
			addCriterion("DataSource >", value, "datasource");
			return (Criteria) this;
		}

		public Criteria andDatasourceGreaterThanOrEqualTo(String value) {
			addCriterion("DataSource >=", value, "datasource");
			return (Criteria) this;
		}

		public Criteria andDatasourceLessThan(String value) {
			addCriterion("DataSource <", value, "datasource");
			return (Criteria) this;
		}

		public Criteria andDatasourceLessThanOrEqualTo(String value) {
			addCriterion("DataSource <=", value, "datasource");
			return (Criteria) this;
		}

		public Criteria andDatasourceLike(String value) {
			addCriterion("DataSource like", value, "datasource");
			return (Criteria) this;
		}

		public Criteria andDatasourceNotLike(String value) {
			addCriterion("DataSource not like", value, "datasource");
			return (Criteria) this;
		}

		public Criteria andDatasourceIn(List<String> values) {
			addCriterion("DataSource in", values, "datasource");
			return (Criteria) this;
		}

		public Criteria andDatasourceNotIn(List<String> values) {
			addCriterion("DataSource not in", values, "datasource");
			return (Criteria) this;
		}

		public Criteria andDatasourceBetween(String value1, String value2) {
			addCriterion("DataSource between", value1, value2, "datasource");
			return (Criteria) this;
		}

		public Criteria andDatasourceNotBetween(String value1, String value2) {
			addCriterion("DataSource not between", value1, value2, "datasource");
			return (Criteria) this;
		}

		public Criteria andDatasourceidIsNull() {
			addCriterion("DataSourceId is null");
			return (Criteria) this;
		}

		public Criteria andDatasourceidIsNotNull() {
			addCriterion("DataSourceId is not null");
			return (Criteria) this;
		}

		public Criteria andDatasourceidEqualTo(String value) {
			addCriterion("DataSourceId =", value, "datasourceid");
			return (Criteria) this;
		}

		public Criteria andDatasourceidNotEqualTo(String value) {
			addCriterion("DataSourceId <>", value, "datasourceid");
			return (Criteria) this;
		}

		public Criteria andDatasourceidGreaterThan(String value) {
			addCriterion("DataSourceId >", value, "datasourceid");
			return (Criteria) this;
		}

		public Criteria andDatasourceidGreaterThanOrEqualTo(String value) {
			addCriterion("DataSourceId >=", value, "datasourceid");
			return (Criteria) this;
		}

		public Criteria andDatasourceidLessThan(String value) {
			addCriterion("DataSourceId <", value, "datasourceid");
			return (Criteria) this;
		}

		public Criteria andDatasourceidLessThanOrEqualTo(String value) {
			addCriterion("DataSourceId <=", value, "datasourceid");
			return (Criteria) this;
		}

		public Criteria andDatasourceidLike(String value) {
			addCriterion("DataSourceId like", value, "datasourceid");
			return (Criteria) this;
		}

		public Criteria andDatasourceidNotLike(String value) {
			addCriterion("DataSourceId not like", value, "datasourceid");
			return (Criteria) this;
		}

		public Criteria andDatasourceidIn(List<String> values) {
			addCriterion("DataSourceId in", values, "datasourceid");
			return (Criteria) this;
		}

		public Criteria andDatasourceidNotIn(List<String> values) {
			addCriterion("DataSourceId not in", values, "datasourceid");
			return (Criteria) this;
		}

		public Criteria andDatasourceidBetween(String value1, String value2) {
			addCriterion("DataSourceId between", value1, value2, "datasourceid");
			return (Criteria) this;
		}

		public Criteria andDatasourceidNotBetween(String value1, String value2) {
			addCriterion("DataSourceId not between", value1, value2, "datasourceid");
			return (Criteria) this;
		}

		public Criteria andRawdatasourceidIsNull() {
			addCriterion("RawDataSourceId is null");
			return (Criteria) this;
		}

		public Criteria andRawdatasourceidIsNotNull() {
			addCriterion("RawDataSourceId is not null");
			return (Criteria) this;
		}

		public Criteria andRawdatasourceidEqualTo(String value) {
			addCriterion("RawDataSourceId =", value, "rawdatasourceid");
			return (Criteria) this;
		}

		public Criteria andRawdatasourceidNotEqualTo(String value) {
			addCriterion("RawDataSourceId <>", value, "rawdatasourceid");
			return (Criteria) this;
		}

		public Criteria andRawdatasourceidGreaterThan(String value) {
			addCriterion("RawDataSourceId >", value, "rawdatasourceid");
			return (Criteria) this;
		}

		public Criteria andRawdatasourceidGreaterThanOrEqualTo(String value) {
			addCriterion("RawDataSourceId >=", value, "rawdatasourceid");
			return (Criteria) this;
		}

		public Criteria andRawdatasourceidLessThan(String value) {
			addCriterion("RawDataSourceId <", value, "rawdatasourceid");
			return (Criteria) this;
		}

		public Criteria andRawdatasourceidLessThanOrEqualTo(String value) {
			addCriterion("RawDataSourceId <=", value, "rawdatasourceid");
			return (Criteria) this;
		}

		public Criteria andRawdatasourceidLike(String value) {
			addCriterion("RawDataSourceId like", value, "rawdatasourceid");
			return (Criteria) this;
		}

		public Criteria andRawdatasourceidNotLike(String value) {
			addCriterion("RawDataSourceId not like", value, "rawdatasourceid");
			return (Criteria) this;
		}

		public Criteria andRawdatasourceidIn(List<String> values) {
			addCriterion("RawDataSourceId in", values, "rawdatasourceid");
			return (Criteria) this;
		}

		public Criteria andRawdatasourceidNotIn(List<String> values) {
			addCriterion("RawDataSourceId not in", values, "rawdatasourceid");
			return (Criteria) this;
		}

		public Criteria andRawdatasourceidBetween(String value1, String value2) {
			addCriterion("RawDataSourceId between", value1, value2, "rawdatasourceid");
			return (Criteria) this;
		}

		public Criteria andRawdatasourceidNotBetween(String value1, String value2) {
			addCriterion("RawDataSourceId not between", value1, value2, "rawdatasourceid");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNull() {
			addCriterion("CreatedBy is null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNotNull() {
			addCriterion("CreatedBy is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyEqualTo(String value) {
			addCriterion("CreatedBy =", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotEqualTo(String value) {
			addCriterion("CreatedBy <>", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThan(String value) {
			addCriterion("CreatedBy >", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
			addCriterion("CreatedBy >=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThan(String value) {
			addCriterion("CreatedBy <", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThanOrEqualTo(String value) {
			addCriterion("CreatedBy <=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLike(String value) {
			addCriterion("CreatedBy like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotLike(String value) {
			addCriterion("CreatedBy not like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIn(List<String> values) {
			addCriterion("CreatedBy in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotIn(List<String> values) {
			addCriterion("CreatedBy not in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyBetween(String value1, String value2) {
			addCriterion("CreatedBy between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotBetween(String value1, String value2) {
			addCriterion("CreatedBy not between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNull() {
			addCriterion("CreatedTime is null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNotNull() {
			addCriterion("CreatedTime is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime =", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <>", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThan(LocalDateTime value) {
			addCriterion("CreatedTime >", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime >=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThan(LocalDateTime value) {
			addCriterion("CreatedTime <", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime not in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime not between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNull() {
			addCriterion("DeletedBy is null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNotNull() {
			addCriterion("DeletedBy is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyEqualTo(String value) {
			addCriterion("DeletedBy =", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotEqualTo(String value) {
			addCriterion("DeletedBy <>", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThan(String value) {
			addCriterion("DeletedBy >", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThanOrEqualTo(String value) {
			addCriterion("DeletedBy >=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThan(String value) {
			addCriterion("DeletedBy <", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThanOrEqualTo(String value) {
			addCriterion("DeletedBy <=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLike(String value) {
			addCriterion("DeletedBy like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotLike(String value) {
			addCriterion("DeletedBy not like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIn(List<String> values) {
			addCriterion("DeletedBy in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotIn(List<String> values) {
			addCriterion("DeletedBy not in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyBetween(String value1, String value2) {
			addCriterion("DeletedBy between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotBetween(String value1, String value2) {
			addCriterion("DeletedBy not between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNull() {
			addCriterion("DeletedTime is null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNotNull() {
			addCriterion("DeletedTime is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime =", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <>", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThan(LocalDateTime value) {
			addCriterion("DeletedTime >", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime >=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThan(LocalDateTime value) {
			addCriterion("DeletedTime <", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime not in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime not between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNull() {
			addCriterion("IsDeleted is null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNotNull() {
			addCriterion("IsDeleted is not null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedEqualTo(Boolean value) {
			addCriterion("IsDeleted =", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotEqualTo(Boolean value) {
			addCriterion("IsDeleted <>", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThan(Boolean value) {
			addCriterion("IsDeleted >", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted >=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThan(Boolean value) {
			addCriterion("IsDeleted <", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted <=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIn(List<Boolean> values) {
			addCriterion("IsDeleted in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotIn(List<Boolean> values) {
			addCriterion("IsDeleted not in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted between", value1, value2, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted not between", value1, value2, "isdeleted");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
	 * @mbg.generated  Fri Apr 21 10:13:35 HKT 2023
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table F_CDMS_WasteTransportationCarbonFactor
     *
     * @mbg.generated do_not_delete_during_merge Tue Oct 25 12:02:18 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}