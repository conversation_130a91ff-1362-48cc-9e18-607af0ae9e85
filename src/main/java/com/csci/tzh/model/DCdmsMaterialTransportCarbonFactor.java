package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class DCdmsMaterialTransportCarbonFactor {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.ChineseName
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private String chinesename;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.MaterialCode
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private String materialcode;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.MaterialAttribute
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private String materialattribute;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.Unit
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private String unit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.Description
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private String description;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.CarbonEmissionLocation
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private String carbonemissionlocation;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.CarbonFactor
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private BigDecimal carbonfactor;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.CarbonFactorUnit
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private String carbonfactorunit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.TransportFactor
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private BigDecimal transportfactor;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.TransportFactorUnit
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private String transportfactorunit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.TransportDistance
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private BigDecimal transportdistance;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.TransportDistanceUnit
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private String transportdistanceunit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.Scope
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private String scope;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.CreatedBy
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private String createdby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.CreatedTime
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private LocalDateTime createdtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.DeletedBy
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private String deletedby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.DeletedTime
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private LocalDateTime deletedtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column D_CDMS_MaterialTransportCarbonFactor.IsDeleted
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	private Boolean isdeleted;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.ChineseName
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.ChineseName
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public String getChinesename() {
		return chinesename;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.ChineseName
	 * @param chinesename  the value for D_CDMS_MaterialTransportCarbonFactor.ChineseName
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setChinesename(String chinesename) {
		this.chinesename = chinesename == null ? null : chinesename.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.MaterialCode
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.MaterialCode
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public String getMaterialcode() {
		return materialcode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.MaterialCode
	 * @param materialcode  the value for D_CDMS_MaterialTransportCarbonFactor.MaterialCode
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setMaterialcode(String materialcode) {
		this.materialcode = materialcode == null ? null : materialcode.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.MaterialAttribute
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.MaterialAttribute
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public String getMaterialattribute() {
		return materialattribute;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.MaterialAttribute
	 * @param materialattribute  the value for D_CDMS_MaterialTransportCarbonFactor.MaterialAttribute
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setMaterialattribute(String materialattribute) {
		this.materialattribute = materialattribute == null ? null : materialattribute.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.Unit
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.Unit
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public String getUnit() {
		return unit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.Unit
	 * @param unit  the value for D_CDMS_MaterialTransportCarbonFactor.Unit
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setUnit(String unit) {
		this.unit = unit == null ? null : unit.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.Description
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.Description
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.Description
	 * @param description  the value for D_CDMS_MaterialTransportCarbonFactor.Description
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setDescription(String description) {
		this.description = description == null ? null : description.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.CarbonEmissionLocation
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.CarbonEmissionLocation
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public String getCarbonemissionlocation() {
		return carbonemissionlocation;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.CarbonEmissionLocation
	 * @param carbonemissionlocation  the value for D_CDMS_MaterialTransportCarbonFactor.CarbonEmissionLocation
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setCarbonemissionlocation(String carbonemissionlocation) {
		this.carbonemissionlocation = carbonemissionlocation == null ? null : carbonemissionlocation.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.CarbonFactor
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.CarbonFactor
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public BigDecimal getCarbonfactor() {
		return carbonfactor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.CarbonFactor
	 * @param carbonfactor  the value for D_CDMS_MaterialTransportCarbonFactor.CarbonFactor
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setCarbonfactor(BigDecimal carbonfactor) {
		this.carbonfactor = carbonfactor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.CarbonFactorUnit
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.CarbonFactorUnit
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public String getCarbonfactorunit() {
		return carbonfactorunit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.CarbonFactorUnit
	 * @param carbonfactorunit  the value for D_CDMS_MaterialTransportCarbonFactor.CarbonFactorUnit
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setCarbonfactorunit(String carbonfactorunit) {
		this.carbonfactorunit = carbonfactorunit == null ? null : carbonfactorunit.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.TransportFactor
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.TransportFactor
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public BigDecimal getTransportfactor() {
		return transportfactor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.TransportFactor
	 * @param transportfactor  the value for D_CDMS_MaterialTransportCarbonFactor.TransportFactor
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setTransportfactor(BigDecimal transportfactor) {
		this.transportfactor = transportfactor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.TransportFactorUnit
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.TransportFactorUnit
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public String getTransportfactorunit() {
		return transportfactorunit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.TransportFactorUnit
	 * @param transportfactorunit  the value for D_CDMS_MaterialTransportCarbonFactor.TransportFactorUnit
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setTransportfactorunit(String transportfactorunit) {
		this.transportfactorunit = transportfactorunit == null ? null : transportfactorunit.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.TransportDistance
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.TransportDistance
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public BigDecimal getTransportdistance() {
		return transportdistance;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.TransportDistance
	 * @param transportdistance  the value for D_CDMS_MaterialTransportCarbonFactor.TransportDistance
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setTransportdistance(BigDecimal transportdistance) {
		this.transportdistance = transportdistance;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.TransportDistanceUnit
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.TransportDistanceUnit
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public String getTransportdistanceunit() {
		return transportdistanceunit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.TransportDistanceUnit
	 * @param transportdistanceunit  the value for D_CDMS_MaterialTransportCarbonFactor.TransportDistanceUnit
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setTransportdistanceunit(String transportdistanceunit) {
		this.transportdistanceunit = transportdistanceunit == null ? null : transportdistanceunit.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.Scope
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.Scope
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public String getScope() {
		return scope;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.Scope
	 * @param scope  the value for D_CDMS_MaterialTransportCarbonFactor.Scope
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setScope(String scope) {
		this.scope = scope == null ? null : scope.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.CreatedBy
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.CreatedBy
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public String getCreatedby() {
		return createdby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.CreatedBy
	 * @param createdby  the value for D_CDMS_MaterialTransportCarbonFactor.CreatedBy
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setCreatedby(String createdby) {
		this.createdby = createdby == null ? null : createdby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.CreatedTime
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.CreatedTime
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public LocalDateTime getCreatedtime() {
		return createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.CreatedTime
	 * @param createdtime  the value for D_CDMS_MaterialTransportCarbonFactor.CreatedTime
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setCreatedtime(LocalDateTime createdtime) {
		this.createdtime = createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.DeletedBy
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.DeletedBy
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public String getDeletedby() {
		return deletedby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.DeletedBy
	 * @param deletedby  the value for D_CDMS_MaterialTransportCarbonFactor.DeletedBy
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setDeletedby(String deletedby) {
		this.deletedby = deletedby == null ? null : deletedby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.DeletedTime
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.DeletedTime
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public LocalDateTime getDeletedtime() {
		return deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.DeletedTime
	 * @param deletedtime  the value for D_CDMS_MaterialTransportCarbonFactor.DeletedTime
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setDeletedtime(LocalDateTime deletedtime) {
		this.deletedtime = deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column D_CDMS_MaterialTransportCarbonFactor.IsDeleted
	 * @return  the value of D_CDMS_MaterialTransportCarbonFactor.IsDeleted
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public Boolean getIsdeleted() {
		return isdeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column D_CDMS_MaterialTransportCarbonFactor.IsDeleted
	 * @param isdeleted  the value for D_CDMS_MaterialTransportCarbonFactor.IsDeleted
	 * @mbg.generated  Tue Jan 03 10:04:49 HKT 2023
	 */
	public void setIsdeleted(Boolean isdeleted) {
		this.isdeleted = isdeleted;
	}
}