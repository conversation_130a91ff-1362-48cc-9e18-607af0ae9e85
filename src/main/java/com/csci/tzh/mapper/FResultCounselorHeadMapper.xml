<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.FResultCounselorHeadMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.FResultCounselorHead">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 07 11:36:50 CST 2025.
    -->
    <id column="id" jdbcType="NVARCHAR" property="id" />
    <result column="organization_id" jdbcType="NVARCHAR" property="organizationId" />
    <result column="organization_name" jdbcType="NVARCHAR" property="organizationName" />
    <result column="protocol" jdbcType="NVARCHAR" property="protocol" />
    <result column="record_year_month" jdbcType="INTEGER" property="recordYearMonth" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="NVARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="NVARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
    <result column="record_year" jdbcType="INTEGER" property="recordYear" />
    <result column="record_month" jdbcType="INTEGER" property="recordMonth" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 07 11:36:50 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 07 11:36:50 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 07 11:36:50 CST 2025.
    -->
    id, organization_id, organization_name, protocol, record_year_month, is_active, creation_time, 
    create_username, create_user_id, last_update_time, last_update_username, last_update_user_id, 
    last_update_version, record_year, record_month
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.FResultCounselorHeadExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 07 11:36:50 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from f_result_counselor_head
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 07 11:36:50 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    from f_result_counselor_head
    where id = #{id,jdbcType=NVARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 07 11:36:50 CST 2025.
    -->
    delete from f_result_counselor_head
    where id = #{id,jdbcType=NVARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.FResultCounselorHeadExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 07 11:36:50 CST 2025.
    -->
    delete from f_result_counselor_head
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.FResultCounselorHead">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 07 11:36:50 CST 2025.
    -->
    insert into f_result_counselor_head (id, organization_id, organization_name, 
      protocol, record_year_month, is_active, 
      creation_time, create_username, create_user_id, 
      last_update_time, last_update_username, 
      last_update_user_id, last_update_version, record_year, 
      record_month)
    values (#{id,jdbcType=NVARCHAR}, #{organizationId,jdbcType=NVARCHAR}, #{organizationName,jdbcType=NVARCHAR}, 
      #{protocol,jdbcType=NVARCHAR}, #{recordYearMonth,jdbcType=INTEGER}, #{isActive,jdbcType=BIT}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{createUsername,jdbcType=NVARCHAR}, #{createUserId,jdbcType=NVARCHAR}, 
      #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUsername,jdbcType=NVARCHAR}, 
      #{lastUpdateUserId,jdbcType=NVARCHAR}, #{lastUpdateVersion,jdbcType=INTEGER}, #{recordYear,jdbcType=INTEGER}, 
      #{recordMonth,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.FResultCounselorHead">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 07 11:36:50 CST 2025.
    -->
    insert into f_result_counselor_head
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="organizationId != null">
        organization_id,
      </if>
      <if test="organizationName != null">
        organization_name,
      </if>
      <if test="protocol != null">
        protocol,
      </if>
      <if test="recordYearMonth != null">
        record_year_month,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
      <if test="recordYear != null">
        record_year,
      </if>
      <if test="recordMonth != null">
        record_month,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=NVARCHAR},
      </if>
      <if test="organizationId != null">
        #{organizationId,jdbcType=NVARCHAR},
      </if>
      <if test="organizationName != null">
        #{organizationName,jdbcType=NVARCHAR},
      </if>
      <if test="protocol != null">
        #{protocol,jdbcType=NVARCHAR},
      </if>
      <if test="recordYearMonth != null">
        #{recordYearMonth,jdbcType=INTEGER},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="recordYear != null">
        #{recordYear,jdbcType=INTEGER},
      </if>
      <if test="recordMonth != null">
        #{recordMonth,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.FResultCounselorHeadExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 07 11:36:50 CST 2025.
    -->
    select count(*) from f_result_counselor_head
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 07 11:36:50 CST 2025.
    -->
    update f_result_counselor_head
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=NVARCHAR},
      </if>
      <if test="record.organizationId != null">
        organization_id = #{record.organizationId,jdbcType=NVARCHAR},
      </if>
      <if test="record.organizationName != null">
        organization_name = #{record.organizationName,jdbcType=NVARCHAR},
      </if>
      <if test="record.protocol != null">
        protocol = #{record.protocol,jdbcType=NVARCHAR},
      </if>
      <if test="record.recordYearMonth != null">
        record_year_month = #{record.recordYearMonth,jdbcType=INTEGER},
      </if>
      <if test="record.isActive != null">
        is_active = #{record.isActive,jdbcType=BIT},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateVersion != null">
        last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="record.recordYear != null">
        record_year = #{record.recordYear,jdbcType=INTEGER},
      </if>
      <if test="record.recordMonth != null">
        record_month = #{record.recordMonth,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 07 11:36:50 CST 2025.
    -->
    update f_result_counselor_head
    set id = #{record.id,jdbcType=NVARCHAR},
      organization_id = #{record.organizationId,jdbcType=NVARCHAR},
      organization_name = #{record.organizationName,jdbcType=NVARCHAR},
      protocol = #{record.protocol,jdbcType=NVARCHAR},
      record_year_month = #{record.recordYearMonth,jdbcType=INTEGER},
      is_active = #{record.isActive,jdbcType=BIT},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=NVARCHAR},
      create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      record_year = #{record.recordYear,jdbcType=INTEGER},
      record_month = #{record.recordMonth,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.tzh.model.FResultCounselorHead">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 07 11:36:50 CST 2025.
    -->
    update f_result_counselor_head
    <set>
      <if test="organizationId != null">
        organization_id = #{organizationId,jdbcType=NVARCHAR},
      </if>
      <if test="organizationName != null">
        organization_name = #{organizationName,jdbcType=NVARCHAR},
      </if>
      <if test="protocol != null">
        protocol = #{protocol,jdbcType=NVARCHAR},
      </if>
      <if test="recordYearMonth != null">
        record_year_month = #{recordYearMonth,jdbcType=INTEGER},
      </if>
      <if test="isActive != null">
        is_active = #{isActive,jdbcType=BIT},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="recordYear != null">
        record_year = #{recordYear,jdbcType=INTEGER},
      </if>
      <if test="recordMonth != null">
        record_month = #{recordMonth,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=NVARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.tzh.model.FResultCounselorHead">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 07 11:36:50 CST 2025.
    -->
    update f_result_counselor_head
    set organization_id = #{organizationId,jdbcType=NVARCHAR},
      organization_name = #{organizationName,jdbcType=NVARCHAR},
      protocol = #{protocol,jdbcType=NVARCHAR},
      record_year_month = #{recordYearMonth,jdbcType=INTEGER},
      is_active = #{isActive,jdbcType=BIT},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=NVARCHAR},
      create_user_id = #{createUserId,jdbcType=NVARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      record_year = #{recordYear,jdbcType=INTEGER},
      record_month = #{recordMonth,jdbcType=INTEGER}
    where id = #{id,jdbcType=NVARCHAR}
  </update>
</mapper>