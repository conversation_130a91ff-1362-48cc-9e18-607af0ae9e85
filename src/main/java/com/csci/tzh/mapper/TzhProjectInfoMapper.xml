<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.TzhProjectInfoMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.TzhProjectInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:02:18 CST 2025.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="Code" jdbcType="NVARCHAR" property="code" />
    <result column="Name" jdbcType="NVARCHAR" property="name" />
    <result column="Type" jdbcType="NVARCHAR" property="type" />
    <result column="IsConsumptionMeasurable" jdbcType="NVARCHAR" property="isconsumptionmeasurable" />
    <result column="IsPeriodicallyReportable" jdbcType="NVARCHAR" property="isperiodicallyreportable" />
    <result column="InvestmentTotal" jdbcType="NUMERIC" property="investmenttotal" />
    <result column="ContractAmount" jdbcType="NUMERIC" property="contractamount" />
    <result column="Area" jdbcType="NUMERIC" property="area" />
    <result column="StartDate" jdbcType="DATE" property="startdate" />
    <result column="EndDate" jdbcType="DATE" property="enddate" />
    <result column="Address" jdbcType="NVARCHAR" property="address" />
    <result column="Contractor" jdbcType="NVARCHAR" property="contractor" />
    <result column="Owner" jdbcType="NVARCHAR" property="owner" />
    <result column="Architect" jdbcType="NVARCHAR" property="architect" />
    <result column="Supervisor" jdbcType="NVARCHAR" property="supervisor" />
    <result column="ManagerZhtAccount" jdbcType="NVARCHAR" property="managerzhtaccount" />
    <result column="Longitude" jdbcType="NVARCHAR" property="longitude" />
    <result column="Latitude" jdbcType="NVARCHAR" property="latitude" />
    <result column="RegionId" jdbcType="CHAR" property="regionid" />
    <result column="CreatedBy" jdbcType="VARCHAR" property="createdby" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
    <result column="DeletedBy" jdbcType="VARCHAR" property="deletedby" />
    <result column="DeletedTime" jdbcType="TIMESTAMP" property="deletedtime" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
    <result column="Province" jdbcType="NVARCHAR" property="province" />
    <result column="City" jdbcType="NVARCHAR" property="city" />
    <result column="District" jdbcType="NVARCHAR" property="district" />
    <result column="MajorProjectNum" jdbcType="INTEGER" property="majorprojectnum" />
    <result column="SiteId" jdbcType="NVARCHAR" property="siteid" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:02:18 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:02:18 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:02:18 CST 2025.
    -->
    Id, Code, Name, Type, IsConsumptionMeasurable, IsPeriodicallyReportable, InvestmentTotal, 
    ContractAmount, Area, StartDate, EndDate, Address, Contractor, Owner, Architect, 
    Supervisor, ManagerZhtAccount, Longitude, Latitude, RegionId, CreatedBy, CreatedTime, 
    DeletedBy, DeletedTime, IsDeleted, Province, City, District, MajorProjectNum, SiteId
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.TzhProjectInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:02:18 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_ProjectInfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.TzhProjectInfoExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:02:18 CST 2025.
    -->
    delete from Tzh_ProjectInfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.TzhProjectInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:02:18 CST 2025.
    -->
    insert into Tzh_ProjectInfo (Id, Code, Name, 
      Type, IsConsumptionMeasurable, IsPeriodicallyReportable, 
      InvestmentTotal, ContractAmount, Area, 
      StartDate, EndDate, Address, 
      Contractor, Owner, Architect, 
      Supervisor, ManagerZhtAccount, Longitude, 
      Latitude, RegionId, CreatedBy, 
      CreatedTime, DeletedBy, DeletedTime, 
      IsDeleted, Province, City, 
      District, MajorProjectNum, SiteId
      )
    values (#{id,jdbcType=CHAR}, #{code,jdbcType=NVARCHAR}, #{name,jdbcType=NVARCHAR}, 
      #{type,jdbcType=NVARCHAR}, #{isconsumptionmeasurable,jdbcType=NVARCHAR}, #{isperiodicallyreportable,jdbcType=NVARCHAR}, 
      #{investmenttotal,jdbcType=NUMERIC}, #{contractamount,jdbcType=NUMERIC}, #{area,jdbcType=NUMERIC}, 
      #{startdate,jdbcType=DATE}, #{enddate,jdbcType=DATE}, #{address,jdbcType=NVARCHAR}, 
      #{contractor,jdbcType=NVARCHAR}, #{owner,jdbcType=NVARCHAR}, #{architect,jdbcType=NVARCHAR}, 
      #{supervisor,jdbcType=NVARCHAR}, #{managerzhtaccount,jdbcType=NVARCHAR}, #{longitude,jdbcType=NVARCHAR}, 
      #{latitude,jdbcType=NVARCHAR}, #{regionid,jdbcType=CHAR}, #{createdby,jdbcType=VARCHAR}, 
      #{createdtime,jdbcType=TIMESTAMP}, #{deletedby,jdbcType=VARCHAR}, #{deletedtime,jdbcType=TIMESTAMP}, 
      #{isdeleted,jdbcType=BIT}, #{province,jdbcType=NVARCHAR}, #{city,jdbcType=NVARCHAR}, 
      #{district,jdbcType=NVARCHAR}, #{majorprojectnum,jdbcType=INTEGER}, #{siteid,jdbcType=NVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.TzhProjectInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:02:18 CST 2025.
    -->
    insert into Tzh_ProjectInfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="code != null">
        Code,
      </if>
      <if test="name != null">
        Name,
      </if>
      <if test="type != null">
        Type,
      </if>
      <if test="isconsumptionmeasurable != null">
        IsConsumptionMeasurable,
      </if>
      <if test="isperiodicallyreportable != null">
        IsPeriodicallyReportable,
      </if>
      <if test="investmenttotal != null">
        InvestmentTotal,
      </if>
      <if test="contractamount != null">
        ContractAmount,
      </if>
      <if test="area != null">
        Area,
      </if>
      <if test="startdate != null">
        StartDate,
      </if>
      <if test="enddate != null">
        EndDate,
      </if>
      <if test="address != null">
        Address,
      </if>
      <if test="contractor != null">
        Contractor,
      </if>
      <if test="owner != null">
        Owner,
      </if>
      <if test="architect != null">
        Architect,
      </if>
      <if test="supervisor != null">
        Supervisor,
      </if>
      <if test="managerzhtaccount != null">
        ManagerZhtAccount,
      </if>
      <if test="longitude != null">
        Longitude,
      </if>
      <if test="latitude != null">
        Latitude,
      </if>
      <if test="regionid != null">
        RegionId,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
      <if test="deletedby != null">
        DeletedBy,
      </if>
      <if test="deletedtime != null">
        DeletedTime,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
      <if test="province != null">
        Province,
      </if>
      <if test="city != null">
        City,
      </if>
      <if test="district != null">
        District,
      </if>
      <if test="majorprojectnum != null">
        MajorProjectNum,
      </if>
      <if test="siteid != null">
        SiteId,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=NVARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=NVARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=NVARCHAR},
      </if>
      <if test="isconsumptionmeasurable != null">
        #{isconsumptionmeasurable,jdbcType=NVARCHAR},
      </if>
      <if test="isperiodicallyreportable != null">
        #{isperiodicallyreportable,jdbcType=NVARCHAR},
      </if>
      <if test="investmenttotal != null">
        #{investmenttotal,jdbcType=NUMERIC},
      </if>
      <if test="contractamount != null">
        #{contractamount,jdbcType=NUMERIC},
      </if>
      <if test="area != null">
        #{area,jdbcType=NUMERIC},
      </if>
      <if test="startdate != null">
        #{startdate,jdbcType=DATE},
      </if>
      <if test="enddate != null">
        #{enddate,jdbcType=DATE},
      </if>
      <if test="address != null">
        #{address,jdbcType=NVARCHAR},
      </if>
      <if test="contractor != null">
        #{contractor,jdbcType=NVARCHAR},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=NVARCHAR},
      </if>
      <if test="architect != null">
        #{architect,jdbcType=NVARCHAR},
      </if>
      <if test="supervisor != null">
        #{supervisor,jdbcType=NVARCHAR},
      </if>
      <if test="managerzhtaccount != null">
        #{managerzhtaccount,jdbcType=NVARCHAR},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=NVARCHAR},
      </if>
      <if test="latitude != null">
        #{latitude,jdbcType=NVARCHAR},
      </if>
      <if test="regionid != null">
        #{regionid,jdbcType=CHAR},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedby != null">
        #{deletedby,jdbcType=VARCHAR},
      </if>
      <if test="deletedtime != null">
        #{deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
      <if test="province != null">
        #{province,jdbcType=NVARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=NVARCHAR},
      </if>
      <if test="district != null">
        #{district,jdbcType=NVARCHAR},
      </if>
      <if test="majorprojectnum != null">
        #{majorprojectnum,jdbcType=INTEGER},
      </if>
      <if test="siteid != null">
        #{siteid,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.TzhProjectInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:02:18 CST 2025.
    -->
    select count(*) from Tzh_ProjectInfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:02:18 CST 2025.
    -->
    update Tzh_ProjectInfo
    <set>
      <if test="record.id != null">
        Id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.code != null">
        Code = #{record.code,jdbcType=NVARCHAR},
      </if>
      <if test="record.name != null">
        Name = #{record.name,jdbcType=NVARCHAR},
      </if>
      <if test="record.type != null">
        Type = #{record.type,jdbcType=NVARCHAR},
      </if>
      <if test="record.isconsumptionmeasurable != null">
        IsConsumptionMeasurable = #{record.isconsumptionmeasurable,jdbcType=NVARCHAR},
      </if>
      <if test="record.isperiodicallyreportable != null">
        IsPeriodicallyReportable = #{record.isperiodicallyreportable,jdbcType=NVARCHAR},
      </if>
      <if test="record.investmenttotal != null">
        InvestmentTotal = #{record.investmenttotal,jdbcType=NUMERIC},
      </if>
      <if test="record.contractamount != null">
        ContractAmount = #{record.contractamount,jdbcType=NUMERIC},
      </if>
      <if test="record.area != null">
        Area = #{record.area,jdbcType=NUMERIC},
      </if>
      <if test="record.startdate != null">
        StartDate = #{record.startdate,jdbcType=DATE},
      </if>
      <if test="record.enddate != null">
        EndDate = #{record.enddate,jdbcType=DATE},
      </if>
      <if test="record.address != null">
        Address = #{record.address,jdbcType=NVARCHAR},
      </if>
      <if test="record.contractor != null">
        Contractor = #{record.contractor,jdbcType=NVARCHAR},
      </if>
      <if test="record.owner != null">
        Owner = #{record.owner,jdbcType=NVARCHAR},
      </if>
      <if test="record.architect != null">
        Architect = #{record.architect,jdbcType=NVARCHAR},
      </if>
      <if test="record.supervisor != null">
        Supervisor = #{record.supervisor,jdbcType=NVARCHAR},
      </if>
      <if test="record.managerzhtaccount != null">
        ManagerZhtAccount = #{record.managerzhtaccount,jdbcType=NVARCHAR},
      </if>
      <if test="record.longitude != null">
        Longitude = #{record.longitude,jdbcType=NVARCHAR},
      </if>
      <if test="record.latitude != null">
        Latitude = #{record.latitude,jdbcType=NVARCHAR},
      </if>
      <if test="record.regionid != null">
        RegionId = #{record.regionid,jdbcType=CHAR},
      </if>
      <if test="record.createdby != null">
        CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      </if>
      <if test="record.createdtime != null">
        CreatedTime = #{record.createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deletedby != null">
        DeletedBy = #{record.deletedby,jdbcType=VARCHAR},
      </if>
      <if test="record.deletedtime != null">
        DeletedTime = #{record.deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isdeleted != null">
        IsDeleted = #{record.isdeleted,jdbcType=BIT},
      </if>
      <if test="record.province != null">
        Province = #{record.province,jdbcType=NVARCHAR},
      </if>
      <if test="record.city != null">
        City = #{record.city,jdbcType=NVARCHAR},
      </if>
      <if test="record.district != null">
        District = #{record.district,jdbcType=NVARCHAR},
      </if>
      <if test="record.majorprojectnum != null">
        MajorProjectNum = #{record.majorprojectnum,jdbcType=INTEGER},
      </if>
      <if test="record.siteid != null">
        SiteId = #{record.siteid,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:02:18 CST 2025.
    -->
    update Tzh_ProjectInfo
    set Id = #{record.id,jdbcType=CHAR},
      Code = #{record.code,jdbcType=NVARCHAR},
      Name = #{record.name,jdbcType=NVARCHAR},
      Type = #{record.type,jdbcType=NVARCHAR},
      IsConsumptionMeasurable = #{record.isconsumptionmeasurable,jdbcType=NVARCHAR},
      IsPeriodicallyReportable = #{record.isperiodicallyreportable,jdbcType=NVARCHAR},
      InvestmentTotal = #{record.investmenttotal,jdbcType=NUMERIC},
      ContractAmount = #{record.contractamount,jdbcType=NUMERIC},
      Area = #{record.area,jdbcType=NUMERIC},
      StartDate = #{record.startdate,jdbcType=DATE},
      EndDate = #{record.enddate,jdbcType=DATE},
      Address = #{record.address,jdbcType=NVARCHAR},
      Contractor = #{record.contractor,jdbcType=NVARCHAR},
      Owner = #{record.owner,jdbcType=NVARCHAR},
      Architect = #{record.architect,jdbcType=NVARCHAR},
      Supervisor = #{record.supervisor,jdbcType=NVARCHAR},
      ManagerZhtAccount = #{record.managerzhtaccount,jdbcType=NVARCHAR},
      Longitude = #{record.longitude,jdbcType=NVARCHAR},
      Latitude = #{record.latitude,jdbcType=NVARCHAR},
      RegionId = #{record.regionid,jdbcType=CHAR},
      CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      CreatedTime = #{record.createdtime,jdbcType=TIMESTAMP},
      DeletedBy = #{record.deletedby,jdbcType=VARCHAR},
      DeletedTime = #{record.deletedtime,jdbcType=TIMESTAMP},
      IsDeleted = #{record.isdeleted,jdbcType=BIT},
      Province = #{record.province,jdbcType=NVARCHAR},
      City = #{record.city,jdbcType=NVARCHAR},
      District = #{record.district,jdbcType=NVARCHAR},
      MajorProjectNum = #{record.majorprojectnum,jdbcType=INTEGER},
      SiteId = #{record.siteid,jdbcType=NVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>