<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.DCdmsMaterialCarbonFactorMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.DCdmsMaterialCarbonFactor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 13:01:19 HKT 2023.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="ChineseName" jdbcType="NVARCHAR" property="chinesename" />
    <result column="MaterialCode" jdbcType="NVARCHAR" property="materialcode" />
    <result column="MaterialAttribute" jdbcType="NVARCHAR" property="materialattribute" />
    <result column="MaterialType" jdbcType="NVARCHAR" property="materialtype" />
    <result column="SiteName" jdbcType="NVARCHAR" property="sitename" />
    <result column="Unit" jdbcType="NVARCHAR" property="unit" />
    <result column="Description" jdbcType="NVARCHAR" property="description" />
    <result column="CarbonEmissionLocationId" jdbcType="CHAR" property="carbonemissionlocationid" />
    <result column="CarbonFactor" jdbcType="NUMERIC" property="carbonfactor" />
    <result column="CarbonFactorUnit" jdbcType="NVARCHAR" property="carbonfactorunit" />
    <result column="TransportFactor" jdbcType="NUMERIC" property="transportfactor" />
    <result column="TransportFactorUnit" jdbcType="NVARCHAR" property="transportfactorunit" />
    <result column="TransportDistance" jdbcType="NUMERIC" property="transportdistance" />
    <result column="TransportDistanceUnit" jdbcType="NVARCHAR" property="transportdistanceunit" />
    <result column="ProtocolSubCategoryId" jdbcType="CHAR" property="protocolsubcategoryid" />
    <result column="SourceDescription" jdbcType="NVARCHAR" property="sourcedescription" />
    <result column="CreatedBy" jdbcType="NVARCHAR" property="createdby" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
    <result column="DeletedBy" jdbcType="NVARCHAR" property="deletedby" />
    <result column="DeletedTime" jdbcType="TIMESTAMP" property="deletedtime" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 13:01:19 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 13:01:19 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 13:01:19 HKT 2023.
    -->
    Id, ChineseName, MaterialCode, MaterialAttribute, MaterialType, SiteName, Unit, Description, 
    CarbonEmissionLocationId, CarbonFactor, CarbonFactorUnit, TransportFactor, TransportFactorUnit, 
    TransportDistance, TransportDistanceUnit, ProtocolSubCategoryId, SourceDescription, 
    CreatedBy, CreatedTime, DeletedBy, DeletedTime, IsDeleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.DCdmsMaterialCarbonFactorExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 13:01:19 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from D_CDMS_MaterialCarbonFactor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.DCdmsMaterialCarbonFactorExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 13:01:19 HKT 2023.
    -->
    delete from D_CDMS_MaterialCarbonFactor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.DCdmsMaterialCarbonFactor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 13:01:19 HKT 2023.
    -->
    insert into D_CDMS_MaterialCarbonFactor (Id, ChineseName, MaterialCode, 
      MaterialAttribute, MaterialType, SiteName, 
      Unit, Description, CarbonEmissionLocationId, 
      CarbonFactor, CarbonFactorUnit, TransportFactor, 
      TransportFactorUnit, TransportDistance, 
      TransportDistanceUnit, ProtocolSubCategoryId, 
      SourceDescription, CreatedBy, CreatedTime, 
      DeletedBy, DeletedTime, IsDeleted
      )
    values (#{id,jdbcType=CHAR}, #{chinesename,jdbcType=NVARCHAR}, #{materialcode,jdbcType=NVARCHAR}, 
      #{materialattribute,jdbcType=NVARCHAR}, #{materialtype,jdbcType=NVARCHAR}, #{sitename,jdbcType=NVARCHAR}, 
      #{unit,jdbcType=NVARCHAR}, #{description,jdbcType=NVARCHAR}, #{carbonemissionlocationid,jdbcType=CHAR}, 
      #{carbonfactor,jdbcType=NUMERIC}, #{carbonfactorunit,jdbcType=NVARCHAR}, #{transportfactor,jdbcType=NUMERIC}, 
      #{transportfactorunit,jdbcType=NVARCHAR}, #{transportdistance,jdbcType=NUMERIC}, 
      #{transportdistanceunit,jdbcType=NVARCHAR}, #{protocolsubcategoryid,jdbcType=CHAR}, 
      #{sourcedescription,jdbcType=NVARCHAR}, #{createdby,jdbcType=NVARCHAR}, #{createdtime,jdbcType=TIMESTAMP}, 
      #{deletedby,jdbcType=NVARCHAR}, #{deletedtime,jdbcType=TIMESTAMP}, #{isdeleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.DCdmsMaterialCarbonFactor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 13:01:19 HKT 2023.
    -->
    insert into D_CDMS_MaterialCarbonFactor
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="chinesename != null">
        ChineseName,
      </if>
      <if test="materialcode != null">
        MaterialCode,
      </if>
      <if test="materialattribute != null">
        MaterialAttribute,
      </if>
      <if test="materialtype != null">
        MaterialType,
      </if>
      <if test="sitename != null">
        SiteName,
      </if>
      <if test="unit != null">
        Unit,
      </if>
      <if test="description != null">
        Description,
      </if>
      <if test="carbonemissionlocationid != null">
        CarbonEmissionLocationId,
      </if>
      <if test="carbonfactor != null">
        CarbonFactor,
      </if>
      <if test="carbonfactorunit != null">
        CarbonFactorUnit,
      </if>
      <if test="transportfactor != null">
        TransportFactor,
      </if>
      <if test="transportfactorunit != null">
        TransportFactorUnit,
      </if>
      <if test="transportdistance != null">
        TransportDistance,
      </if>
      <if test="transportdistanceunit != null">
        TransportDistanceUnit,
      </if>
      <if test="protocolsubcategoryid != null">
        ProtocolSubCategoryId,
      </if>
      <if test="sourcedescription != null">
        SourceDescription,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
      <if test="deletedby != null">
        DeletedBy,
      </if>
      <if test="deletedtime != null">
        DeletedTime,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="chinesename != null">
        #{chinesename,jdbcType=NVARCHAR},
      </if>
      <if test="materialcode != null">
        #{materialcode,jdbcType=NVARCHAR},
      </if>
      <if test="materialattribute != null">
        #{materialattribute,jdbcType=NVARCHAR},
      </if>
      <if test="materialtype != null">
        #{materialtype,jdbcType=NVARCHAR},
      </if>
      <if test="sitename != null">
        #{sitename,jdbcType=NVARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=NVARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=NVARCHAR},
      </if>
      <if test="carbonemissionlocationid != null">
        #{carbonemissionlocationid,jdbcType=CHAR},
      </if>
      <if test="carbonfactor != null">
        #{carbonfactor,jdbcType=NUMERIC},
      </if>
      <if test="carbonfactorunit != null">
        #{carbonfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="transportfactor != null">
        #{transportfactor,jdbcType=NUMERIC},
      </if>
      <if test="transportfactorunit != null">
        #{transportfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="transportdistance != null">
        #{transportdistance,jdbcType=NUMERIC},
      </if>
      <if test="transportdistanceunit != null">
        #{transportdistanceunit,jdbcType=NVARCHAR},
      </if>
      <if test="protocolsubcategoryid != null">
        #{protocolsubcategoryid,jdbcType=CHAR},
      </if>
      <if test="sourcedescription != null">
        #{sourcedescription,jdbcType=NVARCHAR},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=NVARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedby != null">
        #{deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="deletedtime != null">
        #{deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.DCdmsMaterialCarbonFactorExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 13:01:19 HKT 2023.
    -->
    select count(*) from D_CDMS_MaterialCarbonFactor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 13:01:19 HKT 2023.
    -->
    update D_CDMS_MaterialCarbonFactor
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.chinesename != null">
        ChineseName = #{row.chinesename,jdbcType=NVARCHAR},
      </if>
      <if test="row.materialcode != null">
        MaterialCode = #{row.materialcode,jdbcType=NVARCHAR},
      </if>
      <if test="row.materialattribute != null">
        MaterialAttribute = #{row.materialattribute,jdbcType=NVARCHAR},
      </if>
      <if test="row.materialtype != null">
        MaterialType = #{row.materialtype,jdbcType=NVARCHAR},
      </if>
      <if test="row.sitename != null">
        SiteName = #{row.sitename,jdbcType=NVARCHAR},
      </if>
      <if test="row.unit != null">
        Unit = #{row.unit,jdbcType=NVARCHAR},
      </if>
      <if test="row.description != null">
        Description = #{row.description,jdbcType=NVARCHAR},
      </if>
      <if test="row.carbonemissionlocationid != null">
        CarbonEmissionLocationId = #{row.carbonemissionlocationid,jdbcType=CHAR},
      </if>
      <if test="row.carbonfactor != null">
        CarbonFactor = #{row.carbonfactor,jdbcType=NUMERIC},
      </if>
      <if test="row.carbonfactorunit != null">
        CarbonFactorUnit = #{row.carbonfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.transportfactor != null">
        TransportFactor = #{row.transportfactor,jdbcType=NUMERIC},
      </if>
      <if test="row.transportfactorunit != null">
        TransportFactorUnit = #{row.transportfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.transportdistance != null">
        TransportDistance = #{row.transportdistance,jdbcType=NUMERIC},
      </if>
      <if test="row.transportdistanceunit != null">
        TransportDistanceUnit = #{row.transportdistanceunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.protocolsubcategoryid != null">
        ProtocolSubCategoryId = #{row.protocolsubcategoryid,jdbcType=CHAR},
      </if>
      <if test="row.sourcedescription != null">
        SourceDescription = #{row.sourcedescription,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdby != null">
        CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdtime != null">
        CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deletedby != null">
        DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="row.deletedtime != null">
        DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.isdeleted != null">
        IsDeleted = #{row.isdeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 13:01:19 HKT 2023.
    -->
    update D_CDMS_MaterialCarbonFactor
    set Id = #{row.id,jdbcType=CHAR},
      ChineseName = #{row.chinesename,jdbcType=NVARCHAR},
      MaterialCode = #{row.materialcode,jdbcType=NVARCHAR},
      MaterialAttribute = #{row.materialattribute,jdbcType=NVARCHAR},
      MaterialType = #{row.materialtype,jdbcType=NVARCHAR},
      SiteName = #{row.sitename,jdbcType=NVARCHAR},
      Unit = #{row.unit,jdbcType=NVARCHAR},
      Description = #{row.description,jdbcType=NVARCHAR},
      CarbonEmissionLocationId = #{row.carbonemissionlocationid,jdbcType=CHAR},
      CarbonFactor = #{row.carbonfactor,jdbcType=NUMERIC},
      CarbonFactorUnit = #{row.carbonfactorunit,jdbcType=NVARCHAR},
      TransportFactor = #{row.transportfactor,jdbcType=NUMERIC},
      TransportFactorUnit = #{row.transportfactorunit,jdbcType=NVARCHAR},
      TransportDistance = #{row.transportdistance,jdbcType=NUMERIC},
      TransportDistanceUnit = #{row.transportdistanceunit,jdbcType=NVARCHAR},
      ProtocolSubCategoryId = #{row.protocolsubcategoryid,jdbcType=CHAR},
      SourceDescription = #{row.sourcedescription,jdbcType=NVARCHAR},
      CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      IsDeleted = #{row.isdeleted,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>