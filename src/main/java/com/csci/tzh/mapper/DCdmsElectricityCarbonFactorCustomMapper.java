package com.csci.tzh.mapper;

import com.csci.tzh.model.DCdmsElectricityCarbonFactor;
import com.csci.tzh.vo.DCdmsElectricityCarbonFactorVO;
import com.csci.tzh.vo.DCdmsMaterialCarbonFactorVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DCdmsElectricityCarbonFactorCustomMapper {
	
    @Select("""
			SELECT ECF.*, region = R.Name, regionsc = R.NameSC, regionen = R.NameEN,
			CarbonEmissionLocation = CEL.Name, CarbonEmissionLocationSC = CEL.NameSC,
			CarbonEmissionLocationEN = CEL.NameEN, Protocol = P.Name, ProtocolSC = P.NameSC, ProtocolEN = P.NameEN
			FROM D_CDMS_ElectricityCarbonFactor ECF
			LEFT JOIN Tzh_CarbonEmissionLocation CEL ON ECF.CarbonEmissionLocationId = CEL.Id
			LEFT JOIN Tzh_Protocol P ON ECF.ProtocolId = P.Id
			LEFT JOIN Tzh_ProjectInfo PI ON PI.Name = ECF.SiteName AND PI.IsDeleted = 0
			LEFT JOIN Tzh_Region R ON R.Id = PI.RegionId
			WHERE R.Name = #{region} 
			AND ECF.SiteName = #{siteName} 
			AND P.NameEN = #{protocol}
			AND (CEL.Name = #{carbonEmissionLocation} OR #{carbonEmissionLocation} = '')
			AND	(ECF.CarbonFactorRecordYearMonth = #{recordYearMonth} OR #{recordYearMonth} IS NULL)
			AND ECF.IsDeleted = 0
			""")
    public List<DCdmsElectricityCarbonFactorVO> list(@Param("region") String region,
													 @Param("siteName") String siteName,
													 @Param("protocol") String protocol,
													 @Param("carbonEmissionLocation") String carbonEmissionLocation,
													 @Param("recordYearMonth") Integer recordYearMonth);

}