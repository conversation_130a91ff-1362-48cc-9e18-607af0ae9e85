package com.csci.tzh.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.tzh.model.TzhProtocol;
import com.csci.tzh.model.TzhProtocolExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhProtocolMapper extends BaseMapper<com.csci.cohl.model.TzhProtocol> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol
     *
     * @mbg.generated Tue Apr 09 10:46:50 HKT 2024
     */
    long countByExample(TzhProtocolExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol
     *
     * @mbg.generated Tue Apr 09 10:46:50 HKT 2024
     */
    int deleteByExample(TzhProtocolExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol
     *
     * @mbg.generated Tue Apr 09 10:46:50 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol
     *
     * @mbg.generated Tue Apr 09 10:46:50 HKT 2024
     */
    int insert(TzhProtocol record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol
     *
     * @mbg.generated Tue Apr 09 10:46:50 HKT 2024
     */
    int insertSelective(TzhProtocol record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol
     *
     * @mbg.generated Tue Apr 09 10:46:50 HKT 2024
     */
    List<TzhProtocol> selectByExample(TzhProtocolExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol
     *
     * @mbg.generated Tue Apr 09 10:46:50 HKT 2024
     */
    TzhProtocol selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol
     *
     * @mbg.generated Tue Apr 09 10:46:50 HKT 2024
     */
    int updateByExampleSelective(@Param("record") TzhProtocol record, @Param("example") TzhProtocolExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol
     *
     * @mbg.generated Tue Apr 09 10:46:50 HKT 2024
     */
    int updateByExample(@Param("record") TzhProtocol record, @Param("example") TzhProtocolExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol
     *
     * @mbg.generated Tue Apr 09 10:46:50 HKT 2024
     */
    int updateByPrimaryKeySelective(TzhProtocol record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol
     *
     * @mbg.generated Tue Apr 09 10:46:50 HKT 2024
     */
    int updateByPrimaryKey(TzhProtocol record);
}
