<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.TzhPlanningMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.TzhPlanning">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 25 13:08:25 HKT 2023.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="SiteName" jdbcType="NVARCHAR" property="sitename" />
    <result column="ProtocolId" jdbcType="CHAR" property="protocolid" />
    <result column="Title" jdbcType="NVARCHAR" property="title" />
    <result column="TitleSC" jdbcType="NVARCHAR" property="titlesc" />
    <result column="TitleEN" jdbcType="NVARCHAR" property="titleen" />
    <result column="Seq" jdbcType="INTEGER" property="seq" />
    <result column="Description" jdbcType="NVARCHAR" property="description" />
    <result column="DescriptionSC" jdbcType="NVARCHAR" property="descriptionsc" />
    <result column="DescriptionEN" jdbcType="NVARCHAR" property="descriptionen" />
    <result column="CreatedBy" jdbcType="NVARCHAR" property="createdby" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
    <result column="DeletedBy" jdbcType="NVARCHAR" property="deletedby" />
    <result column="DeletedTime" jdbcType="TIMESTAMP" property="deletedtime" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 25 13:08:25 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 25 13:08:25 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 25 13:08:25 HKT 2023.
    -->
    Id, SiteName, ProtocolId, Title, TitleSC, TitleEN, Seq, Description, DescriptionSC, 
    DescriptionEN, CreatedBy, CreatedTime, DeletedBy, DeletedTime, IsDeleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.TzhPlanningExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 25 13:08:25 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_Planning
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.TzhPlanningExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 25 13:08:25 HKT 2023.
    -->
    delete from Tzh_Planning
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.TzhPlanning">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 25 13:08:25 HKT 2023.
    -->
    insert into Tzh_Planning (Id, SiteName, ProtocolId, 
      Title, TitleSC, TitleEN, 
      Seq, Description, DescriptionSC, 
      DescriptionEN, CreatedBy, CreatedTime, 
      DeletedBy, DeletedTime, IsDeleted
      )
    values (#{id,jdbcType=CHAR}, #{sitename,jdbcType=NVARCHAR}, #{protocolid,jdbcType=CHAR}, 
      #{title,jdbcType=NVARCHAR}, #{titlesc,jdbcType=NVARCHAR}, #{titleen,jdbcType=NVARCHAR}, 
      #{seq,jdbcType=INTEGER}, #{description,jdbcType=NVARCHAR}, #{descriptionsc,jdbcType=NVARCHAR}, 
      #{descriptionen,jdbcType=NVARCHAR}, #{createdby,jdbcType=NVARCHAR}, #{createdtime,jdbcType=TIMESTAMP}, 
      #{deletedby,jdbcType=NVARCHAR}, #{deletedtime,jdbcType=TIMESTAMP}, #{isdeleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.TzhPlanning">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 25 13:08:25 HKT 2023.
    -->
    insert into Tzh_Planning
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="sitename != null">
        SiteName,
      </if>
      <if test="protocolid != null">
        ProtocolId,
      </if>
      <if test="title != null">
        Title,
      </if>
      <if test="titlesc != null">
        TitleSC,
      </if>
      <if test="titleen != null">
        TitleEN,
      </if>
      <if test="seq != null">
        Seq,
      </if>
      <if test="description != null">
        Description,
      </if>
      <if test="descriptionsc != null">
        DescriptionSC,
      </if>
      <if test="descriptionen != null">
        DescriptionEN,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
      <if test="deletedby != null">
        DeletedBy,
      </if>
      <if test="deletedtime != null">
        DeletedTime,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="sitename != null">
        #{sitename,jdbcType=NVARCHAR},
      </if>
      <if test="protocolid != null">
        #{protocolid,jdbcType=CHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=NVARCHAR},
      </if>
      <if test="titlesc != null">
        #{titlesc,jdbcType=NVARCHAR},
      </if>
      <if test="titleen != null">
        #{titleen,jdbcType=NVARCHAR},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        #{description,jdbcType=NVARCHAR},
      </if>
      <if test="descriptionsc != null">
        #{descriptionsc,jdbcType=NVARCHAR},
      </if>
      <if test="descriptionen != null">
        #{descriptionen,jdbcType=NVARCHAR},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=NVARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedby != null">
        #{deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="deletedtime != null">
        #{deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.TzhPlanningExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 25 13:08:25 HKT 2023.
    -->
    select count(*) from Tzh_Planning
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 25 13:08:25 HKT 2023.
    -->
    update Tzh_Planning
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.sitename != null">
        SiteName = #{row.sitename,jdbcType=NVARCHAR},
      </if>
      <if test="row.protocolid != null">
        ProtocolId = #{row.protocolid,jdbcType=CHAR},
      </if>
      <if test="row.title != null">
        Title = #{row.title,jdbcType=NVARCHAR},
      </if>
      <if test="row.titlesc != null">
        TitleSC = #{row.titlesc,jdbcType=NVARCHAR},
      </if>
      <if test="row.titleen != null">
        TitleEN = #{row.titleen,jdbcType=NVARCHAR},
      </if>
      <if test="row.seq != null">
        Seq = #{row.seq,jdbcType=INTEGER},
      </if>
      <if test="row.description != null">
        Description = #{row.description,jdbcType=NVARCHAR},
      </if>
      <if test="row.descriptionsc != null">
        DescriptionSC = #{row.descriptionsc,jdbcType=NVARCHAR},
      </if>
      <if test="row.descriptionen != null">
        DescriptionEN = #{row.descriptionen,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdby != null">
        CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdtime != null">
        CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deletedby != null">
        DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="row.deletedtime != null">
        DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.isdeleted != null">
        IsDeleted = #{row.isdeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 25 13:08:25 HKT 2023.
    -->
    update Tzh_Planning
    set Id = #{row.id,jdbcType=CHAR},
      SiteName = #{row.sitename,jdbcType=NVARCHAR},
      ProtocolId = #{row.protocolid,jdbcType=CHAR},
      Title = #{row.title,jdbcType=NVARCHAR},
      TitleSC = #{row.titlesc,jdbcType=NVARCHAR},
      TitleEN = #{row.titleen,jdbcType=NVARCHAR},
      Seq = #{row.seq,jdbcType=INTEGER},
      Description = #{row.description,jdbcType=NVARCHAR},
      DescriptionSC = #{row.descriptionsc,jdbcType=NVARCHAR},
      DescriptionEN = #{row.descriptionen,jdbcType=NVARCHAR},
      CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      IsDeleted = #{row.isdeleted,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>