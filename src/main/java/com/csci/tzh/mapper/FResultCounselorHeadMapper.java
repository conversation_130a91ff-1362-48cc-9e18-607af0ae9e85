package com.csci.tzh.mapper;

import com.csci.tzh.model.FResultCounselorHead;
import com.csci.tzh.model.FResultCounselorHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FResultCounselorHeadMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_head
     *
     * @mbg.generated Tue Jan 07 11:36:50 CST 2025
     */
    long countByExample(FResultCounselorHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_head
     *
     * @mbg.generated Tue Jan 07 11:36:50 CST 2025
     */
    int deleteByExample(FResultCounselorHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_head
     *
     * @mbg.generated Tue Jan 07 11:36:50 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_head
     *
     * @mbg.generated Tue Jan 07 11:36:50 CST 2025
     */
    int insert(FResultCounselorHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_head
     *
     * @mbg.generated Tue Jan 07 11:36:50 CST 2025
     */
    int insertSelective(FResultCounselorHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_head
     *
     * @mbg.generated Tue Jan 07 11:36:50 CST 2025
     */
    List<FResultCounselorHead> selectByExample(FResultCounselorHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_head
     *
     * @mbg.generated Tue Jan 07 11:36:50 CST 2025
     */
    FResultCounselorHead selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_head
     *
     * @mbg.generated Tue Jan 07 11:36:50 CST 2025
     */
    int updateByExampleSelective(@Param("record") FResultCounselorHead record, @Param("example") FResultCounselorHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_head
     *
     * @mbg.generated Tue Jan 07 11:36:50 CST 2025
     */
    int updateByExample(@Param("record") FResultCounselorHead record, @Param("example") FResultCounselorHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_head
     *
     * @mbg.generated Tue Jan 07 11:36:50 CST 2025
     */
    int updateByPrimaryKeySelective(FResultCounselorHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table f_result_counselor_head
     *
     * @mbg.generated Tue Jan 07 11:36:50 CST 2025
     */
    int updateByPrimaryKey(FResultCounselorHead record);
}