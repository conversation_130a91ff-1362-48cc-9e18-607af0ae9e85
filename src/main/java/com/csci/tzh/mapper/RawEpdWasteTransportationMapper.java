package com.csci.tzh.mapper;

import com.csci.tzh.model.RawEpdWasteTransportation;
import com.csci.tzh.model.RawEpdWasteTransportationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RawEpdWasteTransportationMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	long countByExample(RawEpdWasteTransportationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	int deleteByExample(RawEpdWasteTransportationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	int insert(RawEpdWasteTransportation row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	int insertSelective(RawEpdWasteTransportation row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	List<RawEpdWasteTransportation> selectByExampleWithBLOBs(RawEpdWasteTransportationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	List<RawEpdWasteTransportation> selectByExample(RawEpdWasteTransportationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	int updateByExampleSelective(@Param("row") RawEpdWasteTransportation row,
			@Param("example") RawEpdWasteTransportationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	int updateByExampleWithBLOBs(@Param("row") RawEpdWasteTransportation row,
			@Param("example") RawEpdWasteTransportationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table RawEpdWasteTransportation
	 * @mbg.generated  Wed Dec 14 17:16:15 HKT 2022
	 */
	int updateByExample(@Param("row") RawEpdWasteTransportation row,
			@Param("example") RawEpdWasteTransportationExample example);
}