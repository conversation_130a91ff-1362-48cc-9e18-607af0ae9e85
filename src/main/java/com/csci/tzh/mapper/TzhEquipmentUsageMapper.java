package com.csci.tzh.mapper;

import com.csci.tzh.model.TzhEquipmentUsage;
import com.csci.tzh.model.TzhEquipmentUsageExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhEquipmentUsageMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	long countByExample(TzhEquipmentUsageExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	int deleteByExample(TzhEquipmentUsageExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	int insert(TzhEquipmentUsage row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	int insertSelective(TzhEquipmentUsage row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	List<TzhEquipmentUsage> selectByExample(TzhEquipmentUsageExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") TzhEquipmentUsage row,
			@Param("example") TzhEquipmentUsageExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EquipmentUsage
	 * @mbg.generated  Thu Jun 29 09:29:58 HKT 2023
	 */
	int updateByExample(@Param("row") TzhEquipmentUsage row, @Param("example") TzhEquipmentUsageExample example);
}