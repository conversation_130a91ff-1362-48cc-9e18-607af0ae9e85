package com.csci.tzh.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.cohl.beans.vo.TzhProtocolSubCategoryVO;
import com.csci.tzh.model.TzhProtocolSubCategory;
import com.csci.tzh.model.TzhProtocolSubCategoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface TzhProtocolSubCategoryMapper extends BaseMapper<com.csci.cohl.model.TzhProtocolSubCategory> {

    @Select("""
            SELECT SC.Id, P.Name AS Protocol, P.NameSC AS ProtocolSC, P.NameEN AS ProtocolEN, P.Description AS ProtocolDescription,
            C.CategoryName, C.CategoryNameSC, C.CategoryNameEN, SC.SubCategoryName, SC.SubCategoryNameSC, SC.SubCategoryNameEN
            FROM Tzh_Protocol P
            LEFT JOIN Tzh_Protocol_Category C ON P.Id = C.ProtocolId
            LEFT JOIN Tzh_Protocol_SubCategory SC ON C.Id = SC.CategoryId
            WHERE P.NameEN = #{protocol}
            ORDER BY Protocol, CategoryName, SubCategoryName
            """)
    List<TzhProtocolSubCategoryVO> listSubCategory(@Param("protocol") String protocol);
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_SubCategory
     *
     * @mbg.generated Tue Apr 09 10:58:33 HKT 2024
     */
    long countByExample(TzhProtocolSubCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_SubCategory
     *
     * @mbg.generated Tue Apr 09 10:58:33 HKT 2024
     */
    int deleteByExample(TzhProtocolSubCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_SubCategory
     *
     * @mbg.generated Tue Apr 09 10:58:33 HKT 2024
     */
    int insert(TzhProtocolSubCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_SubCategory
     *
     * @mbg.generated Tue Apr 09 10:58:33 HKT 2024
     */
    int insertSelective(TzhProtocolSubCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_SubCategory
     *
     * @mbg.generated Tue Apr 09 10:58:33 HKT 2024
     */
    List<TzhProtocolSubCategory> selectByExample(TzhProtocolSubCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_SubCategory
     *
     * @mbg.generated Tue Apr 09 10:58:33 HKT 2024
     */
    int updateByExampleSelective(@Param("record") TzhProtocolSubCategory record, @Param("example") TzhProtocolSubCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_SubCategory
     *
     * @mbg.generated Tue Apr 09 10:58:33 HKT 2024
     */
    int updateByExample(@Param("record") TzhProtocolSubCategory record, @Param("example") TzhProtocolSubCategoryExample example);
}
