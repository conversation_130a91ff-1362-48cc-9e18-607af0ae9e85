package com.csci.tzh.mapper;

import com.csci.tzh.model.TzhMonthlyMaterialCarbon;
import com.csci.tzh.vo.TzhExpectedEmissionIsoVO;
import com.csci.tzh.vo.TzhMonthlyMaterialCarbonVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TzhMonthlyMaterialCarbonCustomMapper {
	
    @Select("""
			SELECT MMC.*, protocol = P.Name, protocolsc = P.NameSC, protocolen = P.NameEN
			FROM Tzh_MonthlyMaterialCarbon MMC
			LEFT JOIN Tzh_Protocol P ON MMC.ProtocolId = P.Id
			WHERE MMC.SiteName = #{siteName}
			AND MMC.RecordYearMonth >= #{recordYearMonthFrom} AND MMC.RecordYearMonth <= #{recordYearMonthTo}
			AND P.NameEN = #{protocol}
			AND MMC.IsDeleted = 0
 			""")
    public List<TzhMonthlyMaterialCarbonVO> list(@Param("siteName") String siteName,
												 @Param("protocol") String protocol,
												 @Param("recordYearMonthFrom") Integer recordYearMonthFrom,
												 @Param("recordYearMonthTo") Integer recordYearMonthTo);

}