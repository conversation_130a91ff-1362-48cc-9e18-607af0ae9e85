<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.TzhOrgMaterialCarbonFactorGBT51366Mapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.TzhOrgMaterialCarbonFactorGBT51366">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:04 HKT 2023.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="ChineseName" jdbcType="NVARCHAR" property="chinesename" />
    <result column="Specification" jdbcType="NVARCHAR" property="specification" />
    <result column="EnergyConsumption" jdbcType="NVARCHAR" property="energyconsumption" />
    <result column="HeatCo2Factor" jdbcType="NUMERIC" property="heatco2factor" />
    <result column="EffectiveCo2FactorDefault" jdbcType="NUMERIC" property="effectiveco2factordefault" />
    <result column="EffectiveCo2FactorLower" jdbcType="NUMERIC" property="effectiveco2factorlower" />
    <result column="EffectiveCo2FactorUpper" jdbcType="NUMERIC" property="effectiveco2factorupper" />
    <result column="CarbonFactor" jdbcType="NUMERIC" property="carbonfactor" />
    <result column="CarbonFactorUnit" jdbcType="NVARCHAR" property="carbonfactorunit" />
    <result column="CreatedBy" jdbcType="NVARCHAR" property="createdby" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
    <result column="DeletedBy" jdbcType="NVARCHAR" property="deletedby" />
    <result column="DeletedTime" jdbcType="TIMESTAMP" property="deletedtime" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:04 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:04 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:04 HKT 2023.
    -->
    Id, ChineseName, Specification, EnergyConsumption, HeatCo2Factor, EffectiveCo2FactorDefault, 
    EffectiveCo2FactorLower, EffectiveCo2FactorUpper, CarbonFactor, CarbonFactorUnit, 
    CreatedBy, CreatedTime, DeletedBy, DeletedTime, IsDeleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.TzhOrgMaterialCarbonFactorGBT51366Example" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:04 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_Org_MaterialCarbonFactor_GBT51366
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.TzhOrgMaterialCarbonFactorGBT51366Example">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:04 HKT 2023.
    -->
    delete from Tzh_Org_MaterialCarbonFactor_GBT51366
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.TzhOrgMaterialCarbonFactorGBT51366">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:04 HKT 2023.
    -->
    insert into Tzh_Org_MaterialCarbonFactor_GBT51366 (Id, ChineseName, Specification, 
      EnergyConsumption, HeatCo2Factor, EffectiveCo2FactorDefault, 
      EffectiveCo2FactorLower, EffectiveCo2FactorUpper, 
      CarbonFactor, CarbonFactorUnit, CreatedBy, 
      CreatedTime, DeletedBy, DeletedTime, 
      IsDeleted)
    values (#{id,jdbcType=CHAR}, #{chinesename,jdbcType=NVARCHAR}, #{specification,jdbcType=NVARCHAR}, 
      #{energyconsumption,jdbcType=NVARCHAR}, #{heatco2factor,jdbcType=NUMERIC}, #{effectiveco2factordefault,jdbcType=NUMERIC}, 
      #{effectiveco2factorlower,jdbcType=NUMERIC}, #{effectiveco2factorupper,jdbcType=NUMERIC}, 
      #{carbonfactor,jdbcType=NUMERIC}, #{carbonfactorunit,jdbcType=NVARCHAR}, #{createdby,jdbcType=NVARCHAR}, 
      #{createdtime,jdbcType=TIMESTAMP}, #{deletedby,jdbcType=NVARCHAR}, #{deletedtime,jdbcType=TIMESTAMP}, 
      #{isdeleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.TzhOrgMaterialCarbonFactorGBT51366">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:04 HKT 2023.
    -->
    insert into Tzh_Org_MaterialCarbonFactor_GBT51366
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="chinesename != null">
        ChineseName,
      </if>
      <if test="specification != null">
        Specification,
      </if>
      <if test="energyconsumption != null">
        EnergyConsumption,
      </if>
      <if test="heatco2factor != null">
        HeatCo2Factor,
      </if>
      <if test="effectiveco2factordefault != null">
        EffectiveCo2FactorDefault,
      </if>
      <if test="effectiveco2factorlower != null">
        EffectiveCo2FactorLower,
      </if>
      <if test="effectiveco2factorupper != null">
        EffectiveCo2FactorUpper,
      </if>
      <if test="carbonfactor != null">
        CarbonFactor,
      </if>
      <if test="carbonfactorunit != null">
        CarbonFactorUnit,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
      <if test="deletedby != null">
        DeletedBy,
      </if>
      <if test="deletedtime != null">
        DeletedTime,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="chinesename != null">
        #{chinesename,jdbcType=NVARCHAR},
      </if>
      <if test="specification != null">
        #{specification,jdbcType=NVARCHAR},
      </if>
      <if test="energyconsumption != null">
        #{energyconsumption,jdbcType=NVARCHAR},
      </if>
      <if test="heatco2factor != null">
        #{heatco2factor,jdbcType=NUMERIC},
      </if>
      <if test="effectiveco2factordefault != null">
        #{effectiveco2factordefault,jdbcType=NUMERIC},
      </if>
      <if test="effectiveco2factorlower != null">
        #{effectiveco2factorlower,jdbcType=NUMERIC},
      </if>
      <if test="effectiveco2factorupper != null">
        #{effectiveco2factorupper,jdbcType=NUMERIC},
      </if>
      <if test="carbonfactor != null">
        #{carbonfactor,jdbcType=NUMERIC},
      </if>
      <if test="carbonfactorunit != null">
        #{carbonfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=NVARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedby != null">
        #{deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="deletedtime != null">
        #{deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.TzhOrgMaterialCarbonFactorGBT51366Example" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:04 HKT 2023.
    -->
    select count(*) from Tzh_Org_MaterialCarbonFactor_GBT51366
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:04 HKT 2023.
    -->
    update Tzh_Org_MaterialCarbonFactor_GBT51366
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.chinesename != null">
        ChineseName = #{row.chinesename,jdbcType=NVARCHAR},
      </if>
      <if test="row.specification != null">
        Specification = #{row.specification,jdbcType=NVARCHAR},
      </if>
      <if test="row.energyconsumption != null">
        EnergyConsumption = #{row.energyconsumption,jdbcType=NVARCHAR},
      </if>
      <if test="row.heatco2factor != null">
        HeatCo2Factor = #{row.heatco2factor,jdbcType=NUMERIC},
      </if>
      <if test="row.effectiveco2factordefault != null">
        EffectiveCo2FactorDefault = #{row.effectiveco2factordefault,jdbcType=NUMERIC},
      </if>
      <if test="row.effectiveco2factorlower != null">
        EffectiveCo2FactorLower = #{row.effectiveco2factorlower,jdbcType=NUMERIC},
      </if>
      <if test="row.effectiveco2factorupper != null">
        EffectiveCo2FactorUpper = #{row.effectiveco2factorupper,jdbcType=NUMERIC},
      </if>
      <if test="row.carbonfactor != null">
        CarbonFactor = #{row.carbonfactor,jdbcType=NUMERIC},
      </if>
      <if test="row.carbonfactorunit != null">
        CarbonFactorUnit = #{row.carbonfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdby != null">
        CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdtime != null">
        CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deletedby != null">
        DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="row.deletedtime != null">
        DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.isdeleted != null">
        IsDeleted = #{row.isdeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:04 HKT 2023.
    -->
    update Tzh_Org_MaterialCarbonFactor_GBT51366
    set Id = #{row.id,jdbcType=CHAR},
      ChineseName = #{row.chinesename,jdbcType=NVARCHAR},
      Specification = #{row.specification,jdbcType=NVARCHAR},
      EnergyConsumption = #{row.energyconsumption,jdbcType=NVARCHAR},
      HeatCo2Factor = #{row.heatco2factor,jdbcType=NUMERIC},
      EffectiveCo2FactorDefault = #{row.effectiveco2factordefault,jdbcType=NUMERIC},
      EffectiveCo2FactorLower = #{row.effectiveco2factorlower,jdbcType=NUMERIC},
      EffectiveCo2FactorUpper = #{row.effectiveco2factorupper,jdbcType=NUMERIC},
      CarbonFactor = #{row.carbonfactor,jdbcType=NUMERIC},
      CarbonFactorUnit = #{row.carbonfactorunit,jdbcType=NVARCHAR},
      CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      IsDeleted = #{row.isdeleted,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>