package com.csci.tzh.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.cohl.beans.vo.TzhEmissionReductionHeadVO;
import com.csci.tzh.model.TzhEmissionReductionHead;
import com.csci.tzh.model.TzhEmissionReductionHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface TzhEmissionReductionHeadMapper extends BaseMapper<com.csci.cohl.model.TzhEmissionReductionHead> {

	@Select("""
            SELECT ERH.Id, ERH.SiteName, C.CategoryName, C.CategoryNameSC, C.CategoryNameEN, ERH.Title, ERH.TitleSC,
            ERH.TitleEN, L.Name AS CarbonEmissionLocation, L.NameSC AS CarbonEmissionLocationSC, L.NameEN AS CarbonEmissionLocationEN,
            ERH.MethodDescription, ERH.MethodDescriptionSC, ERH.MethodDescriptionEN, ERH.CalculationDescription
            FROM Tzh_EmissionReductionHead ERH
            LEFT JOIN Tzh_Protocol_Category C ON C.Id = ERH.ProtocolCategoryId
            LEFT JOIN Tzh_Protocol PT ON PT.Id = C.ProtocolId
            LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Id = ERH.CarbonEmissionLocationId
            WHERE ERH.IsDeleted = 0
            AND ERH.SiteName = #{siteName} AND PT.NameEN = #{protocol}
            """)
	List<TzhEmissionReductionHeadVO> listEmissionReductionHead(@Param("siteName") String siteName, @Param("protocol") String protocol);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionHead
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	long countByExample(TzhEmissionReductionHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionHead
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	int deleteByExample(TzhEmissionReductionHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionHead
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	int insert(TzhEmissionReductionHead row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionHead
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	int insertSelective(TzhEmissionReductionHead row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionHead
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	List<TzhEmissionReductionHead> selectByExample(TzhEmissionReductionHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionHead
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") TzhEmissionReductionHead row,
			@Param("example") TzhEmissionReductionHeadExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionHead
	 * @mbg.generated  Mon May 29 09:17:55 HKT 2023
	 */
	int updateByExample(@Param("row") TzhEmissionReductionHead row,
			@Param("example") TzhEmissionReductionHeadExample example);
}
