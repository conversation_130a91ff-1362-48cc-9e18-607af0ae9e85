package com.csci.tzh.mapper;

import com.csci.tzh.vo.TzhExpectedEmissionIsoVO;
import com.csci.tzh.vo.TzhProjectInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TzhExpectedEmissionIsoCustomMapper {
	
    @Select("""
            SELECT EEI.*, PC.CategoryName, PC.CategoryNameSC, PC.CategoryNameEN, PSC.SubCategoryName, 
			PSC.SubCategoryNameSC, PSC.SubCategoryNameEN, carbonemissionlocation = CEL.Name, 
			carbonemissionlocationsc = CEL.NameSC, carbonemissionlocationen = CEL.NameEN
			FROM Tzh_ExpectedEmissionIso EEI
			LEFT JOIN Tzh_Protocol_SubCategory PSC ON EEI.ProtocolSubCategoryId = PSC.Id
			LEFT JOIN Tzh_Protocol_Category PC ON PSC.CategoryId = PC.Id
			LEFT JOIN Tzh_Protocol P ON PC.ProtocolId = P.Id
			LEFT JOIN Tzh_CarbonEmissionLocation CEL ON EEI.CarbonEmissionLocationId = CEL.Id
            WHERE EEI.SiteName = #{siteName} AND P.NameEN = #{protocol} AND EEI.IsDeleted = 0 
            ORDER BY PC.CategoryName, PSC.SubCategoryName
            """)
    public List<TzhExpectedEmissionIsoVO> list(@Param("siteName") String siteName, @Param("protocol") String protocol);

}