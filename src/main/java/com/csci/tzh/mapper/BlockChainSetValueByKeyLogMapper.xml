<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.BlockChainSetValueByKeyLogMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.BlockChainSetValueByKeyLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 26 11:23:15 HKT 2022.
    -->
    <result column="Logdatetime" jdbcType="VARCHAR" property="logdatetime" />
    <result column="FilePath" jdbcType="VARCHAR" property="filepath" />
    <result column="FileName" jdbcType="VARCHAR" property="filename" />
    <result column="MD5Code" jdbcType="VARCHAR" property="md5code" />
    <result column="RequestStatusCode" jdbcType="VARCHAR" property="requeststatuscode" />
    <result column="RequestText" jdbcType="VARCHAR" property="requesttext" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 26 11:23:15 HKT 2022.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 26 11:23:15 HKT 2022.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 26 11:23:15 HKT 2022.
    -->
    Logdatetime, FilePath, FileName, MD5Code, RequestStatusCode, RequestText
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.BlockChainSetValueByKeyLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 26 11:23:15 HKT 2022.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from BlockChain_SetValueByKey_Log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.BlockChainSetValueByKeyLogExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 26 11:23:15 HKT 2022.
    -->
    delete from BlockChain_SetValueByKey_Log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.BlockChainSetValueByKeyLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 26 11:23:15 HKT 2022.
    -->
    insert into BlockChain_SetValueByKey_Log (Logdatetime, FilePath, FileName, 
      MD5Code, RequestStatusCode, RequestText
      )
    values (#{logdatetime,jdbcType=VARCHAR}, #{filepath,jdbcType=VARCHAR}, #{filename,jdbcType=VARCHAR}, 
      #{md5code,jdbcType=VARCHAR}, #{requeststatuscode,jdbcType=VARCHAR}, #{requesttext,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.BlockChainSetValueByKeyLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 26 11:23:15 HKT 2022.
    -->
    insert into BlockChain_SetValueByKey_Log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="logdatetime != null">
        Logdatetime,
      </if>
      <if test="filepath != null">
        FilePath,
      </if>
      <if test="filename != null">
        FileName,
      </if>
      <if test="md5code != null">
        MD5Code,
      </if>
      <if test="requeststatuscode != null">
        RequestStatusCode,
      </if>
      <if test="requesttext != null">
        RequestText,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="logdatetime != null">
        #{logdatetime,jdbcType=VARCHAR},
      </if>
      <if test="filepath != null">
        #{filepath,jdbcType=VARCHAR},
      </if>
      <if test="filename != null">
        #{filename,jdbcType=VARCHAR},
      </if>
      <if test="md5code != null">
        #{md5code,jdbcType=VARCHAR},
      </if>
      <if test="requeststatuscode != null">
        #{requeststatuscode,jdbcType=VARCHAR},
      </if>
      <if test="requesttext != null">
        #{requesttext,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.BlockChainSetValueByKeyLogExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 26 11:23:15 HKT 2022.
    -->
    select count(*) from BlockChain_SetValueByKey_Log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 26 11:23:15 HKT 2022.
    -->
    update BlockChain_SetValueByKey_Log
    <set>
      <if test="row.logdatetime != null">
        Logdatetime = #{row.logdatetime,jdbcType=VARCHAR},
      </if>
      <if test="row.filepath != null">
        FilePath = #{row.filepath,jdbcType=VARCHAR},
      </if>
      <if test="row.filename != null">
        FileName = #{row.filename,jdbcType=VARCHAR},
      </if>
      <if test="row.md5code != null">
        MD5Code = #{row.md5code,jdbcType=VARCHAR},
      </if>
      <if test="row.requeststatuscode != null">
        RequestStatusCode = #{row.requeststatuscode,jdbcType=VARCHAR},
      </if>
      <if test="row.requesttext != null">
        RequestText = #{row.requesttext,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 26 11:23:15 HKT 2022.
    -->
    update BlockChain_SetValueByKey_Log
    set Logdatetime = #{row.logdatetime,jdbcType=VARCHAR},
      FilePath = #{row.filepath,jdbcType=VARCHAR},
      FileName = #{row.filename,jdbcType=VARCHAR},
      MD5Code = #{row.md5code,jdbcType=VARCHAR},
      RequestStatusCode = #{row.requeststatuscode,jdbcType=VARCHAR},
      RequestText = #{row.requesttext,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>