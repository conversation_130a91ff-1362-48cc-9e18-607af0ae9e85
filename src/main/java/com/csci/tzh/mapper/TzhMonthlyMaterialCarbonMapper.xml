<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.TzhMonthlyMaterialCarbonMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.TzhMonthlyMaterialCarbon">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 19 10:02:06 HKT 2023.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="ChineseName" jdbcType="NVARCHAR" property="chinesename" />
    <result column="MaterialCode" jdbcType="NVARCHAR" property="materialcode" />
    <result column="MaterialAttribute" jdbcType="NVARCHAR" property="materialattribute" />
    <result column="SiteName" jdbcType="NVARCHAR" property="sitename" />
    <result column="ProtocolId" jdbcType="CHAR" property="protocolid" />
    <result column="RecordYearMonth" jdbcType="INTEGER" property="recordyearmonth" />
    <result column="BillNo" jdbcType="NVARCHAR" property="billno" />
    <result column="Qty" jdbcType="DECIMAL" property="qty" />
    <result column="Unit" jdbcType="NVARCHAR" property="unit" />
    <result column="TransportFactor" jdbcType="NUMERIC" property="transportfactor" />
    <result column="TransportFactorUnit" jdbcType="NVARCHAR" property="transportfactorunit" />
    <result column="TransportDistance" jdbcType="NUMERIC" property="transportdistance" />
    <result column="TransportDistanceUnit" jdbcType="NVARCHAR" property="transportdistanceunit" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
    <result column="CreatedBy" jdbcType="NVARCHAR" property="createdby" />
    <result column="DeletedTime" jdbcType="TIMESTAMP" property="deletedtime" />
    <result column="DeletedBy" jdbcType="NVARCHAR" property="deletedby" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 19 10:02:06 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 19 10:02:06 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 19 10:02:06 HKT 2023.
    -->
    Id, ChineseName, MaterialCode, MaterialAttribute, SiteName, ProtocolId, RecordYearMonth, 
    BillNo, Qty, Unit, TransportFactor, TransportFactorUnit, TransportDistance, TransportDistanceUnit, 
    CreatedTime, CreatedBy, DeletedTime, DeletedBy, IsDeleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.TzhMonthlyMaterialCarbonExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 19 10:02:06 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_MonthlyMaterialCarbon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.TzhMonthlyMaterialCarbonExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 19 10:02:06 HKT 2023.
    -->
    delete from Tzh_MonthlyMaterialCarbon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.TzhMonthlyMaterialCarbon">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 19 10:02:06 HKT 2023.
    -->
    insert into Tzh_MonthlyMaterialCarbon (Id, ChineseName, MaterialCode, 
      MaterialAttribute, SiteName, ProtocolId, 
      RecordYearMonth, BillNo, Qty, 
      Unit, TransportFactor, TransportFactorUnit, 
      TransportDistance, TransportDistanceUnit, 
      CreatedTime, CreatedBy, DeletedTime, 
      DeletedBy, IsDeleted)
    values (#{id,jdbcType=CHAR}, #{chinesename,jdbcType=NVARCHAR}, #{materialcode,jdbcType=NVARCHAR}, 
      #{materialattribute,jdbcType=NVARCHAR}, #{sitename,jdbcType=NVARCHAR}, #{protocolid,jdbcType=CHAR}, 
      #{recordyearmonth,jdbcType=INTEGER}, #{billno,jdbcType=NVARCHAR}, #{qty,jdbcType=DECIMAL}, 
      #{unit,jdbcType=NVARCHAR}, #{transportfactor,jdbcType=NUMERIC}, #{transportfactorunit,jdbcType=NVARCHAR}, 
      #{transportdistance,jdbcType=NUMERIC}, #{transportdistanceunit,jdbcType=NVARCHAR}, 
      #{createdtime,jdbcType=TIMESTAMP}, #{createdby,jdbcType=NVARCHAR}, #{deletedtime,jdbcType=TIMESTAMP}, 
      #{deletedby,jdbcType=NVARCHAR}, #{isdeleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.TzhMonthlyMaterialCarbon">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 19 10:02:06 HKT 2023.
    -->
    insert into Tzh_MonthlyMaterialCarbon
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="chinesename != null">
        ChineseName,
      </if>
      <if test="materialcode != null">
        MaterialCode,
      </if>
      <if test="materialattribute != null">
        MaterialAttribute,
      </if>
      <if test="sitename != null">
        SiteName,
      </if>
      <if test="protocolid != null">
        ProtocolId,
      </if>
      <if test="recordyearmonth != null">
        RecordYearMonth,
      </if>
      <if test="billno != null">
        BillNo,
      </if>
      <if test="qty != null">
        Qty,
      </if>
      <if test="unit != null">
        Unit,
      </if>
      <if test="transportfactor != null">
        TransportFactor,
      </if>
      <if test="transportfactorunit != null">
        TransportFactorUnit,
      </if>
      <if test="transportdistance != null">
        TransportDistance,
      </if>
      <if test="transportdistanceunit != null">
        TransportDistanceUnit,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="deletedtime != null">
        DeletedTime,
      </if>
      <if test="deletedby != null">
        DeletedBy,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="chinesename != null">
        #{chinesename,jdbcType=NVARCHAR},
      </if>
      <if test="materialcode != null">
        #{materialcode,jdbcType=NVARCHAR},
      </if>
      <if test="materialattribute != null">
        #{materialattribute,jdbcType=NVARCHAR},
      </if>
      <if test="sitename != null">
        #{sitename,jdbcType=NVARCHAR},
      </if>
      <if test="protocolid != null">
        #{protocolid,jdbcType=CHAR},
      </if>
      <if test="recordyearmonth != null">
        #{recordyearmonth,jdbcType=INTEGER},
      </if>
      <if test="billno != null">
        #{billno,jdbcType=NVARCHAR},
      </if>
      <if test="qty != null">
        #{qty,jdbcType=DECIMAL},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=NVARCHAR},
      </if>
      <if test="transportfactor != null">
        #{transportfactor,jdbcType=NUMERIC},
      </if>
      <if test="transportfactorunit != null">
        #{transportfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="transportdistance != null">
        #{transportdistance,jdbcType=NUMERIC},
      </if>
      <if test="transportdistanceunit != null">
        #{transportdistanceunit,jdbcType=NVARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=NVARCHAR},
      </if>
      <if test="deletedtime != null">
        #{deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedby != null">
        #{deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.TzhMonthlyMaterialCarbonExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 19 10:02:06 HKT 2023.
    -->
    select count(*) from Tzh_MonthlyMaterialCarbon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 19 10:02:06 HKT 2023.
    -->
    update Tzh_MonthlyMaterialCarbon
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.chinesename != null">
        ChineseName = #{row.chinesename,jdbcType=NVARCHAR},
      </if>
      <if test="row.materialcode != null">
        MaterialCode = #{row.materialcode,jdbcType=NVARCHAR},
      </if>
      <if test="row.materialattribute != null">
        MaterialAttribute = #{row.materialattribute,jdbcType=NVARCHAR},
      </if>
      <if test="row.sitename != null">
        SiteName = #{row.sitename,jdbcType=NVARCHAR},
      </if>
      <if test="row.protocolid != null">
        ProtocolId = #{row.protocolid,jdbcType=CHAR},
      </if>
      <if test="row.recordyearmonth != null">
        RecordYearMonth = #{row.recordyearmonth,jdbcType=INTEGER},
      </if>
      <if test="row.billno != null">
        BillNo = #{row.billno,jdbcType=NVARCHAR},
      </if>
      <if test="row.qty != null">
        Qty = #{row.qty,jdbcType=DECIMAL},
      </if>
      <if test="row.unit != null">
        Unit = #{row.unit,jdbcType=NVARCHAR},
      </if>
      <if test="row.transportfactor != null">
        TransportFactor = #{row.transportfactor,jdbcType=NUMERIC},
      </if>
      <if test="row.transportfactorunit != null">
        TransportFactorUnit = #{row.transportfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.transportdistance != null">
        TransportDistance = #{row.transportdistance,jdbcType=NUMERIC},
      </if>
      <if test="row.transportdistanceunit != null">
        TransportDistanceUnit = #{row.transportdistanceunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdtime != null">
        CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createdby != null">
        CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      </if>
      <if test="row.deletedtime != null">
        DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deletedby != null">
        DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="row.isdeleted != null">
        IsDeleted = #{row.isdeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 19 10:02:06 HKT 2023.
    -->
    update Tzh_MonthlyMaterialCarbon
    set Id = #{row.id,jdbcType=CHAR},
      ChineseName = #{row.chinesename,jdbcType=NVARCHAR},
      MaterialCode = #{row.materialcode,jdbcType=NVARCHAR},
      MaterialAttribute = #{row.materialattribute,jdbcType=NVARCHAR},
      SiteName = #{row.sitename,jdbcType=NVARCHAR},
      ProtocolId = #{row.protocolid,jdbcType=CHAR},
      RecordYearMonth = #{row.recordyearmonth,jdbcType=INTEGER},
      BillNo = #{row.billno,jdbcType=NVARCHAR},
      Qty = #{row.qty,jdbcType=DECIMAL},
      Unit = #{row.unit,jdbcType=NVARCHAR},
      TransportFactor = #{row.transportfactor,jdbcType=NUMERIC},
      TransportFactorUnit = #{row.transportfactorunit,jdbcType=NVARCHAR},
      TransportDistance = #{row.transportdistance,jdbcType=NUMERIC},
      TransportDistanceUnit = #{row.transportdistanceunit,jdbcType=NVARCHAR},
      CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      IsDeleted = #{row.isdeleted,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>