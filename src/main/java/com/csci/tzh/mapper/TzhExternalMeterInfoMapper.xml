<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.TzhExternalMeterInfoMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.TzhExternalMeterInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 03 16:59:30 HKT 2023.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="MeterNo" jdbcType="NVARCHAR" property="meterno" />
    <result column="MaterialName" jdbcType="NVARCHAR" property="materialname" />
    <result column="Region" jdbcType="NVARCHAR" property="region" />
    <result column="SiteName" jdbcType="NVARCHAR" property="sitename" />
    <result column="CarbonEmissionLocation" jdbcType="NVARCHAR" property="carbonemissionlocation" />
    <result column="QtyUnit" jdbcType="NVARCHAR" property="qtyunit" />
    <result column="CreatedBy" jdbcType="NVARCHAR" property="createdby" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 03 16:59:30 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 03 16:59:30 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 03 16:59:30 HKT 2023.
    -->
    Id, MeterNo, MaterialName, Region, SiteName, CarbonEmissionLocation, QtyUnit, CreatedBy, 
    CreatedTime
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.TzhExternalMeterInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 03 16:59:30 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_External_MeterInfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.TzhExternalMeterInfoExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 03 16:59:30 HKT 2023.
    -->
    delete from Tzh_External_MeterInfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.TzhExternalMeterInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 03 16:59:30 HKT 2023.
    -->
    insert into Tzh_External_MeterInfo (Id, MeterNo, MaterialName, 
      Region, SiteName, CarbonEmissionLocation, 
      QtyUnit, CreatedBy, CreatedTime
      )
    values (#{id,jdbcType=CHAR}, #{meterno,jdbcType=NVARCHAR}, #{materialname,jdbcType=NVARCHAR}, 
      #{region,jdbcType=NVARCHAR}, #{sitename,jdbcType=NVARCHAR}, #{carbonemissionlocation,jdbcType=NVARCHAR}, 
      #{qtyunit,jdbcType=NVARCHAR}, #{createdby,jdbcType=NVARCHAR}, #{createdtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.TzhExternalMeterInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 03 16:59:30 HKT 2023.
    -->
    insert into Tzh_External_MeterInfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="meterno != null">
        MeterNo,
      </if>
      <if test="materialname != null">
        MaterialName,
      </if>
      <if test="region != null">
        Region,
      </if>
      <if test="sitename != null">
        SiteName,
      </if>
      <if test="carbonemissionlocation != null">
        CarbonEmissionLocation,
      </if>
      <if test="qtyunit != null">
        QtyUnit,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="meterno != null">
        #{meterno,jdbcType=NVARCHAR},
      </if>
      <if test="materialname != null">
        #{materialname,jdbcType=NVARCHAR},
      </if>
      <if test="region != null">
        #{region,jdbcType=NVARCHAR},
      </if>
      <if test="sitename != null">
        #{sitename,jdbcType=NVARCHAR},
      </if>
      <if test="carbonemissionlocation != null">
        #{carbonemissionlocation,jdbcType=NVARCHAR},
      </if>
      <if test="qtyunit != null">
        #{qtyunit,jdbcType=NVARCHAR},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=NVARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.TzhExternalMeterInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 03 16:59:30 HKT 2023.
    -->
    select count(*) from Tzh_External_MeterInfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 03 16:59:30 HKT 2023.
    -->
    update Tzh_External_MeterInfo
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.meterno != null">
        MeterNo = #{row.meterno,jdbcType=NVARCHAR},
      </if>
      <if test="row.materialname != null">
        MaterialName = #{row.materialname,jdbcType=NVARCHAR},
      </if>
      <if test="row.region != null">
        Region = #{row.region,jdbcType=NVARCHAR},
      </if>
      <if test="row.sitename != null">
        SiteName = #{row.sitename,jdbcType=NVARCHAR},
      </if>
      <if test="row.carbonemissionlocation != null">
        CarbonEmissionLocation = #{row.carbonemissionlocation,jdbcType=NVARCHAR},
      </if>
      <if test="row.qtyunit != null">
        QtyUnit = #{row.qtyunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdby != null">
        CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdtime != null">
        CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 03 16:59:30 HKT 2023.
    -->
    update Tzh_External_MeterInfo
    set Id = #{row.id,jdbcType=CHAR},
      MeterNo = #{row.meterno,jdbcType=NVARCHAR},
      MaterialName = #{row.materialname,jdbcType=NVARCHAR},
      Region = #{row.region,jdbcType=NVARCHAR},
      SiteName = #{row.sitename,jdbcType=NVARCHAR},
      CarbonEmissionLocation = #{row.carbonemissionlocation,jdbcType=NVARCHAR},
      QtyUnit = #{row.qtyunit,jdbcType=NVARCHAR},
      CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>