<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.FResultMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.FResult">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 25 17:21:02 HKT 2022.
    -->
    <result column="SiteId" jdbcType="INTEGER" property="siteid" />
    <result column="SiteName" jdbcType="NVARCHAR" property="sitename" />
    <result column="RecordYearMonth" jdbcType="INTEGER" property="recordyearmonth" />
    <result column="CarbonEmissionLocation" jdbcType="NVARCHAR" property="carbonemissionlocation" />
    <result column="ScopeMain" jdbcType="NVARCHAR" property="scopemain" />
    <result column="ScopeDetail" jdbcType="NVARCHAR" property="scopedetail" />
    <result column="CarbonAmount" jdbcType="NUMERIC" property="carbonamount" />
    <result column="CalculateDate" jdbcType="DATE" property="calculatedate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 25 17:21:02 HKT 2022.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 25 17:21:02 HKT 2022.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 25 17:21:02 HKT 2022.
    -->
    SiteId, SiteName, RecordYearMonth, CarbonEmissionLocation, ScopeMain, ScopeDetail, 
    CarbonAmount, CalculateDate
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.FResultExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 25 17:21:02 HKT 2022.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from F_Result_Latest
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.FResultExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 25 17:21:02 HKT 2022.
    -->
    delete from F_Result_Latest
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.FResult">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 25 17:21:02 HKT 2022.
    -->
    insert into F_Result_Latest (SiteId, SiteName, RecordYearMonth,
      CarbonEmissionLocation, ScopeMain, ScopeDetail, 
      CarbonAmount, CalculateDate)
    values (#{siteid,jdbcType=INTEGER}, #{sitename,jdbcType=NVARCHAR}, #{recordyearmonth,jdbcType=INTEGER}, 
      #{carbonemissionlocation,jdbcType=NVARCHAR}, #{scopemain,jdbcType=NVARCHAR}, #{scopedetail,jdbcType=NVARCHAR}, 
      #{carbonamount,jdbcType=NUMERIC}, #{calculatedate,jdbcType=DATE})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.FResult">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 25 17:21:02 HKT 2022.
    -->
    insert into F_Result_Latest
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="siteid != null">
        SiteId,
      </if>
      <if test="sitename != null">
        SiteName,
      </if>
      <if test="recordyearmonth != null">
        RecordYearMonth,
      </if>
      <if test="carbonemissionlocation != null">
        CarbonEmissionLocation,
      </if>
      <if test="scopemain != null">
        ScopeMain,
      </if>
      <if test="scopedetail != null">
        ScopeDetail,
      </if>
      <if test="carbonamount != null">
        CarbonAmount,
      </if>
      <if test="calculatedate != null">
        CalculateDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="siteid != null">
        #{siteid,jdbcType=INTEGER},
      </if>
      <if test="sitename != null">
        #{sitename,jdbcType=NVARCHAR},
      </if>
      <if test="recordyearmonth != null">
        #{recordyearmonth,jdbcType=INTEGER},
      </if>
      <if test="carbonemissionlocation != null">
        #{carbonemissionlocation,jdbcType=NVARCHAR},
      </if>
      <if test="scopemain != null">
        #{scopemain,jdbcType=NVARCHAR},
      </if>
      <if test="scopedetail != null">
        #{scopedetail,jdbcType=NVARCHAR},
      </if>
      <if test="carbonamount != null">
        #{carbonamount,jdbcType=NUMERIC},
      </if>
      <if test="calculatedate != null">
        #{calculatedate,jdbcType=DATE},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.FResultExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 25 17:21:02 HKT 2022.
    -->
    select count(*) from F_Result_Latest
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 25 17:21:02 HKT 2022.
    -->
    update F_Result_Latest
    <set>
      <if test="row.siteid != null">
        SiteId = #{row.siteid,jdbcType=INTEGER},
      </if>
      <if test="row.sitename != null">
        SiteName = #{row.sitename,jdbcType=NVARCHAR},
      </if>
      <if test="row.recordyearmonth != null">
        RecordYearMonth = #{row.recordyearmonth,jdbcType=INTEGER},
      </if>
      <if test="row.carbonemissionlocation != null">
        CarbonEmissionLocation = #{row.carbonemissionlocation,jdbcType=NVARCHAR},
      </if>
      <if test="row.scopemain != null">
        ScopeMain = #{row.scopemain,jdbcType=NVARCHAR},
      </if>
      <if test="row.scopedetail != null">
        ScopeDetail = #{row.scopedetail,jdbcType=NVARCHAR},
      </if>
      <if test="row.carbonamount != null">
        CarbonAmount = #{row.carbonamount,jdbcType=NUMERIC},
      </if>
      <if test="row.calculatedate != null">
        CalculateDate = #{row.calculatedate,jdbcType=DATE},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 25 17:21:02 HKT 2022.
    -->
    update F_Result_Latest
    set SiteId = #{row.siteid,jdbcType=INTEGER},
      SiteName = #{row.sitename,jdbcType=NVARCHAR},
      RecordYearMonth = #{row.recordyearmonth,jdbcType=INTEGER},
      CarbonEmissionLocation = #{row.carbonemissionlocation,jdbcType=NVARCHAR},
      ScopeMain = #{row.scopemain,jdbcType=NVARCHAR},
      ScopeDetail = #{row.scopedetail,jdbcType=NVARCHAR},
      CarbonAmount = #{row.carbonamount,jdbcType=NUMERIC},
      CalculateDate = #{row.calculatedate,jdbcType=DATE}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>