package com.csci.tzh.mapper;

import com.csci.tzh.model.FResult;
import com.csci.tzh.model.FResultExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FResultMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Result_Latest
     *
     * @mbg.generated Wed May 25 17:21:02 HKT 2022
     */
    long countByExample(FResultExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Result_Latest
     *
     * @mbg.generated Wed May 25 17:21:02 HKT 2022
     */
    int deleteByExample(FResultExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Result_Latest
     *
     * @mbg.generated Wed May 25 17:21:02 HKT 2022
     */
    int insert(FResult row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Result_Latest
     *
     * @mbg.generated Wed May 25 17:21:02 HKT 2022
     */
    int insertSelective(FResult row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Result_Latest
     *
     * @mbg.generated Wed May 25 17:21:02 HKT 2022
     */
    List<FResult> selectByExample(FResultExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Result_Latest
     *
     * @mbg.generated Wed May 25 17:21:02 HKT 2022
     */
    int updateByExampleSelective(@Param("row") FResult row, @Param("example") FResultExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Result_Latest
     *
     * @mbg.generated Wed May 25 17:21:02 HKT 2022
     */
    int updateByExample(@Param("row") FResult row, @Param("example") FResultExample example);
}