<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.TzhEmissionReductionMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.TzhEmissionReduction">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 22 15:51:12 HKT 2023.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="HeadId" jdbcType="VARCHAR" property="headid" />
    <result column="Type" jdbcType="NVARCHAR" property="type" />
    <result column="RecordYearMonth" jdbcType="INTEGER" property="recordyearmonth" />
    <result column="CarbonReductionAmount" jdbcType="NUMERIC" property="carbonreductionamount" />
    <result column="CarbonUnit" jdbcType="NVARCHAR" property="carbonunit" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
    <result column="CreatedBy" jdbcType="VARCHAR" property="createdby" />
    <result column="DeletedTime" jdbcType="TIMESTAMP" property="deletedtime" />
    <result column="DeletedBy" jdbcType="VARCHAR" property="deletedby" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 22 15:51:12 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 22 15:51:12 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 22 15:51:12 HKT 2023.
    -->
    Id, HeadId, Type, RecordYearMonth, CarbonReductionAmount, CarbonUnit, CreatedTime, 
    CreatedBy, DeletedTime, DeletedBy, IsDeleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.TzhEmissionReductionExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 22 15:51:12 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_EmissionReduction
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.TzhEmissionReductionExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 22 15:51:12 HKT 2023.
    -->
    delete from Tzh_EmissionReduction
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.TzhEmissionReduction">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 22 15:51:12 HKT 2023.
    -->
    insert into Tzh_EmissionReduction (Id, HeadId, Type, 
      RecordYearMonth, CarbonReductionAmount, 
      CarbonUnit, CreatedTime, CreatedBy, 
      DeletedTime, DeletedBy, IsDeleted
      )
    values (#{id,jdbcType=CHAR}, #{headid,jdbcType=VARCHAR}, #{type,jdbcType=NVARCHAR}, 
      #{recordyearmonth,jdbcType=INTEGER}, #{carbonreductionamount,jdbcType=NUMERIC}, 
      #{carbonunit,jdbcType=NVARCHAR}, #{createdtime,jdbcType=TIMESTAMP}, #{createdby,jdbcType=VARCHAR}, 
      #{deletedtime,jdbcType=TIMESTAMP}, #{deletedby,jdbcType=VARCHAR}, #{isdeleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.TzhEmissionReduction">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 22 15:51:12 HKT 2023.
    -->
    insert into Tzh_EmissionReduction
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="headid != null">
        HeadId,
      </if>
      <if test="type != null">
        Type,
      </if>
      <if test="recordyearmonth != null">
        RecordYearMonth,
      </if>
      <if test="carbonreductionamount != null">
        CarbonReductionAmount,
      </if>
      <if test="carbonunit != null">
        CarbonUnit,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="deletedtime != null">
        DeletedTime,
      </if>
      <if test="deletedby != null">
        DeletedBy,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="headid != null">
        #{headid,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=NVARCHAR},
      </if>
      <if test="recordyearmonth != null">
        #{recordyearmonth,jdbcType=INTEGER},
      </if>
      <if test="carbonreductionamount != null">
        #{carbonreductionamount,jdbcType=NUMERIC},
      </if>
      <if test="carbonunit != null">
        #{carbonunit,jdbcType=NVARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="deletedtime != null">
        #{deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedby != null">
        #{deletedby,jdbcType=VARCHAR},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.TzhEmissionReductionExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 22 15:51:12 HKT 2023.
    -->
    select count(*) from Tzh_EmissionReduction
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 22 15:51:12 HKT 2023.
    -->
    update Tzh_EmissionReduction
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.headid != null">
        HeadId = #{row.headid,jdbcType=VARCHAR},
      </if>
      <if test="row.type != null">
        Type = #{row.type,jdbcType=NVARCHAR},
      </if>
      <if test="row.recordyearmonth != null">
        RecordYearMonth = #{row.recordyearmonth,jdbcType=INTEGER},
      </if>
      <if test="row.carbonreductionamount != null">
        CarbonReductionAmount = #{row.carbonreductionamount,jdbcType=NUMERIC},
      </if>
      <if test="row.carbonunit != null">
        CarbonUnit = #{row.carbonunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdtime != null">
        CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createdby != null">
        CreatedBy = #{row.createdby,jdbcType=VARCHAR},
      </if>
      <if test="row.deletedtime != null">
        DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deletedby != null">
        DeletedBy = #{row.deletedby,jdbcType=VARCHAR},
      </if>
      <if test="row.isdeleted != null">
        IsDeleted = #{row.isdeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 22 15:51:12 HKT 2023.
    -->
    update Tzh_EmissionReduction
    set Id = #{row.id,jdbcType=CHAR},
      HeadId = #{row.headid,jdbcType=VARCHAR},
      Type = #{row.type,jdbcType=NVARCHAR},
      RecordYearMonth = #{row.recordyearmonth,jdbcType=INTEGER},
      CarbonReductionAmount = #{row.carbonreductionamount,jdbcType=NUMERIC},
      CarbonUnit = #{row.carbonunit,jdbcType=NVARCHAR},
      CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      CreatedBy = #{row.createdby,jdbcType=VARCHAR},
      DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      DeletedBy = #{row.deletedby,jdbcType=VARCHAR},
      IsDeleted = #{row.isdeleted,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>