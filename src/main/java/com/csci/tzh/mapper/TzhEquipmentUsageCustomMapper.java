package com.csci.tzh.mapper;

import com.csci.tzh.vo.TzhEquipmentUsageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TzhEquipmentUsageCustomMapper {

    @Select("""
			SELECT EU.*, protocolid = P.id, protocol = P.Name, protocolsc = P.name_sc, protocolen = P.name_en, 
			subcategoryname = SC.sub_category_name, subcategorynamesc = SC.sub_category_name_sc, subcategorynameen = SC.sub_category_name_en
			FROM Tzh_EquipmentUsage EU
			LEFT JOIN t_protocol_sub_category SC ON EU.ProtocolSubCategoryId = SC.Id
			LEFT JOIN t_protocol_category C ON C.Id = SC.category_id
			LEFT JOIN t_protocol P ON C.protocol_id = P.Id
			WHERE EU.SiteName = #{siteName}
			AND P.name_en = #{protocol}
			AND EU.IsDeleted = 0
				""")
    public List<TzhEquipmentUsageVO> list(@Param("siteName") String siteName,
												 @Param("protocol") String protocol);

}
