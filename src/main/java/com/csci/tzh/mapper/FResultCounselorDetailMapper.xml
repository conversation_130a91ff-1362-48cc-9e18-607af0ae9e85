<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.FResultCounselorDetailMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.FResultCounselorDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 06 11:53:51 CST 2025.
    -->
    <id column="id" jdbcType="NVARCHAR" property="id" />
    <result column="head_id" jdbcType="NVARCHAR" property="headId" />
    <result column="carbon_emission_location" jdbcType="NVARCHAR" property="carbonEmissionLocation" />
    <result column="scope_main" jdbcType="NVARCHAR" property="scopeMain" />
    <result column="scope_detail" jdbcType="NVARCHAR" property="scopeDetail" />
    <result column="carbon_amount" jdbcType="NUMERIC" property="carbonAmount" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 06 11:53:51 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 06 11:53:51 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 06 11:53:51 CST 2025.
    -->
    id, head_id, carbon_emission_location, scope_main, scope_detail, carbon_amount, creation_time
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.FResultCounselorDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 06 11:53:51 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from f_result_counselor_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 06 11:53:51 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    from f_result_counselor_detail
    where id = #{id,jdbcType=NVARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 06 11:53:51 CST 2025.
    -->
    delete from f_result_counselor_detail
    where id = #{id,jdbcType=NVARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.FResultCounselorDetailExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 06 11:53:51 CST 2025.
    -->
    delete from f_result_counselor_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.FResultCounselorDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 06 11:53:51 CST 2025.
    -->
    insert into f_result_counselor_detail (id, head_id, carbon_emission_location, 
      scope_main, scope_detail, carbon_amount, 
      creation_time)
    values (#{id,jdbcType=NVARCHAR}, #{headId,jdbcType=NVARCHAR}, #{carbonEmissionLocation,jdbcType=NVARCHAR}, 
      #{scopeMain,jdbcType=NVARCHAR}, #{scopeDetail,jdbcType=NVARCHAR}, #{carbonAmount,jdbcType=NUMERIC}, 
      #{creationTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.FResultCounselorDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 06 11:53:51 CST 2025.
    -->
    insert into f_result_counselor_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="headId != null">
        head_id,
      </if>
      <if test="carbonEmissionLocation != null">
        carbon_emission_location,
      </if>
      <if test="scopeMain != null">
        scope_main,
      </if>
      <if test="scopeDetail != null">
        scope_detail,
      </if>
      <if test="carbonAmount != null">
        carbon_amount,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=NVARCHAR},
      </if>
      <if test="headId != null">
        #{headId,jdbcType=NVARCHAR},
      </if>
      <if test="carbonEmissionLocation != null">
        #{carbonEmissionLocation,jdbcType=NVARCHAR},
      </if>
      <if test="scopeMain != null">
        #{scopeMain,jdbcType=NVARCHAR},
      </if>
      <if test="scopeDetail != null">
        #{scopeDetail,jdbcType=NVARCHAR},
      </if>
      <if test="carbonAmount != null">
        #{carbonAmount,jdbcType=NUMERIC},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.FResultCounselorDetailExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 06 11:53:51 CST 2025.
    -->
    select count(*) from f_result_counselor_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 06 11:53:51 CST 2025.
    -->
    update f_result_counselor_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=NVARCHAR},
      </if>
      <if test="record.headId != null">
        head_id = #{record.headId,jdbcType=NVARCHAR},
      </if>
      <if test="record.carbonEmissionLocation != null">
        carbon_emission_location = #{record.carbonEmissionLocation,jdbcType=NVARCHAR},
      </if>
      <if test="record.scopeMain != null">
        scope_main = #{record.scopeMain,jdbcType=NVARCHAR},
      </if>
      <if test="record.scopeDetail != null">
        scope_detail = #{record.scopeDetail,jdbcType=NVARCHAR},
      </if>
      <if test="record.carbonAmount != null">
        carbon_amount = #{record.carbonAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 06 11:53:51 CST 2025.
    -->
    update f_result_counselor_detail
    set id = #{record.id,jdbcType=NVARCHAR},
      head_id = #{record.headId,jdbcType=NVARCHAR},
      carbon_emission_location = #{record.carbonEmissionLocation,jdbcType=NVARCHAR},
      scope_main = #{record.scopeMain,jdbcType=NVARCHAR},
      scope_detail = #{record.scopeDetail,jdbcType=NVARCHAR},
      carbon_amount = #{record.carbonAmount,jdbcType=NUMERIC},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.tzh.model.FResultCounselorDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 06 11:53:51 CST 2025.
    -->
    update f_result_counselor_detail
    <set>
      <if test="headId != null">
        head_id = #{headId,jdbcType=NVARCHAR},
      </if>
      <if test="carbonEmissionLocation != null">
        carbon_emission_location = #{carbonEmissionLocation,jdbcType=NVARCHAR},
      </if>
      <if test="scopeMain != null">
        scope_main = #{scopeMain,jdbcType=NVARCHAR},
      </if>
      <if test="scopeDetail != null">
        scope_detail = #{scopeDetail,jdbcType=NVARCHAR},
      </if>
      <if test="carbonAmount != null">
        carbon_amount = #{carbonAmount,jdbcType=NUMERIC},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=NVARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.tzh.model.FResultCounselorDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jan 06 11:53:51 CST 2025.
    -->
    update f_result_counselor_detail
    set head_id = #{headId,jdbcType=NVARCHAR},
      carbon_emission_location = #{carbonEmissionLocation,jdbcType=NVARCHAR},
      scope_main = #{scopeMain,jdbcType=NVARCHAR},
      scope_detail = #{scopeDetail,jdbcType=NVARCHAR},
      carbon_amount = #{carbonAmount,jdbcType=NUMERIC},
      creation_time = #{creationTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=NVARCHAR}
  </update>
</mapper>