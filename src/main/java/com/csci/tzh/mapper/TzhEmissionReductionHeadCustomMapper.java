package com.csci.tzh.mapper;

import com.csci.susdev.controller.TzhEmissionReductionHeadController;
import com.csci.tzh.vo.FCdmsWaterCarbonFactorVO;
import com.csci.tzh.vo.TzhEmissionReductionHeadVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TzhEmissionReductionHeadCustomMapper {
	
    @Select("""
			SELECT ERH.*, region = R.Name, regionsc = R.NameSC, regionen = R.NameEN,
			CarbonEmissionLocation = CEL.Name, CarbonEmissionLocationSC = CEL.NameSC,
			CarbonEmissionLocationEN = CEL.NameEN, Protocol = P.Name, ProtocolSC = P.NameSC, ProtocolEN = P.NameEN,
			PC.CategoryName, PC.CategoryNameSC, PC.CategoryNameEN
			FROM Tzh_EmissionReductionHead ERH
			LEFT JOIN Tzh_CarbonEmissionLocation CEL ON ERH.CarbonEmissionLocationId = CEL.Id
			LEFT JOIN Tzh_Protocol_Category PC ON ERH.ProtocolCategoryId = PC.Id
			LEFT JOIN Tzh_Protocol P ON PC.ProtocolId = P.Id
			LEFT JOIN Tzh_ProjectInfo PI ON PI.Name = ERH.SiteName AND PI.IsDeleted = 0
			LEFT JOIN Tzh_Region R ON R.Id = PI.RegionId
			WHERE ERH.SiteName = #{siteName} 
			AND P.NameEN = #{protocol}
			AND (PC.CategoryName = #{categoryName} OR #{categoryName} = '')
			AND (CEL.Name = #{carbonEmissionLocation} OR #{carbonEmissionLocation} = '')
			AND ERH.IsDeleted = 0
			""")
    public List<TzhEmissionReductionHeadVO> list(@Param("siteName") String siteName,
												 @Param("protocol") String protocol,
												 @Param("categoryName") String categoryName,
												 @Param("carbonEmissionLocation") String carbonEmissionLocation);

}