package com.csci.tzh.mapper;

import com.csci.tzh.model.TzhExternalAccess;
import com.csci.tzh.model.TzhExternalAccessExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhExternalAccessMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_External_Access
	 * @mbg.generated  Fri Feb 17 15:56:03 HKT 2023
	 */
	long countByExample(TzhExternalAccessExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_External_Access
	 * @mbg.generated  Fri Feb 17 15:56:03 HKT 2023
	 */
	int deleteByExample(TzhExternalAccessExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_External_Access
	 * @mbg.generated  Fri Feb 17 15:56:03 HKT 2023
	 */
	int insert(TzhExternalAccess row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_External_Access
	 * @mbg.generated  Fri Feb 17 15:56:03 HKT 2023
	 */
	int insertSelective(TzhExternalAccess row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_External_Access
	 * @mbg.generated  Fri Feb 17 15:56:03 HKT 2023
	 */
	List<TzhExternalAccess> selectByExample(TzhExternalAccessExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_External_Access
	 * @mbg.generated  Fri Feb 17 15:56:03 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") TzhExternalAccess row,
			@Param("example") TzhExternalAccessExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_External_Access
	 * @mbg.generated  Fri Feb 17 15:56:03 HKT 2023
	 */
	int updateByExample(@Param("row") TzhExternalAccess row, @Param("example") TzhExternalAccessExample example);
}