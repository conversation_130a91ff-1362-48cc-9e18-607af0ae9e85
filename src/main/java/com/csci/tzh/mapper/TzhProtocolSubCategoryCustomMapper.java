package com.csci.tzh.mapper;

import com.csci.tzh.model.*;
import com.csci.tzh.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TzhProtocolSubCategoryCustomMapper {

//    @Select("""
//            SELECT CEL_SC.Id, carbonemissionlocationid = CEL.id, carbonemissionlocation = CEL.Name,
//            carbonemissionlocationsc = CEL.NameSC, carbonemissionlocationen = CEL.NameEN,
//            protocolid = P.Id, protocol = P.Name, protocolsc = P.NameSC, protocolen = P.NameEN,
//            PC.CategoryName, PC.CategoryNameEN, PC.CategoryNameSC, PSC.CategoryId, PSC.SubCategoryName, PSC.SubCategoryNameSC,
//            PSC.SubCategoryNameEN, subcategoryid = PSC.Id
//            FROM Tzh_Protocol P
//            LEFT JOIN Tzh_Protocol_Category PC ON PC.ProtocolId = P.Id
//            LEFT JOIN Tzh_Protocol_SubCategory PSC ON PSC.CategoryId = PC.Id
//            LEFT JOIN Tzh_CarbonEmissionLocation_SubCategory CEL_SC ON CEL_SC.SubCategoryId = PSC.Id
//            LEFT JOIN Tzh_CarbonEmissionLocation CEL ON CEL.Id = CEL_SC.CarbonEmissionLocationId
//            WHERE P.NameEN = #{protocol}
//            ORDER BY PC.CategoryName, PSC.SubCategoryName, CEL.Name
//            """)
//    public List<TzhProtocolSubCategoryVO> list(@Param("protocol") String protocol);

    @Select("""
            SELECT CEL.id,carbonemissionlocationid = CEL.id, carbonemissionlocation = CEL.Name,\s
                carbonemissionlocationsc = CEL.name_sc , carbonemissionlocationen = CEL.name_en ,
                protocolid = P.id, protocol = P.name , protocolsc = P.name_sc, protocolen = P.name_en ,
                PC.category_name , PC.category_name_en , PC.category_name_sc , PSC.category_id , PSC.sub_category_name , PSC.sub_category_name_sc ,
                PSC.sub_category_name_en , subcategoryid = PSC.Id
                FROM t_protocol P
                JOIN t_protocol_category PC ON PC.protocol_id = P.id and P.is_deleted =0
                JOIN t_protocol_sub_category PSC ON PSC.category_id = PC.id and PSC.is_deleted =0 and PC.is_deleted =0
                join t_protocol_detail pd on pd.sub_category_id  = PSC.id  and pd.is_deleted =0
                JOIN t_carbon_emission_location CEL ON CEL.id = pd.carbon_emission_location_id  and CEL.is_deleted =0
                 WHERE P.name_en = #{protocol}
                 ORDER BY PC.category_name , PSC.sub_category_name , CEL.name
            """)
    public List<TzhProtocolSubCategoryVO> list(@Param("protocol") String protocol);

}
