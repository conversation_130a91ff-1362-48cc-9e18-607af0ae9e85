<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.TzhEmissionReductionHeadMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.TzhEmissionReductionHead">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 29 09:17:55 HKT 2023.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="SiteName" jdbcType="NVARCHAR" property="sitename" />
    <result column="ProtocolCategoryId" jdbcType="CHAR" property="protocolcategoryid" />
    <result column="Title" jdbcType="NVARCHAR" property="title" />
    <result column="TitleSC" jdbcType="NVARCHAR" property="titlesc" />
    <result column="TitleEN" jdbcType="NVARCHAR" property="titleen" />
    <result column="CarbonEmissionLocationId" jdbcType="CHAR" property="carbonemissionlocationid" />
    <result column="MethodDescription" jdbcType="NVARCHAR" property="methoddescription" />
    <result column="MethodDescriptionSC" jdbcType="NVARCHAR" property="methoddescriptionsc" />
    <result column="MethodDescriptionEN" jdbcType="NVARCHAR" property="methoddescriptionen" />
    <result column="CalculationDescription" jdbcType="NVARCHAR" property="calculationdescription" />
    <result column="CalculationDescriptionSC" jdbcType="NVARCHAR" property="calculationdescriptionsc" />
    <result column="CalculationDescriptionEN" jdbcType="NVARCHAR" property="calculationdescriptionen" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
    <result column="CreatedBy" jdbcType="VARCHAR" property="createdby" />
    <result column="DeletedTime" jdbcType="TIMESTAMP" property="deletedtime" />
    <result column="DeletedBy" jdbcType="VARCHAR" property="deletedby" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 29 09:17:55 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 29 09:17:55 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 29 09:17:55 HKT 2023.
    -->
    Id, SiteName, ProtocolCategoryId, Title, TitleSC, TitleEN, CarbonEmissionLocationId, 
    MethodDescription, MethodDescriptionSC, MethodDescriptionEN, CalculationDescription, 
    CalculationDescriptionSC, CalculationDescriptionEN, CreatedTime, CreatedBy, DeletedTime, 
    DeletedBy, IsDeleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.TzhEmissionReductionHeadExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 29 09:17:55 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_EmissionReductionHead
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.TzhEmissionReductionHeadExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 29 09:17:55 HKT 2023.
    -->
    delete from Tzh_EmissionReductionHead
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.TzhEmissionReductionHead">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 29 09:17:55 HKT 2023.
    -->
    insert into Tzh_EmissionReductionHead (Id, SiteName, ProtocolCategoryId, 
      Title, TitleSC, TitleEN, 
      CarbonEmissionLocationId, MethodDescription, 
      MethodDescriptionSC, MethodDescriptionEN, 
      CalculationDescription, CalculationDescriptionSC, 
      CalculationDescriptionEN, CreatedTime, 
      CreatedBy, DeletedTime, DeletedBy, 
      IsDeleted)
    values (#{id,jdbcType=CHAR}, #{sitename,jdbcType=NVARCHAR}, #{protocolcategoryid,jdbcType=CHAR}, 
      #{title,jdbcType=NVARCHAR}, #{titlesc,jdbcType=NVARCHAR}, #{titleen,jdbcType=NVARCHAR}, 
      #{carbonemissionlocationid,jdbcType=CHAR}, #{methoddescription,jdbcType=NVARCHAR}, 
      #{methoddescriptionsc,jdbcType=NVARCHAR}, #{methoddescriptionen,jdbcType=NVARCHAR}, 
      #{calculationdescription,jdbcType=NVARCHAR}, #{calculationdescriptionsc,jdbcType=NVARCHAR}, 
      #{calculationdescriptionen,jdbcType=NVARCHAR}, #{createdtime,jdbcType=TIMESTAMP}, 
      #{createdby,jdbcType=VARCHAR}, #{deletedtime,jdbcType=TIMESTAMP}, #{deletedby,jdbcType=VARCHAR}, 
      #{isdeleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.TzhEmissionReductionHead">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 29 09:17:55 HKT 2023.
    -->
    insert into Tzh_EmissionReductionHead
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="sitename != null">
        SiteName,
      </if>
      <if test="protocolcategoryid != null">
        ProtocolCategoryId,
      </if>
      <if test="title != null">
        Title,
      </if>
      <if test="titlesc != null">
        TitleSC,
      </if>
      <if test="titleen != null">
        TitleEN,
      </if>
      <if test="carbonemissionlocationid != null">
        CarbonEmissionLocationId,
      </if>
      <if test="methoddescription != null">
        MethodDescription,
      </if>
      <if test="methoddescriptionsc != null">
        MethodDescriptionSC,
      </if>
      <if test="methoddescriptionen != null">
        MethodDescriptionEN,
      </if>
      <if test="calculationdescription != null">
        CalculationDescription,
      </if>
      <if test="calculationdescriptionsc != null">
        CalculationDescriptionSC,
      </if>
      <if test="calculationdescriptionen != null">
        CalculationDescriptionEN,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="deletedtime != null">
        DeletedTime,
      </if>
      <if test="deletedby != null">
        DeletedBy,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="sitename != null">
        #{sitename,jdbcType=NVARCHAR},
      </if>
      <if test="protocolcategoryid != null">
        #{protocolcategoryid,jdbcType=CHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=NVARCHAR},
      </if>
      <if test="titlesc != null">
        #{titlesc,jdbcType=NVARCHAR},
      </if>
      <if test="titleen != null">
        #{titleen,jdbcType=NVARCHAR},
      </if>
      <if test="carbonemissionlocationid != null">
        #{carbonemissionlocationid,jdbcType=CHAR},
      </if>
      <if test="methoddescription != null">
        #{methoddescription,jdbcType=NVARCHAR},
      </if>
      <if test="methoddescriptionsc != null">
        #{methoddescriptionsc,jdbcType=NVARCHAR},
      </if>
      <if test="methoddescriptionen != null">
        #{methoddescriptionen,jdbcType=NVARCHAR},
      </if>
      <if test="calculationdescription != null">
        #{calculationdescription,jdbcType=NVARCHAR},
      </if>
      <if test="calculationdescriptionsc != null">
        #{calculationdescriptionsc,jdbcType=NVARCHAR},
      </if>
      <if test="calculationdescriptionen != null">
        #{calculationdescriptionen,jdbcType=NVARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="deletedtime != null">
        #{deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedby != null">
        #{deletedby,jdbcType=VARCHAR},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.TzhEmissionReductionHeadExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 29 09:17:55 HKT 2023.
    -->
    select count(*) from Tzh_EmissionReductionHead
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 29 09:17:55 HKT 2023.
    -->
    update Tzh_EmissionReductionHead
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.sitename != null">
        SiteName = #{row.sitename,jdbcType=NVARCHAR},
      </if>
      <if test="row.protocolcategoryid != null">
        ProtocolCategoryId = #{row.protocolcategoryid,jdbcType=CHAR},
      </if>
      <if test="row.title != null">
        Title = #{row.title,jdbcType=NVARCHAR},
      </if>
      <if test="row.titlesc != null">
        TitleSC = #{row.titlesc,jdbcType=NVARCHAR},
      </if>
      <if test="row.titleen != null">
        TitleEN = #{row.titleen,jdbcType=NVARCHAR},
      </if>
      <if test="row.carbonemissionlocationid != null">
        CarbonEmissionLocationId = #{row.carbonemissionlocationid,jdbcType=CHAR},
      </if>
      <if test="row.methoddescription != null">
        MethodDescription = #{row.methoddescription,jdbcType=NVARCHAR},
      </if>
      <if test="row.methoddescriptionsc != null">
        MethodDescriptionSC = #{row.methoddescriptionsc,jdbcType=NVARCHAR},
      </if>
      <if test="row.methoddescriptionen != null">
        MethodDescriptionEN = #{row.methoddescriptionen,jdbcType=NVARCHAR},
      </if>
      <if test="row.calculationdescription != null">
        CalculationDescription = #{row.calculationdescription,jdbcType=NVARCHAR},
      </if>
      <if test="row.calculationdescriptionsc != null">
        CalculationDescriptionSC = #{row.calculationdescriptionsc,jdbcType=NVARCHAR},
      </if>
      <if test="row.calculationdescriptionen != null">
        CalculationDescriptionEN = #{row.calculationdescriptionen,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdtime != null">
        CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createdby != null">
        CreatedBy = #{row.createdby,jdbcType=VARCHAR},
      </if>
      <if test="row.deletedtime != null">
        DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deletedby != null">
        DeletedBy = #{row.deletedby,jdbcType=VARCHAR},
      </if>
      <if test="row.isdeleted != null">
        IsDeleted = #{row.isdeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 29 09:17:55 HKT 2023.
    -->
    update Tzh_EmissionReductionHead
    set Id = #{row.id,jdbcType=CHAR},
      SiteName = #{row.sitename,jdbcType=NVARCHAR},
      ProtocolCategoryId = #{row.protocolcategoryid,jdbcType=CHAR},
      Title = #{row.title,jdbcType=NVARCHAR},
      TitleSC = #{row.titlesc,jdbcType=NVARCHAR},
      TitleEN = #{row.titleen,jdbcType=NVARCHAR},
      CarbonEmissionLocationId = #{row.carbonemissionlocationid,jdbcType=CHAR},
      MethodDescription = #{row.methoddescription,jdbcType=NVARCHAR},
      MethodDescriptionSC = #{row.methoddescriptionsc,jdbcType=NVARCHAR},
      MethodDescriptionEN = #{row.methoddescriptionen,jdbcType=NVARCHAR},
      CalculationDescription = #{row.calculationdescription,jdbcType=NVARCHAR},
      CalculationDescriptionSC = #{row.calculationdescriptionsc,jdbcType=NVARCHAR},
      CalculationDescriptionEN = #{row.calculationdescriptionen,jdbcType=NVARCHAR},
      CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      CreatedBy = #{row.createdby,jdbcType=VARCHAR},
      DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      DeletedBy = #{row.deletedby,jdbcType=VARCHAR},
      IsDeleted = #{row.isdeleted,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>