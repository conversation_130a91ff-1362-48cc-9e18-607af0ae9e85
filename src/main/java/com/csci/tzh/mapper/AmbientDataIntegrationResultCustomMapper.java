package com.csci.tzh.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface AmbientDataIntegrationResultCustomMapper {
	
    @Select("""
            SELECT * FROM t_ambient_data_integration_result AS t
            WHERE t.org_id = #{organizationId} AND t.record_year_month/100 = #{year}
            ORDER BY t.classification, t.record_year_month
            """)
    public List<Map<String, String>> list(@Param("organizationId") String organizationId, @Param("year") Integer year);
}