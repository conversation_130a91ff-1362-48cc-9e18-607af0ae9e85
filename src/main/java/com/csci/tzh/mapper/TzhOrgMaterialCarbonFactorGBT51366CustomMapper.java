package com.csci.tzh.mapper;

import com.csci.tzh.model.TzhOrgMaterialCarbonFactorGBT51366;
import com.csci.tzh.model.TzhOrgMaterialCarbonFactorW50;
import com.csci.tzh.vo.TzhOrgMaterialCarbonFactorVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TzhOrgMaterialCarbonFactorGBT51366CustomMapper {


	@Select("""
			SELECT *
			FROM Tzh_Org_MaterialCarbonFactor_GBT51366 OMCF
			WHERE (OMCF.ChineseName LIKE '%' + #{chineseName} + '%' OR #{chineseName} = '')
			AND OMCF.IsDeleted = 0
			""")
	public List<TzhOrgMaterialCarbonFactorGBT51366> list(@Param("chineseName") String chineseName);

}