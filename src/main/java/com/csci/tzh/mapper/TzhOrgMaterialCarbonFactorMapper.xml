<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.TzhOrgMaterialCarbonFactorMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.TzhOrgMaterialCarbonFactor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 01 17:29:32 HKT 2024.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="ChineseName" jdbcType="NVARCHAR" property="chinesename" />
    <result column="MaterialCode" jdbcType="NVARCHAR" property="materialcode" />
    <result column="MaterialAttribute" jdbcType="NVARCHAR" property="materialattribute" />
    <result column="Supplier" jdbcType="NVARCHAR" property="supplier" />
    <result column="ProductionProcess" jdbcType="NVARCHAR" property="productionprocess" />
    <result column="Unit" jdbcType="NVARCHAR" property="unit" />
    <result column="CarbonFactor" jdbcType="NUMERIC" property="carbonfactor" />
    <result column="CarbonFactorUnit" jdbcType="NVARCHAR" property="carbonfactorunit" />
    <result column="ProtocolSubCategoryId" jdbcType="CHAR" property="protocolsubcategoryid" />
    <result column="Description" jdbcType="NVARCHAR" property="description" />
    <result column="SourceDescription" jdbcType="NVARCHAR" property="sourcedescription" />
    <result column="CreatedBy" jdbcType="NVARCHAR" property="createdby" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
    <result column="DeletedBy" jdbcType="NVARCHAR" property="deletedby" />
    <result column="DeletedTime" jdbcType="TIMESTAMP" property="deletedtime" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 01 17:29:32 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 01 17:29:32 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 01 17:29:32 HKT 2024.
    -->
    Id, ChineseName, MaterialCode, MaterialAttribute, Supplier, ProductionProcess, Unit, 
    CarbonFactor, CarbonFactorUnit, ProtocolSubCategoryId, Description, SourceDescription, 
    CreatedBy, CreatedTime, DeletedBy, DeletedTime, IsDeleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.TzhOrgMaterialCarbonFactorExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 01 17:29:32 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_Org_MaterialCarbonFactor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.TzhOrgMaterialCarbonFactorExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 01 17:29:32 HKT 2024.
    -->
    delete from Tzh_Org_MaterialCarbonFactor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.TzhOrgMaterialCarbonFactor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 01 17:29:32 HKT 2024.
    -->
    insert into Tzh_Org_MaterialCarbonFactor (Id, ChineseName, MaterialCode, 
      MaterialAttribute, Supplier, ProductionProcess, 
      Unit, CarbonFactor, CarbonFactorUnit, 
      ProtocolSubCategoryId, Description, SourceDescription, 
      CreatedBy, CreatedTime, DeletedBy, 
      DeletedTime, IsDeleted)
    values (#{id,jdbcType=CHAR}, #{chinesename,jdbcType=NVARCHAR}, #{materialcode,jdbcType=NVARCHAR}, 
      #{materialattribute,jdbcType=NVARCHAR}, #{supplier,jdbcType=NVARCHAR}, #{productionprocess,jdbcType=NVARCHAR}, 
      #{unit,jdbcType=NVARCHAR}, #{carbonfactor,jdbcType=NUMERIC}, #{carbonfactorunit,jdbcType=NVARCHAR}, 
      #{protocolsubcategoryid,jdbcType=CHAR}, #{description,jdbcType=NVARCHAR}, #{sourcedescription,jdbcType=NVARCHAR}, 
      #{createdby,jdbcType=NVARCHAR}, #{createdtime,jdbcType=TIMESTAMP}, #{deletedby,jdbcType=NVARCHAR}, 
      #{deletedtime,jdbcType=TIMESTAMP}, #{isdeleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.TzhOrgMaterialCarbonFactor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 01 17:29:32 HKT 2024.
    -->
    insert into Tzh_Org_MaterialCarbonFactor
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="chinesename != null">
        ChineseName,
      </if>
      <if test="materialcode != null">
        MaterialCode,
      </if>
      <if test="materialattribute != null">
        MaterialAttribute,
      </if>
      <if test="supplier != null">
        Supplier,
      </if>
      <if test="productionprocess != null">
        ProductionProcess,
      </if>
      <if test="unit != null">
        Unit,
      </if>
      <if test="carbonfactor != null">
        CarbonFactor,
      </if>
      <if test="carbonfactorunit != null">
        CarbonFactorUnit,
      </if>
      <if test="protocolsubcategoryid != null">
        ProtocolSubCategoryId,
      </if>
      <if test="description != null">
        Description,
      </if>
      <if test="sourcedescription != null">
        SourceDescription,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
      <if test="deletedby != null">
        DeletedBy,
      </if>
      <if test="deletedtime != null">
        DeletedTime,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="chinesename != null">
        #{chinesename,jdbcType=NVARCHAR},
      </if>
      <if test="materialcode != null">
        #{materialcode,jdbcType=NVARCHAR},
      </if>
      <if test="materialattribute != null">
        #{materialattribute,jdbcType=NVARCHAR},
      </if>
      <if test="supplier != null">
        #{supplier,jdbcType=NVARCHAR},
      </if>
      <if test="productionprocess != null">
        #{productionprocess,jdbcType=NVARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=NVARCHAR},
      </if>
      <if test="carbonfactor != null">
        #{carbonfactor,jdbcType=NUMERIC},
      </if>
      <if test="carbonfactorunit != null">
        #{carbonfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="protocolsubcategoryid != null">
        #{protocolsubcategoryid,jdbcType=CHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=NVARCHAR},
      </if>
      <if test="sourcedescription != null">
        #{sourcedescription,jdbcType=NVARCHAR},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=NVARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedby != null">
        #{deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="deletedtime != null">
        #{deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.TzhOrgMaterialCarbonFactorExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 01 17:29:32 HKT 2024.
    -->
    select count(*) from Tzh_Org_MaterialCarbonFactor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 01 17:29:32 HKT 2024.
    -->
    update Tzh_Org_MaterialCarbonFactor
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.chinesename != null">
        ChineseName = #{row.chinesename,jdbcType=NVARCHAR},
      </if>
      <if test="row.materialcode != null">
        MaterialCode = #{row.materialcode,jdbcType=NVARCHAR},
      </if>
      <if test="row.materialattribute != null">
        MaterialAttribute = #{row.materialattribute,jdbcType=NVARCHAR},
      </if>
      <if test="row.supplier != null">
        Supplier = #{row.supplier,jdbcType=NVARCHAR},
      </if>
      <if test="row.productionprocess != null">
        ProductionProcess = #{row.productionprocess,jdbcType=NVARCHAR},
      </if>
      <if test="row.unit != null">
        Unit = #{row.unit,jdbcType=NVARCHAR},
      </if>
      <if test="row.carbonfactor != null">
        CarbonFactor = #{row.carbonfactor,jdbcType=NUMERIC},
      </if>
      <if test="row.carbonfactorunit != null">
        CarbonFactorUnit = #{row.carbonfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.protocolsubcategoryid != null">
        ProtocolSubCategoryId = #{row.protocolsubcategoryid,jdbcType=CHAR},
      </if>
      <if test="row.description != null">
        Description = #{row.description,jdbcType=NVARCHAR},
      </if>
      <if test="row.sourcedescription != null">
        SourceDescription = #{row.sourcedescription,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdby != null">
        CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdtime != null">
        CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deletedby != null">
        DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="row.deletedtime != null">
        DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.isdeleted != null">
        IsDeleted = #{row.isdeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 01 17:29:32 HKT 2024.
    -->
    update Tzh_Org_MaterialCarbonFactor
    set Id = #{row.id,jdbcType=CHAR},
      ChineseName = #{row.chinesename,jdbcType=NVARCHAR},
      MaterialCode = #{row.materialcode,jdbcType=NVARCHAR},
      MaterialAttribute = #{row.materialattribute,jdbcType=NVARCHAR},
      Supplier = #{row.supplier,jdbcType=NVARCHAR},
      ProductionProcess = #{row.productionprocess,jdbcType=NVARCHAR},
      Unit = #{row.unit,jdbcType=NVARCHAR},
      CarbonFactor = #{row.carbonfactor,jdbcType=NUMERIC},
      CarbonFactorUnit = #{row.carbonfactorunit,jdbcType=NVARCHAR},
      ProtocolSubCategoryId = #{row.protocolsubcategoryid,jdbcType=CHAR},
      Description = #{row.description,jdbcType=NVARCHAR},
      SourceDescription = #{row.sourcedescription,jdbcType=NVARCHAR},
      CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      IsDeleted = #{row.isdeleted,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>