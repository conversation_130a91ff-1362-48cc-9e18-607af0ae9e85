<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.TzhOrgMaterialCarbonFactorW50Mapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.TzhOrgMaterialCarbonFactorW50">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:14 HKT 2023.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="ChineseName" jdbcType="NVARCHAR" property="chinesename" />
    <result column="UpstreamEmission" jdbcType="NUMERIC" property="upstreamemission" />
    <result column="DownstreamEmission" jdbcType="NUMERIC" property="downstreamemission" />
    <result column="EmissionUnit" jdbcType="NVARCHAR" property="emissionunit" />
    <result column="EmissionStage" jdbcType="NVARCHAR" property="emissionstage" />
    <result column="Uncertainty" jdbcType="NVARCHAR" property="uncertainty" />
    <result column="Others" jdbcType="NVARCHAR" property="others" />
    <result column="DataTime" jdbcType="NVARCHAR" property="datatime" />
    <result column="Datasource" jdbcType="NVARCHAR" property="datasource" />
    <result column="CreatedBy" jdbcType="NVARCHAR" property="createdby" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
    <result column="DeletedBy" jdbcType="NVARCHAR" property="deletedby" />
    <result column="DeletedTime" jdbcType="TIMESTAMP" property="deletedtime" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:14 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:14 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:14 HKT 2023.
    -->
    Id, ChineseName, UpstreamEmission, DownstreamEmission, EmissionUnit, EmissionStage, 
    Uncertainty, Others, DataTime, Datasource, CreatedBy, CreatedTime, DeletedBy, DeletedTime, 
    IsDeleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.TzhOrgMaterialCarbonFactorW50Example" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:14 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_Org_MaterialCarbonFactor_W50
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.TzhOrgMaterialCarbonFactorW50Example">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:14 HKT 2023.
    -->
    delete from Tzh_Org_MaterialCarbonFactor_W50
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.TzhOrgMaterialCarbonFactorW50">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:14 HKT 2023.
    -->
    insert into Tzh_Org_MaterialCarbonFactor_W50 (Id, ChineseName, UpstreamEmission, 
      DownstreamEmission, EmissionUnit, EmissionStage, 
      Uncertainty, Others, DataTime, 
      Datasource, CreatedBy, CreatedTime, 
      DeletedBy, DeletedTime, IsDeleted
      )
    values (#{id,jdbcType=CHAR}, #{chinesename,jdbcType=NVARCHAR}, #{upstreamemission,jdbcType=NUMERIC}, 
      #{downstreamemission,jdbcType=NUMERIC}, #{emissionunit,jdbcType=NVARCHAR}, #{emissionstage,jdbcType=NVARCHAR}, 
      #{uncertainty,jdbcType=NVARCHAR}, #{others,jdbcType=NVARCHAR}, #{datatime,jdbcType=NVARCHAR}, 
      #{datasource,jdbcType=NVARCHAR}, #{createdby,jdbcType=NVARCHAR}, #{createdtime,jdbcType=TIMESTAMP}, 
      #{deletedby,jdbcType=NVARCHAR}, #{deletedtime,jdbcType=TIMESTAMP}, #{isdeleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.TzhOrgMaterialCarbonFactorW50">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:14 HKT 2023.
    -->
    insert into Tzh_Org_MaterialCarbonFactor_W50
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="chinesename != null">
        ChineseName,
      </if>
      <if test="upstreamemission != null">
        UpstreamEmission,
      </if>
      <if test="downstreamemission != null">
        DownstreamEmission,
      </if>
      <if test="emissionunit != null">
        EmissionUnit,
      </if>
      <if test="emissionstage != null">
        EmissionStage,
      </if>
      <if test="uncertainty != null">
        Uncertainty,
      </if>
      <if test="others != null">
        Others,
      </if>
      <if test="datatime != null">
        DataTime,
      </if>
      <if test="datasource != null">
        Datasource,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
      <if test="deletedby != null">
        DeletedBy,
      </if>
      <if test="deletedtime != null">
        DeletedTime,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="chinesename != null">
        #{chinesename,jdbcType=NVARCHAR},
      </if>
      <if test="upstreamemission != null">
        #{upstreamemission,jdbcType=NUMERIC},
      </if>
      <if test="downstreamemission != null">
        #{downstreamemission,jdbcType=NUMERIC},
      </if>
      <if test="emissionunit != null">
        #{emissionunit,jdbcType=NVARCHAR},
      </if>
      <if test="emissionstage != null">
        #{emissionstage,jdbcType=NVARCHAR},
      </if>
      <if test="uncertainty != null">
        #{uncertainty,jdbcType=NVARCHAR},
      </if>
      <if test="others != null">
        #{others,jdbcType=NVARCHAR},
      </if>
      <if test="datatime != null">
        #{datatime,jdbcType=NVARCHAR},
      </if>
      <if test="datasource != null">
        #{datasource,jdbcType=NVARCHAR},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=NVARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedby != null">
        #{deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="deletedtime != null">
        #{deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.TzhOrgMaterialCarbonFactorW50Example" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:14 HKT 2023.
    -->
    select count(*) from Tzh_Org_MaterialCarbonFactor_W50
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:14 HKT 2023.
    -->
    update Tzh_Org_MaterialCarbonFactor_W50
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.chinesename != null">
        ChineseName = #{row.chinesename,jdbcType=NVARCHAR},
      </if>
      <if test="row.upstreamemission != null">
        UpstreamEmission = #{row.upstreamemission,jdbcType=NUMERIC},
      </if>
      <if test="row.downstreamemission != null">
        DownstreamEmission = #{row.downstreamemission,jdbcType=NUMERIC},
      </if>
      <if test="row.emissionunit != null">
        EmissionUnit = #{row.emissionunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.emissionstage != null">
        EmissionStage = #{row.emissionstage,jdbcType=NVARCHAR},
      </if>
      <if test="row.uncertainty != null">
        Uncertainty = #{row.uncertainty,jdbcType=NVARCHAR},
      </if>
      <if test="row.others != null">
        Others = #{row.others,jdbcType=NVARCHAR},
      </if>
      <if test="row.datatime != null">
        DataTime = #{row.datatime,jdbcType=NVARCHAR},
      </if>
      <if test="row.datasource != null">
        Datasource = #{row.datasource,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdby != null">
        CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdtime != null">
        CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deletedby != null">
        DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="row.deletedtime != null">
        DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.isdeleted != null">
        IsDeleted = #{row.isdeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 11 15:13:14 HKT 2023.
    -->
    update Tzh_Org_MaterialCarbonFactor_W50
    set Id = #{row.id,jdbcType=CHAR},
      ChineseName = #{row.chinesename,jdbcType=NVARCHAR},
      UpstreamEmission = #{row.upstreamemission,jdbcType=NUMERIC},
      DownstreamEmission = #{row.downstreamemission,jdbcType=NUMERIC},
      EmissionUnit = #{row.emissionunit,jdbcType=NVARCHAR},
      EmissionStage = #{row.emissionstage,jdbcType=NVARCHAR},
      Uncertainty = #{row.uncertainty,jdbcType=NVARCHAR},
      Others = #{row.others,jdbcType=NVARCHAR},
      DataTime = #{row.datatime,jdbcType=NVARCHAR},
      Datasource = #{row.datasource,jdbcType=NVARCHAR},
      CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      IsDeleted = #{row.isdeleted,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>