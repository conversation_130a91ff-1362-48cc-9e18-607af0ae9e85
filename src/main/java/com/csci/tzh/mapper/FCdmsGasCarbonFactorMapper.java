package com.csci.tzh.mapper;

import com.csci.tzh.model.FCdmsGasCarbonFactor;
import com.csci.tzh.model.FCdmsGasCarbonFactorExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FCdmsGasCarbonFactorMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	long countByExample(FCdmsGasCarbonFactorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	int deleteByExample(FCdmsGasCarbonFactorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	int insert(FCdmsGasCarbonFactor row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	int insertSelective(FCdmsGasCarbonFactor row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	List<FCdmsGasCarbonFactor> selectByExample(FCdmsGasCarbonFactorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") FCdmsGasCarbonFactor row,
			@Param("example") FCdmsGasCarbonFactorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_GasCarbonFactor
	 * @mbg.generated  Thu Apr 20 14:52:07 HKT 2023
	 */
	int updateByExample(@Param("row") FCdmsGasCarbonFactor row, @Param("example") FCdmsGasCarbonFactorExample example);
}