<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.RawEpdWasteTransportationSyncLogMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.RawEpdWasteTransportationSyncLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 15:33:44 HKT 2022.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="Filename" jdbcType="NVARCHAR" property="filename" />
    <result column="StartDatetime" jdbcType="TIMESTAMP" property="startdatetime" />
    <result column="EndDatetime" jdbcType="TIMESTAMP" property="enddatetime" />
    <result column="RecordCount" jdbcType="INTEGER" property="recordcount" />
    <result column="InsertCount" jdbcType="INTEGER" property="insertcount" />
    <result column="UpdateCount" jdbcType="INTEGER" property="updatecount" />
    <result column="DeleteCount" jdbcType="INTEGER" property="deletecount" />
    <result column="ErrorDatetime" jdbcType="TIMESTAMP" property="errordatetime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.csci.tzh.model.RawEpdWasteTransportationSyncLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 15:33:44 HKT 2022.
    -->
    <result column="ErrorLog" jdbcType="LONGVARCHAR" property="errorlog" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 15:33:44 HKT 2022.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 15:33:44 HKT 2022.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 15:33:44 HKT 2022.
    -->
    Id, Filename, StartDatetime, EndDatetime, RecordCount, InsertCount, UpdateCount, 
    DeleteCount, ErrorDatetime
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 15:33:44 HKT 2022.
    -->
    ErrorLog
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.csci.tzh.model.RawEpdWasteTransportationSyncLogExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 15:33:44 HKT 2022.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from RawEpdWasteTransportation_SyncLog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.csci.tzh.model.RawEpdWasteTransportationSyncLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 15:33:44 HKT 2022.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from RawEpdWasteTransportation_SyncLog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.RawEpdWasteTransportationSyncLogExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 15:33:44 HKT 2022.
    -->
    delete from RawEpdWasteTransportation_SyncLog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.RawEpdWasteTransportationSyncLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 15:33:44 HKT 2022.
    -->
    insert into RawEpdWasteTransportation_SyncLog (Id, Filename, StartDatetime, 
      EndDatetime, RecordCount, InsertCount, 
      UpdateCount, DeleteCount, ErrorDatetime, 
      ErrorLog)
    values (#{id,jdbcType=CHAR}, #{filename,jdbcType=NVARCHAR}, #{startdatetime,jdbcType=TIMESTAMP}, 
      #{enddatetime,jdbcType=TIMESTAMP}, #{recordcount,jdbcType=INTEGER}, #{insertcount,jdbcType=INTEGER}, 
      #{updatecount,jdbcType=INTEGER}, #{deletecount,jdbcType=INTEGER}, #{errordatetime,jdbcType=TIMESTAMP}, 
      #{errorlog,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.RawEpdWasteTransportationSyncLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 15:33:44 HKT 2022.
    -->
    insert into RawEpdWasteTransportation_SyncLog
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="filename != null">
        Filename,
      </if>
      <if test="startdatetime != null">
        StartDatetime,
      </if>
      <if test="enddatetime != null">
        EndDatetime,
      </if>
      <if test="recordcount != null">
        RecordCount,
      </if>
      <if test="insertcount != null">
        InsertCount,
      </if>
      <if test="updatecount != null">
        UpdateCount,
      </if>
      <if test="deletecount != null">
        DeleteCount,
      </if>
      <if test="errordatetime != null">
        ErrorDatetime,
      </if>
      <if test="errorlog != null">
        ErrorLog,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="filename != null">
        #{filename,jdbcType=NVARCHAR},
      </if>
      <if test="startdatetime != null">
        #{startdatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="enddatetime != null">
        #{enddatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="recordcount != null">
        #{recordcount,jdbcType=INTEGER},
      </if>
      <if test="insertcount != null">
        #{insertcount,jdbcType=INTEGER},
      </if>
      <if test="updatecount != null">
        #{updatecount,jdbcType=INTEGER},
      </if>
      <if test="deletecount != null">
        #{deletecount,jdbcType=INTEGER},
      </if>
      <if test="errordatetime != null">
        #{errordatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorlog != null">
        #{errorlog,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.RawEpdWasteTransportationSyncLogExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 15:33:44 HKT 2022.
    -->
    select count(*) from RawEpdWasteTransportation_SyncLog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 15:33:44 HKT 2022.
    -->
    update RawEpdWasteTransportation_SyncLog
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.filename != null">
        Filename = #{row.filename,jdbcType=NVARCHAR},
      </if>
      <if test="row.startdatetime != null">
        StartDatetime = #{row.startdatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.enddatetime != null">
        EndDatetime = #{row.enddatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.recordcount != null">
        RecordCount = #{row.recordcount,jdbcType=INTEGER},
      </if>
      <if test="row.insertcount != null">
        InsertCount = #{row.insertcount,jdbcType=INTEGER},
      </if>
      <if test="row.updatecount != null">
        UpdateCount = #{row.updatecount,jdbcType=INTEGER},
      </if>
      <if test="row.deletecount != null">
        DeleteCount = #{row.deletecount,jdbcType=INTEGER},
      </if>
      <if test="row.errordatetime != null">
        ErrorDatetime = #{row.errordatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.errorlog != null">
        ErrorLog = #{row.errorlog,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 15:33:44 HKT 2022.
    -->
    update RawEpdWasteTransportation_SyncLog
    set Id = #{row.id,jdbcType=CHAR},
      Filename = #{row.filename,jdbcType=NVARCHAR},
      StartDatetime = #{row.startdatetime,jdbcType=TIMESTAMP},
      EndDatetime = #{row.enddatetime,jdbcType=TIMESTAMP},
      RecordCount = #{row.recordcount,jdbcType=INTEGER},
      InsertCount = #{row.insertcount,jdbcType=INTEGER},
      UpdateCount = #{row.updatecount,jdbcType=INTEGER},
      DeleteCount = #{row.deletecount,jdbcType=INTEGER},
      ErrorDatetime = #{row.errordatetime,jdbcType=TIMESTAMP},
      ErrorLog = #{row.errorlog,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 15:33:44 HKT 2022.
    -->
    update RawEpdWasteTransportation_SyncLog
    set Id = #{row.id,jdbcType=CHAR},
      Filename = #{row.filename,jdbcType=NVARCHAR},
      StartDatetime = #{row.startdatetime,jdbcType=TIMESTAMP},
      EndDatetime = #{row.enddatetime,jdbcType=TIMESTAMP},
      RecordCount = #{row.recordcount,jdbcType=INTEGER},
      InsertCount = #{row.insertcount,jdbcType=INTEGER},
      UpdateCount = #{row.updatecount,jdbcType=INTEGER},
      DeleteCount = #{row.deletecount,jdbcType=INTEGER},
      ErrorDatetime = #{row.errordatetime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>