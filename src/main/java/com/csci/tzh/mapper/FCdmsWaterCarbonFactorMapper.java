package com.csci.tzh.mapper;

import com.csci.tzh.model.FCdmsWaterCarbonFactor;
import com.csci.tzh.model.FCdmsWaterCarbonFactorExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FCdmsWaterCarbonFactorMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WaterCarbonFactor
	 * @mbg.generated  Fri Jul 07 12:29:13 HKT 2023
	 */
	long countByExample(FCdmsWaterCarbonFactorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WaterCarbonFactor
	 * @mbg.generated  Fri Jul 07 12:29:13 HKT 2023
	 */
	int deleteByExample(FCdmsWaterCarbonFactorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WaterCarbonFactor
	 * @mbg.generated  Fri Jul 07 12:29:13 HKT 2023
	 */
	int insert(FCdmsWaterCarbonFactor row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WaterCarbonFactor
	 * @mbg.generated  Fri Jul 07 12:29:13 HKT 2023
	 */
	int insertSelective(FCdmsWaterCarbonFactor row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WaterCarbonFactor
	 * @mbg.generated  Fri Jul 07 12:29:13 HKT 2023
	 */
	List<FCdmsWaterCarbonFactor> selectByExample(FCdmsWaterCarbonFactorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WaterCarbonFactor
	 * @mbg.generated  Fri Jul 07 12:29:13 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") FCdmsWaterCarbonFactor row,
			@Param("example") FCdmsWaterCarbonFactorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table F_CDMS_WaterCarbonFactor
	 * @mbg.generated  Fri Jul 07 12:29:13 HKT 2023
	 */
	int updateByExample(@Param("row") FCdmsWaterCarbonFactor row,
			@Param("example") FCdmsWaterCarbonFactorExample example);
}