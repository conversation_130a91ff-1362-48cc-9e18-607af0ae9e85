package com.csci.tzh.mapper;

import com.csci.tzh.vo.TzhEstimateConstructionWasteSubCategoryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TzhEstimateConstructionWasteSubCategoryCustomMapper {
	
    @Select("""
            SELECT * FROM Tzh_EstimateConstructionWaste_SubCategory SC 
            WHERE SC.CategoryId = #{categoryId}
            ORDER BY SC.SubCategoryName
            """)
    public List<TzhEstimateConstructionWasteSubCategoryVO> list(@Param("categoryId") String categoryId);

}