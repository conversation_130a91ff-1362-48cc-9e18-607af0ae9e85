<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.FMaterialDetailMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.FMaterialDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Oct 19 17:17:08 HKT 2022.
    -->
    <result column="Region" jdbcType="NVARCHAR" property="region" />
    <result column="SiteId" jdbcType="INTEGER" property="siteid" />
    <result column="SiteName" jdbcType="NVARCHAR" property="sitename" />
    <result column="RecordYearMonth" jdbcType="INTEGER" property="recordyearmonth" />
    <result column="CarbonEmissionLocation" jdbcType="NVARCHAR" property="carbonemissionlocation" />
    <result column="MaterialCode" jdbcType="NVARCHAR" property="materialcode" />
    <result column="ChineseName" jdbcType="NVARCHAR" property="chinesename" />
    <result column="Unit" jdbcType="NVARCHAR" property="unit" />
    <result column="Description" jdbcType="NVARCHAR" property="description" />
    <result column="CarbonFactor" jdbcType="NUMERIC" property="carbonfactor" />
    <result column="CarbonFactorUnit" jdbcType="NVARCHAR" property="carbonfactorunit" />
    <result column="TransportFactor" jdbcType="NUMERIC" property="transportfactor" />
    <result column="TransportFactorUnit" jdbcType="NVARCHAR" property="transportfactorunit" />
    <result column="TransportDistance" jdbcType="NUMERIC" property="transportdistance" />
    <result column="TransportDistanceUnit" jdbcType="NVARCHAR" property="transportdistanceunit" />
    <result column="Qty" jdbcType="DECIMAL" property="qty" />
    <result column="CDMS_Unit" jdbcType="NVARCHAR" property="cdmsUnit" />
    <result column="CarbonAmount" jdbcType="NUMERIC" property="carbonamount" />
    <result column="TransportCarbonAmount" jdbcType="NUMERIC" property="transportcarbonamount" />
    <result column="Scope" jdbcType="NVARCHAR" property="scope" />
    <result column="TransportScope" jdbcType="NVARCHAR" property="transportscope" />
    <result column="CalculateDate" jdbcType="DATE" property="calculatedate" />
    <result column="BillNo" jdbcType="NVARCHAR" property="billno" />
    <result column="BizDate" jdbcType="TIMESTAMP" property="bizdate" />
    <result column="DeliveryNoteNo" jdbcType="NVARCHAR" property="deliverynoteno" />
    <result column="DeliveryDate" jdbcType="TIMESTAMP" property="deliverydate" />
    <result column="FubandanNo" jdbcType="NVARCHAR" property="fubandanno" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Oct 19 17:17:08 HKT 2022.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Oct 19 17:17:08 HKT 2022.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Oct 19 17:17:08 HKT 2022.
    -->
    Region, SiteId, SiteName, RecordYearMonth, CarbonEmissionLocation, MaterialCode, 
    ChineseName, Unit, Description, CarbonFactor, CarbonFactorUnit, TransportFactor, 
    TransportFactorUnit, TransportDistance, TransportDistanceUnit, Qty, CDMS_Unit, CarbonAmount, 
    TransportCarbonAmount, Scope, TransportScope, CalculateDate, BillNo, BizDate, DeliveryNoteNo, 
    DeliveryDate, FubandanNo
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.FMaterialDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Oct 19 17:17:08 HKT 2022.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from F_Material_Detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.FMaterialDetailExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Oct 19 17:17:08 HKT 2022.
    -->
    delete from F_Material_Detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.FMaterialDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Oct 19 17:17:08 HKT 2022.
    -->
    insert into F_Material_Detail (Region, SiteId, SiteName, 
      RecordYearMonth, CarbonEmissionLocation, 
      MaterialCode, ChineseName, Unit, 
      Description, CarbonFactor, CarbonFactorUnit, 
      TransportFactor, TransportFactorUnit, 
      TransportDistance, TransportDistanceUnit, 
      Qty, CDMS_Unit, CarbonAmount, 
      TransportCarbonAmount, Scope, TransportScope, 
      CalculateDate, BillNo, BizDate, 
      DeliveryNoteNo, DeliveryDate, FubandanNo
      )
    values (#{region,jdbcType=NVARCHAR}, #{siteid,jdbcType=INTEGER}, #{sitename,jdbcType=NVARCHAR}, 
      #{recordyearmonth,jdbcType=INTEGER}, #{carbonemissionlocation,jdbcType=NVARCHAR}, 
      #{materialcode,jdbcType=NVARCHAR}, #{chinesename,jdbcType=NVARCHAR}, #{unit,jdbcType=NVARCHAR}, 
      #{description,jdbcType=NVARCHAR}, #{carbonfactor,jdbcType=NUMERIC}, #{carbonfactorunit,jdbcType=NVARCHAR}, 
      #{transportfactor,jdbcType=NUMERIC}, #{transportfactorunit,jdbcType=NVARCHAR}, 
      #{transportdistance,jdbcType=NUMERIC}, #{transportdistanceunit,jdbcType=NVARCHAR}, 
      #{qty,jdbcType=DECIMAL}, #{cdmsUnit,jdbcType=NVARCHAR}, #{carbonamount,jdbcType=NUMERIC}, 
      #{transportcarbonamount,jdbcType=NUMERIC}, #{scope,jdbcType=NVARCHAR}, #{transportscope,jdbcType=NVARCHAR}, 
      #{calculatedate,jdbcType=DATE}, #{billno,jdbcType=NVARCHAR}, #{bizdate,jdbcType=TIMESTAMP}, 
      #{deliverynoteno,jdbcType=NVARCHAR}, #{deliverydate,jdbcType=TIMESTAMP}, #{fubandanno,jdbcType=NVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.FMaterialDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Oct 19 17:17:08 HKT 2022.
    -->
    insert into F_Material_Detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="region != null">
        Region,
      </if>
      <if test="siteid != null">
        SiteId,
      </if>
      <if test="sitename != null">
        SiteName,
      </if>
      <if test="recordyearmonth != null">
        RecordYearMonth,
      </if>
      <if test="carbonemissionlocation != null">
        CarbonEmissionLocation,
      </if>
      <if test="materialcode != null">
        MaterialCode,
      </if>
      <if test="chinesename != null">
        ChineseName,
      </if>
      <if test="unit != null">
        Unit,
      </if>
      <if test="description != null">
        Description,
      </if>
      <if test="carbonfactor != null">
        CarbonFactor,
      </if>
      <if test="carbonfactorunit != null">
        CarbonFactorUnit,
      </if>
      <if test="transportfactor != null">
        TransportFactor,
      </if>
      <if test="transportfactorunit != null">
        TransportFactorUnit,
      </if>
      <if test="transportdistance != null">
        TransportDistance,
      </if>
      <if test="transportdistanceunit != null">
        TransportDistanceUnit,
      </if>
      <if test="qty != null">
        Qty,
      </if>
      <if test="cdmsUnit != null">
        CDMS_Unit,
      </if>
      <if test="carbonamount != null">
        CarbonAmount,
      </if>
      <if test="transportcarbonamount != null">
        TransportCarbonAmount,
      </if>
      <if test="scope != null">
        Scope,
      </if>
      <if test="transportscope != null">
        TransportScope,
      </if>
      <if test="calculatedate != null">
        CalculateDate,
      </if>
      <if test="billno != null">
        BillNo,
      </if>
      <if test="bizdate != null">
        BizDate,
      </if>
      <if test="deliverynoteno != null">
        DeliveryNoteNo,
      </if>
      <if test="deliverydate != null">
        DeliveryDate,
      </if>
      <if test="fubandanno != null">
        FubandanNo,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="region != null">
        #{region,jdbcType=NVARCHAR},
      </if>
      <if test="siteid != null">
        #{siteid,jdbcType=INTEGER},
      </if>
      <if test="sitename != null">
        #{sitename,jdbcType=NVARCHAR},
      </if>
      <if test="recordyearmonth != null">
        #{recordyearmonth,jdbcType=INTEGER},
      </if>
      <if test="carbonemissionlocation != null">
        #{carbonemissionlocation,jdbcType=NVARCHAR},
      </if>
      <if test="materialcode != null">
        #{materialcode,jdbcType=NVARCHAR},
      </if>
      <if test="chinesename != null">
        #{chinesename,jdbcType=NVARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=NVARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=NVARCHAR},
      </if>
      <if test="carbonfactor != null">
        #{carbonfactor,jdbcType=NUMERIC},
      </if>
      <if test="carbonfactorunit != null">
        #{carbonfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="transportfactor != null">
        #{transportfactor,jdbcType=NUMERIC},
      </if>
      <if test="transportfactorunit != null">
        #{transportfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="transportdistance != null">
        #{transportdistance,jdbcType=NUMERIC},
      </if>
      <if test="transportdistanceunit != null">
        #{transportdistanceunit,jdbcType=NVARCHAR},
      </if>
      <if test="qty != null">
        #{qty,jdbcType=DECIMAL},
      </if>
      <if test="cdmsUnit != null">
        #{cdmsUnit,jdbcType=NVARCHAR},
      </if>
      <if test="carbonamount != null">
        #{carbonamount,jdbcType=NUMERIC},
      </if>
      <if test="transportcarbonamount != null">
        #{transportcarbonamount,jdbcType=NUMERIC},
      </if>
      <if test="scope != null">
        #{scope,jdbcType=NVARCHAR},
      </if>
      <if test="transportscope != null">
        #{transportscope,jdbcType=NVARCHAR},
      </if>
      <if test="calculatedate != null">
        #{calculatedate,jdbcType=DATE},
      </if>
      <if test="billno != null">
        #{billno,jdbcType=NVARCHAR},
      </if>
      <if test="bizdate != null">
        #{bizdate,jdbcType=TIMESTAMP},
      </if>
      <if test="deliverynoteno != null">
        #{deliverynoteno,jdbcType=NVARCHAR},
      </if>
      <if test="deliverydate != null">
        #{deliverydate,jdbcType=TIMESTAMP},
      </if>
      <if test="fubandanno != null">
        #{fubandanno,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.FMaterialDetailExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Oct 19 17:17:08 HKT 2022.
    -->
    select count(*) from F_Material_Detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Oct 19 17:17:08 HKT 2022.
    -->
    update F_Material_Detail
    <set>
      <if test="row.region != null">
        Region = #{row.region,jdbcType=NVARCHAR},
      </if>
      <if test="row.siteid != null">
        SiteId = #{row.siteid,jdbcType=INTEGER},
      </if>
      <if test="row.sitename != null">
        SiteName = #{row.sitename,jdbcType=NVARCHAR},
      </if>
      <if test="row.recordyearmonth != null">
        RecordYearMonth = #{row.recordyearmonth,jdbcType=INTEGER},
      </if>
      <if test="row.carbonemissionlocation != null">
        CarbonEmissionLocation = #{row.carbonemissionlocation,jdbcType=NVARCHAR},
      </if>
      <if test="row.materialcode != null">
        MaterialCode = #{row.materialcode,jdbcType=NVARCHAR},
      </if>
      <if test="row.chinesename != null">
        ChineseName = #{row.chinesename,jdbcType=NVARCHAR},
      </if>
      <if test="row.unit != null">
        Unit = #{row.unit,jdbcType=NVARCHAR},
      </if>
      <if test="row.description != null">
        Description = #{row.description,jdbcType=NVARCHAR},
      </if>
      <if test="row.carbonfactor != null">
        CarbonFactor = #{row.carbonfactor,jdbcType=NUMERIC},
      </if>
      <if test="row.carbonfactorunit != null">
        CarbonFactorUnit = #{row.carbonfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.transportfactor != null">
        TransportFactor = #{row.transportfactor,jdbcType=NUMERIC},
      </if>
      <if test="row.transportfactorunit != null">
        TransportFactorUnit = #{row.transportfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.transportdistance != null">
        TransportDistance = #{row.transportdistance,jdbcType=NUMERIC},
      </if>
      <if test="row.transportdistanceunit != null">
        TransportDistanceUnit = #{row.transportdistanceunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.qty != null">
        Qty = #{row.qty,jdbcType=DECIMAL},
      </if>
      <if test="row.cdmsUnit != null">
        CDMS_Unit = #{row.cdmsUnit,jdbcType=NVARCHAR},
      </if>
      <if test="row.carbonamount != null">
        CarbonAmount = #{row.carbonamount,jdbcType=NUMERIC},
      </if>
      <if test="row.transportcarbonamount != null">
        TransportCarbonAmount = #{row.transportcarbonamount,jdbcType=NUMERIC},
      </if>
      <if test="row.scope != null">
        Scope = #{row.scope,jdbcType=NVARCHAR},
      </if>
      <if test="row.transportscope != null">
        TransportScope = #{row.transportscope,jdbcType=NVARCHAR},
      </if>
      <if test="row.calculatedate != null">
        CalculateDate = #{row.calculatedate,jdbcType=DATE},
      </if>
      <if test="row.billno != null">
        BillNo = #{row.billno,jdbcType=NVARCHAR},
      </if>
      <if test="row.bizdate != null">
        BizDate = #{row.bizdate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deliverynoteno != null">
        DeliveryNoteNo = #{row.deliverynoteno,jdbcType=NVARCHAR},
      </if>
      <if test="row.deliverydate != null">
        DeliveryDate = #{row.deliverydate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.fubandanno != null">
        FubandanNo = #{row.fubandanno,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Oct 19 17:17:08 HKT 2022.
    -->
    update F_Material_Detail
    set Region = #{row.region,jdbcType=NVARCHAR},
      SiteId = #{row.siteid,jdbcType=INTEGER},
      SiteName = #{row.sitename,jdbcType=NVARCHAR},
      RecordYearMonth = #{row.recordyearmonth,jdbcType=INTEGER},
      CarbonEmissionLocation = #{row.carbonemissionlocation,jdbcType=NVARCHAR},
      MaterialCode = #{row.materialcode,jdbcType=NVARCHAR},
      ChineseName = #{row.chinesename,jdbcType=NVARCHAR},
      Unit = #{row.unit,jdbcType=NVARCHAR},
      Description = #{row.description,jdbcType=NVARCHAR},
      CarbonFactor = #{row.carbonfactor,jdbcType=NUMERIC},
      CarbonFactorUnit = #{row.carbonfactorunit,jdbcType=NVARCHAR},
      TransportFactor = #{row.transportfactor,jdbcType=NUMERIC},
      TransportFactorUnit = #{row.transportfactorunit,jdbcType=NVARCHAR},
      TransportDistance = #{row.transportdistance,jdbcType=NUMERIC},
      TransportDistanceUnit = #{row.transportdistanceunit,jdbcType=NVARCHAR},
      Qty = #{row.qty,jdbcType=DECIMAL},
      CDMS_Unit = #{row.cdmsUnit,jdbcType=NVARCHAR},
      CarbonAmount = #{row.carbonamount,jdbcType=NUMERIC},
      TransportCarbonAmount = #{row.transportcarbonamount,jdbcType=NUMERIC},
      Scope = #{row.scope,jdbcType=NVARCHAR},
      TransportScope = #{row.transportscope,jdbcType=NVARCHAR},
      CalculateDate = #{row.calculatedate,jdbcType=DATE},
      BillNo = #{row.billno,jdbcType=NVARCHAR},
      BizDate = #{row.bizdate,jdbcType=TIMESTAMP},
      DeliveryNoteNo = #{row.deliverynoteno,jdbcType=NVARCHAR},
      DeliveryDate = #{row.deliverydate,jdbcType=TIMESTAMP},
      FubandanNo = #{row.fubandanno,jdbcType=NVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>