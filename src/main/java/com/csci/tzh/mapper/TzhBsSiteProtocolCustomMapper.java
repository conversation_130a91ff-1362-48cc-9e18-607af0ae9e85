package com.csci.tzh.mapper;

import com.csci.tzh.vo.TzhBsSiteProtocolVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface TzhBsSiteProtocolCustomMapper {
	
    @Select("""
            SELECT SP.SiteName, SP.ProtocolId, Protocol = P.Name, ProtocolEN = P.NameEN, ProtocolSC = P.NameSC FROM Tzh_Bs_SiteProtocol SP 
            LEFT JOIN Tzh_Protocol P ON P.Id = SP.ProtocolId 
            WHERE SP.SiteName = #{siteName} 
            """)
    public List<TzhBsSiteProtocolVO> list(@Param("siteName") String siteName);

}