package com.csci.tzh.mapper;

import com.csci.tzh.model.TzhMonthlyMaterialCarbon;
import com.csci.tzh.model.TzhMonthlyMaterialCarbonExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhMonthlyMaterialCarbonMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_MonthlyMaterialCarbon
	 * @mbg.generated  Wed Apr 19 10:02:06 HKT 2023
	 */
	long countByExample(TzhMonthlyMaterialCarbonExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_MonthlyMaterialCarbon
	 * @mbg.generated  Wed Apr 19 10:02:06 HKT 2023
	 */
	int deleteByExample(TzhMonthlyMaterialCarbonExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_MonthlyMaterialCarbon
	 * @mbg.generated  Wed Apr 19 10:02:06 HKT 2023
	 */
	int insert(TzhMonthlyMaterialCarbon row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_MonthlyMaterialCarbon
	 * @mbg.generated  Wed Apr 19 10:02:06 HKT 2023
	 */
	int insertSelective(TzhMonthlyMaterialCarbon row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_MonthlyMaterialCarbon
	 * @mbg.generated  Wed Apr 19 10:02:06 HKT 2023
	 */
	List<TzhMonthlyMaterialCarbon> selectByExample(TzhMonthlyMaterialCarbonExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_MonthlyMaterialCarbon
	 * @mbg.generated  Wed Apr 19 10:02:06 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") TzhMonthlyMaterialCarbon row,
			@Param("example") TzhMonthlyMaterialCarbonExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_MonthlyMaterialCarbon
	 * @mbg.generated  Wed Apr 19 10:02:06 HKT 2023
	 */
	int updateByExample(@Param("row") TzhMonthlyMaterialCarbon row,
			@Param("example") TzhMonthlyMaterialCarbonExample example);
}