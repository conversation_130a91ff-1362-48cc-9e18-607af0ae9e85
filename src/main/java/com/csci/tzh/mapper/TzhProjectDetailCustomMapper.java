package com.csci.tzh.mapper;

import com.csci.tzh.model.*;
import com.csci.tzh.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TzhProjectDetailCustomMapper {
	
    @Select("""
            SELECT PD.id, PD.sitename, PD.protocolid, protocol = P.Name, protocolsc = P.NameSC, protocolen = P.NameEN, 
			PD.carbonemissionlocationid, carbonemissionlocation = CEL.Name, carbonemissionlocationsc = CEL.NameEN, 
			carbonemissionlocationen = CEL.NameSC, PD.emissionreductiontarget, PD.epdwasteaccount, PD.epdwasteaccountpwd, PD.hasfoodcourt, 
			PD.datasourceid, datasource = DS.Name, datasourcesc = DS.NameSC, datasourceen = DS.NameEN, PD.wasterwatercarbonfactor, 
			PD.wasterwatercarbonfactorunit, PD.waterelectricitybillsource, PD.accountingcenterno, 
			PD.createdby, PD.createdtime, PD.deletedby, PD.deletedtime, PD.isdeleted, PD.SiteId 
            FROM Tzh_ProjectDetail PD
            LEFT JOIN Tzh_Protocol P ON PD.ProtocolId = P.Id
            LEFT JOIN Tzh_CarbonEmissionLocation CEL ON PD.CarbonEmissionLocationId = CEL.Id 
            LEFT JOIN Tzh_DataSource DS ON PD.DataSourceId = DS.Id
            WHERE PD.SiteId = #{siteid} AND PD.IsDeleted = 0
            """)
    public List<TzhProjectDetailVO> list(@Param("siteid") String siteid);

}