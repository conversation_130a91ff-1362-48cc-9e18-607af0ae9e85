package com.csci.tzh.mapper;

import com.csci.tzh.model.TzhRegion;
import com.csci.tzh.model.TzhRegionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhRegionMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Region
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    long countByExample(TzhRegionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Region
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    int deleteByExample(TzhRegionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Region
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    int insert(TzhRegion row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Region
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    int insertSelective(TzhRegion row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Region
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    List<TzhRegion> selectByExample(TzhRegionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Region
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    int updateByExampleSelective(@Param("row") TzhRegion row, @Param("example") TzhRegionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Region
     *
     * @mbg.generated Tue Mar 28 15:48:49 HKT 2023
     */
    int updateByExample(@Param("row") TzhRegion row, @Param("example") TzhRegionExample example);
}