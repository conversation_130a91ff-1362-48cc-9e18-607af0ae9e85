<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.TzhCarbonEmissionLocationSubCategoryMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.TzhCarbonEmissionLocationSubCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 28 17:54:24 HKT 2023.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="CarbonEmissionLocationId" jdbcType="CHAR" property="carbonemissionlocationid" />
    <result column="SubCategoryId" jdbcType="CHAR" property="subcategoryid" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 28 17:54:24 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 28 17:54:24 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 28 17:54:24 HKT 2023.
    -->
    Id, CarbonEmissionLocationId, SubCategoryId
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.TzhCarbonEmissionLocationSubCategoryExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 28 17:54:24 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_CarbonEmissionLocation_SubCategory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.TzhCarbonEmissionLocationSubCategoryExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 28 17:54:24 HKT 2023.
    -->
    delete from Tzh_CarbonEmissionLocation_SubCategory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.TzhCarbonEmissionLocationSubCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 28 17:54:24 HKT 2023.
    -->
    insert into Tzh_CarbonEmissionLocation_SubCategory (Id, CarbonEmissionLocationId, SubCategoryId
      )
    values (#{id,jdbcType=CHAR}, #{carbonemissionlocationid,jdbcType=CHAR}, #{subcategoryid,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.TzhCarbonEmissionLocationSubCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 28 17:54:24 HKT 2023.
    -->
    insert into Tzh_CarbonEmissionLocation_SubCategory
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="carbonemissionlocationid != null">
        CarbonEmissionLocationId,
      </if>
      <if test="subcategoryid != null">
        SubCategoryId,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="carbonemissionlocationid != null">
        #{carbonemissionlocationid,jdbcType=CHAR},
      </if>
      <if test="subcategoryid != null">
        #{subcategoryid,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.TzhCarbonEmissionLocationSubCategoryExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 28 17:54:24 HKT 2023.
    -->
    select count(*) from Tzh_CarbonEmissionLocation_SubCategory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 28 17:54:24 HKT 2023.
    -->
    update Tzh_CarbonEmissionLocation_SubCategory
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.carbonemissionlocationid != null">
        CarbonEmissionLocationId = #{row.carbonemissionlocationid,jdbcType=CHAR},
      </if>
      <if test="row.subcategoryid != null">
        SubCategoryId = #{row.subcategoryid,jdbcType=CHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 28 17:54:24 HKT 2023.
    -->
    update Tzh_CarbonEmissionLocation_SubCategory
    set Id = #{row.id,jdbcType=CHAR},
      CarbonEmissionLocationId = #{row.carbonemissionlocationid,jdbcType=CHAR},
      SubCategoryId = #{row.subcategoryid,jdbcType=CHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>