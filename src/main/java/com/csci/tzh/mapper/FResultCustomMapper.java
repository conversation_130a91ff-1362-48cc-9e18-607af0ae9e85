package com.csci.tzh.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;

import com.csci.tzh.model.*;
import com.csci.tzh.provider.*;
import com.csci.tzh.vo.*;

@Mapper
public interface FResultCustomMapper {

    @Select("""
            SELECT DISTINCT RecordYearMonth,CarbonEmissionLocation,ScopeDetail, CONVERT(DECIMAL(12,3), 
            ISNULL(CarbonAmount,0) * 0.001) AS CarbonAmount 
            FROM  ((SELECT * FROM F_Result_All)) t
            WHERE RecordYearMonth IS NOT NULL  
            AND CalculateDate = (SELECT MAX(CalculateDate) FROM F_Result_All)
            AND SiteId = #{siteId}
            AND Protocol = #{protocol}
            AND RecordYearMonth >= #{recordYearMonthFrom}
            AND RecordYearMonth <= #{recordYearMonthTo} 
			""")
    public List<FResultVO> listFResult(@Param("siteId") String siteId,
                                       @Param("protocol") String protocol,
                                       @Param("recordYearMonthFrom") Integer recordYearMonthFrom,
                                       @Param("recordYearMonthTo") Integer recordYearMonthTo);

    @SelectProvider(type=FResultSqlProvider.class,method="listFResultTotalSql")
    public List<FResultTotalVO> listFResultTotal(@Param("siteName") String siteName,
                                                 @Param("recordYearMonthFrom") int recordYearMonthFrom,
                                                 @Param("recordYearMonthTo") int recordYearMonthTo);

    @SelectProvider(type=FResultSqlProvider.class,method="listCarbonAmountByScopeMainSql")
    public List<CarbonAmountByScopeMainVO> listCarbonAmountByScopeMain(@Param("region") String region,
                                                                       @Param("siteName") String siteName,
                                                                       @Param("carbonEmissionLocation") String carbonEmissionLocation, @Param("recordYearMonthFrom") int recordYearMonthFrom, @Param("recordYearMonthTo") int recordYearMonthTo);

    @SelectProvider(type=FResultSqlProvider.class,method="listCarbonAmountByScopeDetailSql")
    public List<CarbonAmountByScopeDetailVO> listCarbonAmountByScopeDetail(@Param("region") String region,
                                                                           @Param("siteName") String siteName,
                                                                           @Param("carbonEmissionLocation") String carbonEmissionLocation, @Param("recordYearMonthFrom") int recordYearMonthFrom, @Param("recordYearMonthTo") int recordYearMonthTo);

    @SelectProvider(type=FResultSqlProvider.class,method="listCarbonAmountPercentageByScopeDetailSql")
    public List<CarbonAmountPercentageByScopeDetailVO> listCarbonAmountPercentageByScopeDetail(@Param("region") String region,
                                                                                               @Param("siteName") String siteName,
                                                                                               @Param("carbonEmissionLocation") String carbonEmissionLocation, @Param("recordYearMonthFrom") int recordYearMonthFrom, @Param("recordYearMonthTo") int recordYearMonthTo);

}
