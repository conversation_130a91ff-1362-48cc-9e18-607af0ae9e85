package com.csci.tzh.mapper;

import com.csci.tzh.model.TzhRpaAccount;
import com.csci.tzh.model.TzhRpaAccountExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhRpaAccountMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	long countByExample(TzhRpaAccountExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	int deleteByExample(TzhRpaAccountExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	int insert(TzhRpaAccount row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	int insertSelective(TzhRpaAccount row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	List<TzhRpaAccount> selectByExample(TzhRpaAccountExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") TzhRpaAccount row, @Param("example") TzhRpaAccountExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_RpaAccount
	 * @mbg.generated  Tue Apr 18 09:36:09 HKT 2023
	 */
	int updateByExample(@Param("row") TzhRpaAccount row, @Param("example") TzhRpaAccountExample example);
}