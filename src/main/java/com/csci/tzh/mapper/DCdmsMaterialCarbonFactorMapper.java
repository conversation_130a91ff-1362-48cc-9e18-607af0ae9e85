package com.csci.tzh.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import com.csci.tzh.model.DCdmsMaterialCarbonFactor;
import com.csci.tzh.model.DCdmsMaterialCarbonFactorExample;

public interface DCdmsMaterialCarbonFactorMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	long countByExample(DCdmsMaterialCarbonFactorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	int deleteByExample(DCdmsMaterialCarbonFactorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	int insert(DCdmsMaterialCarbonFactor row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	int insertSelective(DCdmsMaterialCarbonFactor row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	List<DCdmsMaterialCarbonFactor> selectByExample(DCdmsMaterialCarbonFactorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") DCdmsMaterialCarbonFactor row,
			@Param("example") DCdmsMaterialCarbonFactorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_MaterialCarbonFactor
	 * @mbg.generated  Tue Apr 18 13:01:19 HKT 2023
	 */
	int updateByExample(@Param("row") DCdmsMaterialCarbonFactor row,
			@Param("example") DCdmsMaterialCarbonFactorExample example);
}