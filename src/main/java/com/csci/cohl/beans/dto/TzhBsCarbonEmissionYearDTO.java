package com.csci.cohl.beans.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description ="查询碳排放（按年统计，1+5年）DTO")
public class TzhBsCarbonEmissionYearDTO<T> {

    @Schema(description ="查询年份")
    private String queryYear;
    @Schema(description ="碳排位置")
    private String carbonEmissionLocation;
    @Schema(description ="项目（地盘）名称")
    private String organizationName;
    @Schema(description ="協議")
    private String protocol;
    @Schema(description ="组织架构id")
    private String organizationId;
}
