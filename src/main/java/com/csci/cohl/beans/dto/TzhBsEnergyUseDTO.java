package com.csci.cohl.beans.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description ="查询能源使用排行榜DTO")
public class TzhBsEnergyUseDTO<T> {

    @Schema(description ="开始年月")
    private String startMonth;
    @Schema(description ="结束年月")
    private String endMonth;
    @Schema(description ="碳排位置")
    private String carbonEmissionLocation;
    @Schema(description ="项目（地盘）名称")
    private String organizationName;
    @Schema(description ="協議")
    private String protocol;
    @Schema(description ="组织架构id")
    private String organizationId;
}
