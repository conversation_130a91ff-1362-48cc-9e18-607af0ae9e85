package com.csci.cohl.beans.vo;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TzhBsIntermediateProcessingDetailsVO {

    private String SubCategoryName;
    private String SubCategoryNameSC;
    private String SubCategoryNameEN;
    private String Status;
    private String CalculateDate;
    private String CarbonAmountUnit;
    private String CarbonFactor;
    private String CarbonFactorUnit;
    private String CarbonEmissionLocation;
    private String CarbonEmissionLocationSC;
    private String CarbonEmissionLocationEN;
    private String MaterialName;
    private String MaterialCode;
    private String RecordYearMonth;
    private String Qty;
    private String QtyUnit;
    private String CarbonAmount;
    private String TransportCarbonAmount;
}
