package com.csci.cohl.beans.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "查询地图各省份数据返回VO对象")
public class TzhBsMapProvinceDataVO {

    @Schema(description ="省份")
    private String province;
    @Schema(description ="总量")
    private BigDecimal carbonAmount;
    @Schema(description ="省份（繁体）")
    private String provinceSc;
    @Schema(description ="省份（英文）")
    private String provinceEn;
    @Schema(description ="占所有总量百分比")
    private BigDecimal carbonAmountProportion;
}
