package com.csci.cohl.beans.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "查询项目分布返回VO对象")
public class TzhBsProjectDistributionVO {


    @Schema(description ="项目名称")
    private String organizationName;
    @Schema(description ="总量")
    private BigDecimal carbonAmount;
}
