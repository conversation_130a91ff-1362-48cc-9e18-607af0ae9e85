package com.csci.cohl.beans.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class TzhEmissionReductionHeadVO {

    private String Id;

    private String SiteName;

    private String CategoryName;

    private String CategoryNameSC;

    private String CategoryNameEN;

    private String Title;

    private String TitleSC;

    private String TitleEN;

    private String CarbonEmissionLocation;

    private String CarbonEmissionLocationSC;

    private String CarbonEmissionLocationEN;

    private String MethodDescription;

    private String MethodDescriptionSC;

    private String MethodDescriptionEN;

    private String CalculationDescription;
}
