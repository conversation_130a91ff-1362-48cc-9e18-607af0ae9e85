package com.csci.cohl.beans.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class TokenVO {

    @Schema(description ="appId")
    private String appId;

    @Schema(description ="username")
    private String username;

    @Schema(description ="realName")
    private String realName;

    @Schema(description ="accessToken")
    private String accessToken;

    @Schema(description ="issueTime")
    private String issueTime;

    @Schema(description ="expiresIn")
    private String expiresIn;
}
