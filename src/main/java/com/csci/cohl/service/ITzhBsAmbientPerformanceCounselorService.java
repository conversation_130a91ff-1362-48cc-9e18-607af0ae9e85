package com.csci.cohl.service;

import com.csci.cohl.beans.dto.*;
import com.csci.cohl.beans.vo.*;
import com.csci.cohl.exception.ResultBody;

import java.util.List;

public interface ITzhBsAmbientPerformanceCounselorService {


	/**
	 * 查询能源使用排行榜
	 * <AUTHOR>
	 * @date 2025/1/6 12:23
	 * @param dto
	 * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsEnergyUseVO>>
	 */
	ResultBody<List<TzhBsEnergyUseVO>> getEnergyUseRanking(TzhBsEnergyUseDTO dto);

	/**
	 * 废弃物产生量分布统计
	 * <AUTHOR>
	 * @date 2025/1/6 14:20
	 * @param dto
	 * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsWasteGenerationVO>>
	 */
	ResultBody<List<TzhBsWasteGenerationVO>> getWasteGenerationDistributionStatistics(TzhBsWasteGenerationDTO dto);

	/**
	 * 查询碳排放（按年统计，1+5年）
	 * <AUTHOR>
	 * @date 2025/1/6 14:51
	 * @param dto
	 * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsCarbonEmissionYearVO>>
	 */
	ResultBody<List<TzhBsCarbonEmissionYearVO>> getCarbonEmissionYearSummary(TzhBsCarbonEmissionYearDTO dto);

	/**
	 * 查询原材料使用占比统计
	 * <AUTHOR>
	 * @date 2025/1/6 15:00
	 * @param dto
	 * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsRawMaterialUseVO>>
	 */
	ResultBody<List<TzhBsRawMaterialUseVO>> getRawMaterialUseProportion(TzhBsRawMaterialUseDTO dto);

	/**
	 * 查询资源使用趋势统计
	 * <AUTHOR>
	 * @date 2025/1/6 15:12
	 * @param dto
	 * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsResourceUseTrendStatisticsVO>>
	 */
	ResultBody<List<TzhBsResourceUseTrendStatisticsVO>> getResourceUseTrendStatistics(TzhBsResourceUseTrendStatisticsDTO dto);

	/**
	 * 查询各类别密度
	 * <AUTHOR>
	 * @date 2025/1/7 14:37
	 * @param dto
	 * @return com.csci.cohl.exception.ResultBody<com.csci.cohl.beans.vo.TzhBsCategoryDensityVO>
	 */
	ResultBody<TzhBsCategoryDensityVO> getCategoryDensity(TzhBsCategoryDensityDTO dto);

	/**
	 * 查询地图各省份数据
	 * <AUTHOR>
	 * @date 2025/1/7 14:48
	 * @param dto
	 * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsMapProvinceDataVO>>
	 */
	ResultBody<List<TzhBsMapProvinceDataVO>> getMapProvinceData(TzhBsMapProvinceDataDTO dto);

	/**
	 * 查询用水与污染
	 * <AUTHOR>
	 * @date 2025/1/7 15:06
	 * @param dto
	 * @return com.csci.cohl.exception.ResultBody<com.csci.cohl.beans.vo.TzhBsWaterUseAndPollutionVO>
	 */
	ResultBody<TzhBsWaterUseAndPollutionVO> getWaterUseAndPollution(TzhBsWaterUseAndPollutionDTO dto);

	/**
	 * 查询项目分布
	 * <AUTHOR>
	 * @date 2025/1/7 15:11
	 * @param dto
	 * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsProjectDistributionVO>>
	 */
	ResultBody<List<TzhBsProjectDistributionVO>> getProjectDistribution(TzhBsProjectDistributionDTO dto);

	/**
	 * 查询电力使用排行榜
	 * <AUTHOR>
	 * @date 2025/1/16 14:44
	 * @param dto
	 * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsElectricityUseVO>>
	 */
	ResultBody<List<TzhBsElectricityUseVO>> getElectricityUseRanking(TzhBsElectricityUseDTO dto);

	/**
	 * 查询水资源使用排行榜
	 * <AUTHOR>
	 * @date 2025/1/16 15:14
	 * @param dto
	 * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsWaterResourcesUseVO>>
	 */
	ResultBody<List<TzhBsWaterResourcesUseVO>> getWaterResourcesUseRanking(TzhBsWaterResourcesUseDTO dto);

	/**
	 * 查询温室气体排放总量排行榜
	 * <AUTHOR>
	 * @date 2025/1/16 16:25
	 * @param dto
	 * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsGreenhouseGasEmissionsTotalVO>>
	 */
	ResultBody<List<TzhBsGreenhouseGasEmissionsTotalVO>> getGreenhouseGasEmissionsTotalRanking(TzhBsGreenhouseGasEmissionsTotalDTO dto);

	/**
	 * 查询温室气体排放量总计
	 * <AUTHOR>
	 * @date 2025/1/18 13:06
	 * @param dto
	 * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsGreenhouseGasEmissionsVO>>
	 */
	ResultBody<List<TzhBsGreenhouseGasEmissionsVO>> getGreenhouseGasEmissionsTotal(TzhBsGreenhouseGasEmissionsTotalDTO dto);

	/**
	 * 查询碳排放强度
	 * <AUTHOR>
	 * @date 2025/1/20 10:06
	 * @param dto
	 * @return com.csci.cohl.exception.ResultBody<java.util.List<com.csci.cohl.beans.vo.TzhBsCarbonEmissionYearVO>>
	 */
	ResultBody<List<TzhBsCarbonEmissionYearVO>> getCarbonEmissionIntensity(TzhBsCarbonEmissionYearDTO dto);

	String clearCache();
}
