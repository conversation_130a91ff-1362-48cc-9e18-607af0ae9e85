package com.csci.cohl.service.impl;

import com.csci.cohl.beans.dto.*;
import com.csci.cohl.beans.vo.*;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.mapper.TzhBsCarbonEmissionStatisticsMapper;
import com.csci.cohl.mapper.BiTzhBsUserSiteMapper;
import com.csci.cohl.service.*;
import com.csci.susdev.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TzhBsCarbonEmissionStatisticsServiceImpl extends BaseServiceImpl implements ITzhBsCarbonEmissionStatisticsService {

    @Resource
    private BiTzhBsUserSiteMapper biTzhBsUserSiteMapper;

    @Resource
    private TzhBsCarbonEmissionStatisticsMapper tzhBsCarbonEmissionStatisticsMapper;

    @Override
    public ResultBody<List<TzhBsCarbonEmissionSummaryVO>> getCarbonEmissionSummary(TzhBsCarbonEmissionSummaryDTO dto) {
        String username = (String) request.getAttribute("username");
        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(dto.getSiteId())) return ResultBody.error("無法查詢該項目。");
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");
        }
		List<TzhBsCarbonEmissionSummaryVO> lstVo =
                tzhBsCarbonEmissionStatisticsMapper.getCarbonEmissionSummary(dto.getStartMonth(), dto.getEndMonth(),
                        dto.getCarbonEmissionLocation(), dto.getSiteName(), dto.getProtocol());
        return ResultBody.success(lstVo);
    }

    @Override
    public ResultBody<List<TzhBsMonthlyEmissionVO>> getMonthlyEmission(TzhBsMonthlyEmissionDTO dto) {
        String username = (String) request.getAttribute("username");
        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(dto.getSiteId())) return ResultBody.error("無法查詢該項目。");
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");
        }
        List<TzhBsMonthlyEmissionVO> lstVo =
                tzhBsCarbonEmissionStatisticsMapper.getMonthlyEmission(dto.getStartMonth(), dto.getEndMonth(),
                        dto.getCarbonEmissionLocation(), dto.getSiteName(), dto.getProtocol());
        return ResultBody.success(lstVo);
    }

    @Override
    public ResultBody<List<TzhBsEmissionDetailsVO>> getEmissionDetails(TzhBsEmissionDetailsDTO dto) {
        Logger logger = LoggerFactory.getLogger(TzhBsCarbonEmissionStatisticsServiceImpl.class);
        long startTime = System.currentTimeMillis();

        String username = (String) request.getAttribute("username");
        logger.info("获取用户名耗时: {} ms", (System.currentTimeMillis() - startTime));

        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(dto.getSiteId())) return ResultBody.error("無法查詢該項目。");
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");
        }
        logger.info("检查站点权限耗时: {} ms", (System.currentTimeMillis() - startTime));

        long queryStartTime = System.currentTimeMillis();
        List<TzhBsEmissionDetailsVO> lstVo = tzhBsCarbonEmissionStatisticsMapper.getEmissionDetails(
                dto.getStartMonth(), dto.getEndMonth(), dto.getCarbonEmissionLocation(), dto.getSiteName(), dto.getProtocol());
        logger.info("数据库查询耗时: {} ms", (System.currentTimeMillis() - queryStartTime));

        long endTime = System.currentTimeMillis();
        logger.info("getEmissionDetails 方法总执行时间: {} ms", (endTime - startTime));

        return ResultBody.success(lstVo);
    }

    @Override
    public ResultBody<List<TzhBsEmissionNotZeroDetailsVO>> getEmissionNotZeroDetails(TzhBsEmissionNotZeroDetailsDTO dto) {
        String username = (String) request.getAttribute("username");
        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(dto.getSiteId())) return ResultBody.error("無法查詢該項目。");
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");
        }
        List<TzhBsEmissionNotZeroDetailsVO> lstVo =
                tzhBsCarbonEmissionStatisticsMapper.getEmissionNotZeroDetails(dto.getStartMonth(),
                        dto.getEndMonth(), dto.getCarbonEmissionLocation(), dto.getSiteName(), dto.getProtocol());
        return ResultBody.success(lstVo);
    }

    @Override
    public ResultBody<List<TzhBsEmission3bDetailsVO>> getEmission3bDetails(TzhBsEmission3bDetailsDTO dto) {
        String username = (String) request.getAttribute("username");
        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(dto.getSiteId())) return ResultBody.error("無法查詢該項目。");
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");
        }
        List<TzhBsEmission3bDetailsVO> lstVo =
                tzhBsCarbonEmissionStatisticsMapper.getEmission3bDetails(dto.getStartMonth(),
                        dto.getEndMonth(), dto.getCarbonEmissionLocation(), dto.getSiteName(), dto.getProtocol());
        return ResultBody.success(lstVo);
    }

    @Override
    public ResultBody<TzhBsBlockchainSignatureVO> getBlockchainSignature(TzhBsBlockchainSignatureDTO dto) {
        TzhBsBlockchainSignatureVO vo = tzhBsCarbonEmissionStatisticsMapper.getBlockchainSignature();
        return ResultBody.success(vo);
    }

    @Override
    public ResultBody<List<TzhBsYearlyEmissionVO>> getYearlyEmissionByQuarter(TzhBsMonthlyEmissionDTO dto) {
        String username = (String) request.getAttribute("username");
        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(dto.getSiteId())) return ResultBody.error("無法查詢該項目。");
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");
        }
        List<TzhBsYearlyEmissionVO> lstVo =
                tzhBsCarbonEmissionStatisticsMapper.getYearlyEmissionByQuarter(dto.getStartMonth(), dto.getEndMonth(),
                        dto.getCarbonEmissionLocation(), dto.getSiteName(), dto.getProtocol());
        return ResultBody.success(lstVo);
    }
}
