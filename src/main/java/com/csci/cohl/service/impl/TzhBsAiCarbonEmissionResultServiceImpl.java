package com.csci.cohl.service.impl;

import com.csci.cohl.beans.dto.*;
import com.csci.cohl.beans.vo.*;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.mapper.BiTzhBsUserSiteMapper;
import com.csci.cohl.mapper.TzhBsAiCarbonEmissionResultCustomsMapper;
import com.csci.cohl.mapper.TzhBsAmbientPerformanceCounselorMapper;
import com.csci.cohl.service.ITzhBsAiCarbonEmissionResultService;
import com.csci.cohl.service.ITzhBsAmbientPerformanceCounselorService;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.constant.CacheConstants;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.util.StringUtil;
import com.csci.susdev.util.redis.RedisUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TzhBsAiCarbonEmissionResultServiceImpl extends BaseServiceImpl implements ITzhBsAiCarbonEmissionResultService {


    @Resource
    private TzhBsAiCarbonEmissionResultCustomsMapper tzhBsAiCarbonEmissionResultCustomsMapper;
    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public String getAiCarbonEmissionResult(TzhBsAiCarbonEmissionDTO dto) {
        if (StringUtil.isEmpty(dto.getSql())) {
            throw new ServiceException("sql不能为空");
        }
        if (dto.getSql().contains("update") || dto.getSql().contains("delete") || dto.getSql().contains("insert")) {
            throw new ServiceException("只能查询，不能做其他操作！");
        }
        List<Map<String, Object>> resultList = tzhBsAiCarbonEmissionResultCustomsMapper.getAiCarbonEmissionResult(dto.getSql());
        if (CollectionUtils.isEmpty(resultList)) {
            return "";
        }
        try {
            return objectMapper.writeValueAsString(resultList);
        } catch (Exception e) {
            log.error("转换JSON字符串时发生异常", e);
            throw new ServiceException("转换JSON字符串时发生异常", e);
        }
    }
}
