package com.csci.cohl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.csci.cohl.beans.dto.TzhBsFileDTO;
import com.csci.cohl.beans.dto.TzhBsFileListDTO;
import com.csci.cohl.beans.vo.TzhBsFileVO;
import com.csci.cohl.epidemic.utils.UserInfo;
import com.csci.cohl.model.TzhBsFile;
import com.csci.cohl.service.ITzhBsFileService;
import com.csci.cohl.mapper.BiTzhBsFileMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class TzhBsFileServiceImpl extends BaseServiceImpl implements ITzhBsFileService {

    @Resource
    private BiTzhBsFileMapper mapper;

    @Override
    @Transactional
    public int upload(TzhBsFileDTO dto, MultipartFile multipartFile) throws IOException {
    	int result = 0;

    	String username = UserInfo.getCurrentUser().get("username");

        // 刪除記錄
        if(StringUtils.isNotEmpty(dto.getId())) {
            result += delete(dto.getId());
        }

    	// 生成模型
    	TzhBsFile x = new TzhBsFile();
    	BeanUtils.copyProperties(dto, x);
    	x.setId(UUID.randomUUID().toString());
    	x.setData(multipartFile.getBytes());
    	x.setCreatedBy(username);
    	x.setCreatedTime(LocalDateTime.now());
    	x.setIsDeleted(false);

    	// 新增新的記錄
    	result += mapper.insert(x);

        return result;
    }

    @Override
    @Transactional
    public int delete(String id) {
    	int result = 0;

    	String username = UserInfo.getCurrentUser().get("username");

    	UpdateWrapper<TzhBsFile> wrapper = new UpdateWrapper<>();
    	wrapper.eq("Id", id);
    	wrapper.set("DeletedBy", username);
    	wrapper.set("DeletedTime", LocalDateTime.now());
    	wrapper.set("IsDeleted", true);
    	result += mapper.update(null, wrapper);

        return result;
    }

    @Override
    public TzhBsFile get(String id) {
        QueryWrapper<TzhBsFile> wrapper = new QueryWrapper<>();
    	wrapper.eq("Id", id);
        wrapper.eq("IsDeleted", false);
        TzhBsFile x = mapper.selectOne(wrapper);
        return x;
    }

    @Override
    public List<TzhBsFileVO> list(TzhBsFileListDTO dto) {
        QueryWrapper<TzhBsFile> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(dto.getSiteName())) {
        	wrapper.eq("SiteName", dto.getSiteName());
        }
        if(StringUtils.isNotBlank(dto.getSection())) {
            wrapper.eq("Section", dto.getSection());
        }
        if(StringUtils.isNotBlank(dto.getCategory())) {
            wrapper.eq("Category", dto.getCategory());
        }
        wrapper.eq("IsDeleted", false);
        List<TzhBsFile> lst = mapper.selectList(wrapper);
		List<TzhBsFileVO> lstVO = lst.stream().map(x -> {
            TzhBsFileVO vo = new TzhBsFileVO();
            BeanUtils.copyProperties(x, vo);
            return vo;
        }).collect(Collectors.toList());
        return lstVO;
    }
}
