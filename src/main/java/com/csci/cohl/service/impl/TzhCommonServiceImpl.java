package com.csci.cohl.service.impl;

import com.csci.cohl.beans.dto.*;
import com.csci.cohl.beans.vo.*;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.mapper.BiTzhBsRouteMapper;
import com.csci.cohl.mapper.BiTzhBsUserSiteMapper;
import com.csci.cohl.service.ITzhCommonService;
import com.csci.susdev.util.StringUtil;
import com.csci.tzh.mapper.TzhProjectDetailMapper;
import com.csci.tzh.mapper.TzhProtocolMapper;
import com.csci.tzh.mapper.TzhProtocolSubCategoryMapper;
import com.csci.tzh.mapper.TzhProtocolCategoryMapper;
import com.csci.tzh.mapper.TzhProjectInfoMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class TzhCommonServiceImpl extends BaseServiceImpl implements ITzhCommonService {

    @Resource
    private BiTzhBsUserSiteMapper biTzhBsUserSiteMapper;

    @Resource
    private TzhProtocolMapper tzhProtocolMapper;

    @Resource
    private TzhProtocolSubCategoryMapper tzhProtocolSubCategoryMapper;

    @Resource
    private TzhProtocolCategoryMapper tzhProtocolCategoryMapper;

    @Resource
    private TzhProjectInfoMapper tzhProjectInfoMapper;

    @Resource
    private TzhProjectDetailMapper tzhProjectDetailMapper;

    @Resource
    private BiTzhBsRouteMapper biTzhBsRouteMapper;

    @Override
    public ResultBody<TzhProjectInfoVO> getProjectInfo(SiteNameDTO dto) {
        String username = (String) request.getAttribute("username");
        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(dto.getSiteId())) return ResultBody.error("無法查詢該項目。");
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");
        }

        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            List<TzhProjectInfoVO> lst = tzhProjectInfoMapper.getProjectInfoBySiteId(dto.getSiteId());
            if(lst.size() == 0) {
                return ResultBody.error("找不到項目。");
            } else {
                List<TzhProjectDetailVO> listSiteProtocolVO = tzhProjectDetailMapper.listBySiteId(dto.getSiteId());
                lst.get(0).setListSiteProtocolVO(listSiteProtocolVO);
                return ResultBody.success(lst.get(0));
            }
        }else {
            List<TzhProjectInfoVO> lst = tzhProjectInfoMapper.getProjectInfo(dto.getSiteName());
            if(lst.size() == 0) {
                return ResultBody.error("找不到項目。");
            } else {
                List<TzhProjectDetailVO> listSiteProtocolVO = tzhProjectDetailMapper.list(dto.getSiteName());
                lst.get(0).setListSiteProtocolVO(listSiteProtocolVO);
                return ResultBody.success(lst.get(0));
            }
        }
    }

    @Override
    public List<TzhProtocolSubCategoryVO> listProtocolSubCategory(ProtocolDTO dto) {
        return tzhProtocolSubCategoryMapper.listSubCategory(dto.getProtocol());
    }

    @Override
    public List<TzhProtocolCategoryVO> listProtocolCategory(ProtocolDTO dto) {
        return tzhProtocolCategoryMapper.listCategory(dto.getProtocol());
    }

    @Override
    public ResultBody<List<TzhBsRouteVO>> listRoute(SiteNameDTO dto) {
        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            return ResultBody.success(biTzhBsRouteMapper.listRouteById(dto.getSiteId(), dto.getProtocol()));
        }else {
            return ResultBody.success(biTzhBsRouteMapper.listRoute(dto.getSiteName(), dto.getProtocol()));
        }
    }
}
