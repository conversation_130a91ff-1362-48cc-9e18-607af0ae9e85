package com.csci.cohl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csci.cohl.beans.dto.*;
import com.csci.cohl.beans.vo.*;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.mapper.TzhBsEmissionReductionStatisticsMapper;
import com.csci.cohl.mapper.BiTzhBsUserSiteMapper;
import com.csci.cohl.model.*;
import com.csci.cohl.service.*;
import com.csci.susdev.util.StringUtil;
import com.csci.tzh.mapper.TzhEmissionReductionHeadMapper;
import com.csci.tzh.mapper.TzhEmissionReductionMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class TzhBsEmissionReductionStatisticsServiceImpl extends BaseServiceImpl implements ITzhBsEmissionReductionStatisticsService {

    @Resource
    private BiTzhBsUserSiteMapper biTzhBsUserSiteMapper;

    @Resource
    private TzhBsEmissionReductionStatisticsMapper tzhBsEmissionReductionStatisticsMapper;

    @Resource
    private TzhEmissionReductionHeadMapper tzhEmissionReductionHeadMapper;

    @Resource
    private TzhEmissionReductionMapper tzhEmissionReductionMapper;

    @Override
    public ResultBody<List<TzhEmissionReductionHeadVO>> getEmissionReductionHead(SiteNameDTO dto) {
        String username = (String) request.getAttribute("username");
        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(dto.getSiteId())) return ResultBody.error("無法查詢該項目。");
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");
        }
        List<TzhEmissionReductionHeadVO> lst = tzhEmissionReductionHeadMapper.listEmissionReductionHead(dto.getSiteName(), dto.getProtocol());

        return ResultBody.success(lst);
    }

    @Override
    public ResultBody<List<TzhEmissionReduction>> getEmissionReductionDetail(IdDTO dto) {
        QueryWrapper<TzhEmissionReduction> wrapper = new QueryWrapper<>();
        wrapper.eq("HeadId", dto.getId());
        wrapper.eq("Type", "每月減排");
        wrapper.eq("IsDeleted", false);
        wrapper.orderByAsc("RecordYearMonth");
        List<TzhEmissionReduction> lst = tzhEmissionReductionMapper.selectList(wrapper);

        return ResultBody.success(lst);
    }

    @Override
    public ResultBody<List<TzhBsAccumulatedReductionPerScopeMainVO>> getAccumulatedReductionPerScopeMain(TzhBsAccumulatedReductionPerScopeMainDTO dto) {
        String username = (String) request.getAttribute("username");
        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(dto.getSiteId())) return ResultBody.error("無法查詢該項目。");
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");
        }
		List<TzhBsAccumulatedReductionPerScopeMainVO> lstVo =
                tzhBsEmissionReductionStatisticsMapper.getAccumulatedReductionPerScopeMain(dto.getStartMonth(),
                        dto.getEndMonth(), dto.getCarbonEmissionLocation(), dto.getSiteName(), dto.getProtocol());
        return ResultBody.success(lstVo);
    }

    @Override
    public ResultBody<List<TzhBsAccumulatedReductionPerScopeDetailVO>> getAccumulatedReductionPerScopeDetail(TzhBsAccumulatedReductionPerScopeDetailDTO dto) {
        String username = (String) request.getAttribute("username");
        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(dto.getSiteId())) return ResultBody.error("無法查詢該項目。");
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");
        }
        List<TzhBsAccumulatedReductionPerScopeDetailVO> lstVo =
                tzhBsEmissionReductionStatisticsMapper.getAccumulatedReductionPerScopeDetail(dto.getStartMonth(),
                        dto.getEndMonth(), dto.getCarbonEmissionLocation(), dto.getSiteName(), dto.getProtocol(),
                        dto.getScopeDetail());
        return ResultBody.success(lstVo);
    }

    @Override
    public ResultBody<List<TzhBsAccumulatedReductionDetailPerScopeDetailVO>> getAccumulatedReductionDetailPerScopeDetail(TzhBsAccumulatedReductionDetailPerScopeDetailDTO dto) {
        String username = (String) request.getAttribute("username");
        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(dto.getSiteId())) return ResultBody.error("無法查詢該項目。");
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");
        }
        List<TzhBsAccumulatedReductionDetailPerScopeDetailVO> lstVo =
                tzhBsEmissionReductionStatisticsMapper.getAccumulatedReductionDetailPerScopeDetail(dto.getStartMonth(),
                        dto.getEndMonth(), dto.getCarbonEmissionLocation(), dto.getSiteName(), dto.getProtocol(),
                        dto.getScopeDetail());
        return ResultBody.success(lstVo);
    }

    @Override
    public ResultBody<List<TzhBsReductionAchievementRateVO>> getReductionAchievementRate(TzhBsReductionAchievementRateDTO dto) {
        String username = (String) request.getAttribute("username");
        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(dto.getSiteId())) return ResultBody.error("無法查詢該項目。");
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");
        }
        List<TzhBsReductionAchievementRateVO> lstVo =
                tzhBsEmissionReductionStatisticsMapper.getReductionAchievementRate(dto.getStartMonth(),
                        dto.getEndMonth(), dto.getCarbonEmissionLocation(), dto.getSiteName(), dto.getProtocol());
        return ResultBody.success(lstVo);
    }

    @Override
    public ResultBody<List<TzhBsMonthlyEmissionPredictionByAverageVO>> getMonthlyEmissionPredictionByAverage(TzhBsMonthlyEmissionPredictionByAverageDTO dto) {
        String username = (String) request.getAttribute("username");
        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(dto.getSiteId())) return ResultBody.error("無法查詢該項目。");
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");
        }
        List<TzhBsMonthlyEmissionPredictionByAverageVO> lstVo = tzhBsEmissionReductionStatisticsMapper.getTotalEmissionPrediction(dto.getCarbonEmissionLocation(), dto.getSiteName(), dto.getProtocol());
        lstVo.addAll(tzhBsEmissionReductionStatisticsMapper.getMonthlyEmissionPredictionByAverage(dto.getCarbonEmissionLocation(), dto.getSiteName(), dto.getProtocol()));
        return ResultBody.success(lstVo);
    }

    @Override
    public ResultBody<List<TzhBsCarbonEmissionPredictionVO>> getCarbonEmissionPrediction(TzhBsCarbonEmissionPredictionDTO dto) {
        String username = (String) request.getAttribute("username");
        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(dto.getSiteId())) return ResultBody.error("無法查詢該項目。");
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");
        }
        List<TzhBsCarbonEmissionPredictionVO> lstVo =
                tzhBsEmissionReductionStatisticsMapper.getCarbonEmissionPrediction(dto.getCarbonEmissionLocation(),
                        dto.getSiteName(), dto.getProtocol());
        return ResultBody.success(lstVo);
    }

    @Override
    public ResultBody<List<TzhBsCarbonEmissionPredictionIsoVO>> getCarbonEmissionPredictionIso(TzhBsCarbonEmissionPredictionIsoDTO dto) {
        String username = (String) request.getAttribute("username");
        if (StringUtil.isNotEmpty(dto.getSiteId())) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(dto.getSiteId())) return ResultBody.error("無法查詢該項目。");
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");
        }
        List<TzhBsCarbonEmissionPredictionIsoVO> lstVo =
                tzhBsEmissionReductionStatisticsMapper.getCarbonEmissionPredictionIso(dto.getCarbonEmissionLocation(),
                        dto.getSiteName(), dto.getProtocol());
        return ResultBody.success(lstVo);
    }
}
