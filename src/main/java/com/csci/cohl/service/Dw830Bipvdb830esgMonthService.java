package com.csci.cohl.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.cohl.mapper.Dw830Bipvdb830esgMonthMapper;
import com.csci.cohl.model.Dw830Bipvdb830esgMonth;
@Service
public class Dw830Bipvdb830esgMonthService extends ServiceImpl<Dw830Bipvdb830esgMonthMapper, Dw830Bipvdb830esgMonth> {

    public List<Dw830Bipvdb830esgMonth> getPowerGeneration() {
        LambdaQueryWrapper<Dw830Bipvdb830esgMonth> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.orderByDesc(Dw830Bipvdb830esgMonth::getYearMonth);
        return baseMapper.selectList(queryWrapper);
    }

    public List getPowerGenerationStatistics() {
        return null;
    }

    public BigDecimal getTotalPowerGenFrom202304() {
       return baseMapper.getTotalPowerGeneFrom202304();
    }
}
