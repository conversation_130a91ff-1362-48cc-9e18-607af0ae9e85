package com.csci.cohl.service;

import com.csci.cohl.beans.dto.TzhBsFileDTO;
import com.csci.cohl.beans.dto.TzhBsFileListDTO;
import com.csci.cohl.beans.vo.TzhBsFileVO;
import com.csci.cohl.model.TzhBsFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface ITzhBsFileService {

	int upload(TzhBsFileDTO dto, MultipartFile multipartFile) throws IOException;

	int delete(String id);

	TzhBsFile get(String id);

	List<TzhBsFileVO> list(TzhBsFileListDTO dto);

}
