package com.csci.cohl.service;

import org.springframework.stereotype.Service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.cohl.mapper.Dw830Bipvdb830esgSrMapper;
import com.csci.cohl.model.Dw830Bipvdb830esgSr;
@Service
public class Dw830Bipvdb830esgSrService extends ServiceImpl<Dw830Bipvdb830esgSrMapper, Dw830Bipvdb830esgSr> {

    public List<Dw830Bipvdb830esgSr> getEnergySaving() {
        return baseMapper.getEnergySaving();
    }
}
