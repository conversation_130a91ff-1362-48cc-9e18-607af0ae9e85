package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

@Schema
@Data
@TableName(value = "t_material_trans_head")
public class MaterialTransHead {
    @TableField(value = "id")
    @Schema(description="")
    private String id;

    @TableField(value = "organization_id")
    @Schema(description="")
    private String organizationId;

    @TableField(value = "[year]")
    @Schema(description="")
    private Integer year;

    @TableField(value = "[month]")
    @Schema(description="")
    private Integer month;

    @TableField(value = "is_active")
    @Schema(description="")
    private Boolean isActive;

    @TableField(value = "creation_time")
    @Schema(description="")
    private Date creationTime;

    @TableField(value = "create_username")
    @Schema(description="")
    private String createUsername;

    @TableField(value = "create_user_id")
    @Schema(description="")
    private String createUserId;

    @TableField(value = "last_update_time")
    @Schema(description="")
    private Date lastUpdateTime;

    @TableField(value = "last_update_username")
    @Schema(description="")
    private String lastUpdateUsername;

    @TableField(value = "last_update_user_id")
    @Schema(description="")
    private String lastUpdateUserId;

    @TableField(value = "last_update_version")
    @Schema(description="")
    private Integer lastUpdateVersion;
}