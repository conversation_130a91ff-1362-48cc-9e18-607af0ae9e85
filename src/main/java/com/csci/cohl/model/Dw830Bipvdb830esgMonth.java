package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema
@Data
@TableName(value = "dw_830_bipvdb_830ESG_Month")
public class Dw830Bipvdb830esgMonth {
    @TableField(value = "月發電量")
    @Schema(description = "月發電量")
    @JsonProperty("monthlyPowerGeneration")
    private Double monthlyPowerGeneration;

    @TableField(value = "發電收益")
    @Schema(description = "發電收益")
    @JsonProperty("powerGenerationIncome")
    private Double powerGenerationIncome;

    @TableField(value = "年月")
    @Schema(description = "年月")
    @JsonProperty("yearMonth")
    private String yearMonth;

    @TableField(value = "calculate_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonIgnore
    private LocalDateTime calculateTime;
}
