package com.csci.cohl.model;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class TzhBsPermission extends Model {

    private static final long serialVersionUID = 1L;

    private String user;
    private String permissionRole;
    private Integer id;
}
