package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("D_Scope")
public class DScope extends Model {

    private static final long serialVersionUID = 1L;

    @TableField("Id")
    private String Id;

    @TableField("Protocol")
    private String Protocol;

    @TableField("CarbonEmissionLocation")
    private String CarbonEmissionLocation;

    @TableField("ScopeMain")
    private String ScopeMain;

    @TableField("ScopeMainSC")
    private String ScopeMainSC;

    @TableField("ScopeMainEN")
    private String ScopeMainEN;

    @TableField("ScopeDetail")
    private String ScopeDetail;

    @TableField("ScopeDetailSC")
    private String ScopeDetailSC;

    @TableField("ScopeDetailEN")
    private String ScopeDetailEN;

}
