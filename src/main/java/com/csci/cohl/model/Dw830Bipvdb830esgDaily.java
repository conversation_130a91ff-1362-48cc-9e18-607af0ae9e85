package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Schema
@Data
@TableName(value = "dw_830_bipvdb_830ESG_Daily")
public class Dw830Bipvdb830esgDaily {
    @TableField(value = "發電量")
    @Schema(description="")
    private Double eleGenAmount;

    @TableField(value = "日期")
    @Schema(description="日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;

    @TableField(value = "calculate_time")
    @Schema(description="")
    private LocalDateTime calculateTime;
}
