package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("Tzh_EmissionReductionHead")
public class TzhEmissionReductionHead extends Model {

    private static final long serialVersionUID = 1L;

    @TableField("Id")
    private String Id;

    @TableField("SiteName")
    private String SiteName;

    @TableField("ProtocolCategoryId")
    private String ProtocolCategoryId;

    @TableField("Title")
    private String Title;

    @TableField("TitleSC")
    private String TitleSC;

    @TableField("TitleEN")
    private String TitleEN;

    @TableField("CarbonEmissionLocationId")
    private String CarbonEmissionLocationId;

    @TableField("MethodDescription")
    private String MethodDescription;

    @TableField("MethodDescriptionSC")
    private String MethodDescriptionSC;

    @TableField("MethodDescriptionEN")
    private String MethodDescriptionEN;

    @TableField("CalculationDescription")
    private String CalculationDescription;

    @TableField("CreatedBy")
    private String CreatedBy;

    @TableField("CreatedTime")
    private LocalDateTime CreatedTime;

    @TableField("DeletedBy")
    private String DeletedBy;

    @TableField("DeletedTime")
    private LocalDateTime DeletedTime;

    @TableField("IsDeleted")
    private Boolean IsDeleted;
}
