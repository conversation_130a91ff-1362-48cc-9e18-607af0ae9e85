package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("Tzh_EmissionReduction")
public class TzhEmissionReduction extends Model {

    private static final long serialVersionUID = 1L;

    @TableField("Id")
    private String Id;

    @TableField("HeadId")
    private String HeadId;

    @TableField("RecordYearMonth")
    private String RecordYearMonth;

    @TableField("CarbonReductionAmount")
    private String CarbonReductionAmount;

    @TableField("CarbonUnit")
    private String CarbonUnit;

    @TableField("CreatedBy")
    private String CreatedBy;

    @TableField("CreatedTime")
    private LocalDateTime CreatedTime;

    @TableField("DeletedBy")
    private String DeletedBy;

    @TableField("DeletedTime")
    private LocalDateTime DeletedTime;

    @TableField("IsDeleted")
    private Boolean IsDeleted;
}
