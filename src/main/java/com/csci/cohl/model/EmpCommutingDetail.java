package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

@Schema
@Data
@TableName(value = "t_emp_commuting_detail")
public class EmpCommutingDetail {
    @TableId(value = "id", type = IdType.INPUT)
    @Schema(description = "")
    private String id;

    /**
     * 所在公司
     */
    @TableField(value = "company")
    @Schema(description = "所在公司")
    private String company;

    /**
     * 所在部门
     */
    @TableField(value = "department", updateStrategy = FieldStrategy.IGNORED)
    @Schema(description = "所在部门")
    private String department;

    /**
     * 交通工具
     */
    @TableField(value = "transportation")
    @Schema(description = "交通工具")
    private String transportation;
    /**
     * 其他交通工具
     */
    @TableField(value = "other_transportation")
    @Schema(description = "其他交通工具")
    private String otherTransportation;

    /**
     * 通勤距离
     */
    @TableField(value = "commute_distance")
    @Schema(description = "通勤距离")
    private String commuteDistance;

    /**
     * 单程通勤时间
     */
    @TableField(value = "one_way_commute_time")
    @Schema(description = "单程通勤时间")
    private String oneWayCommuteTime;

    /**
     * 请假天数
     */
    @TableField(value = "leave_days")
    @Schema(description = "请假天数")
    private String leaveDays;

    /**
     * 加班天数
     */
    @TableField(value = "overtime_days")
    @Schema(description = "加班天数")
    private String overtimeDays;

    @TableField(value = "commute_days")
    @Schema(description = "上班天数")
    private String commuteDays;

    /**
     * 备注
     */
    @TableField(value = "remark", updateStrategy = FieldStrategy.IGNORED)
    @Schema(description = "备注")
    private String remark;

    @TableField(value = "seq")
    @Schema(description = "")
    private Integer seq;

    @TableField(value = "creation_time")
    @Schema(description = "")
    private LocalDateTime creationTime;

    @TableField(value = "create_username")
    @Schema(description = "")
    private String createUsername;

    @TableField(value = "create_user_id")
    @Schema(description = "")
    private String createUserId;

    @TableField(value = "last_update_time")
    @Schema(description = "")
    private LocalDateTime lastUpdateTime;

    @TableField(value = "last_update_username")
    @Schema(description = "")
    private String lastUpdateUsername;

    @TableField(value = "last_update_user_id")
    @Schema(description = "")
    private String lastUpdateUserId;

    @TableField(value = "last_update_version")
    @Schema(description = "")
    private Integer lastUpdateVersion;

    @TableField(value = "head_id")
    @Schema(description = "")
    private String headId;
}
