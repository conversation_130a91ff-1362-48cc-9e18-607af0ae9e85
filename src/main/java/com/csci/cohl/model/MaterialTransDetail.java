package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Schema
@Data
@TableName(value = "t_material_trans_detail")
public class MaterialTransDetail {
    @TableField(value = "id")
    @Schema(description="")
    private String id;

    @TableField(value = "head_id")
    @Schema(description="")
    private String headId;

    @TableField(value = "col_1")
    @Schema(description="")
    private String col1;

    @TableField(value = "col_2")
    @Schema(description="")
    private String col2;

    @TableField(value = "col_3")
    @Schema(description="")
    private String col3;

    @TableField(value = "col_4")
    @Schema(description="")
    private String col4;

    @TableField(value = "col_5")
    @Schema(description="")
    private String col5;

    @TableField(value = "col_6")
    @Schema(description="")
    private String col6;

    @TableField(value = "col_7")
    @Schema(description="")
    private String col7;

    @TableField(value = "col_8")
    @Schema(description="")
    private String col8;

    @TableField(value = "col_9")
    @Schema(description="")
    private String col9;

    @TableField(value = "col_10")
    @Schema(description="")
    private String col10;

    @TableField(value = "col_11")
    @Schema(description="")
    private String col11;

    @TableField(value = "col_12")
    @Schema(description="")
    private String col12;

    @TableField(value = "col_13")
    @Schema(description="")
    private String col13;

    @TableField(value = "col_14")
    @Schema(description="")
    private String col14;

    @TableField(value = "col_15")
    @Schema(description="")
    private String col15;

    @TableField(value = "col_16")
    @Schema(description="")
    private String col16;

    @TableField(value = "col_17")
    @Schema(description="")
    private String col17;

    @TableField(value = "col_18")
    @Schema(description="")
    private String col18;

    @TableField(value = "col_19")
    @Schema(description="")
    private String col19;

    @TableField(value = "col_20")
    @Schema(description="")
    private String col20;

    @TableField(value = "col_21")
    @Schema(description="")
    private String col21;

    @TableField(value = "col_22")
    @Schema(description="")
    private String col22;

    @TableField(value = "col_23")
    @Schema(description="")
    private String col23;

    @TableField(value = "col_24")
    @Schema(description="")
    private String col24;

    @TableField(value = "col_25")
    @Schema(description="")
    private String col25;

    @TableField(value = "seq")
    @Schema(description="")
    private Integer seq;

    @TableField(value = "creation_time")
    @Schema(description="")
    private Date creationTime;
}
