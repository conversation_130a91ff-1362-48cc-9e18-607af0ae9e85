package com.csci.cohl.controller;

import com.csci.cohl.beans.dto.*;
import com.csci.cohl.beans.vo.TzhEmissionReductionDescriptionVO;
import com.csci.cohl.epidemic.annotation.UserLoginToken;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.model.TzhPlanning;
import com.csci.cohl.service.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/bi/tzh-bs-main")
@Tag(name = "BI主頁")
@UserLoginToken
public class BiTzhBsMainController {

    @Autowired
    private ITzhBsMainService tzhBsMainService;

    @PostMapping("/row/get-emission-reduction-description")
    @Operation(description = "低碳設計")
    public ResultBody<List<TzhEmissionReductionDescriptionVO>> getTzhEmissionReductionDescription(@RequestBody SiteNameDTO dto) throws Exception {
        return tzhBsMainService.getTzhEmissionReductionDescription(dto);
    }

    @PostMapping("/row/get-planning")
    @Operation(description = "碳排規劃")
    public ResultBody<List<TzhPlanning>> getPlanning(@RequestBody SiteNameDTO dto) {
        return tzhBsMainService.getPlanning(dto);
    }
}
