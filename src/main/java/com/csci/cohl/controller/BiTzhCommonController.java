package com.csci.cohl.controller;

import com.csci.cohl.beans.dto.ProtocolDTO;
import com.csci.cohl.beans.dto.SiteNameDTO;
import com.csci.cohl.beans.vo.TzhBsRouteVO;
import com.csci.cohl.beans.vo.TzhProjectInfoVO;
import com.csci.cohl.epidemic.annotation.UserLoginToken;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.service.ITzhCommonService;
import com.csci.common.exception.ServiceException;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.ClientHttpRequest;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.net.URI;
import java.net.URL;
import java.util.*;


@RestController
@RequestMapping("/bi/tzh-common")
@Tag(name = "通用")
public class BiTzhCommonController {

    @Autowired
    private ITzhCommonService tzhCommonService;

    @UserLoginToken
    @PostMapping("/get-project-info")
    @Operation(description = "獲取項目訊息")
    public ResultBody<TzhProjectInfoVO> getProjectInfo(@RequestBody SiteNameDTO dto) {
        return tzhCommonService.getProjectInfo(dto);
    }

    @UserLoginToken
    @PostMapping("/list-scope")
    @Operation(description = "獲取協議標準範圍列表")
    public ResultBody<List<Map>> listScope(@RequestBody ProtocolDTO dto) {
        Map map = new HashMap();
        map.put("subCategoryList", tzhCommonService.listProtocolSubCategory(dto));
        map.put("categoryList", tzhCommonService.listProtocolCategory(dto));
        return ResultBody.success(map);
    }

    @UserLoginToken
    @PostMapping("/list-route")
    @Operation(description = "獲取路由")
    public ResultBody<List<TzhBsRouteVO>> listRoute(@RequestBody SiteNameDTO dto) {
        return tzhCommonService.listRoute(dto);
    }

    @GetMapping("/proxy")
    @Operation(description = "代理")
    public void proxy(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String target = request.getQueryString().replace("target=","");
        if (target == null || target.equals("") || target.equals("null")) {
            throw new ServiceException("請輸入URL");
        }
        URI newUri = new URI(target);
        // 执行代理查询
        String methodName = request.getMethod();
        HttpMethod httpMethod = HttpMethod.resolve(methodName);
        if (httpMethod == null) {
            throw  new ServiceException("找不到HTTP METHOD");
        }
        ClientHttpRequest delegate = new SimpleClientHttpRequestFactory().createRequest(newUri, httpMethod);
        Enumeration<String> headerNames = request.getHeaderNames();
        // 设置请求头
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            Enumeration<String> v = request.getHeaders(headerName);
            List<String> arr = new ArrayList<>();
            while (v.hasMoreElements()) {
                arr.add(v.nextElement());
            }
            delegate.getHeaders().addAll(headerName, arr);
        }
        StreamUtils.copy(request.getInputStream(), delegate.getBody());
        // 执行远程调用
        ClientHttpResponse clientHttpResponse = delegate.execute();
        response.setStatus(clientHttpResponse.getStatusCode().value());
        // 设置响应头
        clientHttpResponse.getHeaders().forEach((key, value) -> value.forEach(it -> {
            response.setHeader(key, it);
        }));
        StreamUtils.copy(clientHttpResponse.getBody(), response.getOutputStream());
    }

    @GetMapping("/download")
    @Operation(description = "代理download")
    public void download(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String target = request.getQueryString().replace("target=","");
        FileUtils.copyURLToFile(
                new URL(target),
                new File("./download.pdf"),
                10000,
                10000);
    }
}
