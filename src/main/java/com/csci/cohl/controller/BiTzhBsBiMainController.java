package com.csci.cohl.controller;

import com.csci.cohl.beans.dto.SiteNameDTO;
import com.csci.cohl.epidemic.annotation.UserLoginToken;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.service.ITzhBsBiMainService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/bi/tzh-bs-main")
@Tag(name = "BI主頁")
@UserLoginToken
public class BiTzhBsBiMainController {

    @Autowired
    private ITzhBsBiMainService tzhBsBiMainService;

    @PostMapping("/row/get-stat")
    @Operation(description = "獲取統計數據")
    public ResultBody<Map<String, List>> getStat(@RequestBody SiteNameDTO dto) throws Exception {
        return tzhBsBiMainService.getStat(dto);
    }
}
