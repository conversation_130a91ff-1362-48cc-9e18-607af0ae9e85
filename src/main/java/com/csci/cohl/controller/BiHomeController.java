package com.csci.cohl.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping(value = "bi", produces = "application/json")
@RestController
public class BiHomeController {

    @GetMapping("/")
    String index() {
        return "Welcome to big-data-map!";
    }

    @ResponseBody
    @GetMapping("/health-check")
    String healthCheck() {
        return "Hello Spring!";
    }

}
