package com.csci.cohl.controller;

import com.csci.cohl.beans.dto.*;
import com.csci.cohl.beans.vo.*;
import com.csci.cohl.epidemic.annotation.UserLoginToken;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.service.ITzhBsCarbonEmissionStatisticsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/bi/tzh-bs-carbon-emission-statistics")
@Tag(name = "碳排统计")
@UserLoginToken
public class BiTzhBsCarbonEmissionStatisticsController {

    @Autowired
    private ITzhBsCarbonEmissionStatisticsService tzhBsCarbonEmissionStatisticsService;

    @PostMapping("/row/get-carbon-emission-summary")
    @Operation(summary = "查询排放总量数据")
    public ResultBody<List<TzhBsCarbonEmissionSummaryVO>> getCarbonEmissionSummary(@RequestBody TzhBsCarbonEmissionSummaryDTO dto) {
        return tzhBsCarbonEmissionStatisticsService.getCarbonEmissionSummary(dto);
    }

    @PostMapping("/row/get-monthly-emission")
    @Operation(summary = "近半年的月度排放数据显示")
    public ResultBody<List<TzhBsMonthlyEmissionVO>> getMonthlyEmission(@RequestBody TzhBsMonthlyEmissionDTO dto) {
        return tzhBsCarbonEmissionStatisticsService.getMonthlyEmission(dto);
    }
    @PostMapping("/row/get-yearly-emission-by-quarter")
    @Operation(summary = "当年的季度排放数据显示")
    public ResultBody<List<TzhBsYearlyEmissionVO>> getYearlyEmissionByQuarter(@RequestBody TzhBsMonthlyEmissionDTO dto) {
        return tzhBsCarbonEmissionStatisticsService.getYearlyEmissionByQuarter(dto);
    }

    @PostMapping("/row/get-emission-details")
    @Operation(summary = "排放详细数据")
    public ResultBody<List<TzhBsEmissionDetailsVO>> getEmissionDetails(@RequestBody TzhBsEmissionDetailsDTO dto) {
        Logger logger = LoggerFactory.getLogger(BiTzhBsCarbonEmissionStatisticsController.class);
        long startTime = System.currentTimeMillis();

        logger.info("开始执行 getEmissionDetails 方法");

        // 记录服务调用开始时间
        long serviceStartTime = System.currentTimeMillis();
        ResultBody<List<TzhBsEmissionDetailsVO>> result = tzhBsCarbonEmissionStatisticsService.getEmissionDetails(dto);
        long serviceEndTime = System.currentTimeMillis();
        logger.info("服务调用执行时间: {} ms", (serviceEndTime - serviceStartTime));

        long endTime = System.currentTimeMillis();
        logger.info("getEmissionDetails 方法执行时间: {} ms", (endTime - startTime));

        return result;
    }

    @PostMapping("/row/get-emission-not-zero-details")
    @Operation(summary = "排放详细数据(非零)")
    public ResultBody<List<TzhBsEmissionNotZeroDetailsVO>> getEmissionNotZeroDetails(@RequestBody TzhBsEmissionNotZeroDetailsDTO dto) {
        return tzhBsCarbonEmissionStatisticsService.getEmissionNotZeroDetails(dto);
    }

    @PostMapping("/row/get-emission-3b-details")
    @Operation(summary = "排放详细数据(范围三（B）)")
    public ResultBody<List<TzhBsEmission3bDetailsVO>> getEmission3bDetails(@RequestBody TzhBsEmission3bDetailsDTO dto) {
        return tzhBsCarbonEmissionStatisticsService.getEmission3bDetails(dto);
    }

    @PostMapping("/row/get-blockchain-signature")
    @Operation(summary = "区块链签名数据")
    public ResultBody<TzhBsBlockchainSignatureVO> getBlockchainSignature(@RequestBody TzhBsBlockchainSignatureDTO dto) {
        return tzhBsCarbonEmissionStatisticsService.getBlockchainSignature(dto);
    }
}
