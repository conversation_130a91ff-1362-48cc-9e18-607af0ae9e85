package com.csci.cohl.controller;

import com.csci.cohl.beans.dto.IdDTO;
import com.csci.cohl.beans.dto.TzhBsFileDTO;
import com.csci.cohl.beans.dto.TzhBsFileListDTO;
import com.csci.cohl.beans.vo.TzhBsFileVO;
import com.csci.cohl.epidemic.annotation.UserLoginToken;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.model.TzhBsFile;
import com.csci.cohl.service.ITzhBsFileService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.List;


@RestController
@RequestMapping("/bi/tzh-bs-file")
@Tag(name = "文件檔案")
@UserLoginToken
public class BiTzhBsFileController {

    @Autowired
    private ITzhBsFileService tzhBsFileService;

    @PostMapping("/row/list")
    @Operation(description = "获取所有数据")
    public List<TzhBsFileVO> list(@RequestBody TzhBsFileListDTO dto) {
        return tzhBsFileService.list(dto);
    }

    @PostMapping("/row/upload")
    @Operation(description = "上傳")
    public ResultBody<Integer> update(@ModelAttribute TzhBsFileDTO dto, @RequestParam("file") MultipartFile multipartFile) throws IOException {
        return ResultBody.success(tzhBsFileService.upload(dto, multipartFile));
    }

    @PostMapping("/row/delete")
    @Operation(description = "刪除數據")
    public ResultBody<Integer> delete(@RequestBody IdDTO dto) {
        return ResultBody.success(tzhBsFileService.delete(dto.getId()));
    }

    @PostMapping("/row/download")
    @Operation(description = "下載")
    public ResponseEntity<Resource> download(@RequestBody IdDTO dto) {
    	TzhBsFile x = tzhBsFileService.get(dto.getId());
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + x.getName() + "\"")
                .contentType(MediaType.parseMediaType(x.getType()))
                .body(new InputStreamResource(new ByteArrayInputStream(x.getData())));
    }

    @PostMapping("/row/base64")
    @Operation(description = "以base64格式獲取")
    public ResultBody<String> base64(@RequestBody IdDTO dto) {
    	TzhBsFile x = tzhBsFileService.get(dto.getId());
        return ResultBody.success(Base64.getEncoder().encodeToString(x.getData()));
    }

}
