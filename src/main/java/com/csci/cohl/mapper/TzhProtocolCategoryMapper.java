//package com.csci.cohl.mapper;
//
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.csci.cohl.beans.vo.TzhProtocolCategoryVO;
//import com.csci.cohl.model.TzhProtocolCategory;
//import org.apache.ibatis.annotations.Param;
//import org.apache.ibatis.annotations.Select;
//
//import java.util.List;
//
//public interface TzhProtocolCategoryMapper extends BaseMapper<TzhProtocolCategory> {
//
//    @Select("""
//            SELECT C.Id, P.Name AS Protocol, P.NameSC AS ProtocolSC, P.NameEN AS ProtocolEN,
//            C.CategoryName, C.CategoryNameSC, C.CategoryNameEN
//            FROM Tzh_Protocol P
//            LEFT JOIN Tzh_Protocol_Category C ON P.Id = C.ProtocolId
//            WHERE P.NameEN = #{protocol}
//            ORDER BY P.Name, C.CategoryName
//            """)
//    List<TzhProtocolCategoryVO> listCategory(@Param("protocol") String protocol);
//}
