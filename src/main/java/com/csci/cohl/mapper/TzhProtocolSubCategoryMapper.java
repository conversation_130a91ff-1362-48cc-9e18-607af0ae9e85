//package com.csci.cohl.mapper;
//
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.csci.cohl.beans.vo.TzhProtocolSubCategoryVO;
//import com.csci.cohl.model.TzhProtocolSubCategory;
//import org.apache.ibatis.annotations.Param;
//import org.apache.ibatis.annotations.Select;
//
//import java.util.List;
//
//public interface TzhProtocolSubCategoryMapper extends BaseMapper<TzhProtocolSubCategory> {
//
//    @Select("""
//            SELECT SC.Id, P.Name AS Protocol, P.NameSC AS ProtocolSC, P.NameEN AS ProtocolEN, P.Description AS ProtocolDescription,
//            C.CategoryName, C.CategoryNameSC, C.CategoryNameEN, SC.SubCategoryName, SC.SubCategoryNameSC, SC.SubCategoryNameEN
//            FROM Tzh_Protocol P
//            LEFT JOIN Tzh_Protocol_Category C ON P.Id = C.ProtocolId
//            LEFT JOIN Tzh_Protocol_SubCategory SC ON C.Id = SC.CategoryId
//            WHERE P.NameEN = #{protocol}
//            ORDER BY Protocol, CategoryName, SubCategoryName
//            """)
//    List<TzhProtocolSubCategoryVO> listSubCategory(@Param("protocol") String protocol);
//}
