<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.cohl.mapper.Dw830Bipvdb830esgTotalMapper">
  <resultMap id="BaseResultMap" type="com.csci.cohl.model.Dw830Bipvdb830esgTotal">
    <!--@mbg.generated-->
    <!--@Table dw_830_bipvdb_830ESG_total-->
    <result column="累計發電量" jdbcType="FLOAT" property="sumPowerGeneration" />
    <result column="裝機容量" jdbcType="NUMERIC" property="installedCapacity" />
    <result column="累計收益" jdbcType="FLOAT" property="sumIncome" />
    <result column="實時功率" jdbcType="FLOAT" property="realTimePower" />
    <result column="總用電量" jdbcType="NUMERIC" property="totalEleUsage" />
    <result column="上網電量" jdbcType="NUMERIC" property="onlineEleUsage" />
    <result column="calculate_time" property="calculateTime"/>
    <result column="greenEleProportion" property="greenEleProportion"/>
    <result column="consumptionRatio" property="consumptionRatio"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    累計發電量, 裝機容量, 累計收益, 實時功率, 總用電量, 上網電量
  </sql>
  <select id="getPowerGenerationStatistics" resultMap="BaseResultMap">
        select top 1
          t.*,
          (t.累計發電量-t.上網電量)/t.總用電量 as greenEleProportion,
          (t.累計發電量-t.上網電量)/t.累計發電量 as consumptionRatio
        from dw_830_bipvdb_830ESG_total t order by t.calculate_time desc
    </select>
</mapper>
