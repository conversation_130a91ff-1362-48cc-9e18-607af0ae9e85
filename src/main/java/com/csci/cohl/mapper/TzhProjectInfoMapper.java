//package com.csci.cohl.mapper;
//
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.csci.cohl.beans.vo.TzhProjectInfoVO;
//import com.csci.cohl.model.TzhProjectInfo;
//import org.apache.ibatis.annotations.Param;
//import org.apache.ibatis.annotations.Select;
//
//import java.util.List;
//
//public interface TzhProjectInfoMapper extends BaseMapper<TzhProjectInfo> {
//    @Select("""
//            SELECT PI.Id, PI.Code, PI.Name, PI.Type, PI.IsConsumptionMeasurable, PI.IsPeriodicallyReportable,
//            PI.InvestmentTotal, PI.ContractAmount, PI.Area, PI.StartDate, PI.EndDate, PI.Address, PI.Contractor,
//            PI.Owner, PI.Architect, PI.Supervisor, PI.ManagerZhtAccount, PI.Longitude, PI.Latitude, PI.RegionId,
//            R.Name AS Region, R.NameSC AS RegionSC, R.NameEN AS RegionEN
//            FROM Tzh_ProjectInfo PI
//            LEFT JOIN Tzh_Region R ON PI.RegionId = R.Id
//            WHERE PI.Name = #{siteName} AND PI.IsDeleted = 0
//            """)
//    List<TzhProjectInfoVO> getProjectInfo(@Param("siteName") String siteName);
//}
