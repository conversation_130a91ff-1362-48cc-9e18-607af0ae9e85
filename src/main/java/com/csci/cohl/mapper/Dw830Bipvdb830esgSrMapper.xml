<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.cohl.mapper.Dw830Bipvdb830esgSrMapper">
  <resultMap id="BaseResultMap" type="com.csci.cohl.model.Dw830Bipvdb830esgSr">
    <!--@mbg.generated-->
    <!--@Table dw_830_bipvdb_830ESG_SR-->
    <result column="節能減排分類" jdbcType="VARCHAR" property="節能減排分類" />
    <result column="節能減排量" jdbcType="FLOAT" property="節能減排量" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    節能減排分類, 節能減排量
  </sql>

  <select id="getEnergySaving" resultMap="BaseResultMap">
    select t.*
    FROM (
           SELECT * , row_number() over (partition by 節能減排分類 order by calculate_time desc) as rn
           FROM dw_830_bipvdb_830ESG_SR
         ) t
    WHERE t.rn = 1
    order by 節能減排量 desc;
    </select>
</mapper>
