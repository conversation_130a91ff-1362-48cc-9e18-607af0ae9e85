<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.cohl.mapper.Dw830Bipvdb830esgDailyMapper">
  <resultMap id="BaseResultMap" type="com.csci.cohl.model.Dw830Bipvdb830esgDaily">
    <!--@mbg.generated-->
    <!--@Table dw_830_bipvdb_830ESG_Daily-->
    <result column="發電量" jdbcType="FLOAT" property="eleGenAmount" />
    <result column="日期" jdbcType="DATE" property="date" />
    <result column="calculate_time" jdbcType="TIMESTAMP" property="calculateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    發電量, 日期, calculate_time
  </sql>

  <select id="getPowerGeneration" resultMap="BaseResultMap">
    select top 10 t.*
    FROM (
           SELECT * , row_number() over (partition by 日期 order by calculate_time desc) as rn
           FROM dw_830_bipvdb_830ESG_Daily
         ) t
    WHERE t.rn = 1
      and t.日期 is not null
    order by 日期 desc;
    </select>
</mapper>
