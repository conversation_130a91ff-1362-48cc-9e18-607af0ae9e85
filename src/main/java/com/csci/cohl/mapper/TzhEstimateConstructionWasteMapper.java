//package com.csci.cohl.mapper;
//
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.csci.cohl.beans.vo.TzhEstimateConstructionWasteVO;
//import com.csci.cohl.model.TzhEstimateConstructionWaste;
//import org.apache.ibatis.annotations.Param;
//import org.apache.ibatis.annotations.Select;
//
//import java.util.List;
//
//public interface TzhEstimateConstructionWasteMapper extends BaseMapper<TzhEstimateConstructionWaste> {
//
//    @Select("""
//			SELECT C.CategoryName, C.CategoryNameSC, C.CategoryNameEN, WasteQty = ISNULL(T.WasteQty, 0)
//			FROM Tzh_EstimateConstructionWaste_Category C
//			LEFT JOIN (
//				SELECT C.CategoryName, C.CategoryNameSC, C.CategoryNameEN, WasteQty = SUM(ECW.WasteQty)
//				FROM Tzh_EstimateConstructionWaste ECW
//				LEFT JOIN Tzh_Protocol P ON ECW.ProtocolId = P.Id
//				LEFT JOIN Tzh_EstimateConstructionWaste_SubCategory SC ON ECW.SubCategoryId = SC.Id
//				LEFT JOIN Tzh_EstimateConstructionWaste_Category C ON SC.CategoryId = C.Id AND C.ProtocolId = ECW.ProtocolId
//				WHERE ECW.DisposalMethodId != (SELECT Id FROM Tzh_EstimateConstructionWaste_DisposalMethod WHERE Name = N'回收')
//				AND ECW.SiteName = #{siteName}
//				AND P.NameEN = #{protocol}
//				AND ECW.IsDeleted = 0
//				GROUP BY C.CategoryName, C.CategoryNameSC, C.CategoryNameEN, ECW.WasteQtyUnit
//			) T ON C.CategoryName = T.CategoryName
//			ORDER BY C.CategoryName
//            """)
//    List<TzhEstimateConstructionWasteVO> list(@Param("siteName") String siteName, @Param("protocol") String protocol);
//
//}
