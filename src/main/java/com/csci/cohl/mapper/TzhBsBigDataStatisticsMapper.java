package com.csci.cohl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.csci.cohl.beans.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface TzhBsBigDataStatisticsMapper extends BaseMapper<Object> {

    @Select("""
            SELECT TOP 1 MD5Code 
            FROM dbo.BlockChain_SetValueByKey_Log 
            WHERE FileName LIKE 'dbo_F_Result_%' 
            AND RequestStatusCode = 200 AND RequestText = 'true' 
            AND TRY_CONVERT(date, Logdatetime) = #{calculateDate} 
            ORDER BY Logdatetime DESC 
            """)
    TzhBsBlockchainSignatureVO getBlockchainSignature(@Param("calculateDate") LocalDate calculateDate);

    @Select("""
            <script>
            SELECT L.Name AS CarbonEmissionLocation, L.NameSC AS CarbonEmissionLocationSC, L.NameEN AS CarbonEmissionLocationEN, CA.RecordYearMonth,        
            CA.MaterialCode, CA.MaterialName, CA.Qty, CA.QtyUnit, SC.sub_category_name SubCategoryName, SC.sub_category_name_sc SubCategoryNameSC, SC.sub_category_name_en SubCategoryNameEN,        
            CA.CarbonFactor, CA.CarbonFactorUnit, CA.TransportCarbonAmount, CA.CarbonAmount, CA.CarbonAmount_Unit, CA.CalculateDate, CA.Status        
            FROM F_CombineAll_Latest CA        
            LEFT JOIN t_protocol_sub_category SC ON SC.sub_category_name = CA.Scope        
            LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Name = CA.CarbonEmissionLocation
            left join t_organization o  on o.name = CA.SiteName and o.is_deleted = 0
            WHERE CA.SiteName = #{siteName}        
            AND CA.Protocol = #{protocol}        
            AND CA.MaterialName NOT IN (N'廢棄物運輸')        
            AND CA.CarbonEmissionLocation LIKE CONCAT('%', #{carbonEmissionLocation},'%')        
            <if test="siteId != null and siteId != ''">
                and o.id = #{siteId}
			</if>
            </script>
            """)
    IPage<TzhBsIntermediateProcessingDetailsVO> getIntermediateProcessingDetailsLatest(IPage<?> page,
                                                                                 @Param("calculateDate") LocalDate calculateDate,
                                                                                 @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                                                 @Param("siteName") String siteName,
                                                                                 @Param("protocol") String protocol,
                                                                                 @Param("siteId") String siteId);

    @Select("""
            <script>
            SELECT L.Name AS CarbonEmissionLocation, L.NameSC AS CarbonEmissionLocationSC, L.NameEN AS CarbonEmissionLocationEN, CA.RecordYearMonth,        
            CA.MaterialCode, CA.MaterialName, CA.Qty, CA.QtyUnit, SC.sub_category_name, SC.sub_category_name_sc, SC.sub_category_name_en,        
            CA.CarbonFactor, CA.CarbonFactorUnit, CA.TransportCarbonAmount, CA.CarbonAmount, CA.CarbonAmount_Unit, CA.CalculateDate, CA.Status        
            FROM F_CombineAll CA        
            LEFT JOIN t_protocol_sub_category SC ON SC.sub_category_name = CA.Scope        
            LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Name = CA.CarbonEmissionLocation
            left join t_organization o  on o.name = CA.SiteName and o.is_deleted = 0
            WHERE CA.CalculateDate = #{calculateDate}        
            AND CA.SiteName = #{siteName}        
            AND CA.Protocol = #{protocol}        
            AND CA.MaterialName NOT IN (N'廢棄物運輸')        
            AND CA.CarbonEmissionLocation LIKE CONCAT('%', #{carbonEmissionLocation},'%')        
            <if test="siteId != null and siteId != ''">
                and o.id = #{siteId}
			</if>
            </script>
            """)
    IPage<TzhBsIntermediateProcessingDetailsVO> getIntermediateProcessingDetails(IPage<?> page,
                                                                                 @Param("calculateDate") LocalDate calculateDate,
                                                                                 @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                                                 @Param("siteName") String siteName,
                                                                                 @Param("protocol") String protocol,
                                                                                 @Param("siteId") String siteId);



    @Select("""
            <script>
			SELECT L.Name AS CarbonEmissionLocation, L.NameSC AS CarbonEmissionLocationSC, L.NameEN AS CarbonEmissionLocationEN, CA.RecordYearMonth, 
			CA.MaterialCode, CA.MaterialName, CA.Qty, CA.QtyUnit, SC.sub_category_name SubCategoryName, SC.sub_category_name_sc SubCategoryNameSC, SC.sub_category_name_en SubCategoryNameEN, 
			CA.CarbonFactor, CA.CarbonFactorUnit, CA.TransportCarbonAmount, CA.CarbonAmount, CA.CarbonAmount_Unit, CA.CalculateDate, CA.Status 
			FROM F_CombineAll_Latest CA 
			LEFT JOIN t_protocol_sub_category SC ON SC.sub_category_name = CA.Scope 
			LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Name = CA.CarbonEmissionLocation
			left join t_organization o  on o.name = CA.SiteName and o.is_deleted = 0
            WHERE CA.SiteName = #{siteName} 
            AND CA.Protocol = #{protocol} 
            AND CA.MaterialName NOT IN (N'廢棄物運輸') 
            AND CA.CarbonEmissionLocation LIKE CONCAT('%', #{carbonEmissionLocation},'%') 
            <if test="siteId != null and siteId != ''">
                and o.id = #{siteId}
			</if>
            ORDER BY ${orderSql} 
            OFFSET ${offset} ROWS 
            FETCH NEXT ${next} ROWS ONLY 
            </script>
            """)
    List<TzhBsIntermediateProcessingDetailsVO> getIntermediateProcessingDetailsLatestOrderBy(@Param("calculateDate") LocalDate calculateDate,
                                                                                       @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                                                       @Param("orderSql") String orderSql,
                                                                                       @Param("offset") int offset,
                                                                                       @Param("next") int next,
                                                                                       @Param("siteName") String siteName,
                                                                                       @Param("protocol") String protocol,
                                                                                       @Param("siteId") String siteId);

    @Select("""
            <script>
			SELECT L.Name AS CarbonEmissionLocation, L.NameSC AS CarbonEmissionLocationSC, L.NameEN AS CarbonEmissionLocationEN, CA.RecordYearMonth, 
			CA.MaterialCode, CA.MaterialName, CA.Qty, CA.QtyUnit, SC.sub_category_name SubCategoryName, SC.sub_category_name_sc SubCategoryNameSC, SC.sub_category_name_en SubCategoryNameEN,
			CA.CarbonFactor, CA.CarbonFactorUnit, CA.TransportCarbonAmount, CA.CarbonAmount, CA.CarbonAmount_Unit, CA.CalculateDate, CA.Status 
			FROM F_CombineAll CA 
			LEFT JOIN t_protocol_sub_category SC ON SC.sub_category_name = CA.Scope 
			LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Name = CA.CarbonEmissionLocation
			left join t_organization o  on o.name = CA.SiteName and o.is_deleted = 0
            WHERE CA.CalculateDate = #{calculateDate} 
            AND CA.SiteName = #{siteName} 
            AND CA.Protocol = #{protocol} 
            AND CA.MaterialName NOT IN (N'廢棄物運輸') 
            AND CA.CarbonEmissionLocation LIKE CONCAT('%', #{carbonEmissionLocation},'%') 
            <if test="siteId != null and siteId != ''">
                and o.id = #{siteId}
			</if>
            ORDER BY ${orderSql} 
            OFFSET ${offset} ROWS 
            FETCH NEXT ${next} ROWS ONLY 
            </script>
            """)
    List<TzhBsIntermediateProcessingDetailsVO> getIntermediateProcessingDetailsOrderBy(@Param("calculateDate") LocalDate calculateDate,
                                                                                       @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                                                       @Param("orderSql") String orderSql,
                                                                                       @Param("offset") int offset,
                                                                                       @Param("next") int next,
                                                                                       @Param("siteName") String siteName,
                                                                                       @Param("protocol") String protocol,
                                                                                       @Param("siteId") String siteId);

    @Select("""
            SELECT DISTINCT PurPaymentId FROM F_Material_Detail_Latest 
            WHERE RecordYearMonth=#{recordYearMonth} AND CarbonEmissionLocation=#{carbonEmissionLocation} AND MaterialCode=#{materialCode} 
            AND SiteName=#{siteName} 
            AND PurPaymentId IS NOT NULL 
            """)
    List<String> getMaterialPaymentIds(@Param("recordYearMonth") String recordYearMonth,
                                       @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                       @Param("materialCode") String materialCode,
                                       @Param("siteName") String siteName);

    @Select("""
            SELECT DISTINCT PurPaymentId FROM F_WasterWater_Detail_Latest 
            WHERE RecordYearMonth=#{recordYearMonth} AND CarbonEmissionLocation=#{carbonEmissionLocation} 
            AND SiteName=#{siteName} 
            AND PurPaymentId IS NOT NULL 
            """)
    List<String> getWasterWaterPaymentIds(@Param("recordYearMonth") String recordYearMonth,
                                       @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                          @Param("siteName") String siteName);

    @Select("""
            SELECT DISTINCT PurInvoiceId FROM F_Material_Detail_Latest 
            WHERE RecordYearMonth=#{recordYearMonth} AND CarbonEmissionLocation=#{carbonEmissionLocation} AND MaterialCode=#{materialCode} 
            AND SiteName=#{siteName} 
            AND PurPaymentId IS NULL 
            """)
    List<String> getMaterialInvoiceIdsOfPaymentIdNull(@Param("recordYearMonth") String recordYearMonth,
                                                      @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                      @Param("materialCode") String materialCode,
                                                      @Param("siteName") String siteName);

    @Select("""
            SELECT DISTINCT PurInvoiceId FROM F_WasterWater_Detail_Latest 
            WHERE RecordYearMonth=#{recordYearMonth} AND CarbonEmissionLocation=#{carbonEmissionLocation} 
            AND SiteName=#{siteName} 
            AND PurPaymentId IS NULL 
            """)
    List<String> getWasterWaterInvoiceIdsOfPaymentIdNull(@Param("recordYearMonth") String recordYearMonth,
                                                         @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                         @Param("siteName") String siteName);

    @Select("""
            SELECT DISTINCT PurInvoiceId FROM F_Material_Detail_Latest 
            WHERE PurPaymentId = #{purPaymentId} 
            UNION 
            SELECT DISTINCT PurInvoiceId FROM F_WasterWater_Detail 
            WHERE CalculateDate = (SELECT max(CalculateDate) FROM F_Material_Detail) 
            AND PurPaymentId = #{purPaymentId} 
            """)
    List<String> getCdmsInvoiceIds(@Param("purPaymentId") String purPaymentId);

    @Select("""
            select distinct Id from Tzh_Invoice_File
            where IsDeleted = 0
            and siteName = #{siteName}
            and recordYearMonth = #{recordYearMonth}
            and carbonEmissionLocation = #{carbonEmissionLocation}
            and materialName = #{materialName}
            and (#{materialCode} IS NULL OR materialCode = #{materialCode})
            and (#{materialAttribute} IS NULL OR materialAttribute = #{materialAttribute})
            """)
    List<String> getEsgInvoiceIds(@Param("siteName") String siteName,
                                          @Param("recordYearMonth") String recordYearMonth,
                                          @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                          @Param("materialName") String materialName,
                                          @Param("materialCode") String materialCode,
                                          @Param("materialAttribute") String materialAttribute);
}
