<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.cohl.mapper.Dw830BipvdbZhTotalPOSandNVMapper">
  <resultMap id="BaseResultMap" type="com.csci.cohl.model.Dw830BipvdbZhTotalPOSandNV">
    <!--@mbg.generated-->
    <!--@Table dw_830_bipvdb_Zh_Total_POSandNV-->
    <result column="收集時間" jdbcType="DATE" property="收集時間" />
    <result column="日用電量" jdbcType="FLOAT" property="日用電量" />
    <result column="日上網電量" jdbcType="FLOAT" property="日上網電量" />
    <result column="月度用電量" jdbcType="FLOAT" property="月度用電量" />
    <result column="月度上網電量" jdbcType="FLOAT" property="月度上網電量" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    收集時間, 日用電量, 日上網電量, 月度用電量, 月度上網電量
  </sql>
</mapper>