<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.cohl.mapper.Dw830Bipvdb830esgMonthMapper">
  <resultMap id="BaseResultMap" type="com.csci.cohl.model.Dw830Bipvdb830esgMonth">
    <!--@mbg.generated-->
    <!--@Table dw_830_bipvdb_830ESG_Month-->
    <result column="月發電量" jdbcType="FLOAT" property="monthlyPowerGeneration" />
    <result column="發電收益" jdbcType="FLOAT" property="powerGenerationIncome" />
    <result column="年月" jdbcType="VARCHAR" property="yearMonth" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    月發電量, 發電收益, 年月
  </sql>

  <select id="getTotalPowerGeneFrom202304" resultType="java.math.BigDecimal">
    SELECT SUM(t1.月發電量) totalPowerGen from
      (
        SELECT t.*,
               row_number() over (partition by t.年月 order by t.calculate_time desc) as rn
        from dw_830_bipvdb_830ESG_Month t
        where t.年月 >='2023-04'
      ) t1 where t1.rn = 1
    </select>
</mapper>
