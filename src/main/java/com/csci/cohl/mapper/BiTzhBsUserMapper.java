package com.csci.cohl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.cohl.model.TzhBsUser;
import org.apache.ibatis.annotations.Select;

public interface BiTzhBsUserMapper extends BaseMapper<TzhBsUser> {

    @Select("SELECT COUNT(*) FROM Tzh_Bs_User WHERE UserName = #{userName} AND Password = #{password} AND IsEnabled = 1 ")
    boolean login(String userName, String password);
}
