package com.csci.cohl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.cohl.beans.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TzhBsEmissionReductionStatisticsMapper extends BaseMapper<Object> {
    @Select("""
            SELECT c.category_name CategoryName, c.category_name_sc CategoryNameSC, c.category_name_en CategoryNameEN, ISNULL(t.CarbonReductionAmount,0) AS CarbonReductionAmount FROM
			t_protocol_category c
			LEFT JOIN 
            (
            SELECT C.category_name CategoryName, C.category_name_sc CategoryNameSC, C.category_name_en CategoryNameEN, SUM(ISNULL(CarbonReductionAmount, 0))/1000 AS CarbonReductionAmount
            FROM Tzh_EmissionReductionHead ERH
            LEFT JOIN Tzh_EmissionReduction ER ON ER.headId = ERH.id AND ER.isDeleted = 0 AND ERH.isDeleted = 0
            LEFT JOIN t_protocol_category C ON C.Id = ERH.ProtocolCategoryId
            LEFT JOIN t_protocol PT ON PT.id = C.protocol_id
            LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Id = ERH.CarbonEmissionLocationId
            WHERE ERH.SiteName=#{siteName}
            AND ER.Type = N'每月減排'
            AND PT.name_en = #{protocol}
            AND L.Name LIKE CONCAT('%', #{carbonEmissionLocation},'%') 
            AND ((ER.RecordYearMonth >= #{startMonth} AND ER.RecordYearMonth <= #{endMonth}) OR (ER.RecordYearMonth IS NULL))
            GROUP BY C.category_name, C.category_name_sc, C.category_name_en
            ) AS t ON c.category_name = t.CategoryName
            WHERE c.protocol_id = (SELECT Id FROM t_protocol p WHERE p.name_en = #{protocol})
            ORDER BY c.category_name
            """)
    List<TzhBsAccumulatedReductionPerScopeMainVO> getAccumulatedReductionPerScopeMain(@Param("startMonth") String startMonth,
                                                                                      @Param("endMonth") String endMonth,
                                                                                      @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                                                      @Param("siteName") String siteName,
                                                                                      @Param("protocol") String protocol);

    @Select("""
            SELECT Title AS ScopeDetail, SUM(ISNULL(CarbonReductionAmount, 0))/1000 AS  CarbonReductionAmount
            FROM [Tzh_EmissionReductionHead] h
            LEFT JOIN t_protocol_category C ON C.id = h.ProtocolCategoryId
            LEFT JOIN t_protocol PT ON PT.Id = C.protocol_id
            LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Id = h.CarbonEmissionLocationId
            LEFT JOIN  [Tzh_EmissionReduction] t ON t.headId = h.id AND t.isDeleted <> 1 AND h.isDeleted <> 1 
            WHERE h.SiteName=#{siteName}
            AND t.Type = N'每月減排'
            AND PT.name_en = #{protocol}
            AND L.Name LIKE CONCAT('%', #{carbonEmissionLocation},'%') 
            AND RecordYearMonth >= #{startMonth} AND RecordYearMonth <= #{endMonth}
            AND (Title = #{scopeDetail} OR N'' = #{scopeDetail} OR #{scopeDetail} IS NULL)
            GROUP BY Title
            ORDER BY SUM(ISNULL(CarbonReductionAmount, 0))/1000 DESC
            """)
    List<TzhBsAccumulatedReductionPerScopeDetailVO> getAccumulatedReductionPerScopeDetail(@Param("startMonth") String startMonth,
                                                                                          @Param("endMonth") String endMonth,
                                                                                          @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                                                          @Param("siteName") String siteName,
                                                                                          @Param("protocol") String protocol,
                                                                                          @Param("scopeDetail") String scopeDetail);

    @Select("""
			SELECT ScopeDetail, RefAmount = RefAmount + ActualAmount, ActualAmount
			FROM (
				SELECT h.Title AS ScopeDetail, 
				SUM(ISNULL(t.CarbonReductionAmount, 0)) AS RefAmount,
				ISNULL(t2.CarbonReductionAmount, 0) AS  ActualAmount
				FROM [Tzh_EmissionReductionHead] h
				LEFT JOIN t_protocol_category C ON C.Id = h.ProtocolCategoryId
				LEFT JOIN t_protocol PT ON PT.Id = C.protocol_id
				LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Id = h.CarbonEmissionLocationId
				LEFT JOIN Tzh_EmissionReduction t ON t.headId = h.id AND t.isDeleted <> 1 AND h.isDeleted <> 1 
				LEFT JOIN Tzh_EmissionReduction t2 ON t2.HeadId = t.HeadId AND t2.IsDeleted = 0 AND t2.Type = N'減排總值'
				WHERE t.Type = N'每月減排'
				AND h.SiteName=#{siteName}
				AND PT.name_en = #{protocol}
				AND L.Name LIKE CONCAT('%', #{carbonEmissionLocation},'%') 
				AND t.RecordYearMonth >= #{startMonth} AND t.RecordYearMonth <= #{endMonth}
				AND (h.Title = #{scopeDetail} OR N'' = #{scopeDetail} OR #{scopeDetail} IS NULL)
				GROUP BY h.Title, t2.CarbonReductionAmount
			) t
            """)
    List<TzhBsAccumulatedReductionDetailPerScopeDetailVO> getAccumulatedReductionDetailPerScopeDetail(@Param("startMonth") String startMonth,
                                                                                          @Param("endMonth") String endMonth,
                                                                                          @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                                                          @Param("siteName") String siteName,
                                                                                          @Param("protocol") String protocol,
                                                                                          @Param("scopeDetail") String scopeDetail);

    @Select("""
            SELECT t1.RecordYearMonth, t2.ExpectedCarbonReductionAmount,        
            SUM(t1.CarbonReductionAmount) OVER (ORDER BY t1.RecordYearMonth) AS TotalCarbonReductionAmount        
            FROM (        
            SELECT _t.RecordYearMonth, SUM(ISNULL(_t.CarbonReductionAmount, 0))/1000 AS CarbonReductionAmount        
            FROM [Tzh_EmissionReductionHead] _h        
            LEFT JOIN t_protocol_category _C ON _C.Id = _h.ProtocolCategoryId
            LEFT JOIN t_protocol _PT ON _PT.Id = _C.protocol_id
            LEFT JOIN Tzh_CarbonEmissionLocation _L ON _L.Id = _h.CarbonEmissionLocationId
            LEFT JOIN  [Tzh_EmissionReduction] _t ON _t.headId = _h.id AND _t.isDeleted <> 1 AND _h.isDeleted <> 1        
            WHERE _h.SiteName = #{siteName}        
            AND _t.Type = N'每月減排'
            AND _PT.name_en = #{protocol}        
            AND _L.Name LIKE CONCAT('%', #{carbonEmissionLocation},'%')       
            AND _t.RecordYearMonth >= #{startMonth} AND _t.RecordYearMonth <= #{endMonth}        
            GROUP BY RecordYearMonth        
            ) t1        
            LEFT JOIN (
            SELECT SUM(_t.CarbonReductionAmount) AS ExpectedCarbonReductionAmount        
            FROM Tzh_ExpectedEmissionReduction _t        
            LEFT JOIN t_protocol_category _C ON _C.Id = _t.ProtocolCategoryId
            LEFT JOIN t_protocol _PT ON _PT.Id = _C.protocol_id
            LEFT JOIN Tzh_CarbonEmissionLocation _L ON _L.Id = _t.CarbonEmissionLocationId
            WHERE _t.SiteName=#{siteName}        
            AND _PT.name_en = #{protocol}        
            AND _L.Name LIKE CONCAT('%', #{carbonEmissionLocation},'%')     
            ) t2 ON 1 = 1        
            ORDER BY t1.RecordYearMonth        
            """)
    List<TzhBsReductionAchievementRateVO> getReductionAchievementRate(@Param("startMonth") String startMonth,
                                                                      @Param("endMonth") String endMonth,
                                                                      @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                                      @Param("siteName") String siteName,
                                                                      @Param("protocol") String protocol);

    @Select("""
            SELECT DISTINCT t2.ScopeMain AS ScopeMain, IIF(t1.MonthCount > 0, t2.CarbonAmountTotal/t1.MonthCount, 0) AS CarbonAmount FROM
            (
                SELECT DATEDIFF(DAY, PI.StartDate, PI.EndDate)/30 AS MonthCount
                FROM Tzh_ProjectInfo PI WHERE PI.Name = #{siteName}
            ) t1
            CROSS JOIN (
            SELECT _t2.ScopeMain, ISNULL(SUM(ISNULL(_t2.CarbonAmount, 0)),1)/1000 AS CarbonAmountTotal
            FROM dbo.F_Result_Latest _t2
            WHERE _t2.CalculateDate = (SELECT MAX(CalculateDate) FROM dbo.F_Result_Latest)
            AND _t2.SiteName = #{siteName}
            AND _t2.Protocol = #{protocol}
            AND _t2.CarbonEmissionLocation LIKE CONCAT('%', #{carbonEmissionLocation},'%')      
            GROUP BY _t2.ScopeMain
            ) t2
            ORDER BY t2.ScopeMain
            """)
    List<TzhBsMonthlyEmissionPredictionByAverageVO> getMonthlyEmissionPredictionByAverage(@Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                                                          @Param("siteName") String siteName,
                                                                                          @Param("protocol") String protocol);

    @Select("""
            SELECT DISTINCT SC.sub_category_name AS ScopeMain        
            ,SUM(ISNULL(t1.CarbonAmount, 0)) AS CarbonAmount        
            FROM Tzh_ExpectedEmissionIso t1        
            LEFT JOIN t_protocol_sub_category SC ON SC.Id = t1.ProtocolSubCategoryId
            LEFT JOIN t_protocol_category C ON C.Id = SC.category_id
            LEFT JOIN t_protocol PT ON PT.Id = C.protocol_id
            LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Id = t1.CarbonEmissionLocationId
            WHERE t1.SiteName = #{siteName}        
            AND PT.name_en = #{protocol}
            AND L.Name LIKE CONCAT('%', #{carbonEmissionLocation},'%')        
            AND SC.sub_category_name IN (N'鋼筋', N'混凝土')        
            AND t1.IsDeleted = 0        
            GROUP BY SC.sub_category_name        
            ORDER BY SC.sub_category_name        
            """)
    List<TzhBsMonthlyEmissionPredictionByAverageVO> getTotalEmissionPrediction(@Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                                               @Param("siteName") String siteName,
                                                                               @Param("protocol") String protocol);

    @Select("""
            SELECT c.category_name CategoryName, c.category_name_sc CategoryNameSC, c.category_name_en CategoryNameEN, ISNULL(t.CarbonAmount,0) AS CarbonAmount FROM
			t_protocol_category c
			LEFT JOIN 
            (
            SELECT C.category_name CategoryName, C.category_name_sc CategoryNameSC, C.category_name_en CategoryNameEN, SUM(ISNULL(EEI.CarbonAmount,0)) AS CarbonAmount
            FROM Tzh_ExpectedEmissionIso EEI
            LEFT JOIN t_protocol_sub_category SC ON EEI.ProtocolSubCategoryId = SC.Id 
            LEFT JOIN t_protocol_category C ON SC.category_id = C.Id
            LEFT JOIN t_protocol PT ON PT.Id = C.protocol_id
            LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Id = EEI.CarbonEmissionLocationId
            WHERE EEI.IsDeleted = 0
            AND EEI.SiteName = #{siteName}
            AND PT.name_en = #{protocol}
            AND L.Name LIKE CONCAT('%', #{carbonEmissionLocation},'%')   
            GROUP BY C.category_name, C.category_name_sc, C.category_name_en
            ) AS t ON c.category_name = t.CategoryName
            WHERE c.protocol_id = (SELECT Id FROM t_protocol p WHERE p.name_en = #{protocol})
            ORDER BY c.category_name
            """)
    List<TzhBsCarbonEmissionPredictionVO> getCarbonEmissionPrediction(@Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                                      @Param("siteName") String siteName,
                                                                      @Param("protocol") String protocol);

    @Select("""
            SELECT C.category_name CategoryName, C.category_name_sc CategoryNameSC, C.category_name_en CategoryNameEN, SC.sub_category_name SubCategoryName, SC.sub_category_name_sc SubCategoryNameSC, SC.sub_category_name_en SubCategoryNameEN, CEL.Name AS CarbonEmissionLocation
            ,t1.CarbonAmount, t1.CarbonAmountUnderMeasure, t1.CarbonUnit
            ,t2.CarbonAmountTotal, IIF(t2.CarbonAmountTotal > 0, t1.CarbonAmount/t2.CarbonAmountTotal*100, 0) AS CarbonAmountProportion
            ,t3.CarbonAmountUnderMeasureTotal, IIF(t3.CarbonAmountUnderMeasureTotal > 0, t1.CarbonAmountUnderMeasure/t3.CarbonAmountUnderMeasureTotal*100, 0) AS CarbonAmountUnderMeasureProportion
            FROM Tzh_ExpectedEmissionIso t1
			LEFT JOIN Tzh_CarbonEmissionLocation CEL ON CEL.Id = t1.CarbonEmissionLocationId
            LEFT JOIN
            (
            SELECT ISNULL(SUM(ISNULL(_t2.CarbonAmount, 0)), 1) AS CarbonAmountTotal
            FROM dbo.Tzh_ExpectedEmissionIso _t2
            LEFT JOIN t_protocol_sub_category SC ON _t2.ProtocolSubCategoryId = SC.Id         
            LEFT JOIN t_protocol_category C ON SC.category_id = C.Id        
            LEFT JOIN t_protocol PT ON PT.Id = C.protocol_id
            LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Id = _t2.CarbonEmissionLocationId        
            WHERE _t2.SiteName = #{siteName}
            AND PT.name_en = #{protocol}
            AND L.Name LIKE CONCAT('%', #{carbonEmissionLocation},'%')      
            AND _t2.IsDeleted = 0
            ) t2 ON 1 = 1
            LEFT JOIN
            (
            SELECT ISNULL(SUM(ISNULL(_t3.CarbonAmountUnderMeasure, 0)), 1) AS CarbonAmountUnderMeasureTotal
            FROM dbo.Tzh_ExpectedEmissionIso _t3
            LEFT JOIN t_protocol_sub_category SC ON _t3.ProtocolSubCategoryId = SC.Id         
            LEFT JOIN t_protocol_category C ON SC.category_id = C.Id        
            LEFT JOIN t_protocol PT ON PT.Id = C.protocol_id
            LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Id = _t3.CarbonEmissionLocationId        
            WHERE _t3.SiteName = #{siteName}
            AND PT.name_en = #{protocol}
            AND L.Name LIKE CONCAT('%', #{carbonEmissionLocation},'%')    
            AND _t3.IsDeleted = 0
            ) t3 ON 1 = 1
            LEFT JOIN Tzh_ProjectInfo PI ON PI.Name = t1.SiteName AND PI.IsDeleted = 0
            LEFT JOIN t_protocol_sub_category SC ON t1.ProtocolSubCategoryId = SC.Id
            LEFT JOIN t_protocol_category C ON SC.category_id = C.Id
            LEFT JOIN t_protocol PT ON PT.Id = C.protocol_id
            LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Id = t1.CarbonEmissionLocationId   
            WHERE t1.SiteName = #{siteName}
            AND PT.name_en = #{protocol}
            AND L.Name LIKE CONCAT('%', #{carbonEmissionLocation},'%')   
            AND t1.IsDeleted = 0
            ORDER BY L.Name, C.category_name, t1.CarbonAmount DESC
            """)
    List<TzhBsCarbonEmissionPredictionIsoVO> getCarbonEmissionPredictionIso(@Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                                            @Param("siteName") String siteName,
                                                                            @Param("protocol") String protocol);
}
