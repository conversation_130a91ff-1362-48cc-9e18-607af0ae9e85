package com.csci.cohl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.cohl.beans.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TzhBsCarbonEmissionStatisticsMapper extends BaseMapper<Object> {
    @Select("""
            SELECT c.category_name CategoryName, c.category_name_sc CategoryNameSC, c.category_name_en CategoryNameEN, ISNULL(t.CarbonAmount,0) AS CarbonAmount FROM
			t_protocol_category c
			LEFT JOIN 
            (
            SELECT C.category_name, C.category_name_sc, C.category_name_en, SUM(ISNULL(t1.CarbonAmount, 0))/1000 AS CarbonAmount 
            FROM dbo.F_result t1        
            LEFT JOIN Tzh_ProjectInfo PI ON PI.Name = t1.SiteName AND PI.IsDeleted = 0
            LEFT JOIN t_protocol_category C ON C.category_name = LTRIM(RTRIM(t1.ScopeMain))        
            WHERE t1.SiteName=#{siteName}
            AND t1.Protocol=#{protocol}
            AND t1.CarbonEmissionLocation LIKE CONCAT('%', #{carbonEmissionLocation},'%')
            AND t1.CalculateDate = (SELECT MAX(CalculateDate) from dbo.F_result)
            AND t1.RecordYearMonth >= #{startMonth} AND t1.RecordYearMonth <= #{endMonth}
            GROUP BY C.category_name, C.category_name_sc, C.category_name_en
            ) AS t ON c.category_name = t.category_name
            WHERE c.protocol_id = (SELECT Id FROM t_protocol p WHERE p.name_en = #{protocol})
            ORDER BY c.category_name
            """)
    List<TzhBsCarbonEmissionSummaryVO> getCarbonEmissionSummary(@Param("startMonth") String startMonth,
                                                                @Param("endMonth") String endMonth,
                                                                @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                                @Param("siteName") String siteName,
                                                                @Param("protocol") String protocol);

    @Select("""
            SELECT * FROM
            (
            SELECT t1.RecordYearMonth, C.category_name CategoryName, C.category_name_sc CategoryNameSC, C.category_name_en CategoryNameEN, SUM(ISNULL(t1.CarbonAmount, 0))/1000 AS CarbonAmount        
            FROM F_result t1
            LEFT JOIN t_protocol_category C ON C.category_name = t1.ScopeMain
            WHERE t1.SiteName = #{siteName}
            AND t1.Protocol = #{protocol}
            AND t1.CarbonEmissionLocation LIKE CONCAT('%', #{carbonEmissionLocation},'%')
            AND t1.CalculateDate = (SELECT MAX(CalculateDate) from dbo.F_result)
            AND t1.RecordYearMonth >= #{startMonth} AND t1.RecordYearMonth <= #{endMonth}
            GROUP BY C.category_name, C.category_name_sc, C.category_name_en, t1.RecordYearMonth
            ) t ORDER BY RecordYearMonth, CategoryName
            """)
    List<TzhBsMonthlyEmissionVO> getMonthlyEmission(@Param("startMonth") String startMonth,
                                                    @Param("endMonth") String endMonth,
                                                    @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                    @Param("siteName") String siteName,
                                                    @Param("protocol") String protocol);

    @Select("""
            SELECT t1.CarbonEmissionLocation, t1.CarbonEmissionLocationSC, t1.CarbonEmissionLocationEN, t1.CategoryName, t1.CategoryNameSC, t1.CategoryNameEN, t1.SubCategoryName, t1.SubCategoryNameSC, t1.SubCategoryNameEN, t1.CarbonAmount
            , t2.CarbonAmountTotal, IIF(t2.CarbonAmountTotal*100 > 0, t1.CarbonAmount/t2.CarbonAmountTotal*100, 0) AS CarbonAmountProportion FROM
            (
            SELECT CEL.Name AS CarbonEmissionLocation, CEL.NameSC AS CarbonEmissionLocationSC, CEL.NameEN AS CarbonEmissionLocationEN, C.category_name CategoryName, C.category_name_sc CategoryNameSC, C.category_name_en CategoryNameEN, SC.sub_category_name SubCategoryName, SC.sub_category_name_sc SubCategoryNameSC, SC.sub_category_name_en SubCategoryNameEN,
            SUM(ISNULL(_t1.CarbonAmount, 0))/1000 AS CarbonAmount
            FROM F_result _t1
            INNER JOIN Tzh_ProjectInfo PI ON _t1.SiteName = PI.Name AND PI.IsDeleted = 0
            INNER JOIN t_protocol_sub_category SC ON _t1.ScopeDetail = SC.sub_category_name
            INNER JOIN t_protocol_category C ON SC.category_id = C.Id
            INNER JOIN t_protocol P ON C.protocol_id = P.Id AND P.name_en = _t1.Protocol
            INNER JOIN Tzh_CarbonEmissionLocation CEL ON CEL.Name = _t1.CarbonEmissionLocation
            WHERE _t1.CalculateDate = (SELECT MAX(CalculateDate) from dbo.F_result)
            AND _t1.SiteName=#{siteName}
            AND _t1.Protocol=#{protocol}
            AND _t1.CarbonEmissionLocation LIKE CONCAT('%', #{carbonEmissionLocation},'%')
            AND _t1.RecordYearMonth >= #{startMonth} AND _t1.RecordYearMonth <= #{endMonth}
            GROUP BY C.category_name, C.category_name_sc, C.category_name_en, SC.sub_category_name, SC.sub_category_name_sc, SC.sub_category_name_en, CEL.Name, CEL.NameSC, CEL.NameEN 
            ) t1
            LEFT JOIN (
            SELECT ISNULL(SUM(ISNULL(_t2.CarbonAmount, 0)),1)/1000 AS CarbonAmountTotal
            FROM F_result _t2
            WHERE _t2.CalculateDate = (SELECT MAX(CalculateDate) from dbo.F_result)
            AND _t2.SiteName=#{siteName}
            AND _t2.Protocol=#{protocol}
            AND _t2.CarbonEmissionLocation LIKE CONCAT('%', #{carbonEmissionLocation},'%')
            AND _t2.RecordYearMonth >= #{startMonth} AND _t2.RecordYearMonth <= #{endMonth}
            ) t2 ON 1 = 1
            ORDER BY t1.CarbonEmissionLocation, t1.CategoryName, t1.SubCategoryName DESC
            """)
    List<TzhBsEmissionDetailsVO> getEmissionDetails(@Param("startMonth") String startMonth,
                                                    @Param("endMonth") String endMonth,
                                                    @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                    @Param("siteName") String siteName,
                                                    @Param("protocol") String protocol);


    @Select("""
            SELECT TOP 6 SC.sub_category_name SubCategoryName, SC.sub_category_name_sc SubCategoryNameSC, SC.sub_category_name_en SubCategoryNameEN, SUM(ISNULL(t1.CarbonAmount, 0))/1000 AS CarbonAmount
            FROM F_result t1
            INNER JOIN Tzh_ProjectInfo PI ON t1.SiteName = PI.Name AND PI.IsDeleted = 0
            INNER JOIN t_protocol_sub_category SC ON t1.ScopeDetail = SC.sub_category_name
            INNER JOIN t_protocol_category C ON SC.category_id = C.Id
            INNER JOIN t_protocol P ON C.protocol_id = P.Id AND P.name_en = t1.Protocol
            WHERE t1.CalculateDate = (SELECT MAX(CalculateDate) from dbo.F_result)
            AND t1.SiteName=#{siteName}
            AND t1.Protocol=#{protocol}
            AND t1.CarbonEmissionLocation LIKE CONCAT('%', #{carbonEmissionLocation},'%')
            AND t1.RecordYearMonth >= #{startMonth} AND t1.RecordYearMonth <= #{endMonth}
            GROUP BY SC.sub_category_name, SC.sub_category_name_sc, SC.sub_category_name_en
            HAVING TRY_CONVERT(DECIMAL(10,2),SUM(ISNULL(t1.CarbonAmount, 0)/1000)) > 0
            ORDER BY SUM(ISNULL(t1.CarbonAmount, 0))/1000 DESC
            """)
    List<TzhBsEmissionNotZeroDetailsVO> getEmissionNotZeroDetails(@Param("startMonth") String startMonth,
                                                                  @Param("endMonth") String endMonth,
                                                                  @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                                  @Param("siteName") String siteName,
                                                                  @Param("protocol") String protocol);

    @Select("""
            SELECT TOP 6 t1.ScopeDetail, SUM(ISNULL(t1.CarbonAmount, 0))/1000 AS CarbonAmount 
            FROM dbo.F_result t1 
            WHERE t1.CalculateDate = (SELECT MAX(CalculateDate) from dbo.F_result) 
            AND t1.SiteName=#{siteName} 
            AND t1.Protocol=#{protocol}
            AND t1.ScopeMain IN (N'木設', N'範圍三（B）', N'類別四') 
            AND t1.CarbonEmissionLocation LIKE CONCAT('%', #{carbonEmissionLocation},'%') 
            AND t1.RecordYearMonth >= #{startMonth} AND t1.RecordYearMonth <= #{endMonth} 
            GROUP BY t1.ScopeDetail 
            ORDER BY SUM(ISNULL(t1.CarbonAmount, 0))/1000 DESC 
            """)
    List<TzhBsEmission3bDetailsVO> getEmission3bDetails(@Param("startMonth") String startMonth,
                                                        @Param("endMonth") String endMonth,
                                                        @Param("carbonEmissionLocation") String carbonEmissionLocation,
                                                        @Param("siteName") String siteName,
                                                        @Param("protocol") String protocol);

    @Select("""
            SELECT TOP 1 MD5Code 
            FROM dbo.BlockChain_SetValueByKey_Log 
            WHERE FileName LIKE 'dbo_F_Result_%' 
            AND RequestStatusCode = 200 AND RequestText = 'true' 
            AND TRY_CONVERT(date, Logdatetime) = (SELECT MAX(CalculateDate) from dbo.F_result) 
            ORDER BY Logdatetime DESC 
            """)
    TzhBsBlockchainSignatureVO getBlockchainSignature();

    @Select("""
                            SELECT
                left(t1.RecordYearMonth, 4) AS Year,
                CEILING(CAST(right(t1.RecordYearMonth,2) AS INT) / 3.0) AS Quarter,
                C.category_name AS CategoryName,
                C.category_name_sc AS CategoryNameSC,
                C.category_name_en AS CategoryNameEN,
                SUM(ISNULL(t1.CarbonAmount, 0)) / 1000 AS CarbonAmount
            FROM
                F_result t1
            LEFT JOIN
                t_protocol_category C ON C.category_name = t1.ScopeMain
            WHERE
                t1.SiteName = #{siteName}
                AND t1.Protocol = #{protocol}
                AND t1.CarbonEmissionLocation LIKE CONCAT('%', #{carbonEmissionLocation}, '%')
                AND t1.CalculateDate = (SELECT MAX(CalculateDate) FROM dbo.F_result)
                AND t1.RecordYearMonth >= #{startMonth}
                AND t1.RecordYearMonth <= #{endMonth}
            GROUP BY
                left(t1.RecordYearMonth, 4),
                CEILING(CAST(right(t1.RecordYearMonth, 2) AS INT) / 3.0),
                C.category_name,
                C.category_name_sc,
                C.category_name_en
            ORDER BY
                Year,
                Quarter,
                CategoryName;
            """)
    List<TzhBsYearlyEmissionVO> getYearlyEmissionByQuarter(String startMonth, String endMonth, String carbonEmissionLocation, String siteName, String protocol);
}
