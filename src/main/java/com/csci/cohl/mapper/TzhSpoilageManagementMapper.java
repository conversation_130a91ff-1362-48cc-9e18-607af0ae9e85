package com.csci.cohl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.cohl.beans.vo.TzhSpoilageManagementVO;
import com.csci.cohl.model.TzhSpoilageManagement;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface TzhSpoilageManagementMapper extends BaseMapper<TzhSpoilageManagement> {

    @Select("""
   			<script>
			SELECT TOP 5 ECW.Id, SC.SubCategoryName, SC.SubCategoryNameSC, SC.SubCategoryNameEN, TargetAttritionRate = ECW.TargetAttritionRate*100, 
			ActualAttritionRate = ECW.WasteQty/ISNULL(ECW.Qty, 1)*100
			FROM Tzh_EstimateConstructionWaste ECW
			LEFT JOIN t_protocol P ON ECW.ProtocolId = P.id
			left join t_organization o  on o.name = ECW.SiteName and o.is_deleted = 0
			LEFT JOIN Tzh_EstimateConstructionWaste_SubCategory SC ON ECW.SubCategoryId = SC.Id
			LEFT JOIN Tzh_EstimateConstructionWaste_Category C ON SC.CategoryId = C.Id AND C.ProtocolId = ECW.ProtocolId
			WHERE  ECW.DisposalMethodId != (SELECT Id FROM Tzh_EstimateConstructionWaste_DisposalMethod WHERE Name = N'回收')
			AND IsDeleted = 0
            AND ECW.SiteName = #{siteName}
            AND P.name_en = #{protocol}
            <if test="siteId != null and siteId != ''">
                and o.id = #{siteId}
			</if>
			ORDER BY ECW.Qty DESC
			</script>
            """)
    List<TzhSpoilageManagementVO> list(@Param("siteName") String siteName, @Param("protocol") String protocol, @Param("siteId") String siteId);

}
