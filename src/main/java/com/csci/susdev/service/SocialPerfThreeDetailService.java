package com.csci.susdev.service;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.SocialPerfThreeDetailMapper;
import com.csci.susdev.model.SocialPerfThreeDetail;
import com.csci.susdev.model.SocialPerfThreeDetailExample;
import com.csci.susdev.modelcovt.SocialPerfThreeDetailConverter;
import com.csci.susdev.modelcovt.SocialPerfThreeTableDataConverter;
import com.csci.susdev.vo.SocialPerfThreeDetailVO;
import com.csci.susdev.vo.SocialPerfThreeTableDataVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class SocialPerfThreeDetailService {

    @Resource
    private SocialPerfThreeDetailMapper socialPerfThreeDetailMapper;

    /**
     * 查询指定社会绩效三主表下的明细数据
     *
     * @param headId 社会绩效三主表ID
     * @return
     */
    public List<SocialPerfThreeDetailVO> listSocialPerfThreeDetail(String headId) {
        checkExist(headId, "社会绩效三主表ID不能为空");

        List<SocialPerfThreeDetail> lstDetails = selectSocialPerfThreeDetailList(headId);
        return lstDetails.stream().map(SocialPerfThreeDetailConverter::convert).collect(Collectors.toList());
    }

    /**
     * 查询指定社会绩效三主表下的明细数据
     *
     * @param headId 社会绩效三主表ID
     * @return
     */
    public List<SocialPerfThreeTableDataVO> listSocialPerfThreeTableData(String headId) {
        checkExist(headId, "社会绩效三主表ID不能为空");
        List<SocialPerfThreeDetail> lstDetails = selectSocialPerfThreeDetailList(headId);
        return lstDetails.stream().map(SocialPerfThreeTableDataConverter::convert).collect(Collectors.toList());
    }

    private List<SocialPerfThreeDetail> selectSocialPerfThreeDetailList(String headId) {
        SocialPerfThreeDetailExample example = new SocialPerfThreeDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq");

        return socialPerfThreeDetailMapper.selectByExample(example);
    }
}
