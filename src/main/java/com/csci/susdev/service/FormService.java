package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.FormMapper;
import com.csci.susdev.model.Form;
import com.csci.susdev.model.FormExample;
import com.csci.susdev.model.FormExample.Criteria;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.FormPageableQO;
import com.csci.susdev.qo.FormQO;
import com.csci.susdev.qo.KeywordQO;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class FormService {
    @Autowired
    private FormMapper mapper;

    public List<Form> selectByExample(FormExample example) {
        return mapper.selectByExample(example);
    }
    
    /**
     * 表格 数据列表
     *
     * @param qo
     * @return
     */
    public ResultPage<Form> listForm(FormQO qo) {
    	ResultPage<Form> resultPage;
    	
    	FormExample example = new FormExample();
        FormExample.Criteria criteria1 = example.or();
        FormExample.Criteria criteria2 = example.or();

    	if(StringUtils.isNotBlank(qo.getKeyword())) {
            criteria1.andCodeLike("%" + qo.getKeyword() + "%");
            criteria2.andNameLike("%" + qo.getKeyword() + "%");
    	}

        if(ObjectUtils.isNotEmpty(qo.getIsFactorRelated())) {
            criteria1.andIsFactorRelatedEqualTo(qo.getIsFactorRelated());
            criteria2.andIsFactorRelatedEqualTo(qo.getIsFactorRelated());
        }
    	
        example.setOrderByClause("code");

        PageHelper.startPage(qo.getCurPage(), qo.getPageSize());
        List<Form> lstForm = mapper.selectByExample(example);
	    resultPage = new ResultPage<>(lstForm, true);
	    
        return resultPage;
    }

    /**
     * 保存 表格 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param formLst
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> saveFormList(List<Form> formLst) {
    	List<String> idLst = new ArrayList<>();
    	for(Form form : formLst) {
    		this.saveForm(form);
            idLst.add(form.getId());
    	}
        return idLst;
    }

    /**
     * 保存 表格 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param form
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveForm(Form form) {
    	UserInfo currentUser = ContextUtils.getCurrentUser();
    	LocalDateTime now = LocalDateTime.now();
        if (StringUtils.isBlank(form.getId())) {
        	form.setCreationTime(now);
        	form.setCreateUsername(currentUser.getUsername());
        	form.setLastUpdateTime(now);
        	form.setLastUpdateUsername(currentUser.getUsername());
        	mapper.insertSelective(form);
        } else {
        	form.setLastUpdateTime(now);
        	form.setLastUpdateUsername(currentUser.getUsername());
        	mapper.updateByPrimaryKeySelective(form);
        }
        return form.getId();
    }

    /**
     * 刪除 表格 信息
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteForm(String id) {
        return mapper.deleteByPrimaryKey(id);
    }

    /**
     * 根据表单code获取表单信息
     *
     * @param code 表单code
     * @return
     */
    public Form findFormByCode(String code) {
        FormExample example = new FormExample();
        example.or().andCodeEqualTo(code);
        return mapper.selectByExample(example).stream().findFirst().orElse(null);
    }
}
