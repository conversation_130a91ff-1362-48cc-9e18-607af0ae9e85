package com.csci.susdev.service;

import cn.hutool.core.util.StrUtil;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.constant.WorkflowControlState;
import com.csci.susdev.mapper.CeIdentificationDetailMapper;
import com.csci.susdev.mapper.CeIdentificationHeadMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.*;
import com.csci.susdev.modelcovt.CeIdentificationTableDataConverter;
import com.csci.susdev.qo.CeIdentificationQO;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
@Slf4j
public class CeIdentificationService {
    /**
     * 初始化记录的过期时间
     */
    @Value("${lock.init.head.expire.milis:1000}")
    private long lockInitHeadExpireMilis;
    /**
     * 查询head记录时，获取锁失败循环等待的次数
     */
    @Value("${query.head.wait.loop.times:5}")
    private int lockQueryHeadWaitLoopTimes;
    /**
     * 查询head记录时，获取锁失败循环时每次休眠的时间
     */
    @Value("${query.head.wait.sleep.milis:50}")
    private int lockQueryHeadWaitSleepMilis;
    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(CeIdentificationService.class);

    @Resource
    private CeIdentificationHeadMapper ceIdentificationHeadMapper;

    @Resource
    private CeIdentificationDetailMapper ceIdentificationDetailMapper;

    /**
     * 根据查询条件查询碳排識別信息
     * 只会查询 active = true 的记录
     *
     * @param organizationId 组织 id
     * @param year           年份
     * @param month          月份
     * @return
     */
    CeIdentificationHead getCeIdentificationHeadBy(String organizationId, Integer year, Integer month) {
        CeIdentificationHeadExample example = new CeIdentificationHeadExample();
        example.or().andOrganizationIdEqualTo(organizationId).andYearEqualTo(year)
                .andMonthEqualTo(month).andIsActiveEqualTo(Boolean.TRUE);
        return ceIdentificationHeadMapper.selectByExample(example).stream().findFirst().orElse(null);
    }

    /**
     * @param qo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CeIdentificationHead createCeIdentificationHead(CeIdentificationQO qo) {
        CeIdentificationHead ceIdentificationHead = getCeIdentificationHeadBy(qo.getOrganizationId(), qo.getYear(),
                qo.getMonth());
        if (ceIdentificationHead == null) {
            ceIdentificationHead = new CeIdentificationHead();
            ceIdentificationHead.setOrganizationId(qo.getOrganizationId());
            ceIdentificationHead.setYear(qo.getYear());
            ceIdentificationHead.setMonth(qo.getMonth());
            ceIdentificationHead.setIsActive(Boolean.TRUE);
            ceIdentificationHead.setLastUpdateVersion(0);
            ceIdentificationHeadMapper.insertSelective(ceIdentificationHead);
        }
        return ceIdentificationHead;
    }

    /**
     * 根据查询条件查询碳排識別信息，如果不存在则创建
     *
     * @param ceIdentificationQO 查询条件
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CeIdentificationHeadVO getOrInit(CeIdentificationQO ceIdentificationQO) {
        checkExist(ceIdentificationQO.getOrganizationId(), "组织编号不能为空");
        checkExist(ceIdentificationQO.getYear(), "年份不能为空");
        if(ObjectUtils.isEmpty(ceIdentificationQO.getMonth())) {
            ceIdentificationQO.setMonth(12);
        }

        if (ceIdentificationQO.getYear() < 1900 || ceIdentificationQO.getYear() > 2100) {
            throw new ServiceException("年份必须在 1900-2100 之间");
        }
        //查询或初始化记录，如果获取失败，直接抛出异常退出
        CeIdentificationHead ceIdentificationHead = getOrInitHeadRecord(ceIdentificationQO);

        // 查询流程审批状态
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(ceIdentificationHead.getId());
        CeIdentificationHeadVO ceIdentificationHeadVO = CeIdentificationHeadConverter.convert(ceIdentificationHead);
        if (Objects.nonNull(workflowControl)) {
            ceIdentificationHeadVO.setWorkflowControlState(workflowControl.getState());
            ceIdentificationHeadVO.setWorkflowControlStateName(Optional.ofNullable(WorkflowControlState.getWorkflowControlState(workflowControl.getState())).map(WorkflowControlState::getName).orElse(null));
        }
        return ceIdentificationHeadVO;
    }

    /**
     * 查询或初始化记录，如果获取失败，直接抛出异常退出
     * @param ceIdentificationQO
     * @return
     */
    private CeIdentificationHead getOrInitHeadRecord(CeIdentificationQO ceIdentificationQO) {
        CeIdentificationHead ceIdentificationHead = getCeIdentificationHeadBy(ceIdentificationQO.getOrganizationId(),
                ceIdentificationQO.getYear(), ceIdentificationQO.getMonth());
        if (ObjectUtils.isEmpty(ceIdentificationHead)) {
            //加锁
            String key = StrUtil.format(SusDevConsts.DISTRIBUTE_LOCK_KEY_FORMAT, SusDevConsts.PROJECT_PREFIX,
                    "CeIdentificationHead:getOrInit", ceIdentificationQO.getOrganizationId(),
                    ceIdentificationQO.getYear(), ceIdentificationQO.getMonth());
            //超时时间1s
            boolean redisLock = RedisLockUtil.lock(key, lockInitHeadExpireMilis);
            if (!redisLock) {
                //获取锁失败，停顿50ms一次，停顿5次
                int count = lockQueryHeadWaitLoopTimes;
                while(count-- > 0){
                    ceIdentificationHead = getCeIdentificationHeadBy(ceIdentificationQO.getOrganizationId(),
                            ceIdentificationQO.getYear(), ceIdentificationQO.getMonth());
                    if(ceIdentificationHead != null){
                        break;
                    }
                    try {
                        Thread.sleep(lockQueryHeadWaitSleepMilis);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }else{
                try{
                    long startMillis = System.currentTimeMillis();
                    ceIdentificationHead = createCeIdentificationHead(ceIdentificationQO);
                    // 初始化明细数据,如果存在上个月的数据，则复制上个月的数据
                    CeIdentificationHead prevHead = getPrevHead(ceIdentificationQO.getOrganizationId(), ceIdentificationQO.getYear(),
                            ceIdentificationQO.getMonth());
                    if (ObjectUtils.isNotEmpty(prevHead)) {
                        initDetailsWithPrevData(prevHead.getId(), ceIdentificationHead.getId());     // 初始化明细数据
                    }
                    log.debug("初始化记录耗时:{}ms", (System.currentTimeMillis() - startMillis));
                }finally {
                    RedisLockUtil.unlock(key);
                }
            }
        }
        //如果最终没拿到数据，直接返回
        if(ceIdentificationHead == null){
            throw new ServiceException("生成记录失败, 请稍后再尝试！");
        }
        return ceIdentificationHead;
    }

    public CeIdentificationHead getPrevHead(String organizationId, Integer year, Integer month) {
        CeIdentificationHeadExample example = new CeIdentificationHeadExample();
        example.or().andIsActiveEqualTo(true).andOrganizationIdEqualTo(organizationId)
                .andYearEqualTo(year).andMonthLessThan(month);
        example.setOrderByClause("month desc");
        List<CeIdentificationHead> lst = selectByExample(example);
        if(lst.size() > 0) {
            return lst.get(0);
        } else {
            return null;
        }
    }

    void initDetailsWithPrevData(String prevHeadId, String curHeadId) {
        CeIdentificationDetailExample example = new CeIdentificationDetailExample();
        example.or().andHeadIdEqualTo(prevHeadId);
        List<CeIdentificationDetail> details = ceIdentificationDetailMapper.selectByExample(example);
        for(CeIdentificationDetail detail : details) {
            detail.setId(null);
            detail.setHeadId(curHeadId);
            ceIdentificationDetailMapper.insert(detail);
        }
    }


    /**
     * 根据主表id查询碳排識別明细信息
     *
     * @param headId 碳排識別头 id
     * @return
     */
    public List<CeIdentificationDetailVO> listCeIdentificationDetail(String headId) {
        checkExist(headId, "碳排識別主表id不能为空");
        CeIdentificationDetailExample example = new CeIdentificationDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq");
        return ceIdentificationDetailMapper.selectByExample(example).stream().map(new CeIdentificationDetailConverter()::convert).collect(Collectors.toList());
    }

    /**
     * 查询用于前端页面handsontable使用的数据
     *
     * @param headId 碳排識別头 id
     * @return
     */
    public List<CeIdentificationTableDataVO> listCeIdentificationTable(String headId) {
        checkExist(headId, "碳排識別主表id不能为空");
        CeIdentificationDetailExample example = new CeIdentificationDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq");
        return ceIdentificationDetailMapper.selectByExample(example).stream().map(CeIdentificationTableDataConverter::convert).collect(Collectors.toList());
    }


    /**
     * proxy for mapper method
     *
     * @param record
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertDetailSelective(CeIdentificationDetail record) {
        return ceIdentificationDetailMapper.insertSelective(record);
    }

    /**
     * 保存碳排識別明细信息
     *
     * @param ceIdentificationHeadVO 碳排識別头信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCeIdentificationWithDetail(CeIdentificationHeadVO ceIdentificationHeadVO) {
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        if (workflowControlService.isNotAllowEdit(ceIdentificationHeadVO.getId())) {
            throw new ServiceException("已经发起审批流程的单据不允许进行修改");
        }
        // 更新主表信息
        updateHead(ceIdentificationHeadVO);
        // 删除明细
        deleteDetailByHeadId(ceIdentificationHeadVO.getId());
        List<CeIdentificationDetail> lstDetails = ceIdentificationHeadVO.getDetails().stream().map(CeIdentificationTableDataConverter::convert).collect(Collectors.toList());

        addDetails(ceIdentificationHeadVO.getId(), lstDetails);
    }

    /**
     * 提交碳排識別
     *
     * @param idVersionVO
     */
    public void submit(IdVersionVO idVersionVO) {
        // 验证逻辑---------
        checkExist(idVersionVO.getId(), "碳排識別主表id不能为空");
        checkExist(idVersionVO.getLastUpdateVersion(), "碳排識別主表版本号不能为空");

        CeIdentificationHead existedRecord = ceIdentificationHeadMapper.selectByPrimaryKey(idVersionVO.getId());
        Optional.ofNullable(existedRecord).orElseThrow(() -> new ServiceException("指定的碳排識別记录不存在"));
        // 验证逻辑---------

        CeIdentificationHeadExample example = new CeIdentificationHeadExample();
        example.or().andIdEqualTo(idVersionVO.getId()).andLastUpdateVersionEqualTo(idVersionVO.getLastUpdateVersion());

        CeIdentificationHead record = new CeIdentificationHead();
        //record.setApproveStatus(ApproveStatus.SUBMIT.getCode());
        record.setLastUpdateVersion(idVersionVO.getLastUpdateVersion());

        int count = ceIdentificationHeadMapper.updateByExampleSelective(record, example);
        if (count == 0) {
            throw new ServiceException("碳排識別已经被修改，请刷新后重试");
        }
        // todo 增加流程审批记录
    }

    private void addDetails(String headId, List<CeIdentificationDetail> ceIdentificationDetails) {
        if (CollectionUtils.isEmpty(ceIdentificationDetails)) {
            throw new ServiceException("碳排識別明细不能为空");
        }
        int seq = 0;
        CeIdentificationDetail lastDetail = ceIdentificationDetails.get(0);
        for (CeIdentificationDetail ceIdentificationDetail : ceIdentificationDetails) {
            /*
            if (StringUtils.isNotBlank(ceIdentificationDetail.getCategory()) && !StringUtils.equals(ceIdentificationDetail.getCategory(), lastDetail.getCategory())) {
                lastDetail = ceIdentificationDetail;
            }
            if (StringUtils.isBlank(ceIdentificationDetail.getCategory())) {
                ceIdentificationDetail.setCategory(lastDetail.getCategory());
            }
             */
            ceIdentificationDetail.setId(UUID.randomUUID().toString());
            ceIdentificationDetail.setHeadId(headId);
            ceIdentificationDetail.setSeq(seq++);
            ceIdentificationDetailMapper.insertSelective(ceIdentificationDetail);
        }
    }

    private void deleteDetailByHeadId(String headId) {
        CeIdentificationDetailExample example = new CeIdentificationDetailExample();
        example.or().andHeadIdEqualTo(headId);
        ceIdentificationDetailMapper.deleteByExample(example);
    }

    private void updateHead(CeIdentificationHeadVO ceIdentificationHeadVO) {
        checkExist(ceIdentificationHeadVO.getId(), "碳排識別主表id不能为空");
        checkExist(ceIdentificationHeadVO.getOrganizationId(), "组织编号不能为空");
        checkExist(ceIdentificationHeadVO.getLastUpdateVersion(), "版本号不能为空");

        CeIdentificationHeadExample headExample = new CeIdentificationHeadExample();
        headExample.or().andIdEqualTo(ceIdentificationHeadVO.getId()).andLastUpdateVersionEqualTo(ceIdentificationHeadVO.getLastUpdateVersion());

        CeIdentificationHead ceIdentificationHead = CeIdentificationHeadConverter.convert(ceIdentificationHeadVO);
        ceIdentificationHead.setId(null);
        int updateCount = ceIdentificationHeadMapper.updateByExampleSelective(ceIdentificationHead, headExample);
        if (updateCount == 0) {
            throw new ServiceException("碳排識別主表信息已经被修改，请刷新后重试");
        }
    }

    /**
     * proxy for mapper method
     *
     * @param id 碳排識別主表id
     * @return
     */
    public CeIdentificationHead selectByPrimaryKey(String id) {
        return ceIdentificationHeadMapper.selectByPrimaryKey(id);
    }

    public List<CeIdentificationHead> selectByExample(CeIdentificationHeadExample example) {
        return ceIdentificationHeadMapper.selectByExample(example);
    }

}


