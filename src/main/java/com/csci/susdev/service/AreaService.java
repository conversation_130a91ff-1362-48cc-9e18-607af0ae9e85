package com.csci.susdev.service;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.AreaMapper;
import com.csci.susdev.model.Area;
import com.csci.susdev.model.AreaExample;
import com.csci.susdev.modelcovt.AreaConverter;
import com.csci.susdev.vo.AreaVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@LogMethod
public class AreaService {

    @Resource
    private AreaMapper areaMapper;

    /**
     * proxy for mapper method
     *
     * @param record 待新增记录
     * @return
     */
    public int insertSelective(Area record) {
        return areaMapper.insertSelective(record);
    }

    public Area selectByPrimaryKey(String id) {
        return areaMapper.selectByPrimaryKey(id);
    }

    public List<AreaVO> listArea() {
        List<Area> lstArea = areaMapper.selectByExample(new AreaExample());
        return lstArea.stream().map(x -> new AreaConverter().convert(x)).collect(Collectors.toList());
    }
}
