package com.csci.susdev.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.handler.CeIdentificationFugitiveEmissionHandler;
import com.csci.susdev.handler.FugitiveEmissionImageCellWriteHandler;
import com.csci.susdev.mapper.CeIdentificationFugitiveEmissionMapper;
import com.csci.susdev.mapper.MinioAttachmentMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.CeIdentificationFugitiveEmissionConverter;
import com.csci.susdev.vo.CeIdentificationFugitiveEmissionVO;
import com.csci.susdev.vo.CeIdentificationFugitiveHeadVO;
import com.csci.susdev.vo.ExcelCeIdentificationFugitiveEmissionVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class CeIdentificationFugitiveEmissionService {

    @Resource
    private CeIdentificationFugitiveEmissionMapper ceIdentificationFugitiveEmissionMapper;
    @Resource
    private CeIdentificationService ceIdentificationService;
    @Resource
    private OrganizationService organizationService;
    @Resource
    private MinioAttachmentService minioAttachmentService;
    @Resource
    private MinioAttachmentMapper mapper;



    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(CeIdentificationFugitiveEmission record) {
        return ceIdentificationFugitiveEmissionMapper.insertSelective(record);
    }

    public CeIdentificationFugitiveEmission selectByPrimaryKey(String id) {
        return ceIdentificationFugitiveEmissionMapper.selectByPrimaryKey(id);
    }

    public CeIdentificationFugitiveEmissionVO getCeIdentificationFugitiveEmission(String id) {
        checkExist(id, "id不能为空");
        CeIdentificationFugitiveEmission ceIdentificationFugitiveEmission = selectByPrimaryKey(id);
        checkExist(ceIdentificationFugitiveEmission, "未找到对应的记录");
        if(ceIdentificationFugitiveEmission.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return CeIdentificationFugitiveEmissionConverter.convertToVO(ceIdentificationFugitiveEmission);
    }

    private void deleteDetailByHeadId(String headId) {
        CeIdentificationFugitiveEmissionExample example = new CeIdentificationFugitiveEmissionExample();
        example.or().andCeIdentificationHeadIdEqualTo(headId);
        ceIdentificationFugitiveEmissionMapper.deleteByExample(example);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveCeIdentificationFugitiveEmissionList(CeIdentificationFugitiveHeadVO ceIdentificationFugitiveHeadVO) {

        List<CeIdentificationFugitiveEmissionVO> ceIdentificationFugitiveEmissionVOList = ceIdentificationFugitiveHeadVO.getDetails();
        if (CollectionUtils.isEmpty(ceIdentificationFugitiveEmissionVOList)) {
            throw new ServiceException("请传入数据！");
        }
        deleteDetailByHeadId(ceIdentificationFugitiveHeadVO.getId());

        List<CeIdentificationFugitiveEmission> lstDetails = ceIdentificationFugitiveEmissionVOList.stream().map(CeIdentificationFugitiveEmissionConverter::convertToModel).collect(Collectors.toList());

        addDetails(ceIdentificationFugitiveHeadVO.getId(), lstDetails);
    }

    private void addDetails(String headId, List<CeIdentificationFugitiveEmission> ceIdentificationFugitiveEmissionVOList) {
        if (CollectionUtils.isEmpty(ceIdentificationFugitiveEmissionVOList)) {
            throw new ServiceException("逸散排放不能为空");
        }
        int seq = 0;
        for (CeIdentificationFugitiveEmission ceIdentificationDetails : ceIdentificationFugitiveEmissionVOList) {
            ceIdentificationDetails.setId(null);
            ceIdentificationDetails.setCeIdentificationHeadId(headId);
            ceIdentificationDetails.setSeq(seq++);
            ceIdentificationFugitiveEmissionMapper.insertSelective(ceIdentificationDetails);
        }
    }

    public List<CeIdentificationFugitiveEmissionVO> listCeIdentificationFugitiveEmission(String headId) {
        checkExist(headId, "碳排识别主表记录ID不能为空");
        CeIdentificationFugitiveEmissionExample example = new CeIdentificationFugitiveEmissionExample();
        example.or().andCeIdentificationHeadIdEqualTo(headId).andIsDeletedEqualTo(false);
        example.setOrderByClause("seq asc");
        return ceIdentificationFugitiveEmissionMapper.selectByExample(example).stream().map(CeIdentificationFugitiveEmissionConverter::convertToVO).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteCeIdentificationFugitiveEmission(String id) {
        CeIdentificationFugitiveEmission record = selectByPrimaryKey(id);

        record.setIsDeleted(true);
        ceIdentificationFugitiveEmissionMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(CeIdentificationFugitiveEmissionVO ceIdentificationFugitiveEmissionVO) {
        CeIdentificationFugitiveEmission ceIdentificationFugitiveEmission = CeIdentificationFugitiveEmissionConverter.convertToModel(ceIdentificationFugitiveEmissionVO);
        ceIdentificationFugitiveEmissionMapper.insertSelective(ceIdentificationFugitiveEmission);
        return ceIdentificationFugitiveEmission.getId();
    }

    private String doUpdate(CeIdentificationFugitiveEmissionVO ceIdentificationFugitiveEmissionVO) {
        CeIdentificationFugitiveEmission originalRecord = selectByPrimaryKey(ceIdentificationFugitiveEmissionVO.getId());
        CeIdentificationFugitiveEmission ceIdentificationFugitiveEmission = CeIdentificationFugitiveEmissionConverter.convertToModelWithBase(ceIdentificationFugitiveEmissionVO, originalRecord);

        CeIdentificationFugitiveEmissionExample example = new CeIdentificationFugitiveEmissionExample();
        example.or().andIdEqualTo(ceIdentificationFugitiveEmissionVO.getId()).andLastUpdateVersionEqualTo(ceIdentificationFugitiveEmissionVO.getLastUpdateVersion());
        int updateCount = ceIdentificationFugitiveEmissionMapper.updateByExample(ceIdentificationFugitiveEmission, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return ceIdentificationFugitiveEmission.getId();
    }

    public List<CeIdentificationFugitiveEmission> selectByExample(CeIdentificationFugitiveEmissionExample example) {
        return ceIdentificationFugitiveEmissionMapper.selectByExample(example);
    }

    public byte[] exportCeIdentificationFugitiveEmission(String headId, HttpHeaders headers) {
        CeIdentificationHead ceIdentificationHead = ceIdentificationService.selectByPrimaryKey(headId);
        if(ceIdentificationHead == null) {
            throw new ServiceException("head不存在");
        }
        Organization organization = organizationService.selectByPrimaryKey(ceIdentificationHead.getOrganizationId());
        if(organization == null) {
            throw new ServiceException("organization不存在");
        }

        List<CeIdentificationFugitiveEmissionVO> list = this.listCeIdentificationFugitiveEmission(headId);
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("无数据可导出！");
        }
        String filename = new StringBuilder()
                .append("碳排识别_逸散排放填写").append("_")
                .append(String.valueOf(ceIdentificationHead.getYear()*100 + ceIdentificationHead.getMonth())).append("_")
                .append(DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()))
                .append(".xlsx").toString();
        String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8);
        headers.setContentDispositionFormData("attachment", encodedFilename);

        try (ByteArrayOutputStream out = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcel.write(out).build()) {

            // 查找附件
            MinioAttachmentExample example = new MinioAttachmentExample();
            MinioAttachmentExample.Criteria criteria = example.or();
            criteria.andRefIdEqualTo(headId);
            criteria.andSectionEqualTo("逸散排放");
            example.setOrderByClause("last_update_time desc");
            List<MinioAttachment> minioAttachments = mapper.selectByExample(example);
            Map<String, List<MinioAttachment>> fileMap = minioAttachments.stream().collect(Collectors.groupingBy(MinioAttachment::getCategory));

            // 数据转换
            List<ExcelCeIdentificationFugitiveEmissionVO> excelData = new ArrayList<>();
            for (CeIdentificationFugitiveEmissionVO fugitiveEmissionVO : list) {
                ExcelCeIdentificationFugitiveEmissionVO excelVO = new ExcelCeIdentificationFugitiveEmissionVO();
                BeanUtils.copyProperties(fugitiveEmissionVO, excelVO);
                if (fugitiveEmissionVO.getConfirmationItemOne().contains("新增空调")) {
                    excelVO.setConfirmationItemOne("空调");
                    excelVO.setConfirmationItemTwo("请拍摄所有自己运营管理的空调、冰箱冷柜的铭牌信息(如下图，放大可查看），需包含填充的制冷剂型号和重量信息，填写并粘贴在右侧表格中");
                }else if (fugitiveEmissionVO.getConfirmationItemOne().contains("新增冰箱/冷柜")) {
                    excelVO.setConfirmationItemOne("冰箱/冷柜");
                    excelVO.setConfirmationItemTwo("请拍摄所有自己运营管理的空调、冰箱冷柜的铭牌信息(如下图，放大可查看），需包含填充的制冷剂型号和重量信息，填写并粘贴在右侧表格中");
                }else if (fugitiveEmissionVO.getConfirmationItemOne().contains("新增自有车辆")) {
                    excelVO.setConfirmationItemOne("自有车辆");
                    excelVO.setConfirmationItemTwo("请确认公务车的制冷剂型号和填充量并填写，如下图所示信息");
                }
                if (fugitiveEmissionVO.getConfirmationItemThree().contains("拍摄图片")) {
                    excelVO.setInputValue("");
                }
                if (fileMap.containsKey(fugitiveEmissionVO.getConfirmationItemThree())) {
                    List<MinioAttachment> attachments = fileMap.get(fugitiveEmissionVO.getConfirmationItemThree());
                    /*List<byte[]> images = attachments.stream()
                            .map(att -> minioAttachmentService.getObject(att.getMinioFileName()))
                            .collect(Collectors.toList());*/
                    byte[] images = minioAttachmentService.getObject(attachments.get(0).getMinioFileName());
                    excelVO.setImgData(images);
                }
                excelData.add(excelVO);
            }

            // 创建Sheet并注册合并策略
            WriteSheet writeSheet = EasyExcel.writerSheet("逸散排放填写")
                    .head(ExcelCeIdentificationFugitiveEmissionVO.class)
                    .registerWriteHandler(new CeIdentificationFugitiveEmissionHandler(excelData))
                    .registerWriteHandler(new FugitiveEmissionImageCellWriteHandler())
                    .build();

            excelWriter.write(excelData, writeSheet);
            excelWriter.finish();
            return out.toByteArray();
        }catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
