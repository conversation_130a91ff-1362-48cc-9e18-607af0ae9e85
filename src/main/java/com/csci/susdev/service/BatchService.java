package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.AirPollutantsMapper;
import com.csci.susdev.mapper.BatchCustomMapper;
import com.csci.susdev.mapper.BatchMapper;
import com.csci.susdev.mapper.EmissionFactorMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.AirPollutantsConverter;
import com.csci.susdev.modelcovt.EmissionFactorConverter;
import com.csci.susdev.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class BatchService {

    @Autowired
    private BatchCustomMapper batchCustomMapper;

    @Autowired
    private BatchMapper batchMapper;

    @Autowired
    private EmissionFactorMapper emissionFactorMapper;

    @Autowired
    private AirPollutantsMapper airPollutantsMapper;

    public Batch selectByPrimaryKey(String id) {
        return batchMapper.selectByPrimaryKey(id);
    }

    /**
     * 保存批次信息以及批次对应的排放因子信息
     *
     * @param batchWithEmissionFactorVO 批次信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchWithEmissionFactors(BatchWithEmissionFactorVO batchWithEmissionFactorVO) {
        validateForSaveEmissionFactor(batchWithEmissionFactorVO);

        // 添加批次信息
        Batch batch = getOrInitBatch(batchWithEmissionFactorVO);

        batchWithEmissionFactorVO.setId(batch.getId());

        deleteEmissionFactorByBatchId(batch.getId());
        // 添加排放因子信息

        addEmissionFactorToBatch(batchWithEmissionFactorVO.getEmissionFactors(), batch.getId());
    }

    /**
     * 保存批次记录及对应的空气污染物因子记录
     *
     * @param batchWithAirPollutantsVO 批次信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchWithAirPollutants(BatchWithAirPollutantsVO batchWithAirPollutantsVO) {
        validateForSaveAirPollutants(batchWithAirPollutantsVO);

        // 添加批次信息
        Batch batch = getOrInitBatch(batchWithAirPollutantsVO);

        batchWithAirPollutantsVO.setId(batch.getId());

        deleteAirPollutantsByBatchId(batch.getId());
        // 添加排放因子信息

        addAirPollutantsToBatch(batchWithAirPollutantsVO.getAirPollutants(), batch.getId());
    }

    private void deleteAirPollutantsByBatchId(String batchId) {
        AirPollutantsExample example = new AirPollutantsExample();
        example.or().andBatchIdEqualTo(batchId);
        airPollutantsMapper.deleteByExample(example);
    }

    private void addAirPollutantsToBatch(List<AirPollutantsVO> airPollutantsVOList, String batchId) {
        for (AirPollutantsVO airPollutantsVO : airPollutantsVOList) {
            AirPollutants airPollutants = new AirPollutantsConverter().revert(airPollutantsVO);
            airPollutants.setBatchId(batchId);
            airPollutantsMapper.insertSelective(airPollutants);
        }
    }

    private static void validateForSaveAirPollutants(BatchWithAirPollutantsVO batchWithAirPollutantsVO) {
        checkExist(batchWithAirPollutantsVO.getAreaId(), "区域信息不能为空");
        checkExist(batchWithAirPollutantsVO.getEffectiveDate(), "生效日期不能为空");
        if (CollectionUtils.isEmpty(batchWithAirPollutantsVO.getAirPollutants())) {
            throw new ServiceException("空气污染物排放因子记录不能为空");
        }
    }

    /**
     * 验证保存排放因子信息的参数
     *
     * @param batchVO
     */
    private static void validateForSaveEmissionFactor(BatchWithEmissionFactorVO batchVO) {
        checkExist(batchVO.getAreaId(), "区域信息不能为空");
        checkExist(batchVO.getEffectiveDate(), "生效日期不能为空");
        if (CollectionUtils.isEmpty(batchVO.getEmissionFactors())) {
            throw new ServiceException("排放因子不能为空");
        }
    }

    /**
     * 根据区域 id 以及年月查询指定的批次记录 id
     *
     * @param areaId
     * @param year
     * @param month
     * @return
     */
    public String getBatchIdBy(String areaId, Integer year, Integer month) {
        return Optional.ofNullable(batchCustomMapper.selectBatchBy(areaId, year, month)).map(Batch::getId).orElseThrow(() -> new ServiceException("未找到对应的批次信息"));
    }

    /**
     * 将指定的批次记录设置为未激活状态
     *
     * @param id 批次记录id
     * @return 更新的记录数
     */
    @Transactional(rollbackFor = Exception.class)
    public int inActiveBatch(String id) {
        Batch batch = new Batch();
        batch.setId(id);
        batch.setIsActive(false);
        return batchMapper.updateByPrimaryKeySelective(batch);
    }

    /**
     * 根据批次 id 删除关联的所有碳排放因子
     *
     * @param batchId 批次 id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteEmissionFactorByBatchId(String batchId) {
        EmissionFactorExample example = new EmissionFactorExample();
        example.or().andBatchIdEqualTo(batchId);
        emissionFactorMapper.deleteByExample(example);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addEmissionFactorToBatch(List<EmissionFactorVO> emissionFactorVOS, String batchId) {
        for (EmissionFactorVO emissionFactorVO : emissionFactorVOS) {
            EmissionFactor emissionFactor = new EmissionFactorConverter().revert(emissionFactorVO);
            emissionFactor.setBatchId(batchId);
            emissionFactorMapper.insertSelective(emissionFactor);
        }
    }

    /**
     * 如果已经指定区域以及期限内已经存在激活的记录，直接查询出来，否则新增
     *
     * @param batchVO 待新增记录
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Batch getOrInitBatch(BatchVO batchVO) {
        return getOrInitBatch(batchVO.getAreaId(), batchVO.getEffectiveDate().getYear(), batchVO.getEffectiveDate().getMonthValue());
    }

    public Batch getOrInitBatch(String areaId, Integer year, Integer month) {
        Batch batch = new Batch();
        batch.setAreaId(areaId);
        batch.setIsActive(Boolean.TRUE);
        batch.setEffectiveDate(LocalDateTime.of(year, month, 1, 0, 0));
        int count = batchCustomMapper.insertBatchIgnoreExist(batch);
        if (Objects.equals(count, 0)) {
            // 查询
            batch = batchCustomMapper.selectBatchBy(areaId, year, month);
        }
        if (Objects.isNull(batch)) {
            throw new ServiceException("未找到指定年月的批次信息，并且初始化失败");
        }
        return batch;
    }

    /**
     * 根据区域 id 以及年月查询指定的排放因子记录
     *
     * @param batchId 批次 id
     * @return 排放因子记录
     */
    public List<EmissionFactorVO> listEmissionFactorByBatchId(String batchId) {
        checkExist(batchId, "批次 id 不能为空");
        EmissionFactorExample example = new EmissionFactorExample();
        example.or().andBatchIdEqualTo(batchId);
        List<EmissionFactor> emissionFactors = emissionFactorMapper.selectByExample(example);
        return emissionFactors.stream().map(x -> new EmissionFactorConverter().convert(x)).collect(Collectors.toList());
    }

    /**
     * 根据区域 id 以及年月查询指定的空气污染物排放因子记录
     *
     * @param batchId 批次 id
     * @return 空气污染物排放因子记录
     */
    public List<AirPollutantsVO> listAirPollutantsByBatchId(String batchId) {
        checkExist(batchId, "批次 id 不能为空");
        AirPollutantsExample example = new AirPollutantsExample();
        example.or().andBatchIdEqualTo(batchId);
        List<AirPollutants> airPollutants = airPollutantsMapper.selectByExample(example);
        return airPollutants.stream().map(x -> new AirPollutantsConverter().convert(x)).collect(Collectors.toList());
    }

    /**
     * 更新碳排放因子
     *
     * @param emissionFactorVO 碳排放因子
     */
    public void updateEmissionFactor(EmissionFactorVO emissionFactorVO) {
        validateUpdateEmissionFactor(emissionFactorVO);
        EmissionFactor emissionFactor = new EmissionFactorConverter().revert(emissionFactorVO);

        // 需要根据最后更新版本号来进行乐观锁校验
        EmissionFactorExample example = new EmissionFactorExample();
        example.or().andIdEqualTo(emissionFactorVO.getId()).andLastUpdateVersionEqualTo(emissionFactorVO.getLastUpdateVersion());

        int updateCount = emissionFactorMapper.updateByExampleSelective(emissionFactor, example);
        if (Objects.equals(updateCount, 0)) {
            throw new ServiceException("更新失败，可能是数据已经被其他用户修改，请刷新后重试");
        }
    }

    /**
     * 更新空气污染物排放因子
     *
     * @param airPollutantsVO 空气污染物排放因子
     */
    public void updateAirPollutants(AirPollutantsVO airPollutantsVO) {
        AirPollutantsService.validateUpdateAirPollutants(airPollutantsVO);
        AirPollutants airPollutants = new AirPollutantsConverter().revert(airPollutantsVO);

        // 需要根据最后更新版本号来进行乐观锁校验
        AirPollutantsExample example = new AirPollutantsExample();
        example.or().andIdEqualTo(airPollutantsVO.getId()).andLastUpdateVersionEqualTo(airPollutantsVO.getLastUpdateVersion());

        int updateCount = airPollutantsMapper.updateByExampleSelective(airPollutants, example);
        if (Objects.equals(updateCount, 0)) {
            throw new ServiceException("更新失败，可能是数据已经被其他用户修改，请刷新后重试");
        }
    }

    /**
     * 校验更新空气污染物排放因子必填字段
     *
     * @param emissionFactorVO 空气污染物排放因子
     */
    private void validateUpdateEmissionFactor(EmissionFactorVO emissionFactorVO) {
        EmissionFactorService.validateRequiredFieldForEmissionFactor(emissionFactorVO);
        checkExist(emissionFactorVO.getLastUpdateVersion(), "排放因子版本号不能为空");
    }

}
