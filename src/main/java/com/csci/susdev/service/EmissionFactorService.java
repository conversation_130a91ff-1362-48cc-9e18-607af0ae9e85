package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.EmissionFactorMapper;
import com.csci.susdev.model.EmissionFactor;
import com.csci.susdev.model.EmissionFactorExample;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.modelcovt.EmissionFactorConverter;
import com.csci.susdev.qo.BatchIdPageQO;
import com.csci.susdev.vo.EmissionFactorVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;
import static com.csci.susdev.service.ServiceHelper.initPageableVO;

@Service
@LogMethod
public class EmissionFactorService {

    @Autowired
    private EmissionFactorMapper emissionFactorMapper;

    static void validateAddEmissionFactor(EmissionFactorVO emissionFactorVO) {
        validateRequiredFieldForEmissionFactor(emissionFactorVO);
    }

    /**
     * 校验碳排放因子必填字段
     *
     * @param emissionFactorVO
     */
    static void validateRequiredFieldForEmissionFactor(EmissionFactorVO emissionFactorVO) {
        checkExist(emissionFactorVO.getBatchId(), "批次 id 不能为空");
        checkExist(emissionFactorVO.getEmissionSource(), "排放源不能为空");
        checkExist(emissionFactorVO.getEmissionFactor(), "排放因子不能为空");
        if (!NumberUtils.isParsable(emissionFactorVO.getEmissionFactor())) {
            throw new ServiceException("排放因子必须为数字");
        }
        Optional.ofNullable(emissionFactorVO.getEnergyConsumptionFactor()).ifPresent(x -> {
            if (!NumberUtils.isParsable(x)) {
                throw new ServiceException("能耗因子必须为数字");
            }
        });
    }


    /**
     * proxy for mapper method
     *
     * @param record the record
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(EmissionFactor record) {
        return emissionFactorMapper.insertSelective(record);
    }

    /**
     * 新增碳排放因子
     *
     * @param emissionFactorVO 碳排放因子
     * @return 新增的记录 id
     */
    @Transactional(rollbackFor = Exception.class)
    public String addEmissionFactor(EmissionFactorVO emissionFactorVO) {
        validateAddEmissionFactor(emissionFactorVO);
        EmissionFactor emissionFactor = new EmissionFactorConverter().revert(emissionFactorVO);
        emissionFactorMapper.insertSelective(emissionFactor);
        return emissionFactor.getId();
    }

    /**
     * 分页查找碳排放因子
     *
     * @param batchIdPageQO
     * @return
     */
    public ResultPage<EmissionFactorVO> listEmissionFactorByPage(BatchIdPageQO batchIdPageQO) {
        initPageableVO(batchIdPageQO);
        checkExist(batchIdPageQO.getBatchId(), "batchId 不能为空");
        EmissionFactorExample example = new EmissionFactorExample();
        example.or().andBatchIdEqualTo(batchIdPageQO.getBatchId());
        example.setOrderByClause("creation_time desc");
        PageHelper.startPage(batchIdPageQO.getCurPage(), batchIdPageQO.getPageSize());
        List<EmissionFactor> lstEmissionFactor = emissionFactorMapper.selectByExample(example);
        ResultPage<EmissionFactorVO> resultPage = new ResultPage<>(lstEmissionFactor);
        resultPage.setList(lstEmissionFactor.stream().map(x -> new EmissionFactorConverter().convert(x)).collect(Collectors.toList()));
        return resultPage;
    }

    /**
     * proxy for mapper method
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteByPrimaryKey(String id) {
        checkExist(id, "id 不能为空");
        return emissionFactorMapper.deleteByPrimaryKey(id);
    }

}
