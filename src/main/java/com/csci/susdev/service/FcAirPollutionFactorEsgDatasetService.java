package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.*;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.*;
import com.csci.susdev.qo.*;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.vo.*;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.*;

@Service
@LogMethod
public class FcAirPollutionFactorEsgDatasetService {

    @Resource
    private FcAirPollutionFactorEsgDatasetMapper fcAirPollutionFactorEsgDatasetMapper;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FcAirPollutionFactorEsgDataset record) {
        return fcAirPollutionFactorEsgDatasetMapper.insertSelective(record);
    }

    public FcAirPollutionFactorEsgDataset selectByPrimaryKey(String id) {
        return fcAirPollutionFactorEsgDatasetMapper.selectByPrimaryKey(id);
    }

    public FcAirPollutionFactorEsgDatasetVO getFcAirPollutionFactorEsgDataset(String id) {
        checkExist(id, "id不能为空");
        FcAirPollutionFactorEsgDataset fcAirPollutionFactorEsgDataset = selectByPrimaryKey(id);
        checkExist(fcAirPollutionFactorEsgDataset, "未找到对应的记录");
        if(fcAirPollutionFactorEsgDataset.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return FcAirPollutionFactorEsgDatasetConverter.convertToVO(fcAirPollutionFactorEsgDataset);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveFcAirPollutionFactorEsgDataset(FcAirPollutionFactorEsgDatasetVO fcAirPollutionFactorEsgDatasetVO) {
        if (StringUtils.isBlank(fcAirPollutionFactorEsgDatasetVO.getId())) {
            // 新增
            return doAdd(fcAirPollutionFactorEsgDatasetVO);
        } else {
            checkExist(fcAirPollutionFactorEsgDatasetVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(fcAirPollutionFactorEsgDatasetVO);
        }

    }

    public ResultPage<FcAirPollutionFactorEsgDatasetVO> listFcAirPollutionFactorEsgDataset(FcAirPollutionFactorEsgDatasetQO fcAirPollutionFactorEsgDatasetQO) {
        FcAirPollutionFactorEsgDatasetExample example = new FcAirPollutionFactorEsgDatasetExample();

        SimpTradUtil simpTradUtil = new SimpTradUtil();
        String chineseNameTc = simpTradUtil.convert2Trad(fcAirPollutionFactorEsgDatasetQO.getChineseName());
        String chineseNameSc = simpTradUtil.convert2Simp(fcAirPollutionFactorEsgDatasetQO.getChineseName());

        if(StringUtils.isNotBlank(fcAirPollutionFactorEsgDatasetQO.getChineseName())) {
            example.or().andChineseNameLike("%" + chineseNameTc + "%").andIsDeletedEqualTo(false);
            example.or().andChineseNameLike("%" + chineseNameSc + "%").andIsDeletedEqualTo(false);
        } else {
            example.or().andIsDeletedEqualTo(false);
        }

        if (StringUtils.isNotBlank(fcAirPollutionFactorEsgDatasetQO.getOrderBy())) {
            example.setOrderByClause(NamingConventionUtils.snakeCaseToCamelCase(fcAirPollutionFactorEsgDatasetQO.getOrderBy()));
        }

        PageHelper.startPage(fcAirPollutionFactorEsgDatasetQO.getCurPage(), fcAirPollutionFactorEsgDatasetQO.getPageSize());
        List<FcAirPollutionFactorEsgDataset> fcAirPollutionFactorEsgDatasets = fcAirPollutionFactorEsgDatasetMapper.selectByExample(example);
        return new ResultPage<>(fcAirPollutionFactorEsgDatasets, fcAirPollutionFactorEsgDatasets.stream().map(FcAirPollutionFactorEsgDatasetConverter::convertToVO).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFcAirPollutionFactorEsgDataset(String id) {
        FcAirPollutionFactorEsgDataset record = selectByPrimaryKey(id);
        record.setIsDeleted(true);
        fcAirPollutionFactorEsgDatasetMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(FcAirPollutionFactorEsgDatasetVO fcAirPollutionFactorEsgDatasetVO) {
        FcAirPollutionFactorEsgDataset fcAirPollutionFactorEsgDataset = FcAirPollutionFactorEsgDatasetConverter.convertToModel(fcAirPollutionFactorEsgDatasetVO);
        fcAirPollutionFactorEsgDatasetMapper.insertSelective(fcAirPollutionFactorEsgDataset);
        return fcAirPollutionFactorEsgDataset.getId();
    }

    private String doUpdate(FcAirPollutionFactorEsgDatasetVO fcAirPollutionFactorEsgDatasetVO) {
        FcAirPollutionFactorEsgDataset originalRecord = selectByPrimaryKey(fcAirPollutionFactorEsgDatasetVO.getId());
        FcAirPollutionFactorEsgDataset fcAirPollutionFactorEsgDataset = FcAirPollutionFactorEsgDatasetConverter.convertToModelWithBase(fcAirPollutionFactorEsgDatasetVO, originalRecord);

        FcAirPollutionFactorEsgDatasetExample example = new FcAirPollutionFactorEsgDatasetExample();
        example.or().andIdEqualTo(fcAirPollutionFactorEsgDatasetVO.getId()).andLastUpdateVersionEqualTo(fcAirPollutionFactorEsgDatasetVO.getLastUpdateVersion());
        int updateCount = fcAirPollutionFactorEsgDatasetMapper.updateByExample(fcAirPollutionFactorEsgDataset, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return fcAirPollutionFactorEsgDataset.getId();
    }

    public List<FcAirPollutionFactorEsgDataset> selectByExample(FcAirPollutionFactorEsgDatasetExample example) {
        return fcAirPollutionFactorEsgDatasetMapper.selectByExample(example);
    }
}
