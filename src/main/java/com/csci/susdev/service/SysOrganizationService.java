package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.tzh.mapper.SysOrganizationMapper;
import com.csci.tzh.model.*;
import com.csci.tzh.model.SysOrganizationExample.Criteria;
import com.csci.susdev.model.ResultPage;
import com.csci.tzh.qo.*;
import com.github.pagehelper.PageHelper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class SysOrganizationService {

	@Autowired
	private SysOrganizationMapper mapper;

	public List<SysOrganization> selectByExample(SysOrganizationExample example) {
		return mapper.selectByExample(example);
	}

	/**
	 * 組織信息 数据列表
	 *
	 * @param qo
	 * @return
	 */
	public ResultPage<SysOrganization> listSysOrganization(SysOrganizationPageableQO qo) {
		SysOrganizationExample example = new SysOrganizationExample();

		Criteria criteria = example.or();
		criteria.andTypeEqualTo(qo.getType());
		criteria.andNameLike("%" + qo.getName() + "%");
		criteria.andIsdeletedEqualTo(false);
		example.setOrderByClause("InnerNo");

		PageHelper.startPage(qo.getCurPage(), qo.getPageSize());

		List<SysOrganization> lst = mapper.selectByExample(example);
		ResultPage<SysOrganization> resultPage = new ResultPage<>(lst, true);

		return resultPage;
	}

	public ResultPage<String> nameListSysOrganization(SysOrganizationPageableQO sysOrganizationQO) {
		SysOrganizationExample example = new SysOrganizationExample();

		Criteria criteria = example.or();
		criteria.andTypeEqualTo(sysOrganizationQO.getType());
		criteria.andNameLike("%" + sysOrganizationQO.getName() + "%");
		criteria.andIsdeletedEqualTo(false);
		example.setOrderByClause("InnerNo");

		PageHelper.startPage(sysOrganizationQO.getCurPage(), sysOrganizationQO.getPageSize());

		List<String> lstName = new ArrayList<>();
		List<SysOrganization> lst = mapper.selectByExample(example);
		for(SysOrganization sysOrganization: lst) {
			lstName.add(sysOrganization.getName());
		}
		ResultPage<String> resultPage = new ResultPage<>(lst);
		resultPage.setList(lstName);
		
		return resultPage;
	}

	

	/**
	 * 組織信息 数据
	 *
	 * @param sysOrganizationQO
	 * @return
	 */
	public SysOrganization getSysOrganization() {
		// 查询头行信息
		SysOrganizationExample example = new SysOrganizationExample();

		Criteria criteria = example.or();
		criteria.andIsdeletedEqualTo(false);
		example.setOrderByClause("InnerNo");

		List<SysOrganization> lst = mapper.selectByExample(example);

		if (lst.size() == 0)
			return null;
		
		return lst.get(0);
	}

	/**
	 * 驗證 組織信息 信息, 如有錯誤返回相關資訊
	 *
	 * @param sysOrganization
	 * @return
	 */
	public String checkSysOrganization(SysOrganization sysOrganization) {
		SysOrganizationPageableQO sysOrganizationQO = new SysOrganizationPageableQO();
		sysOrganizationQO.setCurPage(0);
		sysOrganizationQO.setPageSize(0);
		ResultPage<SysOrganization> resultPage = this.listSysOrganization(sysOrganizationQO);
		
		return "";
	}
	
	/**
	 * 保存 組織信息 信息
	 *
	 * @param sysOrganization
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public SysOrganization saveSysOrganization(SysOrganization sysOrganization) {
		SysOrganizationExample example = new SysOrganizationExample();
		Criteria criteria = example.or();
		criteria.andIdEqualTo(sysOrganization.getId());
    	mapper.updateByExampleSelective(sysOrganization, example);
		return sysOrganization;
	}

	/**
	 * 新增 組織信息 信息
	 *
	 * @param sysOrganization
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public SysOrganization createSysOrganization(SysOrganization sysOrganization) {
    	mapper.insertSelective(sysOrganization);
		return sysOrganization;
	}

}
