package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.FcEnergyFactorMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.FcEnergyFactorConverter;
import com.csci.susdev.qo.FcEnergyFactorQO;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.vo.FcEnergyFactorVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class FcEnergyFactorService {

    @Resource
    private FcEnergyFactorMapper fcEnergyFactorMapper;
    @Resource
    private ProtocolConfigurationService protocolConfigurationService;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FcEnergyFactor record) {
        return fcEnergyFactorMapper.insertSelective(record);
    }

    public FcEnergyFactor selectByPrimaryKey(String id) {
        return fcEnergyFactorMapper.selectByPrimaryKey(id);
    }

    public FcEnergyFactorVO getFcEnergyFactor(String id) {
        checkExist(id, "id不能为空");
        FcEnergyFactor fcEnergyFactor = selectByPrimaryKey(id);
        checkExist(fcEnergyFactor, "未找到对应的记录");
        if(fcEnergyFactor.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return FcEnergyFactorConverter.convertToVO(fcEnergyFactor);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveFcEnergyFactor(FcEnergyFactorVO fcEnergyFactorVO) {
        if (StringUtils.isBlank(fcEnergyFactorVO.getId())) {
            // 新增
            return doAdd(fcEnergyFactorVO);
        } else {
            checkExist(fcEnergyFactorVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(fcEnergyFactorVO);
        }

    }

    public ResultPage<FcEnergyFactorVO> listFcEnergyFactor(FcEnergyFactorQO fcEnergyFactorQO) {
        FcEnergyFactorExample example = new FcEnergyFactorExample();

        SimpTradUtil simpTradUtil = new SimpTradUtil();
        String chineseNameTc = simpTradUtil.convert2Trad(fcEnergyFactorQO.getChineseName());
        String chineseNameSc = simpTradUtil.convert2Simp(fcEnergyFactorQO.getChineseName());

        if(StringUtils.isNotBlank(fcEnergyFactorQO.getChineseName())) {
            example.or().andChineseNameLike("%" + chineseNameTc + "%").andIsDeletedEqualTo(false);
            example.or().andChineseNameLike("%" + chineseNameSc + "%").andIsDeletedEqualTo(false);
        } else {
            example.or().andIsDeletedEqualTo(false);
        }

        if (StringUtils.isNotBlank(fcEnergyFactorQO.getOrderBy())) {
            example.setOrderByClause(NamingConventionUtils.snakeCaseToCamelCase(fcEnergyFactorQO.getOrderBy()));
        }else {
            example.setOrderByClause("creation_time desc");
        }

        PageHelper.startPage(fcEnergyFactorQO.getCurPage(), fcEnergyFactorQO.getPageSize());
        List<FcEnergyFactor> fcEnergyFactors = fcEnergyFactorMapper.selectByExample(example);
        return new ResultPage<>(fcEnergyFactors, fcEnergyFactors.stream().map(FcEnergyFactorConverter::convertToVO).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFcEnergyFactor(String id) {
        ProtocolConfigurationExample configurationExample = new ProtocolConfigurationExample();
        configurationExample.or().andFcFactorIdEqualTo(id).andIsDeletedEqualTo(false).andFcFactorTypeEqualTo("物料因子");
        List<ProtocolConfiguration> protocolConfigurations = protocolConfigurationService.selectByExample(configurationExample);
        if (CollectionUtils.isNotEmpty(protocolConfigurations)) {
            throw new ServiceException("已在协议配置中被使用，不能删除！");
        }
        FcEnergyFactor record = selectByPrimaryKey(id);
        record.setIsDeleted(true);
        fcEnergyFactorMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(FcEnergyFactorVO fcEnergyFactorVO) {
        FcEnergyFactor fcEnergyFactor = FcEnergyFactorConverter.convertToModel(fcEnergyFactorVO);
        fcEnergyFactorMapper.insertSelective(fcEnergyFactor);
        return fcEnergyFactor.getId();
    }

    private String doUpdate(FcEnergyFactorVO fcEnergyFactorVO) {
        FcEnergyFactor originalRecord = selectByPrimaryKey(fcEnergyFactorVO.getId());
        FcEnergyFactor fcEnergyFactor = FcEnergyFactorConverter.convertToModelWithBase(fcEnergyFactorVO, originalRecord);

        FcEnergyFactorExample example = new FcEnergyFactorExample();
        example.or().andIdEqualTo(fcEnergyFactorVO.getId()).andLastUpdateVersionEqualTo(fcEnergyFactorVO.getLastUpdateVersion());
        int updateCount = fcEnergyFactorMapper.updateByExample(fcEnergyFactor, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return fcEnergyFactor.getId();
    }

    public List<FcEnergyFactor> selectByExample(FcEnergyFactorExample example) {
        return fcEnergyFactorMapper.selectByExample(example);
    }
}
