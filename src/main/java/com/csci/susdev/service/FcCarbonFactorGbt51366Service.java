package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.FcCarbonFactorGbt51366Mapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.FcCarbonFactorGbt51366Converter;
import com.csci.susdev.qo.FcCarbonFactorGbt51366QO;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.vo.FcCarbonFactorGbt51366VO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class FcCarbonFactorGbt51366Service {

    @Resource
    private FcCarbonFactorGbt51366Mapper fcCarbonFactorGbt51366Mapper;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FcCarbonFactorGbt51366 record) {
        return fcCarbonFactorGbt51366Mapper.insertSelective(record);
    }

    public FcCarbonFactorGbt51366 selectByPrimaryKey(String id) {
        return fcCarbonFactorGbt51366Mapper.selectByPrimaryKey(id);
    }

    public FcCarbonFactorGbt51366VO getFcCarbonFactorGbt51366(String id) {
        checkExist(id, "id不能为空");
        FcCarbonFactorGbt51366 fcCarbonFactorGbt51366 = selectByPrimaryKey(id);
        checkExist(fcCarbonFactorGbt51366, "未找到对应的记录");
        if(fcCarbonFactorGbt51366.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return FcCarbonFactorGbt51366Converter.convertToVO(fcCarbonFactorGbt51366);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveFcCarbonFactorGbt51366(FcCarbonFactorGbt51366VO fcCarbonFactorGbt51366VO) {
        if (StringUtils.isBlank(fcCarbonFactorGbt51366VO.getId())) {
            // 新增
            return doAdd(fcCarbonFactorGbt51366VO);
        } else {
            checkExist(fcCarbonFactorGbt51366VO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(fcCarbonFactorGbt51366VO);
        }

    }

    public ResultPage<FcCarbonFactorGbt51366VO> listFcCarbonFactorGbt51366(FcCarbonFactorGbt51366QO fcCarbonFactorGbt51366QO) {
        FcCarbonFactorGbt51366Example example = new FcCarbonFactorGbt51366Example();

        SimpTradUtil simpTradUtil = new SimpTradUtil();
        String chineseNameTc = simpTradUtil.convert2Trad(fcCarbonFactorGbt51366QO.getChineseName());
        String chineseNameSc = simpTradUtil.convert2Simp(fcCarbonFactorGbt51366QO.getChineseName());

        if(StringUtils.isNotBlank(fcCarbonFactorGbt51366QO.getChineseName())) {
            example.or().andChineseNameLike("%" + chineseNameTc + "%").andIsDeletedEqualTo(false);
            example.or().andChineseNameLike("%" + chineseNameSc + "%").andIsDeletedEqualTo(false);
        } else {
            example.or().andIsDeletedEqualTo(false);
        }

        if (StringUtils.isNotBlank(fcCarbonFactorGbt51366QO.getOrderBy())) {
            example.setOrderByClause(NamingConventionUtils.snakeCaseToCamelCase(fcCarbonFactorGbt51366QO.getOrderBy()));
        }

        PageHelper.startPage(fcCarbonFactorGbt51366QO.getCurPage(), fcCarbonFactorGbt51366QO.getPageSize());
        List<FcCarbonFactorGbt51366> fcCarbonFactorGbt51366s = fcCarbonFactorGbt51366Mapper.selectByExample(example);
        return new ResultPage<>(fcCarbonFactorGbt51366s, fcCarbonFactorGbt51366s.stream().map(FcCarbonFactorGbt51366Converter::convertToVO).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFcCarbonFactorGbt51366(String id) {
        FcCarbonFactorGbt51366 record = selectByPrimaryKey(id);
        record.setIsDeleted(true);
        fcCarbonFactorGbt51366Mapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(FcCarbonFactorGbt51366VO fcCarbonFactorGbt51366VO) {
        FcCarbonFactorGbt51366 fcCarbonFactorGbt51366 = FcCarbonFactorGbt51366Converter.convertToModel(fcCarbonFactorGbt51366VO);
        fcCarbonFactorGbt51366Mapper.insertSelective(fcCarbonFactorGbt51366);
        return fcCarbonFactorGbt51366.getId();
    }

    private String doUpdate(FcCarbonFactorGbt51366VO fcCarbonFactorGbt51366VO) {
        FcCarbonFactorGbt51366 originalRecord = selectByPrimaryKey(fcCarbonFactorGbt51366VO.getId());
        FcCarbonFactorGbt51366 fcCarbonFactorGbt51366 = FcCarbonFactorGbt51366Converter.convertToModelWithBase(fcCarbonFactorGbt51366VO, originalRecord);

        FcCarbonFactorGbt51366Example example = new FcCarbonFactorGbt51366Example();
        example.or().andIdEqualTo(fcCarbonFactorGbt51366VO.getId()).andLastUpdateVersionEqualTo(fcCarbonFactorGbt51366VO.getLastUpdateVersion());
        int updateCount = fcCarbonFactorGbt51366Mapper.updateByExample(fcCarbonFactorGbt51366, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return fcCarbonFactorGbt51366.getId();
    }

    public List<FcCarbonFactorGbt51366> selectByExample(FcCarbonFactorGbt51366Example example) {
        return fcCarbonFactorGbt51366Mapper.selectByExample(example);
    }
}
