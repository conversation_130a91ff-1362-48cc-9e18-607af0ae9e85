package com.csci.susdev.service;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.WorkflowNodeUserMapper;
import com.csci.susdev.model.WorkflowNodeUser;
import com.csci.susdev.model.WorkflowNodeUserExample;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class WorkflowNodeUserService {

    @Resource
    private WorkflowNodeUserMapper workflowNodeUserMapper;

    /**
     * 添加用户到节点
     * 目前只支持单个用户，因此添加一个用户时，把原有的用户删除
     *
     * @param userId
     * @param nodeId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String addUserToNode(String userId, String nodeId) {
        checkExist(userId, "用户id不能为空");
        checkExist(nodeId, "节点id不能为空");

        removeAllUserFromNode(nodeId);

        WorkflowNodeUser workflowNodeUser = new WorkflowNodeUser();
        workflowNodeUser.setNodeId(nodeId);
        workflowNodeUser.setUserId(userId);
        workflowNodeUserMapper.insertSelective(workflowNodeUser);
        return workflowNodeUser.getId();
    }

    /**
     * 删除指定节点的用户
     *
     * @param nodeId 节点id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int removeAllUserFromNode(String nodeId) {
        checkExist(nodeId, "节点id不能为空");

        WorkflowNodeUserExample example = new WorkflowNodeUserExample();
        example.or().andNodeIdEqualTo(nodeId);
        return workflowNodeUserMapper.deleteByExample(example);
    }

}
