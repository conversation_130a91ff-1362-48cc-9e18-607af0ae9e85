package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.TzhBsUserSiteMapper;
import com.csci.susdev.mapper.UserMapper;
import com.csci.susdev.mapper.UserOrganizationMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class UserOrganizationService {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(UserOrganizationService.class);

    @Autowired
    private UserOrganizationMapper mapper;

    @Autowired
    private TzhBsUserSiteMapper tzhBsUserSiteMapper;

    @Autowired
    private UserMapper userMapper;

    public List<UserOrganization> selectByExample(UserOrganizationExample example) {
        return mapper.selectByExample(example);
    }
	
    /**
     * 用戶組織關係 数据列表
     *
     * @param 
     * @return
     */
    public List<UserOrganization> listUserOrganizationByUsername(String username) {
    	User user = ServiceHelper.getUserByUsername(username, userMapper);
    	if(user == null)
    		throw new ServiceException("找不到用戶");
    	
        return ServiceHelper.listUserOrganization(user.getId(), mapper);
    }

    /**
     * 用戶組織關係 数据列表
     *
     * @param userId
     * @return
     */
    public List<UserOrganization> listUserOrganization(String userId) {
        return ServiceHelper.listUserOrganization(userId, mapper);
    }

    /**
     * 保存 用戶組織關係 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param userOrganizationLst
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> saveUserOrganizationList(List<UserOrganization> userOrganizationLst) {
    	List<String> idLst = new ArrayList<>();
    	for(UserOrganization userOrganization : userOrganizationLst) {
    		this.saveUserOrganization(userOrganization);
            idLst.add(userOrganization.getId());
    	}
        return idLst;
    }

    /**
     * 保存 用戶組織關係 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param userOrganization
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveUserOrganization(UserOrganization userOrganization) {
        if (Objects.equals(userOrganization.getIsDeleted(), Boolean.TRUE)) {
            this.deleteUserOrganization(userOrganization.getId());
        } else {
            UserInfo currentUser = ContextUtils.getCurrentUser();
            LocalDateTime now = LocalDateTime.now();
            if (StringUtils.isBlank(userOrganization.getId())) {
                userOrganization.setCreationTime(now);
                userOrganization.setCreateUsername(currentUser.getUsername());
                userOrganization.setLastUpdateTime(now);
                userOrganization.setLastUpdateUsername(currentUser.getUsername());
                mapper.insertSelective(userOrganization);
            } else {
                userOrganization.setLastUpdateTime(now);
                userOrganization.setLastUpdateUsername(currentUser.getUsername());
                mapper.updateByPrimaryKeySelective(userOrganization);
            }
        }
        return userOrganization.getId();
    }

    /**
     * 保存 大屏用戶組織關係 信息
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param tzhBsUserSite
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveTzhBsUserSite(TzhBsUserSite tzhBsUserSite) {
        UserInfo currentUser = ContextUtils.getCurrentUser();
        LocalDateTime now = LocalDateTime.now();
        if (StringUtils.isBlank(tzhBsUserSite.getId())) {
            tzhBsUserSiteMapper.insertSelective(tzhBsUserSite);
        } else {
            tzhBsUserSiteMapper.updateByPrimaryKeySelective(tzhBsUserSite);
        }
        return tzhBsUserSite.getId();
    }

    /**
     * 刪除 用戶組織關係 信息
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserOrganization(String id) {
        return mapper.deleteByPrimaryKey(id);
    }

    /**
     * 刪除 用戶組織關係 信息
     *
     * @param username
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserOrganizationByUsername(String username) {
        int result = 0;
        User user = ServiceHelper.getUserByUsername(username, userMapper);
        if (user == null) {
            return 0;
        }
        UserOrganizationExample example = new UserOrganizationExample();
        example.or().andUserIdEqualTo(user.getId());
        mapper.deleteByExample(example);
        return result;
    }

    /**
     * 刪除指定用戶的所有組織關係
     *
     * @param userId 用戶id
     * @return
     */
    public int deleteUserOrganizationByUserId(String userId) {
        UserOrganizationExample example = new UserOrganizationExample();
        example.or().andUserIdEqualTo(userId);
        return mapper.deleteByExample(example);
    }

    /**
     * 刪除指定用戶的所有大屏組織關係
     *
     * @param username 用戶id
     * @return
     */
    public int deleteBsUserSiteByUsername(String username) {
        TzhBsUserSiteExample example = new TzhBsUserSiteExample();
        example.or().andUsernameEqualTo(username);
        return tzhBsUserSiteMapper.deleteByExample(example);
    }

}
