package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.mapper.TzhEmissionReductionHeadCustomMapper;
import com.csci.tzh.mapper.TzhEmissionReductionHeadMapper;
import com.csci.tzh.mapper.TzhEmissionReductionMapper;
import com.csci.tzh.model.TzhEmissionReduction;
import com.csci.tzh.model.TzhEmissionReductionExample;
import com.csci.tzh.model.TzhEmissionReductionHead;
import com.csci.tzh.model.TzhEmissionReductionHeadExample;
import com.csci.tzh.model.TzhEmissionReductionHeadExample.Criteria;
import com.csci.tzh.qo.TzhEmissionReductionHeadPageableQO;
import com.csci.tzh.vo.TzhEmissionReductionHeadVO;
import com.github.houbb.heaven.util.lang.StringUtil;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhEmissionReductionHeadService {

	@Autowired
	private TzhEmissionReductionHeadMapper headMapper;

	@Autowired
	private TzhEmissionReductionHeadCustomMapper customHeadMapper;
	@Autowired
	private TzhEmissionReductionMapper mapper;

	public List<TzhEmissionReductionHead> selectByExample(TzhEmissionReductionHeadExample example) {
		return headMapper.selectByExample(example);
	}

	public ResultPage<TzhEmissionReductionHeadVO> list(TzhEmissionReductionHeadPageableQO qo) {
		PageHelper.startPage(qo.getCurPage(), qo.getPageSize(), "ERH.SiteName, PC.CategoryName, CEL.Name");
		List<TzhEmissionReductionHeadVO> lst = customHeadMapper.list(qo.getSiteName(),
				qo.getProtocol(), qo.getCategoryName(), qo.getCarbonEmissionLocation());
		ResultPage<TzhEmissionReductionHeadVO> resultPage = new ResultPage<>(lst, true);
		return resultPage;
	}

	@Transactional(rollbackFor = Exception.class)
	public TzhEmissionReductionHead save(TzhEmissionReductionHead headModel) throws Exception {
		UserInfo currentUser = ContextUtils.getCurrentUser();
		String originalId = headModel.getId();
		String newId = "";

		//刪除數據
		if(headModel.getId() != null) {
			this.delete(headModel.getId());
		}

		// 插入數據
		if(headModel.getIsdeleted() == false) {
			newId = UUID.randomUUID().toString();
			headModel.setId(newId);
			headModel.setCreatedby(currentUser.getUsername());
			headModel.setCreatedtime(LocalDateTime.now());
			headMapper.insertSelective(headModel);
		}

		// 子表數據關聯更新
		if(StringUtil.isNotEmpty(originalId)) {
			// 查詢子表數據
			TzhEmissionReductionExample example = new TzhEmissionReductionExample();
			TzhEmissionReductionExample.Criteria criteria = example.or();
			criteria.andHeadidEqualTo(originalId);
			criteria.andIsdeletedEqualTo(false);
			List<TzhEmissionReduction> lstModel = mapper.selectByExample(example);

			// 刪除子表數據
			for(TzhEmissionReduction model: lstModel) {
				model.setDeletedby(currentUser.getUsername());
				model.setDeletedtime(LocalDateTime.now());
				model.setIsdeleted(true);
				mapper.updateByExampleSelective(model, example);
			}

			if(StringUtil.isNotEmpty(newId)) {
				// 插入子表數據
				for(TzhEmissionReduction model: lstModel) {
					model.setId(UUID.randomUUID().toString());
					model.setHeadid(newId);
					model.setCreatedby(currentUser.getUsername());
					model.setCreatedtime(LocalDateTime.now());
					model.setDeletedby(null);
					model.setDeletedtime(null);
					model.setIsdeleted(false);
					mapper.insertSelective(model);
				}
			}

		}

		return headModel;
	}

	@Transactional(rollbackFor = Exception.class)
	public int delete(String id) {
		int result = 0;

		TzhEmissionReductionHeadExample example = new TzhEmissionReductionHeadExample();
		Criteria criteria = example.or();
		criteria.andIdEqualTo(id);

		TzhEmissionReductionHead x = new TzhEmissionReductionHead();
		x.setIsdeleted(true);

		result += headMapper.updateByExampleSelective(x, example);

		return result;
	}
}
