package com.csci.susdev.service;

import cn.hutool.core.math.MathUtil;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.*;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.AmbientEnergyBillConverter;
import com.csci.susdev.qo.AmbientEnergyBillQO;
import com.csci.susdev.qo.AttachmentListQO;
import com.csci.susdev.qo.SyncAmbientEnergyBillQO;
import com.csci.susdev.util.DateUtils;
import com.csci.susdev.util.DemoUtils;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.util.StringUtil;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.susdev.vo.AmbientEnergyBillVO;
import com.csci.susdev.vo.AmbientHeadVO;
import com.csci.susdev.vo.AttachmentVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;
import static com.csci.susdev.service.ServiceHelper.validateMonth;

@Service
@LogMethod
public class AmbientEnergyBillService {

    @Resource
    private AmbientEnergyBillMapper ambientEnergyBillMapper;

    @Resource
    private AmbientEnergyBillCustomMapper ambientEnergyBillCustomMapper;

    @Resource
    private AmbientService ambientService;

    @Resource
    private AttachmentService attachmentService;

    @Resource
    private MinioAttachmentMapper minioAttachmentMapper;

    @Resource
    private AttachmentMapper attachmentMapper;

    @Resource
    private AmbientDetailMapper ambientDetailMapper;

    /**
     * 保存记录
     *
     * @param ambientEnergyBillVO
     * @return
     */
    public String saveAmbientEnergyBill(AmbientEnergyBillVO ambientEnergyBillVO) {

        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        workflowControlService.throwIfHasWorkflowControl(ambientEnergyBillVO.getHeadId(), "本期数据已经提交，不能进行新增或修改");

        if (StringUtils.isBlank(ambientEnergyBillVO.getId())) {
            // 新增
            return doAdd(ambientEnergyBillVO);
        } else {
            checkExist(ambientEnergyBillVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(ambientEnergyBillVO);
        }
    }

    /**
     * 复制指定的记录
     *
     * @param id
     * @return
     */
    public AmbientEnergyBill duplicateAmbientEnergyBill(String id) {
        AmbientEnergyBill ambientEnergyBill = ambientEnergyBillMapper.selectByPrimaryKey(id);
        checkExist(ambientEnergyBill, "未找到对应的记录");

        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        workflowControlService.throwIfHasWorkflowControl(ambientEnergyBill.getHeadId(), "本期数据已经提交，不能进行复制操作");

        ambientEnergyBill.setId(null);
        ambientEnergyBill.setCreationTime(null);
        ambientEnergyBill.setCreateUserId(null);
        ambientEnergyBill.setCreateUsername(null);
        ambientEnergyBill.setLastUpdateVersion(0);
        ambientEnergyBill.setLastUpdateTime(null);
        ambientEnergyBill.setLastUpdateUserId(null);
        ambientEnergyBill.setLastUpdateUsername(null);
        ambientEnergyBillMapper.insert(ambientEnergyBill);
        return ambientEnergyBill;
    }

    public List<Map<String, String>> calMonthlyConsumption(String headId) {
        return ambientEnergyBillCustomMapper.calMonthlyConsumption(headId);
    }

    /**
     * 查询记录
     *
     * @return
     */
    public ResultPage<AmbientEnergyBillVO> listAmbientEnergyBill(AmbientEnergyBillQO ambientEnergyBillQO) {
        checkExist(ambientEnergyBillQO.getHeadId(), "主表记录ID不能为空");
        AmbientEnergyBillExample example = new AmbientEnergyBillExample();
        example.or().andHeadIdEqualTo(ambientEnergyBillQO.getHeadId());

        if (StringUtils.isBlank(ambientEnergyBillQO.getSortName())) {
            example.setOrderByClause("from_time, to_time, bill_no");
        } else {
            String columnName = getColumnName(ambientEnergyBillQO.getSortName());
            if (StringUtils.isBlank(columnName)) {
                throw new ServiceException("排序字段不正确");
            }
            if (StringUtils.equals("asc", ambientEnergyBillQO.getSortOrder())) {
                example.setOrderByClause(columnName + " asc");
            } else {
                example.setOrderByClause(columnName + " desc");
            }
        }

        PageHelper.startPage(ambientEnergyBillQO.getCurPage(), ambientEnergyBillQO.getPageSize());
        List<AmbientEnergyBill> ambientEnergyBills = ambientEnergyBillMapper.selectByExample(example);
        return new ResultPage<>(ambientEnergyBills, ambientEnergyBills.stream().map(AmbientEnergyBillConverter::convert).collect(Collectors.toList()));
    }

    private String getColumnName(String sortName) {
        if (StringUtils.isBlank(sortName)) {
            return null;
        }
        switch (sortName) {
            case "type":
                return "type";
            case "billNo":
                return "bill_no";
            case "fromTime":
                return "from_time";
            case "toTime":
                return "to_time";
            case "consumption":
                return "consumption";
            case "attachmentId":
                return "attachment_id";
            default:
                return null;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    private String doAdd(AmbientEnergyBillVO ambientEnergyBillVO) {
        AmbientEnergyBill ambientEnergyBill = AmbientEnergyBillConverter.convert(ambientEnergyBillVO);
        ambientEnergyBillMapper.insertSelective(ambientEnergyBill);
        return ambientEnergyBill.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    private String doUpdate(AmbientEnergyBillVO ambientEnergyBillVO) {
        AmbientEnergyBill ambientEnergyBill = AmbientEnergyBillConverter.convert(ambientEnergyBillVO);
        AmbientEnergyBillExample example = new AmbientEnergyBillExample();
        example.or().andIdEqualTo(ambientEnergyBillVO.getId()).andLastUpdateVersionEqualTo(ambientEnergyBillVO.getLastUpdateVersion());
        int updateCount = ambientEnergyBillMapper.updateByExampleSelective(ambientEnergyBill, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return ambientEnergyBill.getId();
    }

    public AmbientEnergyBillVO getAmbientEnergyBillById(String id) {
        AmbientEnergyBill ambientEnergyBill = ambientEnergyBillMapper.selectByPrimaryKey(id);
        if (ambientEnergyBill == null) {
            throw new ServiceException("数据不存在");
        }
        return AmbientEnergyBillConverter.convert(ambientEnergyBill);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteAmbientEnergyBillById(String id) {
        checkExist(id, "id不能为空");
        ambientEnergyBillMapper.deleteByPrimaryKey(id);

    }

    public List<AmbientEnergyBill> selectByExample(AmbientEnergyBillExample example) {
        return ambientEnergyBillMapper.selectByExample(example);
    }

    // 根据组织机构id及年份查询记录
    public List<AmbientEnergyBill> listAmbientEnergyBillByOrgIdAndYear(String orgId, Integer year) {
        // first, query head record by org id and year
        /*AmbientHeadExample headExample = new AmbientHeadExample();
        headExample.or().and

        AmbientEnergyBillExample example = new AmbientEnergyBillExample();
        example.or().andOrgIdEqualTo(orgId).andYearValueEqualTo(year);
        return ambientEnergyBillMapper.selectByExample(example);*/
        throw new ServiceException("未实现");
    }

    /**
     * 同步水电费数据
     * <AUTHOR>
     * @date 2025/1/22 16:38
     * @param syncAmbientEnergyBillQO
     */
    @Transactional(rollbackFor = Exception.class)
    public void synchronizationData(SyncAmbientEnergyBillQO syncAmbientEnergyBillQO) {
        // 1、先查询当前的环境绩效数据
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        workflowControlService.throwIfHasWorkflowControl(syncAmbientEnergyBillQO.getHeadId(), "本期数据已经提交，不能进行同步");
        // 2、根据组织架构id、年份、月份查询环境绩效
        AmbientHeadVO syncAmbientHeadVO = ambientService.get(syncAmbientEnergyBillQO.getOrganizationId(), syncAmbientEnergyBillQO.getYear(), syncAmbientEnergyBillQO.getMonth());
        AmbientEnergyBillExample example = new AmbientEnergyBillExample();
        example.or().andHeadIdEqualTo(syncAmbientHeadVO.getId());
        example.setOrderByClause("from_time, to_time, bill_no");
        // 3、查出要同步月份的水电费
        List<AmbientEnergyBill> syncAmbientEnergyBills = ambientEnergyBillMapper.selectByExample(example);
        UserInfo userInfo = ContextUtils.getCurrentUser();
        if (CollectionUtils.isNotEmpty(syncAmbientEnergyBills)) {
            AttachmentListQO qo = new AttachmentListQO();
            qo.setSection("環境績效");
            for (AmbientEnergyBill oldBill : syncAmbientEnergyBills) {
                qo.setRefId(oldBill.getHeadId());
                qo.setCategory(oldBill.getId());
                // 新增新的水电费
                AmbientEnergyBill newAmbientEnergyBill = new AmbientEnergyBill();
                BeanUtils.copyProperties(oldBill, newAmbientEnergyBill);
                newAmbientEnergyBill.setId(null);
                newAmbientEnergyBill.setHeadId(syncAmbientEnergyBillQO.getHeadId());
                ambientEnergyBillMapper.insertSelective(newAmbientEnergyBill);
                // 查询是否有附件
                List<AttachmentVO> attachmentVOS = attachmentService.newList(qo);
                if (CollectionUtils.isNotEmpty(attachmentVOS)) {
                    // 有附件复制水电费附件
                    for (AttachmentVO oldAttachment : attachmentVOS) {
                        // 生成模型
                        if (StringUtils.isNotEmpty(oldAttachment.getMinioFileName())) {
                            MinioAttachment x = new MinioAttachment();
                            BeanUtils.copyProperties(oldAttachment, x);
                            x.setId(null);
                            x.setRefId(newAmbientEnergyBill.getHeadId());
                            x.setCategory(newAmbientEnergyBill.getId());
                            x.setCreateUserId(userInfo.getId());
                            x.setCreateUsername(userInfo.getUsername());
                            x.setCreationTime(LocalDateTime.now());
                            minioAttachmentMapper.insert(x);
                        }else {
                            Attachment x = new Attachment();
                            BeanUtils.copyProperties(oldAttachment, x);
                            Attachment primaryKey = attachmentMapper.selectByPrimaryKey(oldAttachment.getId());
                            x.setId(null);
                            x.setRefId(newAmbientEnergyBill.getHeadId());
                            x.setCategory(newAmbientEnergyBill.getId());
                            x.setData(primaryKey.getData());
                            x.setCreateUserId(userInfo.getId());
                            x.setCreateUsername(userInfo.getUsername());
                            x.setCreationTime(LocalDateTime.now());
                            attachmentMapper.insert(x);
                        }
                    }
                }
            }
        }
    }


    /**
     * 更新环境绩效明细的水电费：3D、4U
     * <AUTHOR>
     * @date 2025/2/13 16:51
     * @param headId
     */
    private void updateAmbientWaterAndElectricity(String headId) {
        AmbientHead ambientHead = ambientService.selectByPrimaryKey(headId);
        if (ambientHead == null) {
            throw new ServiceException("环境绩效不存在！");
        }
        List<Map<String, String>> mapList = ambientEnergyBillCustomMapper.calMonthlyConsumption(headId);
        List<String> unitCodeList = new ArrayList<>();
        unitCodeList.add("3D");
        unitCodeList.add("4U");
        AmbientDetailExample detailExample = new AmbientDetailExample();
        detailExample.or().andHeadIdEqualTo(headId)
                .andUnitCodeIn(unitCodeList);
        List<AmbientDetail> ambientDetails = ambientDetailMapper.selectByExample(detailExample);
        if (CollectionUtils.isNotEmpty(ambientDetails)) {
            String recordYear = StringUtil.valueOf(ambientHead.getYear());
            for (AmbientDetail ambientDetail : ambientDetails) {
                BigDecimal seasonValue1 = new BigDecimal("0.00");
                BigDecimal seasonValue2 = new BigDecimal("0.00");
                BigDecimal seasonValue3 = new BigDecimal("0.00");
                BigDecimal seasonValue4 = new BigDecimal("0.00");
                BigDecimal yearTotalValue = new BigDecimal("0.00");
                for (Map<String, String> map : mapList) {
                    String recordYearMonth = StringUtil.valueOf(map.get("recordYearMonth"));
                    String year = recordYearMonth.substring(0, 4);
                    String month = recordYearMonth.substring(4);
                    if (StringUtil.equals(recordYear, year)) {
                        String avgConsumption = StringUtil.valueOf(map.get("avgConsumption"));
                        String type = StringUtil.valueOf(map.get("type"));
                        BigDecimal decimal = new BigDecimal(avgConsumption).setScale(2, RoundingMode.HALF_UP);
                        if ("electricity".equals(type) && "4U".equals(ambientDetail.getUnitCode())) {
                            switch (month) {
                                case "01":
                                    ambientDetail.setMonthValue1(StringUtil.valueOf(decimal));
                                    seasonValue1 = DemoUtils.add(seasonValue1, decimal);
                                    break;
                                case "02":
                                    ambientDetail.setMonthValue2(StringUtil.valueOf(decimal));
                                    seasonValue1 = DemoUtils.add(seasonValue1, decimal);
                                    break;
                                case "03":
                                    ambientDetail.setMonthValue3(StringUtil.valueOf(decimal));
                                    seasonValue1 = DemoUtils.add(seasonValue1, decimal);
                                    break;
                                case "04":
                                    ambientDetail.setMonthValue4(StringUtil.valueOf(decimal));
                                    seasonValue2 = DemoUtils.add(seasonValue2, decimal);
                                    break;
                                case "05":
                                    ambientDetail.setMonthValue5(StringUtil.valueOf(decimal));
                                    seasonValue2 = DemoUtils.add(seasonValue2, decimal);
                                    break;
                                case "06":
                                    ambientDetail.setMonthValue6(StringUtil.valueOf(decimal));
                                    seasonValue2 = DemoUtils.add(seasonValue2, decimal);
                                    break;
                                case "07":
                                    ambientDetail.setMonthValue7(StringUtil.valueOf(decimal));
                                    seasonValue3 = DemoUtils.add(seasonValue3, decimal);
                                    break;
                                case "08":
                                    ambientDetail.setMonthValue8(StringUtil.valueOf(decimal));
                                    seasonValue3 = DemoUtils.add(seasonValue3, decimal);
                                    break;
                                case "09":
                                    ambientDetail.setMonthValue9(StringUtil.valueOf(decimal));
                                    seasonValue3 = DemoUtils.add(seasonValue3, decimal);
                                    break;
                                case "10":
                                    ambientDetail.setMonthValue10(StringUtil.valueOf(decimal));
                                    seasonValue4 = DemoUtils.add(seasonValue4, decimal);
                                    break;
                                case "11":
                                    ambientDetail.setMonthValue11(StringUtil.valueOf(decimal));
                                    seasonValue4 = DemoUtils.add(seasonValue4, decimal);
                                    break;
                                case "12":
                                    ambientDetail.setMonthValue12(StringUtil.valueOf(decimal));
                                    seasonValue4 = DemoUtils.add(seasonValue4, decimal);
                                    break;
                                default:
                                    break;
                            }
                            yearTotalValue = DemoUtils.add(yearTotalValue, decimal);
                            
                        }
                        if ("water".equals(type) && "3D".equals(ambientDetail.getUnitCode())) {
                            switch (month) {
                                case "01":
                                    ambientDetail.setMonthValue1(StringUtil.valueOf(decimal));
                                    seasonValue1 = DemoUtils.add(seasonValue1, decimal);
                                    break;
                                case "02":
                                    ambientDetail.setMonthValue2(StringUtil.valueOf(decimal));
                                    seasonValue1 = DemoUtils.add(seasonValue1, decimal);
                                    break;
                                case "03":
                                    ambientDetail.setMonthValue3(StringUtil.valueOf(decimal));
                                    seasonValue1 = DemoUtils.add(seasonValue1, decimal);
                                    break;
                                case "04":
                                    ambientDetail.setMonthValue4(StringUtil.valueOf(decimal));
                                    seasonValue2 = DemoUtils.add(seasonValue2, decimal);
                                    break;
                                case "05":
                                    ambientDetail.setMonthValue5(StringUtil.valueOf(decimal));
                                    seasonValue2 = DemoUtils.add(seasonValue2, decimal);
                                    break;
                                case "06":
                                    ambientDetail.setMonthValue6(StringUtil.valueOf(decimal));
                                    seasonValue2 = DemoUtils.add(seasonValue2, decimal);
                                    break;
                                case "07":
                                    ambientDetail.setMonthValue7(StringUtil.valueOf(decimal));
                                    seasonValue3 = DemoUtils.add(seasonValue3, decimal);
                                    break;
                                case "08":
                                    ambientDetail.setMonthValue8(StringUtil.valueOf(decimal));
                                    seasonValue3 = DemoUtils.add(seasonValue3, decimal);
                                    break;
                                case "09":
                                    ambientDetail.setMonthValue9(StringUtil.valueOf(decimal));
                                    seasonValue3 = DemoUtils.add(seasonValue3, decimal);
                                    break;
                                case "10":
                                    ambientDetail.setMonthValue10(StringUtil.valueOf(decimal));
                                    seasonValue4 = DemoUtils.add(seasonValue4, decimal);
                                    break;
                                case "11":
                                    ambientDetail.setMonthValue11(StringUtil.valueOf(decimal));
                                    seasonValue4 = DemoUtils.add(seasonValue4, decimal);
                                    break;
                                case "12":
                                    ambientDetail.setMonthValue12(StringUtil.valueOf(decimal));
                                    seasonValue4 = DemoUtils.add(seasonValue4, decimal);
                                    break;
                                default:
                                    break;
                            }
                            yearTotalValue = DemoUtils.add(yearTotalValue, decimal);
                        }

                    }
                }
                ambientDetail.setSeasonValue1(StringUtil.valueOf(seasonValue1));
                ambientDetail.setSeasonValue2(StringUtil.valueOf(seasonValue2));
                ambientDetail.setSeasonValue3(StringUtil.valueOf(seasonValue3));
                ambientDetail.setSeasonValue4(StringUtil.valueOf(seasonValue4));
                ambientDetail.setYearTotalValue(StringUtil.valueOf(yearTotalValue));
                ambientDetailMapper.updateByPrimaryKeySelective(ambientDetail);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public List<String> saveAmbientEnergyBillList(List<AmbientEnergyBillVO> ambientEnergyBillVOList) {
        if (CollectionUtils.isEmpty(ambientEnergyBillVOList)) {
            throw new ServiceException("数据为空！");
        }
        if(ambientEnergyBillVOList.size() > 5000) {
            throw new ServiceException("每次只能新增5000條數據，請分開兩個檔案處理");
        }
        AmbientEnergyBillVO energyBillVO = ambientEnergyBillVOList.get(0);
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        workflowControlService.throwIfHasWorkflowControl(energyBillVO.getHeadId(), "本期数据已经提交，不能进行导入");

        List<String> ids = new ArrayList<>();
        for(int i = 0; i < ambientEnergyBillVOList.size(); i++) {
            AmbientEnergyBillVO ambientEnergyBillVO = ambientEnergyBillVOList.get(i);
            checkExist(ambientEnergyBillVO.getHeadId(), "[第" + (i + 1) + "行]" + "环境绩效id不能为空");
            checkExist(ambientEnergyBillVO.getType(), "[第" + (i + 1) + "行]" + "单据类型不能为空");
            checkExist(ambientEnergyBillVO.getBillNo(), "[第" + (i + 1) + "行]" + "账号单不能为空");
            checkExist(ambientEnergyBillVO.getFromTime(), "[第" + (i + 1) + "行]" + "开始时间不能为空");
            checkExist(ambientEnergyBillVO.getToTime(), "[第" + (i + 1) + "行]" + "结束时间不能为空");
            checkExist(ambientEnergyBillVO.getConsumption(), "[第" + (i + 1) + "行]" + "用量不能为空");
            int compare = DateUtils.compare(ambientEnergyBillVO.getFromTime(), ambientEnergyBillVO.getToTime());
            if (compare == 1) {
                throw new ServiceException("[第" + (i + 1) + "行]" + "开始时间不能大于结束时间");
            }
            if (DemoUtils.compareWithNullAsZero(ambientEnergyBillVO.getConsumption(), BigDecimal.ZERO) == -1) {
                throw new ServiceException("[第" + (i + 1) + "行]" + "用量不能小于0");
            }

            if (StringUtils.isBlank(ambientEnergyBillVO.getId())) {
                // 新增
                ids.add(doAdd(ambientEnergyBillVO));
            } else {
                checkExist(ambientEnergyBillVO.getLastUpdateVersion(), "[第" + i + "行]" + "更新时版本号不能为空");
                // 修改
                ids.add(doUpdate(ambientEnergyBillVO));
            }
        }
        return ids;
    }
}
