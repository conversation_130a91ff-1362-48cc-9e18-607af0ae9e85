package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.NamingConventionEnum;
import com.csci.susdev.mapper.FormDetailCustomMapper;
import com.csci.susdev.mapper.FormDetailMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.FormDetailConverter;
import com.csci.susdev.modelcovt.ProtocolConverter;
import com.csci.susdev.qo.FormDetailQO;
import com.csci.susdev.util.MybatisHelper;
import com.csci.susdev.vo.FormDetailVO;
import com.csci.susdev.vo.ProtocolDetailVO;
import com.csci.susdev.vo.ProtocolVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class FormDetailService {

    @Resource
    private FormDetailMapper formDetailMapper;

    @Resource
    private FormDetailCustomMapper formDetailCustomMapper;

    @Resource
    private ProtocolConfigurationService protocolConfigurationService;

    public FormDetail selectByPrimaryKey(String id) {
        return formDetailMapper.selectByPrimaryKey(id);
    }

    public ResultPage<FormDetailVO> listFormDetail(FormDetailQO formDetailQO) {
        if (StringUtils.isNotBlank(formDetailQO.getOrderBy())) {
            List<String> fieldNames = Arrays.stream(FormDetailVO.class.getDeclaredFields()).map(field -> field.getName()).collect(Collectors.toList());
            if(MybatisHelper.checkSqlInjectionForOrderBy(formDetailQO.getOrderBy(), fieldNames, NamingConventionEnum.CAMO)) {
                throw new ServiceException("orderBy 有SQL注入行為");
            }
        }

        PageHelper.startPage(formDetailQO.getCurPage(), formDetailQO.getPageSize());

        List<FormDetailVO> formDetailVOS = new ArrayList<>();
                formDetailVOS = formDetailCustomMapper.listAmbientFormDetail(formDetailQO);

        return new ResultPage<>(formDetailVOS, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public String saveFormDetail(FormDetailVO formDetailVO) {
        if (StringUtils.isBlank(formDetailVO.getId())) {
            // 新增
            return doAdd(formDetailVO);
        } else {
            checkExist(formDetailVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(formDetailVO);
        }
    }


    private String doAdd(FormDetailVO formDetailVO) {
        FormDetail formDetail = FormDetailConverter.convertToModel(formDetailVO);
        formDetailMapper.insertSelective(formDetail);
        return formDetail.getId();
    }

    private String doUpdate(FormDetailVO formDetailVO) {

        FormDetail originalRecord = formDetailMapper.selectByPrimaryKey(formDetailVO.getId());
        FormDetail formDetail = FormDetailConverter.convertToModelWithBase(formDetailVO, originalRecord);

        FormDetailExample formDetailExample = new FormDetailExample();
        formDetailExample.or().andIdEqualTo(formDetailVO.getId()).andLastUpdateVersionEqualTo(formDetailVO.getLastUpdateVersion());


        int updateCount = formDetailMapper.updateByExample(formDetail, formDetailExample);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }

        return formDetail.getId();
    }


    @Transactional(rollbackFor = Exception.class)
    public void deleteFormDetail(String id) {
        FormDetail record = selectByPrimaryKey(id);
        ProtocolConfigurationExample configurationExample = new ProtocolConfigurationExample();
        configurationExample.or().andFormDetailIdEqualTo(id).andIsDeletedEqualTo(false);
        List<ProtocolConfiguration> protocolConfigurations = protocolConfigurationService.selectByExample(configurationExample);
        if (CollectionUtils.isNotEmpty(protocolConfigurations)) {
            throw new ServiceException("该表单明细已在协议配置中被使用，不能删除！");
        }

        record.setIsDeleted(true);
        formDetailMapper.updateByPrimaryKeySelective(record);
    }
}
