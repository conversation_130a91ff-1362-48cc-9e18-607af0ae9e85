package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.AirportCustomMapper;
import com.csci.susdev.mapper.AirportMapper;
import com.csci.susdev.model.Airport;
import com.csci.susdev.model.AirportExample;
import com.csci.susdev.model.FlightInfo;
import com.csci.susdev.model.FlightInfoExample;
import com.csci.susdev.qo.AirportQO;
import com.csci.susdev.qo.FlightInfoQO;
import com.csci.susdev.util.JsonUtils;
import com.csci.susdev.vo.AirportVO;
import com.fasterxml.jackson.core.type.TypeReference;
import io.swagger.v3.core.util.Json;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class AirportService {
	@Autowired
	private AirportMapper airportMapper;
	@Autowired
	private AirportCustomMapper airportCustomMapper;
	private static final Logger logger = LoggerFactory.getLogger(AirportService.class);


	public List<AirportVO> list(AirportQO qo) {
		List<AirportVO> airportVOList = airportCustomMapper.list(qo.getStartPlace(), qo.getKeyword());
		return airportVOList;
	}

	public void importAirportData() throws Exception {

		String airportJsonEn = new String(Files.readAllBytes(Paths.get("D:\\JAVA\\sus-dev\\doc\\data\\airport\\airport_en.txt")));
		List<Airport> lstAirportEn = JsonUtils.getObjectMapper().readValue(airportJsonEn, new TypeReference<List<Airport>>(){});
		System.out.println(Json.pretty(lstAirportEn));

		for(Airport airportEn : lstAirportEn) {
			airportEn.setId(UUID.randomUUID().toString());
			airportEn.setCreationTime(LocalDateTime.now());
		}

		String airportJsonSc = new String(Files.readAllBytes(Paths.get("D:\\JAVA\\sus-dev\\doc\\data\\airport\\airport_sc.txt")));
		List<Airport> lstAirportSc = JsonUtils.getObjectMapper().readValue(airportJsonSc, new TypeReference<List<Airport>>(){});
		System.out.println(Json.pretty(airportJsonSc));

		for(Airport airportEn : lstAirportEn) {
			for(Airport airportSc : lstAirportSc) {
				if(airportEn.getCode().equals(airportSc.getCode())) {
					airportEn.setNameSc(airportSc.getNameSc());
				}
			}
		}

		String airportJsonTc = new String(Files.readAllBytes(Paths.get("D:\\JAVA\\sus-dev\\doc\\data\\airport\\airport_tc.txt")));
		List<Airport> lstAirportTc = JsonUtils.getObjectMapper().readValue(airportJsonTc, new TypeReference<List<Airport>>(){});
		System.out.println(Json.pretty(airportJsonSc));

		for(Airport airportEn : lstAirportEn) {
			for(Airport airportTc : lstAirportTc) {
				if(airportEn.getCode().equals(airportTc.getCode())) {
					airportEn.setName(airportTc.getName());
				}
			}
		}

		Connection conn1 = Jsoup.connect("https://applications.icao.int/icec/Home/Compute");
		Document doc1 = conn1
				.data("userID", "ICEC")
				.data("unitofMeasureTag", "1")
				.data("triptype", "One Way")
				.data("cabinclass", "Economy")
				.data("noofpassenger", "1")
				.data("noofArrAirport", "1")
				.data("depCode", "LCG")
				.data("arrCode1", "BIO")
				.data("arrCode2", "#")
				.data("arrCode3", "#")
				.data("TypeCargoPassenger", "Passenger")
				.data("NumberKg", "1")
				.data("BellyFreighter", "Freighter")
				.ignoreContentType(true)
				.post();
		Map<String, String> cookies = conn1.response().cookies();
		Document doc2 = Jsoup.connect("https://applications.icao.int/icec/Home/ChildResultDetailView")
				.data("type", "Metric")
				.cookies(cookies)
				.post();
		List<String> lstFlightDetail = doc2.getElementsByClass("lblResultDetail").eachText();
	}
}
