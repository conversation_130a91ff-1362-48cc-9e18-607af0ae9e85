package com.csci.susdev.service;

import cn.hutool.core.util.StrUtil;
import com.csci.common.exception.ServiceException;
import com.csci.common.util.CustomGsonBuilder;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.*;
import com.csci.susdev.model.*;
import com.csci.susdev.model.OrganizationExample.Criteria;
import com.csci.susdev.modelcovt.OrganizationConverter;
import com.csci.susdev.qo.*;
import com.csci.susdev.util.MybatisHelper;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.susdev.vo.*;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;
import static com.csci.susdev.service.ServiceHelper.getUserByUsername;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class OrganizationService {
	/**
	 * 日志记录对象
	 */
	private static final Logger logger = LoggerFactory.getLogger(OrganizationService.class);

	private final Gson gson = CustomGsonBuilder.createGson();

	@Autowired
	private OrganizationMapper organizationMapper;
	/**
	 * 自定义组织机构mapper
	 */
	@Autowired
	private OrganizationCustomMapper organizationCustomMapper;

	@Autowired
	private UserMapper userMapper;

	@Autowired
	private UserOrganizationMapper userOrganizationMapper;
	@Resource
	private OrganizationMapperCustom organizationMapperCustom;

	@Autowired
	private UserOrganizationService userOrganizationService;

	public List<Organization> selectByExample(OrganizationExample example) {
		return organizationMapper.selectByExample(example);
	}

	public Organization selectByPrimaryKey(String id) {
		return organizationMapper.selectByPrimaryKey(id);
	}

	/**
	 * 查询组织机构列表；
	 * 会根据当前用户的权限，过滤掉不可见的组织机构；
	 *
	 * @param organizationQO
	 * @return
	 */
	public ResultPage<OrganizationVO> listOrganization(OrganizationPageableQO organizationQO) {
		if (Objects.equals(organizationQO.getPageSize(), 0)) {
			organizationQO.setPageSize(Integer.MAX_VALUE);
		}
		List<String> lstOrganizationId = new ArrayList<>();
		if (StringUtils.isNotBlank(organizationQO.getUsername())) {
			User user = ServiceHelper.getUserByUsername(organizationQO.getUsername(), userMapper);
			if (user == null) {
				return new ResultPage<>(new ArrayList<Organization>());
			}
			List<UserOrganization> lstUserOrganization = ServiceHelper.listUserOrganization(user.getId(), userOrganizationMapper);
			for (UserOrganization userOrganization : lstUserOrganization) {
				lstOrganizationId.add(userOrganization.getOrganizationId());
			}
		}
		PageHelper.startPage(organizationQO.getCurPage(), organizationQO.getPageSize());
		List<OrganizationVO> resultList = organizationCustomMapper.listOrganization(lstOrganizationId, organizationQO.getParentId(),
				organizationQO.getKeyword(), organizationQO.getShowCompanyOnly());
		ResultPage<OrganizationVO> resultPage = new ResultPage<>(resultList);
		resultPage.setList(resultList);
		return resultPage;
	}

	/**
	 * 根据 keyword 查询所有组织机构信息
	 * 忽略权限配置
	 *
	 * @param keywordPageQO
	 * @return
	 */
	public ResultPage<OrganizationVO> listAllOrgs(KeywordPageQO keywordPageQO) {
		OrganizationExample example = new OrganizationExample();
		Criteria criteria = example.or();

		if (StringUtils.isNotBlank(keywordPageQO.getKeyword())) {
			criteria.andNameLike(MybatisHelper.like(keywordPageQO.getKeyword()));
		}

		criteria.andIsDeletedEqualTo(false);

		example.setOrderByClause("no");

		PageHelper.startPage(keywordPageQO.getCurPage(), keywordPageQO.getPageSize());
		List<Organization> lstOrganization = organizationMapper.selectByExample(example);
		ResultPage<OrganizationVO> resultPage = new ResultPage<>(lstOrganization);
		resultPage.setList(lstOrganization.stream().map(OrganizationConverter::convert).collect(Collectors.toList()));
		return resultPage;
	}

	/**
	 * 保存 組織 信息, 如果是已经存在的记录就保存，否则新增
	 * 传入的参数id为空表示新增，否则表示更新
	 *
	 * @param organizationLst
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public List<String> saveOrganizationList(List<OrganizationVO> organizationLst) {
		List<String> idLst = new ArrayList<>();
		for (OrganizationVO organizationVO : organizationLst) {
			saveOrganization(organizationVO);
			idLst.add(organizationVO.getId());
		}
		return idLst;
	}

	/**
	 * 刪除 組織 記錄樹(所有子類及子類的子類)
	 *
	 * @param no
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public List<String> deleteOrganizationTree(String no) {
		UserInfo currentUser = ContextUtils.getCurrentUser();
		LocalDateTime now = LocalDateTime.now();

		OrganizationExample example = new OrganizationExample();
		Criteria criteria = example.or();
		criteria.andNoLike(no + "%");
		criteria.andIsDeletedEqualTo(false);
		List<Organization> organizationLst = organizationMapper.selectByExample(example);

		List<String> idLst = new ArrayList<>();
		for (Organization organization : organizationLst) {
			// 暂时移除这个限制；因为填报初期有很多重复的项目；提升韩清处理用户问题的效率；等以后组织机构数据稳定下来了再开启这个限制
			/*if (hasUser(organization.getId())) {
				throw new ServiceException(StringUtils.replace("組織[{userName}]下存在用戶，不能刪除", "{userName}", organization.getName()));
			}*/
			organization.setIsDeleted(true);
			organization.setLastUpdateTime(now);
			organization.setLastUpdateUsername(currentUser.getUsername());
			organizationMapper.updateByPrimaryKeySelective(organization);
			idLst.add(organization.getId());
		}
		return idLst;
	}

	private boolean hasUser(String organizationId) {
		UserOrganizationExample example = new UserOrganizationExample();
		example.or().andIsDeletedEqualTo(false).andOrganizationIdEqualTo(organizationId);
		return userOrganizationMapper.countByExample(example) > 0;
	}

	@Transactional(rollbackFor = Exception.class)
	public String moveOrg(OrganizationMoveQO organizationMoveQO) {
		checkExist(organizationMoveQO.getId(), "單位Id不能為空");
		checkExist(organizationMoveQO.getTargetParentId(), "目標父單位Id不能為空");

		Organization organization = organizationMapper.selectByPrimaryKey(organizationMoveQO.getId());
		checkExist(organization.getLastUpdateVersion(), "更新时版本号不能为空");
		if(organization.getIsDeleted()) {
			throw new ServiceException("無法操作，目標單位已被刪除");
		}
		organization.setParentId(organizationMoveQO.getTargetParentId());

		OrganizationExample example = new OrganizationExample();
		example.or().andIdEqualTo(organization.getId()).andLastUpdateVersionEqualTo(organization.getLastUpdateVersion());
		int updateCount = organizationMapper.updateByExample(organization, example);
		if (updateCount == 0) {
			throw new ServiceException("更新失败，可能是数据已经被删除");
		}

		doUpdate(organization);
		handleOrganizationNo(organizationMoveQO.getTargetParentId());

		return organization.getId();
	}

	/**
	 * 保存 組織 信息, 如果是已经存在的记录就保存，否则新增
	 * 传入的参数id为空表示新增，否则表示更新
	 *
	 * @param organizationVO
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public String saveOrganization(OrganizationVO organizationVO) {
		checkExist(organizationVO.getName(), "单位名称不能为空");

		Organization organization = OrganizationConverter.convert(organizationVO);
		organization.setIsDeleted(Boolean.FALSE);

		checkDuplicate(organization);
		if (StringUtils.isBlank(organization.getId())) {
			// 新增
			doAddOrg(organization);
			// addOrgToUser(ContextUtils.getCurrentUser().getId(), organization.getId());
		} else {
			// 更新
			doUpdate(organization);
		}
		return organization.getId();
	}

	private void doUpdate(Organization organization) {
		checkExist(organization.getLastUpdateVersion(), "更新时版本号不能为空");
		OrganizationExample example = new OrganizationExample();
		example.or().andIdEqualTo(organization.getId()).andLastUpdateVersionEqualTo(organization.getLastUpdateVersion());
		int updateCount = organizationMapper.updateByExample(organization, example);
		if (updateCount == 0) {
			throw new ServiceException("更新失败，可能是数据已经被删除");
		}
	}

	private void doAddOrg(Organization organization) {
		String key = "addOrganization" + organization.getParentId() + organization.getName();
		try {
			if (RedisLockUtil.lock(key)) {

				organization.setNo(generateNo(organization.getParentId()));
				organizationMapper.insertSelective(organization);
				// 創建組織架構的時候，分配權限給所有擁有該組織父級權限的人
				// 先根据父级id，查找拥有父级权限的用户
				List<String> orgUserIds = organizationMapperCustom.selectUserIdsByOrgId(organization.getParentId());
				if (CollectionUtils.isNotEmpty(orgUserIds)) {
					for (String userId : orgUserIds) {
						UserOrganization userOrganization = new UserOrganization();
						userOrganization.setOrganizationId(organization.getId());
						userOrganization.setUserId(userId);
						userOrganization.setIsDeleted(false);
						userOrganizationService.saveUserOrganization(userOrganization);
					}
				}
				List<String> bsOrgUserNameList = organizationMapperCustom.selectUserNamesByBsOrgId(organization.getParentId());
				if (CollectionUtils.isNotEmpty(bsOrgUserNameList)) {
					for (String username : bsOrgUserNameList) {
						TzhBsUserSite tzhBsUserSite = new TzhBsUserSite();
						tzhBsUserSite.setSiteid(organization.getId());
						tzhBsUserSite.setUsername(username);
						userOrganizationService.saveTzhBsUserSite(tzhBsUserSite);
					}
				}
			} else {
				throw new ServiceException("该单位名称的组织已经在添加中，请稍后再试");
			}
		} finally {
			RedisLockUtil.unlock(key);
		}
	}

	/**
	 * 验证是否重复
	 * 姓名、编号都不能重复
	 *
	 * @param organization 組織
	 */
	private void checkDuplicate(Organization organization) {
		if (StringUtils.isNotBlank(organization.getName())) {
			OrganizationExample example = new OrganizationExample();
			Criteria criteria = example.or();
			criteria.andNameEqualTo(organization.getName());
			criteria.andIsDeletedEqualTo(false);
			if (StringUtils.isBlank(organization.getParentId())) {
				criteria.andParentIdIsNull();
			} else {
				criteria.andParentIdEqualTo(organization.getParentId());
			}
			if (StringUtils.isNotBlank(organization.getId())) {
				criteria.andIdNotEqualTo(organization.getId());
			}
			if (organizationMapper.countByExample(example) > 0) {
				throw new ServiceException("该单位名称已经存在");
			}
		}
		if (StringUtils.isNotBlank(organization.getCode())) {
			OrganizationExample example = new OrganizationExample();
			Criteria criteria = example.or();
			criteria.andCodeEqualTo(organization.getCode());
			criteria.andIsDeletedEqualTo(false);
			if (StringUtils.isBlank(organization.getParentId())) {
				criteria.andParentIdIsNull();
			} else {
				criteria.andParentIdEqualTo(organization.getParentId());
			}
			if (StringUtils.isNotBlank(organization.getId())) {
				criteria.andIdNotEqualTo(organization.getId());
			}
			if (organizationMapper.countByExample(example) > 0) {
				throw new ServiceException("该单位编号已经存在");
			}
		}
	}

	private void addOrgToUser(String userId, String orgId) {
		UserOrganization userOrganization = new UserOrganization();
		userOrganization.setUserId(userId);
		userOrganization.setOrganizationId(orgId);
		userOrganizationMapper.insertSelective(userOrganization);
	}

	public boolean isExistBy(String no) {
		OrganizationExample example = new OrganizationExample();
		example.or().andNoEqualTo(no).andIsDeletedEqualTo(false);
		return organizationMapper.countByExample(example) > 0;
	}

	/**
	 * 查看所有组织机构(非樹狀)
	 *
	 * @param keywordQO
	 * @return
	 */
	public ResultPage<CompanyDeptVO> listAllOrganization(KeywordQO keywordQO) {
		SimpTradUtil simpTradUtil = new SimpTradUtil();
		String keywordTc = simpTradUtil.convert2Trad(keywordQO.getKeyword());
		String keywordSc = simpTradUtil.convert2Simp(keywordQO.getKeyword());
		PageHelper.startPage(keywordQO.getCurPage(), keywordQO.getPageSize());
		String pattern = simpTradUtil.generateLikePattern(keywordTc);
		String patternSc = simpTradUtil.generateLikePattern(keywordSc);
		List<CompanyDeptVO> list = organizationMapperCustom.selectCompanyDept(keywordTc, keywordSc, null, false, pattern, patternSc,false);
		return new ResultPage<>(list, true);
	}


	/**
	 * 根据用户id获取用户拥有查看权限的组织机构
	 *
	 * @param qo
	 * @return
	 */
	public ResultPage<CompanyDeptVO> listCurrentUsersOrganization(OrganizationSearchQO qo) {
		if(qo.getKeyword() == null) {
			qo.setKeyword("");
		}
		if(qo.getShowLeafOnly() == null) {
			qo.setShowLeafOnly(false);
		}
		if(qo.getShowCompanyOnly() == null) {
			qo.setShowCompanyOnly(false);
		}
		PageHelper.startPage(qo.getCurPage(), qo.getPageSize());
		List<CompanyDeptVO> list = listCompanyDept(qo.getKeyword(),
				ContextUtils.getCurrentUser().getId(), qo.getShowLeafOnly(), qo.getShowCompanyOnly());
		//拼接公司名称到项目名后：项目名（公司名）
		list.forEach(it -> {
			String nameWithCompanyName = StringUtils.isNotBlank(it.getCompanyName()) ? StrUtil.format("{}（{}）",
					it.getName(), it.getCompanyName()) : it.getName();
			it.setNameWithCompanyName(nameWithCompanyName);
		});
		return new ResultPage<>(list, true);
	}

	public List<CompanyDeptVO> listCompanyDept(String keyword, String userId, boolean showLeafOnly, boolean showCompanyOnly) {
		SimpTradUtil simpTradUtil = new SimpTradUtil();
		String keywordTc = simpTradUtil.convert2Trad(keyword);
		String keywordSc = simpTradUtil.convert2Simp(keyword);
		String pattern = simpTradUtil.generateLikePattern(keywordTc);
		String patternSc = simpTradUtil.generateLikePattern(keywordSc);
		List<CompanyDeptVO> list = organizationMapperCustom.selectCompanyDept(
				keywordTc, keywordSc, userId, showLeafOnly, pattern, patternSc, showCompanyOnly);
		return list;
	}

	/**
	 * 查询用户分配的部门/项目
	 * 组织机构树中的叶子节点
	 *
	 * @param userId
	 * @return
	 */
	public List<OrganizationVO> listDeptByUserId(String userId) {
		checkExist(userId, "用户id不能为空");
		return organizationMapperCustom.selectUserDepartments(userId).stream().map(OrganizationConverter::convert).collect(Collectors.toList());
	}

	/**
	 * 根据父级id获取组织机构
	 * 如果是超级管理员，返回所有组织机构，否则返回用户拥有查看权限的组织机构
	 *
	 * @param parentId 父级id
	 * @param pageNum  页码
	 * @param pageSize 每页数量
	 * @return
	 */
	public ResultPage<OrganizationVO> listOrgByParentId(String parentId, int pageNum, int pageSize) {
		checkExist(parentId, "父级组织id不能为空");

		OrganizationExampleCustom example = new OrganizationExampleCustom();

		OrganizationExampleCustom.CustomCriteria criteria = example.createCriteria();
		criteria.andParentIdEqualTo(parentId);
		criteria.andIsDeletedEqualTo(Boolean.FALSE);
		if (!ContextUtils.isSuperAdmin()) {
			// 非超级管理员只能查看自己的组织
			criteria.andExampleCondition(
					StringUtils.replace(" exists (select 1 from t_user_organization where user_id = '{userId}' and organization_id = t_organization.id) ", "{userId}", ContextUtils.getCurrentUser().getId()));

			// 不是超级管理员时同时允许修改自己创建的组织机构
			example.or().andIsDeletedEqualTo(Boolean.FALSE).andParentIdEqualTo(parentId).andCreateUserIdEqualTo(ContextUtils.getCurrentUser().getId());
		}

		// 或者是自己创建的组织

		example.setOrderByClause("sort asc, no asc");

		PageHelper.startPage(pageNum, pageSize);
		List<Organization> lstOrganization = organizationMapper.selectByExample(example);
		List<OrganizationVO> lstOrganizationVO = lstOrganization.stream().map(OrganizationConverter::convert).collect(Collectors.toList());
		return new ResultPage<>(lstOrganization, lstOrganizationVO);
	}

	/**
	 * 查询指定组织机构下的所有子组织机构
	 *
	 * @param parentId
	 * @return
	 */
	public List<Organization> listOrganizationTreeByParentId(String parentId) {
		checkExist(parentId, "父级组织id不能为空");
		Organization parentOrganization = organizationMapper.selectByPrimaryKey(parentId);
		if (Objects.isNull(parentOrganization)) {
			throw new ServiceException("父级组织不存在");
		}

		OrganizationExample example = new OrganizationExample();
		example.or().andNoLike(parentOrganization.getNo() + "%").andIsDeletedEqualTo(false);
		List<Organization> organizationList = organizationMapper.selectByExample(example);
		return organizationList;
	}

	/**
	 * 根据组织机构名称查询组织机构
	 *
	 * @param name 组织机构名称
	 * @return
	 */
	public Organization getOrganizationByName(String name) {
		return ServiceHelper.getOrganizationByName(name);
	}

	/**
	 * 根据父级 id 生成组织机构编码
	 * 如果父级 id 为空，则生成一级组织机构编码
	 *
	 * @param parentId
	 * @return
	 */
	public String generateNo(String parentId) {
		Organization parent = organizationMapper.selectByPrimaryKey(parentId);
		OrganizationExample example = new OrganizationExample();
		if (parent == null) {
			example.or().andParentIdIsNull().andIsDeletedEqualTo(false);
			example.or().andParentIdEqualTo("").andIsDeletedEqualTo(false);
			example.setOrderByClause("no desc");

			List<Organization> organizationList = organizationMapper.selectByExample(example);
			if (organizationList.size() > 0) {
				String lastNo = organizationList.get(0).getNo();
				return String.format("%03d", Integer.parseInt(lastNo) + 1);
			} else {
				return "001";
			}
		} else {
			example.or().andParentIdEqualTo(parentId).andIsDeletedEqualTo(false);
			example.setOrderByClause("no desc");
			List<Organization> organizationList = organizationMapper.selectByExample(example);
			if (organizationList.isEmpty()) {
				return parent.getNo() + "001";
			}
			String lastNo = organizationList.get(0).getNo();
			lastNo = StringUtils.substring(lastNo, lastNo.length() - 3);
			return parent.getNo() + String.format("%03d", Integer.parseInt(lastNo) + 1);
		}
	}

	/**
	 * 重新生成组织机构编码
	 * 这个编码是用于表示组织层级结构的
	 */
	public void reGenerateNo() {
		String key = "Organization reGenerateNo";
		try {
			if (RedisLockUtil.lock(key)) {
				// 先查询出第一级的组织
				OrganizationExample example = new OrganizationExample();
				example.or().andIsDeletedEqualTo(Boolean.FALSE).andParentIdIsNull();
				example.or().andIsDeletedEqualTo(Boolean.FALSE).andParentIdEqualTo("");
				example.setOrderByClause("sort asc");
				List<Organization> lstOrganization = organizationMapper.selectByExample(example);
				logger.info("lstOrganization:{}", gson.toJson(lstOrganization));

				int count = 1;
				for (Organization organization : lstOrganization) {
					String no = String.format("%03d", count++);
					Organization record = new Organization();
					record.setId(organization.getId());
					record.setNo(no);
					organizationMapper.updateByPrimaryKeySelective(record);

					handleOrganizationNo(organization.getId());
				}
			} else {
				throw new ServiceException("正在重新生成組織號碼");
			}
		} finally {
			RedisLockUtil.unlock(key);
		}
	}

	private void handleOrganizationNo(String parentId) {
		String key = "handleOrganizationNo" + parentId;
		try {
			if (RedisLockUtil.lock(key)) {
				OrganizationExample example = new OrganizationExample();
				example.or().andIsDeletedEqualTo(Boolean.FALSE).andParentIdEqualTo(parentId);
				example.setOrderByClause("sort asc");
				List<Organization> lstOrganization = organizationMapper.selectByExample(example);
				logger.info("lstOrganization:{}", gson.toJson(lstOrganization));
				if (CollectionUtils.isEmpty(lstOrganization)) {
					return;
				}

				int count = 1;
				for (Organization organization : lstOrganization) {
					String no = organizationMapper.selectByPrimaryKey(parentId).getNo() + String.format("%03d", count++);
					Organization record = new Organization();
					record.setId(organization.getId());
					record.setNo(no);
					organizationMapper.updateByPrimaryKeySelective(record);

					handleOrganizationNo(organization.getId());
				}
			} else {
				throw new ServiceException("正在重新生成組織號碼");
			}
		} finally {
			RedisLockUtil.unlock(key);
		}
	}

	/**
	 * 根据组织机构id查询组织机构信息
	 *
	 * @param id 组织机构id
	 * @return
	 */
	public OrganizationVO getOrganizationById(String id) {
		checkExist(id, "组织机构id不能为空");
		OrganizationExample example = new OrganizationExample();
		example.or().andIdEqualTo(id).andIsDeletedEqualTo(false);
		Organization organization = organizationMapper.selectByExample(example).stream().findFirst().orElse(null);
		if (organization == null) {
			return null;
		}
		return OrganizationConverter.convert(organization);
	}

	/**
	 * 初始化用户组织机构
	 * 默认配置中建香港
	 *
	 * @param username
	 */
	public void initUserDefaultOrganizations(String username) {
		try {
			User user = getUserByUsername(username);
			List<String> organizationIds = new ArrayList<>();
			Organization organization = getOrganizationByName("中建香港");
			if (Objects.nonNull(organization)) {
				listOrganizationTreeByParentId(organization.getId()).forEach(org -> organizationIds.add(org.getId()));
			}

			for (String orgId : organizationIds) {
				UserOrganization userOrganization = new UserOrganization();
				userOrganization.setUserId(user.getId());
				userOrganization.setOrganizationId(orgId);
				userOrganization.setIsDeleted(Boolean.FALSE);
				userOrganizationMapper.insertSelective(userOrganization);
			}
		} catch (Exception e) {
			logger.error("初始化用户组织机构失败", e);
		}
	}

	@Transactional(rollbackFor = Exception.class)
	public int insertSelective(Organization record) {
		return organizationMapper.insertSelective(record);
	}

	public void initOrganization(OrganizationVO organizationVO) {
		Organization organization = new Organization();
		BeanUtils.copyProperties(organizationVO, organization);
		organization.setNo(generateNo(organization.getParentId()));
		logger.info("organization:{}", gson.toJson(organization));
	}

	/**
	 * 根据名称以及父级单位名称初始化一个组织机构
	 *
	 * @param name
	 * @param parentName
	 */
	public void initOrganizationByName(String name, String parentName) {
		if (StringUtils.isBlank(name)) {
			logger.error("组织机构名称不能为空");
			return;
		}
		Organization existOrg = getOrganizationByName(name);
		if (Objects.nonNull(existOrg)) {
			logger.warn("组织机构已存在，不需要初始化");
			return;
		}
		Organization parent = getOrganizationByName(parentName);
		String parentId = Optional.ofNullable(parent).map(Organization::getId).orElse(null);

		Organization organization = new Organization();
		organization.setName(name);
		organization.setNo(generateNo(parentId));
		organization.setIsDeleted(false);

		if (StringUtils.isNotBlank(parentId)) {
			organization.setParentId(parentId);
		}
		logger.info("organization:{}", gson.toJson(organization));
		insertSelective(organization);
	}

	/**
	 * 判断是否是叶子节点
	 *
	 * @param orgId
	 * @return
	 */
	public boolean isOrganizationLeaf(String orgId) {
		return ServiceHelper.isOrganizationLeaf(orgId);
	}

	/**
	 * 此方法没有考虑组织机构的层级关系
	 * 并且不涉及权限控制
	 *
	 * @param keywordQO
	 * @return
	 */
	public ResultPage<CompanyDeptVO> listCompanyDeptByPage(KeywordQO keywordQO) {
		PageHelper.startPage(keywordQO.getCurPage(), keywordQO.getPageSize());
		SimpTradUtil simpTradUtil = new SimpTradUtil();
		String keywordTc = simpTradUtil.convert2Trad(keywordQO.getKeyword());
		String keywordSc = simpTradUtil.convert2Simp(keywordQO.getKeyword());
		String pattern = simpTradUtil.generateLikePattern(keywordTc);
		String patternSc = simpTradUtil.generateLikePattern(keywordSc);
		List<CompanyDeptVO> companyDeptVOS = organizationMapperCustom.selectCompanyDept(keywordTc, keywordSc, null, false, pattern, patternSc, false);
		return new ResultPage<>(companyDeptVOS, true);
	}

	/**
	 * 根据组织机构id查询所有顶级组织机构
	 *
	 * @return
	 */
	public List<CompanyVO> listTopCompanies() {
		OrganizationExample example = new OrganizationExample();
		example.or().andParentIdIsNull().andIsDeletedEqualTo(Boolean.FALSE);
		example.or().andParentIdEqualTo("").andIsDeletedEqualTo(Boolean.FALSE);
		List<Organization> lstOrgs = organizationMapper.selectByExample(example);
		List<CompanyVO> lstCompanies = new ArrayList<>();
		for (Organization org : lstOrgs) {
			CompanyVO companyVO = new CompanyVO();
			companyVO.setId(org.getId());
			companyVO.setName(org.getName());
			lstCompanies.add(companyVO);
		}
		return lstCompanies;
	}

	public List<Organization> findLeafChildrenByOrgId(String organizationId) {
		Organization organization = organizationMapper.selectByPrimaryKey(organizationId);
		return organizationMapperCustom.selectLeafChildrenByNo(organization.getNo());
	}

	public List<Organization> findLeafChildrenByNo(String no) {
		return organizationMapperCustom.selectLeafChildrenByNo(no);
	}

	public String getOrgNameByNo(String orgNo) {
		OrganizationExample example = new OrganizationExample();
		example.or().andNoEqualTo(orgNo);
		List<Organization> organizations = organizationMapper.selectByExample(example);
		if (CollectionUtils.isNotEmpty(organizations)) {
			return organizations.get(0).getName();
		}
		return null;
	}

	public ResultPage<CompanyPlatformVO> listFuzzySearch(KeywordQO keywordQO) {
		PageHelper.startPage(keywordQO.getCurPage(), keywordQO.getPageSize());
		SimpTradUtil simpTradUtil = new SimpTradUtil();
		String keywordTc = simpTradUtil.convert2Trad(keywordQO.getKeyword());
		String keywordSc = simpTradUtil.convert2Simp(keywordQO.getKeyword());
		List<CompanyPlatformVO> companyDeptVOS = organizationMapperCustom.listFuzzySearch(keywordTc, keywordSc);
		return new ResultPage<>(companyDeptVOS, true);
	}

	public List<OrganizationTreeVO> selectOrganizationTree(String organizationId) {
		return organizationMapperCustom.selectOrganizationTree(organizationId);
	}
}
