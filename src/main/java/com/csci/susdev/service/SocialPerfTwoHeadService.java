package com.csci.susdev.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.listener.SocialPerfTwoListener;
import com.csci.susdev.mapper.SocialPerfTwoDetailMapper;
import com.csci.susdev.mapper.SocialPerfTwoHeadMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.SocialPerfTwoHeadConverter;
import com.csci.susdev.modelcovt.SocialPerfTwoTableDataConverter;
import com.csci.susdev.qo.SocialPerformanceQO;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.vo.SocialPerfTwoHeadVO;
import com.csci.susdev.vo.SocialPerfTwoImportDataVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class SocialPerfTwoHeadService {
    /**
     * 初始化记录的过期时间
     */
    @Value("${lock.init.head.expire.milis:1000}")
    private long lockInitHeadExpireMilis;
    /**
     * 查询head记录时，获取锁失败循环等待的次数
     */
    @Value("${query.head.wait.loop.times:5}")
    private int lockQueryHeadWaitLoopTimes;
    /**
     * 查询head记录时，获取锁失败循环时每次休眠的时间
     */
    @Value("${query.head.wait.sleep.milis:50}")
    private int lockQueryHeadWaitSleepMilis;
    @Resource
    private SocialPerfTwoHeadMapper socialPerfTwoHeadMapper;

    @Resource
    private SocialPerfTwoDetailMapper socialPerfTwoDetailMapper;

    @Transactional(rollbackFor = Exception.class)
    public SocialPerfTwoHeadVO getOrInitSocialPerfTwo(String organizationId, Integer year, Integer month) {
        checkExist(organizationId, "组织机构编号不能为空");
        checkExist(year, "年份不能为空");
        if(ObjectUtils.isEmpty(month)) {
            month = 12;
        }

        SocialPerfTwoHead head = getOrInitHeadRecord(organizationId, year, month);

        return SocialPerfTwoHeadConverter.convert(head);
    }

    /**
     * 查询或初始化记录，如果获取失败，直接抛出异常退出
     * @param
     * @return
     */
    private SocialPerfTwoHead getOrInitHeadRecord(String organizationId, Integer year, Integer month) {
        SocialPerfTwoHead head = findSocialPerfTwoHead(organizationId, year, month);
        if (Objects.isNull(head)) {
            //分布式锁的key：分别表示前缀:业务:组织ID:年:月
            String key = StrUtil.format(SusDevConsts.DISTRIBUTE_LOCK_KEY_FORMAT, SusDevConsts.PROJECT_PREFIX,
                    "SocialPerfTwoHead:getOrInit", organizationId, year, month);
            //超时时间1s
            boolean redisLock = RedisLockUtil.lock(key, lockInitHeadExpireMilis);
            if (!redisLock) {
                //获取锁失败，停顿50ms一次，停顿5次
                int count = lockQueryHeadWaitLoopTimes;
                while(count-- > 0){
                    head = findSocialPerfTwoHead(organizationId, year, month);
                    if(head != null){
                        break;
                    }
                    try {
                        Thread.sleep(lockQueryHeadWaitSleepMilis);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }else{
                try{
                    head = createSocialPerfTwoHead(organizationId, year, month);
                }
                finally {
                    RedisLockUtil.unlock(key);
                }
            }
        }
        //如果最终没拿到数据，直接返回
        if(head == null){
            throw new ServiceException("生成记录失败, 请稍后再尝试！");
        }
        return head;
    }

    public SocialPerfTwoHead getPrevHead(String organizationId, Integer year, Integer month) {
        SocialPerfTwoHeadExample example = new SocialPerfTwoHeadExample();
        example.or().andIsActiveEqualTo(true).andOrganizationIdEqualTo(organizationId)
                .andYearEqualTo(year).andMonthLessThan(month);
        example.setOrderByClause("month desc");
        List<SocialPerfTwoHead> lst = socialPerfTwoHeadMapper.selectByExample(example);
        if(lst.size() > 0) {
            return lst.get(0);
        } else {
            return null;
        }
    }

    void initDetailsWithPrevData(String prevHeadId, String curHeadId) {
        SocialPerfTwoDetailExample example = new SocialPerfTwoDetailExample();
        example.or().andHeadIdEqualTo(prevHeadId);
        List<SocialPerfTwoDetail> details = socialPerfTwoDetailMapper.selectByExample(example);
        for(SocialPerfTwoDetail detail : details) {
            detail.setId(null);
            detail.setHeadId(curHeadId);
            socialPerfTwoDetailMapper.insert(detail);
        }
    }

    /**
     * 创建社会绩效二主表记录
     * 同时初始化明细数据
     *
     * @param organizationId 组织机构编号
     * @param year           年份
     * @param month          月份
     * @return
     */
    private SocialPerfTwoHead createSocialPerfTwoHead(String organizationId, Integer year, Integer month) {
        SocialPerfTwoHead head = findSocialPerfTwoHead(organizationId, year, month);
        if (Objects.nonNull(head)) {
            return head;
        }

        head = new SocialPerfTwoHead();
        head.setOrganizationId(organizationId);
        head.setYear(year);
        head.setMonth(month);
        head.setIsActive(Boolean.TRUE);
        socialPerfTwoHeadMapper.insertSelective(head);

        // 在这里进行明细数据初始化是因为放在同步方法里面避免并发问题
        SocialPerfTwoHead prevHead = getPrevHead(organizationId, year, month);
        if(Objects.nonNull(prevHead)) {
            initDetailsWithPrevData(prevHead.getId(), head.getId());
        } else {
            initSocialPerfTwoDetail(head.getId());
        }

        return head;
    }

    /**
     * 根据组织机构编号、年份、月份查询社会绩效考核表头信息
     *
     * @param organizationId 组织机构编号
     * @param year           年份
     * @param month          月份
     * @return
     */
    public SocialPerfTwoHead findSocialPerfTwoHead(String organizationId, Integer year, Integer month) {
        SocialPerfTwoHeadExample example = new SocialPerfTwoHeadExample();
        example.or().andOrganizationIdEqualTo(organizationId).andYearEqualTo(year)
                .andMonthEqualTo(month).andIsActiveEqualTo(Boolean.TRUE);
        List<SocialPerfTwoHead> lstSocialPerfTwoHead = socialPerfTwoHeadMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(lstSocialPerfTwoHead)) {
            return null;
        }
        if (lstSocialPerfTwoHead.size() > 1) {
            throw new ServiceException("社会绩效二表头信息不唯一");
        }
        return lstSocialPerfTwoHead.get(0);
    }

    void initSocialPerfTwoDetail(String headId) {
        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream("template/social_perf_two_init.xlsx")) {
            EasyExcel.read(is, SocialPerfTwoImportDataVO.class, new SocialPerfTwoListener(headId)).sheet().doRead();
        } catch (IOException e) {
            throw new ServiceException("數據初始化出錯", e);
        }
    }

    /**
     * 更新社会绩效二主表及明细信息
     *
     * @param socialPerfTwoHeadVO
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSocialPerfTwo(SocialPerfTwoHeadVO socialPerfTwoHeadVO) {
        checkExist(socialPerfTwoHeadVO.getId(), "社会绩效二表头ID不能为空");
        checkExist(socialPerfTwoHeadVO.getLastUpdateVersion(), "社会绩效二表头版本号不能为空");

        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        if (workflowControlService.isNotAllowEdit(socialPerfTwoHeadVO.getId())) {
            throw new ServiceException("已经发起审批流程的单据不允许进行修改");
        }

        if (CollectionUtils.isEmpty(socialPerfTwoHeadVO.getDetails())) {
            throw new ServiceException("社会绩效二明细不能为空");
        }

        // 更新表头
        SocialPerfTwoHead socialPerfTwoHead = SocialPerfTwoHeadConverter.convert(socialPerfTwoHeadVO);
        SocialPerfTwoHeadExample headExample = new SocialPerfTwoHeadExample();
        headExample.or().andIsActiveEqualTo(Boolean.TRUE).andIdEqualTo(socialPerfTwoHeadVO.getId()).andLastUpdateVersionEqualTo(socialPerfTwoHeadVO.getLastUpdateVersion());
        int updateCount = socialPerfTwoHeadMapper.updateByExampleSelective(socialPerfTwoHead, headExample);
        if (updateCount != 1) {
            throw new ServiceException("社会绩效二表头信息已被修改，请刷新后重试");
        }

        // 更新明细
        // 先删除
        SocialPerfTwoDetailExample detailExample = new SocialPerfTwoDetailExample();
        detailExample.or().andHeadIdEqualTo(socialPerfTwoHeadVO.getId());
        socialPerfTwoDetailMapper.deleteByExample(detailExample);
        // 再新增
        List<SocialPerfTwoDetail> details = socialPerfTwoHeadVO.getDetails().stream().map(SocialPerfTwoTableDataConverter::convert).collect(Collectors.toList());
        int seq = 1;
        for (SocialPerfTwoDetail detail : details) {
            detail.setHeadId(socialPerfTwoHeadVO.getId());
            detail.setId(UUID.randomUUID().toString());
            detail.setSeq(seq++);
            socialPerfTwoDetailMapper.insertSelective(detail);
        }
    }

    /**
     * 查询指定编号的社会绩效二记录
     *
     * @param id 社会绩效二编号
     * @return
     */
    public SocialPerfTwoHead selectByPrimaryKey(String id) {
        return socialPerfTwoHeadMapper.selectByPrimaryKey(id);
    }
}
