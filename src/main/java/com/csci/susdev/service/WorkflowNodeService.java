package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.UserMapper;
import com.csci.susdev.mapper.WorkflowNodeMapper;
import com.csci.susdev.mapper.WorkflowNodeUserMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.WorkflowNodeVOConverter;
import com.csci.susdev.vo.WorkflowNodeVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@LogMethod
@DS(DatasourceContextEnum.SUSDEV)
public class WorkflowNodeService {

    @Resource
    private WorkflowNodeMapper workflowNodeMapper;

    @Resource
    private WorkflowNodeUserMapper workflowNodeUserMapper;

    @Resource
    private UserMapper userMapper;

    /**
     * 查找流程第一个审批节点
     * （起始节点的下一个节点）
     *
     * @param workflowId 流程ID
     * @return
     */
    public WorkflowNode findFirstAuditNodeByWorkflowId(String workflowId) {
        WorkflowNodeExample example = new WorkflowNodeExample();
        example.or().andWorkflowIdEqualTo(workflowId).andPreviousNodeIdIsNull().andIsBeginNodeEqualTo(Boolean.TRUE);
        WorkflowNode beginNode = workflowNodeMapper.selectByExample(example).stream().findFirst().orElseThrow(() -> new ServiceException("流程节点不存在"));

        WorkflowNodeExample example2 = new WorkflowNodeExample();
        example2.or().andWorkflowIdEqualTo(workflowId).andPreviousNodeIdEqualTo(beginNode.getId()).andIsBeginNodeEqualTo(Boolean.FALSE);
        return workflowNodeMapper.selectByExample(example2).stream().findFirst().orElseThrow(() -> new ServiceException("流程节点不存在"));
    }

    /**
     * 查找流程起始节点
     *
     * @param workflowId
     * @return
     */
    public WorkflowNode findBeginNodeByWorkflowId(String workflowId) {
        WorkflowNodeExample example = new WorkflowNodeExample();
        example.or().andWorkflowIdEqualTo(workflowId).andPreviousNodeIdIsNull().andIsBeginNodeEqualTo(Boolean.TRUE);
        return workflowNodeMapper.selectByExample(example).stream().findFirst().orElseThrow(() -> new ServiceException("流程节点不存在"));
    }

    /**
     * proxy for {@link WorkflowNodeMapper#insertSelective(WorkflowNode)}
     *
     * @param record
     * @return
     */
    public int insertSelective(WorkflowNode record) {
        return workflowNodeMapper.insertSelective(record);
    }

    /**
     * 新增流程节点
     *
     * @param workflowNodeVO 流程节点VO
     * @return
     */
    public String addWorkflowNode(WorkflowNodeVO workflowNodeVO) {
        WorkflowNode workflowNode = WorkflowNodeVOConverter.convert(workflowNodeVO);
        workflowNodeMapper.insertSelective(workflowNode);
        return workflowNode.getId();
    }

    /**
     * 根据 workflowId 删除流程节点
     *
     * @param workflowId 流程ID
     * @return
     */
    public int deleteByWorkflowId(String workflowId) {
        WorkflowNodeExample example = new WorkflowNodeExample();
        example.or().andWorkflowIdEqualTo(workflowId);
        return workflowNodeMapper.deleteByExample(example);
    }


    /**
     * proxy for {@link WorkflowNodeMapper#selectByPrimaryKey(String)}
     *
     * @param id 流程节点ID
     * @return
     */
    public WorkflowNode selectByPrimaryKey(String id) {
        return workflowNodeMapper.selectByPrimaryKey(id);
    }

    /**
     * 查找指定節點的下一個節點
     *
     * @param workflowId 流程ID
     * @param nodeId     節點ID
     * @return
     */
    public WorkflowNode findNextNodeByWorkflowIdAndNodeId(String workflowId, String nodeId) {
        WorkflowNodeExample example = new WorkflowNodeExample();
        example.or().andWorkflowIdEqualTo(workflowId).andPreviousNodeIdEqualTo(nodeId);
        return workflowNodeMapper.selectByExample(example).stream().findFirst().orElse(null);
    }

    public WorkflowNode findNextAvailableNodeByWorkflowIdAndNodeId(String workflowId, String nodeId) {
        WorkflowNode nextNode = findNextNodeByWorkflowIdAndNodeId(workflowId, nodeId);
        if (Objects.nonNull(nextNode)) {
            User user = getNodeUser(nextNode.getId());
            if (Objects.isNull(user)) {
                return findNextAvailableNodeByWorkflowIdAndNodeId(workflowId, nextNode.getId());
            }
        }

        return nextNode;
    }

    /**
     * 获取节点用户
     *
     * @param nodeId 节点ID
     * @return
     */
    public User getNodeUser(String nodeId) {
        WorkflowNodeUserExample example = new WorkflowNodeUserExample();
        example.or().andNodeIdEqualTo(nodeId);
        WorkflowNodeUser nodeUser = workflowNodeUserMapper.selectByExample(example).stream().findFirst().orElse(null);
        String userId = Optional.ofNullable(nodeUser).map(WorkflowNodeUser::getUserId).orElse(null);
        if (StringUtils.isBlank(userId)) {
            return null;
        }
        return userMapper.selectByPrimaryKey(userId);
    }

    /**
     * 查询指定节点的所有用户
     *
     * @param nodeId 节点ID
     * @return
     */
    public List<User> listNodeUser(String nodeId) {
        WorkflowNodeUserExample example = new WorkflowNodeUserExample();
        example.or().andNodeIdEqualTo(nodeId);
        List<WorkflowNodeUser> nodeUsers = workflowNodeUserMapper.selectByExample(example);
        List<String> userIds = nodeUsers.stream().map(WorkflowNodeUser::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        UserExample userExample = new UserExample();
        userExample.or().andIdIn(userIds);
        return userMapper.selectByExample(userExample);
    }

    /**
     * 更新流程节点信息
     * @param node
     * @return
     */
    public int updateBySelective(WorkflowNode node){
        return workflowNodeMapper.updateByPrimaryKeySelective(node);
    }
}
