package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.ProtocolCategoryMapper;
import com.csci.susdev.mapper.ProtocolManagementCustomMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.ProtocolCategoryConverter;
import com.csci.susdev.qo.ProtocolCategoryQO;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.vo.ProtocolCategoryVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class ProtocolCategoryService {

    @Resource
    private ProtocolCategoryMapper protocolCategoryMapper;

    @Resource
    private ProtocolSubCategoryService protocolSubCategoryService;

    @Resource
    private ProtocolConfigurationService protocolConfigurationService;

    @Resource
    private ProtocolManagementCustomMapper protocolManagementCustomMapper;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(ProtocolCategory record) {
        return protocolCategoryMapper.insertSelective(record);
    }

    public ProtocolCategory selectByPrimaryKey(String id) {
        return protocolCategoryMapper.selectByPrimaryKey(id);
    }

    public ProtocolCategoryVO getProtocolCategory(String id) {
        checkExist(id, "id不能为空");
        ProtocolCategory protocolCategory = selectByPrimaryKey(id);
        checkExist(protocolCategory, "未找到对应的记录");
        if(protocolCategory.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return ProtocolCategoryConverter.convertToVO(protocolCategory);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveProtocolCategory(ProtocolCategoryVO protocolCategoryVO) {
        if (StringUtils.isBlank(protocolCategoryVO.getId())) {
            // 新增
            return doAdd(protocolCategoryVO);
        } else {
            checkExist(protocolCategoryVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(protocolCategoryVO);
        }

    }

    public ResultPage<ProtocolCategoryVO> listProtocolCategory(ProtocolCategoryQO protocolCategoryQO) {
        ProtocolCategoryExample example = new ProtocolCategoryExample();

        if(StringUtils.isNotBlank(protocolCategoryQO.getProtocolId())) {
            example.or().andProtocolIdEqualTo(protocolCategoryQO.getProtocolId()).andIsDeletedEqualTo(false);
        } else {
            example.or().andIsDeletedEqualTo(false);
        }

        if (StringUtils.isNotBlank(protocolCategoryQO.getOrderBy())) {
            example.setOrderByClause(NamingConventionUtils.snakeCaseToCamelCase(protocolCategoryQO.getOrderBy()));
        }

        PageHelper.startPage(protocolCategoryQO.getCurPage(), protocolCategoryQO.getPageSize());
        List<ProtocolCategory> protocolCategorys = protocolCategoryMapper.selectByExample(example);
        List<ProtocolCategoryVO> protocolCategoryVOS = protocolCategorys.stream().map(ProtocolCategoryConverter::convertToVO).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(protocolCategoryVOS)) {
            protocolCategoryVOS.stream().forEach(e -> {
                e.setProtocolName(protocolCategoryQO.getProtocolName());
            });
        }
        return new ResultPage<>(protocolCategoryVOS, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteProtocolCategory(String id) {
        ProtocolCategory record = selectByPrimaryKey(id);

        ProtocolSubCategoryExample example = new ProtocolSubCategoryExample();
        example.or().andCategoryIdEqualTo(id).andIsDeletedEqualTo(false);
        List<ProtocolSubCategory> protocolSubCategoryList = protocolSubCategoryService.selectByExample(example);
        if(protocolSubCategoryList.size() > 0) {
            List<String> subCategoryIds = protocolSubCategoryList.stream().map(ProtocolSubCategory::getId).collect(Collectors.toList());
            ProtocolConfigurationExample configurationExample = new ProtocolConfigurationExample();
            configurationExample.or().andSubCategoryIdIn(subCategoryIds).andIsDeletedEqualTo(false);
            List<ProtocolConfiguration> protocolConfigurations = protocolConfigurationService.selectByExample(configurationExample);
            if (CollectionUtils.isNotEmpty(protocolConfigurations)) {
                throw new ServiceException("该大类对应的小类已在协议配置中被使用，不能删除！");
            }
            // 批量删除小类
            protocolManagementCustomMapper.deleteSubCategoryByIds(subCategoryIds);
        }

        record.setIsDeleted(true);
        protocolCategoryMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(ProtocolCategoryVO protocolCategoryVO) {
        ProtocolCategory protocolCategory = ProtocolCategoryConverter.convertToModel(protocolCategoryVO);
        protocolCategoryMapper.insertSelective(protocolCategory);
        return protocolCategory.getId();
    }

    private String doUpdate(ProtocolCategoryVO protocolCategoryVO) {
        ProtocolCategory originalRecord = selectByPrimaryKey(protocolCategoryVO.getId());
        ProtocolCategory protocolCategory = ProtocolCategoryConverter.convertToModelWithBase(protocolCategoryVO, originalRecord);

        // 查询小类
        ProtocolSubCategoryExample subCategoryExample = new ProtocolSubCategoryExample();
        subCategoryExample.or().andCategoryIdEqualTo(protocolCategoryVO.getId()).andIsDeletedEqualTo(false);
        List<ProtocolSubCategory> protocolSubCategoryList = protocolSubCategoryService.selectByExample(subCategoryExample);
        if(protocolSubCategoryList.size() > 0) {
            throw new ServiceException("已在小类中使用，不允许修改！");
        }
        ProtocolCategoryExample example = new ProtocolCategoryExample();
        example.or().andIdEqualTo(protocolCategoryVO.getId()).andLastUpdateVersionEqualTo(protocolCategoryVO.getLastUpdateVersion());
        int updateCount = protocolCategoryMapper.updateByExample(protocolCategory, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return protocolCategory.getId();
    }

    public List<ProtocolCategory> selectByExample(ProtocolCategoryExample example) {
        return protocolCategoryMapper.selectByExample(example);
    }
}
