package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.TzhBsUserRoleMapper;
import com.csci.susdev.mapper.UserMapper;
import com.csci.susdev.mapper.UserRoleMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class UserRoleService {

    @Resource
    private UserRoleMapper userRoleMapper;

    @Resource
    private TzhBsUserRoleMapper tzhBsUserRoleMapper;

    @Resource
    private UserMapper userMapper;

    public List<UserRole> selectByExample(UserRoleExample example) {
        return userRoleMapper.selectByExample(example);
    }

    /**
     * 用戶角色關係 数据列表
     *
     * @param
     * @return
     */
    public List<UserRole> listUserRoleByUsername(String username) {
        User user = ServiceHelper.getUserByUsername(username, userMapper);
        if (user == null)
            throw new ServiceException("找不到用戶");

        return ServiceHelper.listUserRole(user.getId(), userRoleMapper);
    }
    
    

	
    /**
     * 用戶角色關係 数据列表
     *
     * @param userId
     * @return
     */
    public List<UserRole> listUserRole(String userId) {
        return ServiceHelper.listUserRole(userId, userRoleMapper);
    }
    
    /**
     * 保存 用戶角色關係 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param userRoleLst
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> saveUserRoleList(List<UserRole> userRoleLst) {
    	List<String> idLst = new ArrayList<>();
    	for(UserRole userRole : userRoleLst) {
    		this.saveUserRole(userRole);
            idLst.add(userRole.getId());
    	}
        return idLst;
    }

    /**
     * 保存 用戶角色關係 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param userRole
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveUserRole(UserRole userRole) {
    	if(userRole.getIsDeleted() == true) {
    		this.deleteUserRole(userRole.getId());
    	} else {
	    	UserInfo currentUser = ContextUtils.getCurrentUser();
	    	LocalDateTime now = LocalDateTime.now();
	        if (StringUtils.isBlank(userRole.getId())) {
                userRole.setCreationTime(now);
                userRole.setCreateUsername(currentUser.getUsername());
                userRole.setLastUpdateTime(now);
                userRole.setLastUpdateUsername(currentUser.getUsername());
                userRoleMapper.insertSelective(userRole);
            } else {
                userRole.setLastUpdateTime(now);
                userRole.setLastUpdateUsername(currentUser.getUsername());
                userRoleMapper.updateByPrimaryKeySelective(userRole);
            }
    		
    	}
        return userRole.getId();
    }

    /**
     * 保存 大屏用戶角色關係 信息
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param tzhBsUserRole
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveBsUserRole(TzhBsUserRole tzhBsUserRole) {
        UserInfo currentUser = ContextUtils.getCurrentUser();
        LocalDateTime now = LocalDateTime.now();
        if (StringUtils.isBlank(tzhBsUserRole.getId())) {
            tzhBsUserRoleMapper.insertSelective(tzhBsUserRole);
        } else {
            tzhBsUserRoleMapper.updateByPrimaryKeySelective(tzhBsUserRole);
        }
        return tzhBsUserRole.getId();
    }

    /**
     * 刪除 用戶角色關係 信息
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserRole(String id) {
        return userRoleMapper.deleteByPrimaryKey(id);
    }

    /**
     * 刪除 用戶角色關係 信息
     *
     * @param username
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserRoleByUsername(String username) {
        int result = 0;
        User user = ServiceHelper.getUserByUsername(username, userMapper);
        if (user == null) {
            return 0;
        }
        UserRoleExample example = new UserRoleExample();
        example.or().andUserIdEqualTo(user.getId());
        userRoleMapper.deleteByExample(example);
        return result;
    }

    /**
     * 刪除指定用戶的所有角色
     *
     * @param userId
     * @return
     */
    public int deleteUserRoleByUserId(String userId) {
        UserRoleExample example = new UserRoleExample();
        example.or().andUserIdEqualTo(userId);
        return userRoleMapper.deleteByExample(example);
    }


    /**
     * 刪除指定用戶的所有大屏角色
     *
     * @param username
     * @return
     */
    public int deleteBsUserRoleByUsername(String username) {
        TzhBsUserRoleExample example = new TzhBsUserRoleExample();
        example.or().andUsernameEqualTo(username);
        return tzhBsUserRoleMapper.deleteByExample(example);
    }

    /**
     * 判断指定用户是否有指定角色
     *
     * @param username
     * @param roleCode 角色编码
     * @return
     */
    public boolean hasRole(String username, String roleCode) {
        checkExist(username, "username不能为空");
        checkExist(roleCode, "roleCode不能为空");
        User user = ServiceHelper.getUserByUsername(username);
        checkExist(user, "找不到用户");
        Role role = ServiceHelper.getRoleByCode(roleCode);
        checkExist(role, "角色不存在");
        UserRoleExample example = new UserRoleExample();
        example.or().andUserIdEqualTo(user.getId()).andRoleIdEqualTo(role.getId());
        return userRoleMapper.countByExample(example) > 0;
    }
}
