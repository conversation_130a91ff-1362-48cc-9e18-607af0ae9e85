package com.csci.susdev.service;

import cn.hutool.core.collection.CollectionUtil;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.*;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.BusinessTripConverter;
import com.csci.susdev.qo.*;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.util.StringUtil;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.susdev.vo.*;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;
import static com.csci.susdev.service.ServiceHelper.validateMonth;

@Service
@LogMethod
public class BusinessTripService {

    @Resource
    private BusinessTripMapper businessTripMapper;
    @Resource
    private BusinessTripCustomMapper businessTripCustomMapper;

    @Resource
    private FlightInfoService flightInfoService;

    @Resource
    private TrainService trainService;

    @Autowired
    private AirportCustomMapper airportCustomMapper;
    @Resource
    private AmbientService ambientService;
    @Resource
    private AttachmentService attachmentService;
    @Resource
    private MinioAttachmentMapper minioAttachmentMapper;
    @Resource
    private AttachmentMapper attachmentMapper;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(BusinessTrip record) {
        return businessTripMapper.insertSelective(record);
    }

    public BusinessTrip selectByPrimaryKey(String id) {
        return businessTripMapper.selectByPrimaryKey(id);
    }

    public BusinessTripVO getBusinessTrip(String id) {
        checkExist(id, "id不能为空");
        BusinessTrip businessTrip = selectByPrimaryKey(id);
        checkExist(businessTrip, "未找到对应的出差记录");
        BusinessTripVO tripVO = BusinessTripConverter.convert(businessTrip);
        if (StringUtil.equals("飛機", tripVO.getVehicleType())) {
            AirportVO startAirportVO = airportCustomMapper.getByCode(tripVO.getStartPlace());
            if (!Objects.isNull(startAirportVO) && StringUtils.isNotEmpty(startAirportVO.getName())) {
                tripVO.setStartPlaceAll(startAirportVO.getName());
            }else {
                tripVO.setStartPlaceAll(tripVO.getStartPlace());
            }
            AirportVO endAirportVO = airportCustomMapper.getByCode(tripVO.getDestination());
            if (!Objects.isNull(endAirportVO) && StringUtils.isNotEmpty(endAirportVO.getName())) {
                tripVO.setDestinationAll(endAirportVO.getName());
            }else {
                tripVO.setDestinationAll(tripVO.getDestination());
            }
        }
        return tripVO;
    }

    /**
     * 保存商务旅行记录
     *
     * @param businessTripVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveBusinessTrip(BusinessTripVO businessTripVO) {
        validateMonth(businessTripVO.getMonthValue());
        checkExist(businessTripVO.getAmbientHeadId(), "环境绩效id不能为空");
        checkExist(businessTripVO.getStartPlace(), "出发地不能为空");
        checkExist(businessTripVO.getDestination(), "目的地不能为空");
        checkExist(businessTripVO.getVehicleType(), "请选择運輸工具");
        checkExist(businessTripVO.getLevel(), "级别不能为空");
        checkExist(businessTripVO.getPersonCount(), "人数不能为空");
        checkExist(businessTripVO.getTicketType(), "请选择往返或单程");
        checkExist(businessTripVO.getMonthValue(), "月份不能为空");

        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        workflowControlService.throwIfHasWorkflowControl(businessTripVO.getAmbientHeadId(), "本期环境绩效数据已经提交，不能进行新增或修改");

        if (StringUtils.isBlank(businessTripVO.getId())) {
            // 新增
            return doAdd(businessTripVO);
        } else {
            checkExist(businessTripVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(businessTripVO);
        }

    }


    @Transactional(rollbackFor = Exception.class)
    public List<String> saveBusinessTripList(List<BusinessTripVO> businessTripVOList) {
        if(businessTripVOList.size() > 5000) {
            throw new ServiceException("每次只能新增5000條數據，請分開兩個檔案處理");
        }
        List<String> ids = new ArrayList<>();
        for(int i = 0; i < businessTripVOList.size(); i++) {
            validateMonth(businessTripVOList.get(i).getMonthValue());
            checkExist(businessTripVOList.get(i).getAmbientHeadId(), "[第" + i + "行]" + "环境绩效id不能为空");
            checkExist(businessTripVOList.get(i).getStartPlace(), "[第" + i + "行]" + "出发地不能为空");
            checkExist(businessTripVOList.get(i).getDestination(), "[第" + i + "行]" + "目的地不能为空");
            checkExist(businessTripVOList.get(i).getVehicleType(), "[第" + i + "行]" + "请选择運輸工具");
            checkExist(businessTripVOList.get(i).getLevel(), "[第" + i + "行]" + "级别不能为空");
            checkExist(businessTripVOList.get(i).getPersonCount(), "[第" + i + "行]" + "人数不能为空");
            checkExist(businessTripVOList.get(i).getTicketType(), "[第" + i + "行]" + "请选择往返或单程");
            checkExist(businessTripVOList.get(i).getMonthValue(), "[第" + i + "行]" + "月份不能为空");

            WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
            workflowControlService.throwIfHasWorkflowControl(businessTripVOList.get(i).getAmbientHeadId(), "[第" + i + "行]" + "本期环境绩效数据已经提交，不能进行新增或修改");

            if(businessTripVOList.get(i).getVehicleType() == "飛機") {
                FlightInfoQO flightInfoQO = new FlightInfoQO();
                flightInfoQO.setDestination(businessTripVOList.get(i).getDestination());
                flightInfoQO.setStartPlace(businessTripVOList.get(i).getStartPlace());
                flightInfoQO.setLevel(businessTripVOList.get(i).getLevel());
                flightInfoQO.setPersonCount(businessTripVOList.get(i).getPersonCount());

                FlightInfo flightInfo = flightInfoService.get(flightInfoQO);
                if(flightInfo != null) {
                    businessTripVOList.get(i).setCarbonEmission(flightInfo.getCarbonEmission());
                    businessTripVOList.get(i).setFlightDistance(flightInfo.getFlightDistance());
                }
            } else if(businessTripVOList.get(i).getVehicleType() == "火車") {
                TrainQO trainQO = new TrainQO();
                trainQO.setDestination(businessTripVOList.get(i).getDestination());
                trainQO.setStartPlace(businessTripVOList.get(i).getStartPlace());
                trainQO.setTicketType(businessTripVOList.get(i).getTicketType());
                trainQO.setPersonCount(businessTripVOList.get(i).getPersonCount());

                Train train = trainService.calCarbonEmission(trainQO);
                if(train != null) {
                    businessTripVOList.get(i).setCarbonEmission(train.getCarbonEmission());
                }
            }

            if (StringUtils.isBlank(businessTripVOList.get(i).getId())) {
                // 新增
                ids.add(doAdd(businessTripVOList.get(i)));
            } else {
                checkExist(businessTripVOList.get(i).getLastUpdateVersion(), "[第" + i + "行]" + "更新时版本号不能为空");
                // 修改
                ids.add(doUpdate(businessTripVOList.get(i)));
            }
        }
        return ids;
    }

    /**
     * 查询商务旅行记录
     *
     * @return
     */
    public ResultPage<BusinessTripVO> listBusinessTrip(BusinessTripQO businessTripQO) {
        checkExist(businessTripQO.getAmbientHeadId(), "环境绩效主记录id不能为空");
        BusinessTripExample example = new BusinessTripExample();
        BusinessTripExample.Criteria criteria = example.or().andAmbientHeadIdEqualTo(businessTripQO.getAmbientHeadId());
        if(businessTripQO.getFilter() != null) {
            if(businessTripQO.getFilter().get("monthValue") != null && businessTripQO.getFilter().get("monthValue").size() > 0) {
                criteria.andMonthValueIn(businessTripQO.getFilter().get("monthValue"));
            }
            if(businessTripQO.getFilter().get("vehicleType") != null && businessTripQO.getFilter().get("vehicleType").size() > 0) {
                criteria.andVehicleTypeIn(businessTripQO.getFilter().get("vehicleType"));
            }
            if(businessTripQO.getFilter().get("level") != null && businessTripQO.getFilter().get("level").size() > 0) {
                criteria.andLevelIn(businessTripQO.getFilter().get("level"));
            }
            if(businessTripQO.getFilter().get("ticketType") != null && businessTripQO.getFilter().get("ticketType").size() > 0) {
                criteria.andTicketTypeIn(businessTripQO.getFilter().get("ticketType"));
            }
        }

        if (StringUtils.isBlank(businessTripQO.getSortName())) {
            example.setOrderByClause("month_value desc");
        } else {
            String columnName = getColumnName(businessTripQO.getSortName());
            if (StringUtils.equals(businessTripQO.getSortOrder(), "asc")) {
                example.setOrderByClause(columnName + " asc");
            } else {
                example.setOrderByClause(columnName + " desc");
            }
        }

        PageHelper.startPage(businessTripQO.getCurPage(), businessTripQO.getPageSize());
        List<BusinessTrip> businessTrips = businessTripMapper.selectByExample(example);
        ResultPage<BusinessTripVO> resultPage = new ResultPage<>(businessTrips);
        List<BusinessTripVO> businessTripVOS = businessTrips.stream().map(BusinessTripConverter::convert).collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(businessTripVOS)) {
            List<AirportVO> airportVOList = airportCustomMapper.list("", "");
            Map<String, String> airportMap = airportVOList.stream().collect(Collectors.toMap(k -> k.getCode(), v -> StringUtil.valueOf(v.getName())));
            // 飞机 类型 根据编码 找名字
            for (BusinessTripVO tripVO : businessTripVOS) {
                if (StringUtil.equals("飛機", tripVO.getVehicleType()) && airportMap.containsKey(tripVO.getStartPlace())) {
                    String startPlaceAll = airportMap.get(tripVO.getStartPlace());
                    tripVO.setStartPlaceAll(StringUtil.isEmpty(startPlaceAll) ? tripVO.getStartPlace() : startPlaceAll);
                }else {
                    tripVO.setStartPlaceAll(tripVO.getStartPlace());
                }
                if (StringUtil.equals("飛機", tripVO.getVehicleType()) && airportMap.containsKey(tripVO.getDestination())) {
                    String destinationAll = airportMap.get(tripVO.getDestination());
                    tripVO.setDestinationAll(StringUtil.isEmpty(destinationAll) ? tripVO.getDestination() : destinationAll);
                }else {
                    tripVO.setDestinationAll(tripVO.getDestination());
                }
            }
        }
        resultPage.setList(businessTripVOS);
        return resultPage;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteBusinessTrip(String id) {
        businessTripMapper.deleteByPrimaryKey(id);
    }

    /**
     * 批量删除
     * @param businessTripBatchDeleteVO
     */
    public String batchDeleteBusinessTrip(BusinessTripBatchDeleteVO businessTripBatchDeleteVO) {
        if(businessTripBatchDeleteVO == null || CollectionUtils.isEmpty(businessTripBatchDeleteVO.getDeleteIdList())){
            return "";
        }
        businessTripCustomMapper.batchDeleteByIdList(businessTripBatchDeleteVO.getDeleteIdList());
        return "";
    }

    private String getColumnName(String sortName) {
        switch (sortName) {
            case "monthValue":
                return "month_value";
            case "tripType":
                return "trip_type";
            case "startPlace":
                return "start_place";
            case "destination":
                return "destination";
            case "level":
                return "level";
            case "personCount":
                return "person_count";
            case "ticketType":
                return "ticket_type";
            case "flightDistance":
                return "flight_distance";
            default:
                return "month_value";
        }
    }

    private String doAdd(BusinessTripVO businessTripVO) {
        BusinessTrip businessTrip = BusinessTripConverter.convert(businessTripVO);
        businessTripMapper.insertSelective(businessTrip);
        return businessTrip.getId();
    }

    private String doUpdate(BusinessTripVO businessTripVO) {
        BusinessTrip businessTrip = BusinessTripConverter.convert(businessTripVO);
        BusinessTripExample example = new BusinessTripExample();
        example.or().andIdEqualTo(businessTripVO.getId()).andLastUpdateVersionEqualTo(businessTripVO.getLastUpdateVersion());
        int updateCount = businessTripMapper.updateByExampleSelective(businessTrip, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return businessTrip.getId();
    }

    public List<BusinessTrip> selectByExample(BusinessTripExample example) {
        return businessTripMapper.selectByExample(example);
    }

    /**
     * 同步商务旅行数据
     * <AUTHOR>
     * @date 2025/3/5 14:06
     * @param syncAmbientEnergyBillQO
     */
    public void synchronizationData(SyncAmbientEnergyBillQO syncAmbientEnergyBillQO) {
        // 1、先查询当前的环境绩效数据
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        workflowControlService.throwIfHasWorkflowControl(syncAmbientEnergyBillQO.getHeadId(), "本期数据已经提交，不能进行同步");
        // 2、根据组织架构id、年份、月份查询环境绩效
        AmbientHeadVO syncAmbientHeadVO = ambientService.get(syncAmbientEnergyBillQO.getOrganizationId(), syncAmbientEnergyBillQO.getYear(), syncAmbientEnergyBillQO.getMonth());
        // 3、查出要同步月份的商务旅行
        BusinessTripExample example = new BusinessTripExample();
        example.or().andAmbientHeadIdEqualTo(syncAmbientHeadVO.getId());
        example.setOrderByClause("month_value, creation_time");
        List<BusinessTrip> syncBusinessTrips = businessTripMapper.selectByExample(example);
        UserInfo userInfo = ContextUtils.getCurrentUser();
        if (!CollectionUtils.isEmpty(syncBusinessTrips)) {
            AttachmentListQO qo = new AttachmentListQO();
            qo.setSection("商務旅行");
            for (BusinessTrip oldTrip : syncBusinessTrips) {
                qo.setRefId(oldTrip.getAmbientHeadId());
                qo.setCategory(oldTrip.getId());
                // 新增新的商务旅行
                BusinessTrip newBusinessTrip = new BusinessTrip();
                BeanUtils.copyProperties(oldTrip, newBusinessTrip);
                newBusinessTrip.setId(null);
                newBusinessTrip.setAmbientHeadId(syncAmbientEnergyBillQO.getHeadId());
                businessTripMapper.insertSelective(newBusinessTrip);
                // 查询是否有附件
                List<AttachmentVO> attachmentVOS = attachmentService.newList(qo);
                if (!CollectionUtils.isEmpty(attachmentVOS)) {
                    // 有附件复制商务旅行附件
                    for (AttachmentVO oldAttachment : attachmentVOS) {
                        // 生成模型
                        if (StringUtils.isNotEmpty(oldAttachment.getMinioFileName())) {
                            MinioAttachment x = new MinioAttachment();
                            BeanUtils.copyProperties(oldAttachment, x);
                            x.setId(null);
                            x.setRefId(newBusinessTrip.getAmbientHeadId());
                            x.setCategory(newBusinessTrip.getId());
                            x.setCreateUserId(userInfo.getId());
                            x.setCreateUsername(userInfo.getUsername());
                            x.setCreationTime(LocalDateTime.now());
                            minioAttachmentMapper.insert(x);
                        }else {
                            Attachment x = new Attachment();
                            BeanUtils.copyProperties(oldAttachment, x);
                            Attachment primaryKey = attachmentMapper.selectByPrimaryKey(oldAttachment.getId());
                            x.setId(null);
                            x.setRefId(newBusinessTrip.getAmbientHeadId());
                            x.setCategory(newBusinessTrip.getId());
                            x.setData(primaryKey.getData());
                            x.setCreateUserId(userInfo.getId());
                            x.setCreateUsername(userInfo.getUsername());
                            x.setCreationTime(LocalDateTime.now());
                            attachmentMapper.insert(x);
                        }
                    }
                }
            }
        }
    }
}
