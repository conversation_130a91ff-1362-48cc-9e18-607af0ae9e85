package com.csci.susdev.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import com.alibaba.fastjson.JSON;
import net.minidev.json.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.*;
import com.csci.susdev.model.*;
import com.csci.susdev.model.Permission;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class PermissionMenuService {
    
    @Autowired
    private PermissionMenuMapper mapper;
    
    @Autowired
    private PermissionMapper permissionMapper;

    public List<PermissionMenu> selectByExample(PermissionMenuExample example) {
        return mapper.selectByExample(example);
    }

    public List<PermissionMenu> getByMenuId(String menuId) {
        PermissionMenuExample example = new PermissionMenuExample();
        PermissionMenuExample.Criteria criteria = example.or();
        criteria.andMenuIdEqualTo(menuId);
        criteria.andIsDeletedEqualTo(false);
        List<PermissionMenu> lstPermissionMenu = mapper.selectByExample(example);
        return lstPermissionMenu;
    }

    public List<PermissionMenu> listPermissionMenuByCode(String permissionname) {
    	Permission permission = ServiceHelper.getPermissionByCode(permissionname, permissionMapper);
    	if(permission == null)
    		throw new ServiceException("找不到權限");
    	
        return ServiceHelper.listPermissionMenu(permission.getId(), mapper);
    }

    public List<PermissionMenu> listPermissionMenu(String permissionId) {
        return ServiceHelper.listPermissionMenu(permissionId, mapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<String> savePermissionMenuList(List<PermissionMenu> permissionMenuLst) {
    	List<String> idLst = new ArrayList<>();
    	for(PermissionMenu permissionMenu : permissionMenuLst) {
    		this.savePermissionMenu(permissionMenu);
            idLst.add(permissionMenu.getId());
    	}
        return idLst;
    }

    @Transactional(rollbackFor = Exception.class)
    public String savePermissionMenu(PermissionMenu permissionMenu) {
    	if(permissionMenu.getIsDeleted() == true) {
    		this.deletePermissionMenu(permissionMenu.getId());
    	} else {
	    	UserInfo currentUser = ContextUtils.getCurrentUser();
	    	LocalDateTime now = LocalDateTime.now();
	        if (StringUtils.isBlank(permissionMenu.getId())) {
	        	permissionMenu.setCreationTime(now);
	        	permissionMenu.setCreateUsername(currentUser.getUsername());
	        	permissionMenu.setLastUpdateTime(now);
	        	permissionMenu.setLastUpdateUsername(currentUser.getUsername());
	        	mapper.insertSelective(permissionMenu);
	        } else {
	        	permissionMenu.setLastUpdateTime(now);
	        	permissionMenu.setLastUpdateUsername(currentUser.getUsername());
	        	mapper.updateByPrimaryKeySelective(permissionMenu);
	        }
    	}
        return permissionMenu.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public int deletePermissionMenu(String id) {
        return mapper.deleteByPrimaryKey(id);
    }

    public List<PermissionMenu> getByMenuIds(Set<String> menuIds) {
        PermissionMenuExample example = new PermissionMenuExample();
        PermissionMenuExample.Criteria criteria = example.or();
        criteria.andMenuIdIn(new ArrayList<>(menuIds));
        criteria.andIsDeletedEqualTo(false);
        List<PermissionMenu> lstPermissionMenu = mapper.selectByExample(example);
        return lstPermissionMenu;
    }
}
