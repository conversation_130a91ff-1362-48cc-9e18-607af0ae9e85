package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.TzhCarbonPredictionDetail1Mapper;
import com.csci.susdev.mapper.TzhCarbonPredictionDetail2Mapper;
import com.csci.susdev.mapper.TzhCarbonPredictionHeadMapper;
import com.csci.susdev.model.TzhCarbonPredictionHead;
import com.csci.susdev.model.TzhCarbonPredictionDetail1;
import com.csci.susdev.model.TzhCarbonPredictionDetail2;
import com.csci.susdev.model.TzhCarbonPredictionDetail1Example;
import com.csci.susdev.model.TzhCarbonPredictionDetail2Example;
import com.csci.susdev.model.TzhCarbonPredictionHeadExample;
import com.csci.susdev.model.TzhCarbonPredictionHeadExample.Criteria;
import com.csci.susdev.qo.TzhCarbonPredictionExportQO;
import com.csci.susdev.qo.TzhCarbonPredictionPageableQO;
import com.csci.susdev.qo.TzhCarbonPredictionQO;
import com.csci.susdev.util.ExcelUtils;
import com.csci.susdev.util.IntegerUtils;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.vo.TzhCarbonPredictionDetail1VO;
import com.csci.tzh.vo.TzhCarbonPredictionDetail2VO;
import com.csci.tzh.vo.TzhCarbonPredictionVO;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.model.User;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class TzhCarbonPredictionService {


    @Autowired
    private UserService userService;

    @Autowired
    private TzhCarbonPredictionHeadMapper headMapper;

    @Autowired
    private TzhCarbonPredictionDetail1Mapper detail1Mapper;
    
    @Autowired
    private TzhCarbonPredictionDetail2Mapper detail2Mapper;

    public List<TzhCarbonPredictionHead> selectByExample(TzhCarbonPredictionHeadExample example) {
        return headMapper.selectByExample(example);
    }

    public List<TzhCarbonPredictionDetail1> selectByExample(TzhCarbonPredictionDetail1Example example) {
        return detail1Mapper.selectByExample(example);
    }
    
    public List<TzhCarbonPredictionDetail2> selectByExample(TzhCarbonPredictionDetail2Example example) {
        return detail2Mapper.selectByExample(example);
    }
    
	/**
	 * 資源和能源使用 - 車輛與商務旅行 數據導入
	 *
	 * @param file, tzhCarbonPredictionHead
	 * @return
	 */
	public TzhCarbonPredictionVO importExcel(MultipartFile file, TzhCarbonPredictionHead tzhCarbonPredictionHead, String ambientId) {
		TzhCarbonPredictionVO tzhCarbonPredictionVO = this.excelToData(file, tzhCarbonPredictionHead);
		String id = this.saveTzhCarbonPrediction(tzhCarbonPredictionVO, ambientId);
		tzhCarbonPredictionVO.setId(id);
		return tzhCarbonPredictionVO;
	}

	/**
	 * 資源和能源使用 - 車輛與商務旅行 數據導出
	 *
	 * @param tzhCarbonPredictionExportQO
	 * @return
	 */
	public ByteArrayInputStream exportExcel(TzhCarbonPredictionExportQO tzhCarbonPredictionExportQO) {
		TzhCarbonPredictionVO tzhCarbonPredictionVO = this.getTzhCarbonPrediction(tzhCarbonPredictionExportQO.getTzhCarbonPredictionQO());
		if(tzhCarbonPredictionVO == null) throw new ServiceException("找不到數據");
		return this.dataToExcel(tzhCarbonPredictionExportQO.getSheetTitles(), tzhCarbonPredictionExportQO.getHeaders(), tzhCarbonPredictionVO);
	}

	/**
	 * 資源和能源使用 - 車輛與商務旅行 Excel轉換成数据
	 *
	 * @param file, tzhCarbonPredictionHead
	 * @return
	 */
	private TzhCarbonPredictionVO excelToData(MultipartFile file, TzhCarbonPredictionHead tzhCarbonPredictionHead) {

        try (InputStream is = file.getInputStream();
            Workbook workbook = ExcelUtils.getWorkbookFromUrl(is, file.getOriginalFilename())) {
        	
			TzhCarbonPredictionVO tzhCarbonPredictionVO = new TzhCarbonPredictionVO();
			BeanUtils.copyProperties(tzhCarbonPredictionHead, tzhCarbonPredictionVO);
			
			Sheet sheet = workbook.getSheetAt(0);
			Iterator<Row> rows = sheet.iterator();
			
			List<TzhCarbonPredictionDetail1VO> lstTzhCarbonPredictionDetail1VO = new ArrayList<>();
			int rowNumber = 0;
			while (rows.hasNext()) {
				Row currentRow = rows.next();
				if (rowNumber == 0) {
					rowNumber++;
					continue;
				}
				Iterator<Cell> cellsInRow = currentRow.iterator();
				
				TzhCarbonPredictionDetail1VO tzhCarbonPredictionDetail1VO = new TzhCarbonPredictionDetail1VO();
				
				int cellIdx = 0;
				while (cellsInRow.hasNext()) {
					Cell currentCell = cellsInRow.next();
					switch (cellIdx) {
					case 0:
						tzhCarbonPredictionDetail1VO.setCol1(currentCell.getStringCellValue());
						break;
					case 1:
						tzhCarbonPredictionDetail1VO.setCol2(currentCell.getStringCellValue());
						break;
					case 2:
						tzhCarbonPredictionDetail1VO.setCol3(currentCell.getStringCellValue());
						break;
					case 3:
						tzhCarbonPredictionDetail1VO.setCol4(currentCell.getStringCellValue());
						break;
					case 4:
						tzhCarbonPredictionDetail1VO.setCol5(currentCell.getStringCellValue());
						break;
					case 5:
						tzhCarbonPredictionDetail1VO.setCol6(currentCell.getStringCellValue());
						break;
					default:
						break;
					}
					cellIdx++;
				}
				lstTzhCarbonPredictionDetail1VO.add(tzhCarbonPredictionDetail1VO);
			}
			tzhCarbonPredictionVO.setLstDetail1(lstTzhCarbonPredictionDetail1VO);
			
			sheet = workbook.getSheetAt(1);
			rows = sheet.iterator();
			
			List<TzhCarbonPredictionDetail2VO> lstTzhCarbonPredictionDetail2VO = new ArrayList<>();
			rowNumber = 0;
			while (rows.hasNext()) {
				Row currentRow = rows.next();
				if (rowNumber == 0) {
					rowNumber++;
					continue;
				}
				Iterator<Cell> cellsInRow = currentRow.iterator();
				
				TzhCarbonPredictionDetail2VO tzhCarbonPredictionDetail2VO = new TzhCarbonPredictionDetail2VO();
				
				int cellIdx = 0;
				while (cellsInRow.hasNext()) {
					Cell currentCell = cellsInRow.next();
					switch (cellIdx) {
					case 0:
						tzhCarbonPredictionDetail2VO.setCol1(currentCell.getStringCellValue());
						break;
					case 1:
						tzhCarbonPredictionDetail2VO.setCol2(currentCell.getStringCellValue());
						break;
					case 2:
						tzhCarbonPredictionDetail2VO.setCol3(currentCell.getStringCellValue());
						break;
					case 3:
						tzhCarbonPredictionDetail2VO.setCol4(currentCell.getStringCellValue());
						break;
					case 4:
						tzhCarbonPredictionDetail2VO.setCol5(currentCell.getStringCellValue());
						break;
					case 5:
						tzhCarbonPredictionDetail2VO.setCol6(currentCell.getStringCellValue());
						break;
					case 6:
						tzhCarbonPredictionDetail2VO.setCol6(currentCell.getStringCellValue());
						break;
					default:
						break;
					}
					cellIdx++;
				}
				lstTzhCarbonPredictionDetail2VO.add(tzhCarbonPredictionDetail2VO);
			}
			tzhCarbonPredictionVO.setLstDetail2(lstTzhCarbonPredictionDetail2VO);
			
			workbook.close();
			return tzhCarbonPredictionVO;
		} catch (IOException e) {
			throw new RuntimeException("fail to parse Excel file: " + e.getMessage());
		}
	}

	/**
	 * 資源和能源使用 - 車輛與商務旅行 数据轉換成Excel
	 *
	 * @param sheetTitles, headers, tzhCarbonPredictionVO
	 * @return
	 */
	private ByteArrayInputStream dataToExcel(String[] sheetTitles, String[][] headers, TzhCarbonPredictionVO tzhCarbonPredictionVO) {
		try (Workbook workbook = new XSSFWorkbook(); ByteArrayOutputStream out = new ByteArrayOutputStream();) {
			Sheet sheet = workbook.createSheet(sheetTitles[0]);
			Row headerRow = sheet.createRow(0);
			for (int col = 0; col < headers[0].length; col++) {
				Cell cell = headerRow.createCell(col);
                CellStyle style = workbook.createCellStyle(); 
                style.setWrapText(true); 
                cell.setCellStyle(style); 
				cell.setCellValue(headers[0][col]);
			}
			int rowIdx = 1;
			for (TzhCarbonPredictionDetail1VO detail1VO : tzhCarbonPredictionVO.getLstDetail1()) {
				int cellIdx = 0;
				Row row = sheet.createRow(rowIdx++);
				row.createCell(cellIdx++).setCellValue(detail1VO.getCol1());
				row.createCell(cellIdx++).setCellValue(detail1VO.getCol2());
				row.createCell(cellIdx++).setCellValue(detail1VO.getCol3());
				row.createCell(cellIdx++).setCellValue(detail1VO.getCol4());
				row.createCell(cellIdx++).setCellValue(detail1VO.getCol5());
				row.createCell(cellIdx++).setCellValue(detail1VO.getCol6());
				
			}
			
			sheet = workbook.createSheet(sheetTitles[1]);
			headerRow = sheet.createRow(0);
			for (int col = 0; col < headers[1].length; col++) {
				Cell cell = headerRow.createCell(col);
				cell.setCellValue(headers[1][col]);
			}
			rowIdx = 1;
			for (TzhCarbonPredictionDetail2VO detail2VO : tzhCarbonPredictionVO.getLstDetail2()) {
				int cellIdx = 0;
				Row row = sheet.createRow(rowIdx++);
				row.createCell(cellIdx++).setCellValue(detail2VO.getCol1());
				row.createCell(cellIdx++).setCellValue(detail2VO.getCol2());
				row.createCell(cellIdx++).setCellValue(detail2VO.getCol3());
				row.createCell(cellIdx++).setCellValue(detail2VO.getCol4());
				row.createCell(cellIdx++).setCellValue(detail2VO.getCol5());
				row.createCell(cellIdx++).setCellValue(detail2VO.getCol6());
				
			}
			ExcelUtils.autoSizeColumns(workbook, 22);
			workbook.write(out);
			return new ByteArrayInputStream(out.toByteArray());
		} catch (IOException e) {
			throw new RuntimeException("fail to import data to Excel file: " + e.getMessage());
		}
	}
    
    /**
     * 資源和能源使用 - 車輛與商務旅行 数据列表
     *
     * @param resourcesEnergyQO
     * @return
     */
    public ResultPage<TzhCarbonPredictionVO> listTzhCarbonPrediction(TzhCarbonPredictionPageableQO tzhCarbonPredictionQO) {
        // 查询头行信息
    	TzhCarbonPredictionHeadExample headExample = new TzhCarbonPredictionHeadExample();
        Criteria criteria = headExample.or();
        if(StringUtils.isNotBlank(tzhCarbonPredictionQO.getOrganizationId())) {
        	criteria.andOrganizationIdEqualTo(tzhCarbonPredictionQO.getOrganizationId());
        }
        if(IntegerUtils.isNotNullOrZero(tzhCarbonPredictionQO.getYear())) {
        	criteria.andReportingYearEqualTo(tzhCarbonPredictionQO.getYear());
        }
        if(IntegerUtils.isNotNullOrZero(tzhCarbonPredictionQO.getMonth())) {
        	criteria.andReportingMonthEqualTo(tzhCarbonPredictionQO.getMonth());
        }
        if(StringUtils.isNotBlank(tzhCarbonPredictionQO.getFormCode())) {
        	criteria.andFormCodeEqualTo(tzhCarbonPredictionQO.getFormCode());
        }
        criteria.andIsDeletedEqualTo(false);
        headExample.setOrderByClause("reporting_date DESC, is_audited DESC, is_submitted DESC");
        
        PageHelper.startPage(tzhCarbonPredictionQO.getCurPage(), tzhCarbonPredictionQO.getPageSize());
        
        List<TzhCarbonPredictionHead> lstHead = headMapper.selectByExample(headExample);
        
        Page<TzhCarbonPredictionVO> lstResultVO = new Page<>();
        
        for(TzhCarbonPredictionHead head: lstHead) {
	        TzhCarbonPredictionVO resultVO = new TzhCarbonPredictionVO();
	        BeanUtils.copyProperties(head, resultVO);

	
	        // 根据headId查询出对应的明细行，detail表
	        TzhCarbonPredictionDetail1Example detail1Example = new TzhCarbonPredictionDetail1Example();
	        detail1Example.or().andHeadIdEqualTo(head.getId());
	        detail1Example.setOrderByClause("seq");
	        
	        List<TzhCarbonPredictionDetail1> lstDetail1 = detail1Mapper.selectByExample(detail1Example);
	        List<TzhCarbonPredictionDetail1VO> lstDetail1VO = lstDetail1.stream().map(x -> {
	        	TzhCarbonPredictionDetail1VO vo = new TzhCarbonPredictionDetail1VO();
	            BeanUtils.copyProperties(x, vo);
	            return vo;
	        }).collect(Collectors.toList());
	
	        resultVO.setLstDetail1(lstDetail1VO);
	        
	        TzhCarbonPredictionDetail2Example detail2Example = new TzhCarbonPredictionDetail2Example();
	        detail2Example.or().andHeadIdEqualTo(head.getId());
	        detail2Example.setOrderByClause("seq");
	        
	        List<TzhCarbonPredictionDetail2> lstDetail2 = detail2Mapper.selectByExample(detail2Example);
	        List<TzhCarbonPredictionDetail2VO> lstDetail2VO = lstDetail2.stream().map(x -> {
	        	TzhCarbonPredictionDetail2VO vo = new TzhCarbonPredictionDetail2VO();
	            BeanUtils.copyProperties(x, vo);
	            return vo;
	        }).collect(Collectors.toList());
	
	        resultVO.setLstDetail2(lstDetail2VO);
            lstResultVO.add(resultVO);
        }
        ResultPage<TzhCarbonPredictionVO> resultPage = new ResultPage<>(lstHead);
        resultPage.setList(lstResultVO);
        
        return resultPage;
    }
    
    
    /**
     * 資源和能源使用 - 車輛與商務旅行 数据
     *
     * @param resourcesEnergyQO
     * @return
     */
    public TzhCarbonPredictionVO getTzhCarbonPrediction(TzhCarbonPredictionQO tzhCarbonPredictionQO) {
        // 查询头行信息
    	TzhCarbonPredictionHeadExample headExample = new TzhCarbonPredictionHeadExample();
        Criteria criteria = headExample.or();
        if(StringUtils.isNotBlank(tzhCarbonPredictionQO.getOrganizationId())) {
        	criteria.andOrganizationIdEqualTo(tzhCarbonPredictionQO.getOrganizationId());
        }
        if(IntegerUtils.isNotNullOrZero(tzhCarbonPredictionQO.getYear())) {
        	criteria.andReportingYearEqualTo(tzhCarbonPredictionQO.getYear());
        }
        if(IntegerUtils.isNotNullOrZero(tzhCarbonPredictionQO.getMonth())) {
        	criteria.andReportingMonthEqualTo(tzhCarbonPredictionQO.getMonth());
        }
        if(StringUtils.isNotBlank(tzhCarbonPredictionQO.getFormCode())) {
        	criteria.andFormCodeEqualTo(tzhCarbonPredictionQO.getFormCode());
        }
        criteria.andIsDeletedEqualTo(false);
        headExample.setOrderByClause("reporting_date DESC, is_audited DESC, is_submitted DESC");
        
        List<TzhCarbonPredictionHead> lstHead = headMapper.selectByExample(headExample);
        
        if (lstHead.size() == 0) 
            return null;
        
        TzhCarbonPredictionHead head = lstHead.get(0);
        TzhCarbonPredictionVO resultVO = new TzhCarbonPredictionVO();
        BeanUtils.copyProperties(head, resultVO);

        // 根据headId查询出对应的明细行，detail表
        TzhCarbonPredictionDetail1Example detail1Example = new TzhCarbonPredictionDetail1Example();
        detail1Example.or().andHeadIdEqualTo(head.getId());
        detail1Example.setOrderByClause("seq");
        
        List<TzhCarbonPredictionDetail1> lstDetail1 = detail1Mapper.selectByExample(detail1Example);
        List<TzhCarbonPredictionDetail1VO> lstDetail1VO = lstDetail1.stream().map(x -> {
        	TzhCarbonPredictionDetail1VO vo = new TzhCarbonPredictionDetail1VO();
            BeanUtils.copyProperties(x, vo);
            return vo;
        }).collect(Collectors.toList());

        resultVO.setLstDetail1(lstDetail1VO);
        
        TzhCarbonPredictionDetail2Example detail2Example = new TzhCarbonPredictionDetail2Example();
        detail2Example.or().andHeadIdEqualTo(head.getId());
        detail2Example.setOrderByClause("seq");
        
        List<TzhCarbonPredictionDetail2> lstDetail2 = detail2Mapper.selectByExample(detail2Example);
        List<TzhCarbonPredictionDetail2VO> lstDetail2VO = lstDetail2.stream().map(x -> {
        	TzhCarbonPredictionDetail2VO vo = new TzhCarbonPredictionDetail2VO();
            BeanUtils.copyProperties(x, vo);
            return vo;
        }).collect(Collectors.toList());

        resultVO.setLstDetail2(lstDetail2VO);
        
        return resultVO;
    }
    /**
     * 提交 碳中和 - 減排預測表 VO 信息
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param tzhCarbonPredictionVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String submitTzhCarbonPrediction(TzhCarbonPredictionVO tzhCarbonPredictionVO, String ambientId) {
    	tzhCarbonPredictionVO.setIsSubmitted(true);
    	tzhCarbonPredictionVO.setIsAudited(false);
    	tzhCarbonPredictionVO.setAuditNode(0);
    	tzhCarbonPredictionVO.setIsDeleted(false);
    	return saveTzhCarbonPrediction(tzhCarbonPredictionVO, ambientId);
    }
    
    /**
     * 保存 碳中和 - 減排預測表 VO 信息
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param tzhCarbonPredictionVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveTzhCarbonPredictionWithStatus(TzhCarbonPredictionVO tzhCarbonPredictionVO, String ambientId) {
    	tzhCarbonPredictionVO.setIsSubmitted(false);
    	tzhCarbonPredictionVO.setIsAudited(false);
    	tzhCarbonPredictionVO.setAuditNode(0);
    	tzhCarbonPredictionVO.setIsDeleted(false);
    	return saveTzhCarbonPrediction(tzhCarbonPredictionVO, ambientId);
    }
    
    /**
     * 保存 碳中和 - 減排預測表 VO 信息
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param tzhCarbonPredictionVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveTzhCarbonPrediction(TzhCarbonPredictionVO tzhCarbonPredictionVO, String ambientId) {
        TzhCarbonPredictionHead tzhCarbonPredictionHead = new TzhCarbonPredictionHead();
        BeanUtils.copyProperties(tzhCarbonPredictionVO, tzhCarbonPredictionHead);
        String headId = saveTzhCarbonPredictionHead(tzhCarbonPredictionHead);

        List<TzhCarbonPredictionDetail1> lstDetail1 = tzhCarbonPredictionVO.getLstDetail1().stream().map(vo -> {
        	TzhCarbonPredictionDetail1 x = new TzhCarbonPredictionDetail1();
            BeanUtils.copyProperties(vo, x);
            x.setHeadId(headId);
            return x;
        }).collect(Collectors.toList());
        int seq = 0;
        for(TzhCarbonPredictionDetail1 tzhCarbonPredictionDetail1: lstDetail1) {
        	tzhCarbonPredictionDetail1.setSeq(++seq);
            saveTzhCarbonPredictionDetail1(tzhCarbonPredictionDetail1);
        }
        
        List<TzhCarbonPredictionDetail2> lstDetail2 = tzhCarbonPredictionVO.getLstDetail2().stream().map(vo -> {
        	TzhCarbonPredictionDetail2 x = new TzhCarbonPredictionDetail2();
            BeanUtils.copyProperties(vo, x);
            x.setHeadId(headId);
            return x;
        }).collect(Collectors.toList());
        seq = 0;
        for(TzhCarbonPredictionDetail2 tzhCarbonPredictionDetail2: lstDetail2) {
        	tzhCarbonPredictionDetail2.setSeq(++seq);
            saveTzhCarbonPredictionDetail2(tzhCarbonPredictionDetail2);
        }
        
        return headId;
    }
    
    /**
     * 保存 碳中和 - 減排預測表 头行信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param tzhCarbonPredictionHead
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveTzhCarbonPredictionHead(TzhCarbonPredictionHead tzhCarbonPredictionHead) {
    	UserInfo currentUser = ContextUtils.getCurrentUser();
    	LocalDateTime now = LocalDateTime.now();
        if (StringUtils.isBlank(tzhCarbonPredictionHead.getId())) {
        	tzhCarbonPredictionHead.setCreationTime(now);
        	tzhCarbonPredictionHead.setCreateUsername(currentUser.getUsername());
        	tzhCarbonPredictionHead.setLastUpdateTime(now);
        	tzhCarbonPredictionHead.setLastUpdateUsername(currentUser.getUsername());
        	headMapper.insertSelective(tzhCarbonPredictionHead);
        } else {
        	tzhCarbonPredictionHead.setLastUpdateTime(now);
        	tzhCarbonPredictionHead.setLastUpdateUsername(currentUser.getUsername());
        	headMapper.updateByPrimaryKeySelective(tzhCarbonPredictionHead);
        }
        return tzhCarbonPredictionHead.getId();
    }

    /**
     * 保存 碳中和 - 減排預測表 明细行信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param tzhCarbonPredictiondetail1
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveTzhCarbonPredictionDetail1(TzhCarbonPredictionDetail1 tzhCarbonPredictiondetail1) {
        if (StringUtils.isBlank(tzhCarbonPredictiondetail1.getId())) {
        	detail1Mapper.insertSelective(tzhCarbonPredictiondetail1);
        } else {
        	detail1Mapper.updateByPrimaryKeySelective(tzhCarbonPredictiondetail1);
        }
        return tzhCarbonPredictiondetail1.getId();
    }
    
    /**
     * 保存 碳中和 - 減排預測表 明细行信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param tzhCarbonPredictiondetail2
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveTzhCarbonPredictionDetail2(TzhCarbonPredictionDetail2 tzhCarbonPredictiondetail2) {
        if (StringUtils.isBlank(tzhCarbonPredictiondetail2.getId())) {
        	detail2Mapper.insertSelective(tzhCarbonPredictiondetail2);
        } else {
        	detail2Mapper.updateByPrimaryKeySelective(tzhCarbonPredictiondetail2);
        }
        return tzhCarbonPredictiondetail2.getId();
    }
}
