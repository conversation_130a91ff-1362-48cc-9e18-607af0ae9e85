package com.csci.susdev.service;

import com.alibaba.fastjson.JSON;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.mapper.*;
import com.csci.tzh.model.TzhEstimateConstructionWaste;
import com.csci.tzh.model.TzhEstimateConstructionWasteExample;
import com.csci.tzh.qo.*;
import com.csci.tzh.vo.*;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhEstimateConstructionWasteService {

	@Autowired
	private TzhEstimateConstructionWasteMapper mapper;

	@Autowired
	private TzhEstimateConstructionWasteCustomMapper customMapper;

	@Autowired
	private TzhEstimateConstructionWasteDisposalMethodCustomMapper disposalMethodCustomMapper;

	@Autowired
	private TzhEstimateConstructionWasteCategoryCustomMapper categoryCustomMapper;

	@Autowired
	private TzhEstimateConstructionWasteSubCategoryCustomMapper subCategoryCustomMapper;

	public List<TzhEstimateConstructionWaste> selectByExample(TzhEstimateConstructionWasteExample example) {
		return mapper.selectByExample(example);
	}

	public ResultPage<TzhEstimateConstructionWasteVO> list(SiteNameProtocolPageableQO qo) {
		PageHelper.startPage(qo.getCurPage(), qo.getPageSize(), "P.NameEN, ECW.SiteName");

		List<TzhEstimateConstructionWasteVO> lst = customMapper.list(qo.getSiteName(), qo.getProtocol());
		ResultPage<TzhEstimateConstructionWasteVO> resultPage = new ResultPage<>(lst, true);

		return resultPage;
	}


	@Transactional(rollbackFor = Exception.class)
	public TzhEstimateConstructionWaste save(TzhEstimateConstructionWaste newModel) {
		UserInfo currentUser = ContextUtils.getCurrentUser();

		if(newModel.getIsdeleted() == false) {
			if(StringUtils.isNotBlank(newModel.getId())) {
				// 備份已刪除數據
				TzhEstimateConstructionWasteExample originalExample = new TzhEstimateConstructionWasteExample();
				TzhEstimateConstructionWasteExample.Criteria originalCriteria = originalExample.or();
				originalCriteria.andIdEqualTo(newModel.getId());
				List<TzhEstimateConstructionWaste> lstOriginalModel = mapper.selectByExample(originalExample);
				if(lstOriginalModel.size() > 0) {
					TzhEstimateConstructionWaste originalModel = lstOriginalModel.get(0);
					originalModel.setId(UUID.randomUUID().toString());
					originalModel.setIsdeleted(true);
					originalModel.setDeletedby(currentUser.getUsername());
					originalModel.setDeletedtime(LocalDateTime.now());
					mapper.insertSelective(originalModel);
				}

				// 新增數據(保留舊ID)
				TzhEstimateConstructionWasteExample newExample = new TzhEstimateConstructionWasteExample();
				TzhEstimateConstructionWasteExample.Criteria newCriteria = newExample.or();
				newCriteria.andIdEqualTo(newModel.getId());
				newCriteria.andIsdeletedEqualTo(false);
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				newModel.setDeletedby(null);
				newModel.setDeletedtime(null);
				mapper.deleteByExample(newExample);
				mapper.insertSelective(newModel);
			} else {
				// 新增數據(新建ID)
				newModel.setId(UUID.randomUUID().toString());
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				mapper.insertSelective(newModel);
			}
		} else {
			// 刪除數據
			TzhEstimateConstructionWasteExample example = new TzhEstimateConstructionWasteExample();
			TzhEstimateConstructionWasteExample.Criteria criteria = example.or();
			criteria.andIdEqualTo(newModel.getId());
			newModel.setIsdeleted(true);
			newModel.setDeletedby(currentUser.getUsername());
			newModel.setDeletedtime(LocalDateTime.now());
			mapper.updateByExampleSelective(newModel, example);
		}

		return newModel;
	}

	@Transactional(rollbackFor = Exception.class)
	public List<TzhEstimateConstructionWaste> saveAll(List<TzhEstimateConstructionWaste> newModelList) {
		UserInfo currentUser = ContextUtils.getCurrentUser();

		for(TzhEstimateConstructionWaste newModel : newModelList) {
			try {
				if(newModel.getIsdeleted() == false) {
					if(StringUtils.isNotBlank(newModel.getId())) {
						// 備份已刪除數據
						TzhEstimateConstructionWasteExample originalExample = new TzhEstimateConstructionWasteExample();
						TzhEstimateConstructionWasteExample.Criteria originalCriteria = originalExample.or();
						originalCriteria.andIdEqualTo(newModel.getId());
						List<TzhEstimateConstructionWaste> lstOriginalModel = mapper.selectByExample(originalExample);
						if(lstOriginalModel.size() > 0) {
							TzhEstimateConstructionWaste originalModel = lstOriginalModel.get(0);
							originalModel.setId(UUID.randomUUID().toString());
							originalModel.setIsdeleted(true);
							originalModel.setDeletedby(currentUser.getUsername());
							originalModel.setDeletedtime(LocalDateTime.now());
							mapper.insertSelective(originalModel);
						}

						// 新增數據(保留舊ID)
						TzhEstimateConstructionWasteExample newExample = new TzhEstimateConstructionWasteExample();
						TzhEstimateConstructionWasteExample.Criteria newCriteria = newExample.or();
						newCriteria.andIdEqualTo(newModel.getId());
						newCriteria.andIsdeletedEqualTo(false);
						newModel.setCreatedby(currentUser.getUsername());
						newModel.setCreatedtime(LocalDateTime.now());
						newModel.setDeletedby(null);
						newModel.setDeletedtime(null);
						mapper.deleteByExample(newExample);
						mapper.insertSelective(newModel);
					} else {
						// 新增數據(新建ID)
						newModel.setId(UUID.randomUUID().toString());
						newModel.setCreatedby(currentUser.getUsername());
						newModel.setCreatedtime(LocalDateTime.now());
						mapper.insertSelective(newModel);
					}
				} else {
					// 刪除數據
					TzhEstimateConstructionWasteExample example = new TzhEstimateConstructionWasteExample();
					TzhEstimateConstructionWasteExample.Criteria criteria = example.or();
					criteria.andIdEqualTo(newModel.getId());
					newModel.setIsdeleted(true);
					newModel.setDeletedby(currentUser.getUsername());
					newModel.setDeletedtime(LocalDateTime.now());
					mapper.updateByExampleSelective(newModel, example);
				}
			} catch(Exception ex) {
				throw new ServiceException("錄入數據出問題:" + JSON.toJSONString(newModel));
			}
		}

		return newModelList;
	}

	public List<TzhEstimateConstructionWasteDisposalMethodVO> listDisposalMethod() {
		return disposalMethodCustomMapper.list();
	}

	public List<TzhEstimateConstructionWasteCategoryVO> listSubCategory(ProtocolQO qo) {
		List<TzhEstimateConstructionWasteCategoryVO> lst = categoryCustomMapper.list(qo.getProtocol());

		for(TzhEstimateConstructionWasteCategoryVO vo : lst) {
			vo.setSubcategorylist(subCategoryCustomMapper.list(vo.getId()));
		}

		return lst;
	}
}
