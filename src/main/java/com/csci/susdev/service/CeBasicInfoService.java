package com.csci.susdev.service;

import cn.hutool.core.util.StrUtil;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.constant.WorkflowControlState;
import com.csci.susdev.mapper.CeBasicInfoDetailMapper;
import com.csci.susdev.mapper.CeBasicInfoHeadMapper;
import com.csci.susdev.mapper.CeOperationDataDetailMapper;
import com.csci.susdev.mapper.OrganizationMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.*;
import com.csci.susdev.qo.CeBasicInfoQO;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.*;
import com.csci.tzh.mapper.TzhProjectInfoCustomMapper;
import com.csci.tzh.model.TzhProjectInfo;
import com.csci.tzh.vo.TzhProjectInfoVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class CeBasicInfoService {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(CeBasicInfoService.class);
    /**
     * 初始化记录的过期时间
     */
    @Value("${lock.init.head.expire.milis:1000}")
    private long lockInitHeadExpireMilis;
    /**
     * 查询head记录时，获取锁失败循环等待的次数
     */
    @Value("${query.head.wait.loop.times:5}")
    private int lockQueryHeadWaitLoopTimes;
    /**
     * 查询head记录时，获取锁失败循环时每次休眠的时间
     */
    @Value("${query.head.wait.sleep.milis:50}")
    private int lockQueryHeadWaitSleepMilis;

    @Resource
    private CeBasicInfoHeadMapper ceBasicInfoHeadMapper;

    @Resource
    private CeBasicInfoDetailMapper ceBasicInfoDetailMapper;

    @Resource
    private CeOperationDataDetailMapper ceOperationDataDetailMapper;

    @Resource
    private WorkflowControlService workflowControlService;

    @Resource
    private OrganizationMapper organizationMapper;

    @Resource
    private TzhProjectInfoCustomMapper tzhProjectInfoCustomMapper;

    @Resource
    private TzhProjectInfoService tzhProjectInfoService;

    /**
     * 根据查询条件查询碳排基礎訊息信息
     * 只会查询 active = true 的记录
     *
     * @param organizationId 组织 id
     * @param year           年份
     * @param month          月份
     * @return
     */
    CeBasicInfoHead getCeBasicInfoHeadBy(String organizationId, Integer year, Integer month) {
        CeBasicInfoHeadExample example = new CeBasicInfoHeadExample();
        example.or().andOrganizationIdEqualTo(organizationId).andYearEqualTo(year)
                .andMonthEqualTo(month).andIsActiveEqualTo(Boolean.TRUE);
        return ceBasicInfoHeadMapper.selectByExample(example).stream().findFirst().orElse(null);
    }

    /**
     * @param qo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CeBasicInfoHead createCeBasicInfoHead(CeBasicInfoQO qo) {
        /*String key = "createCeBasicInfoHead-" + qo.getOrganizationId() + qo.getYear();
        boolean getLock = RedisLockUtil.lock(key);
        if (!getLock) {
            throw new ServiceException("获取锁失败");
        }
        try {
        */
            CeBasicInfoHead ceBasicInfoHead = getCeBasicInfoHeadBy(qo.getOrganizationId(), qo.getYear(),
                    qo.getMonth());
            if (ceBasicInfoHead == null) {
                ceBasicInfoHead = new CeBasicInfoHead();
                ceBasicInfoHead.setOrganizationId(qo.getOrganizationId());
                ceBasicInfoHead.setYear(qo.getYear());
                ceBasicInfoHead.setMonth(qo.getMonth());
                ceBasicInfoHead.setIsActive(Boolean.TRUE);
                ceBasicInfoHead.setLastUpdateVersion(0);
                ceBasicInfoHeadMapper.insertSelective(ceBasicInfoHead);
            }
            return ceBasicInfoHead;
        /*} finally {
            RedisLockUtil.unlock(key);
        }*/
    }

    /**
     * 根据查询条件查询碳排基礎訊息信息，如果不存在则创建
     *
     * @param ceBasicInfoQO 查询条件
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CeBasicInfoHeadVO getOrInit(CeBasicInfoQO ceBasicInfoQO) {
        checkExist(ceBasicInfoQO.getOrganizationId(), "组织编号不能为空");
        checkExist(ceBasicInfoQO.getYear(), "年份不能为空");
        if(ObjectUtils.isEmpty(ceBasicInfoQO.getMonth())) {
            ceBasicInfoQO.setMonth(12);
        }

        if (ceBasicInfoQO.getYear() < 1900 || ceBasicInfoQO.getYear() > 2100) {
            throw new ServiceException("年份必须在 1900-2100 之间");
        }

        //查询或初始化记录，如果获取失败，直接抛出异常退出
        CeBasicInfoHead ceBasicInfoHead = getOrInitHeadRecord(ceBasicInfoQO);

        // 查询流程审批状态
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(ceBasicInfoHead.getId());
        CeBasicInfoHeadVO ceBasicInfoHeadVO = CeBasicInfoHeadConverter.convert(ceBasicInfoHead);
        if (Objects.nonNull(workflowControl)) {
            ceBasicInfoHeadVO.setWorkflowControlState(workflowControl.getState());
            ceBasicInfoHeadVO.setWorkflowControlStateName(Optional.ofNullable(WorkflowControlState.getWorkflowControlState(workflowControl.getState())).map(WorkflowControlState::getName).orElse(null));
        }
        return ceBasicInfoHeadVO;
    }

    /**
     * 查询或初始化记录，如果获取失败，直接抛出异常退出
     * @param
     * @return
     */
    private CeBasicInfoHead getOrInitHeadRecord(CeBasicInfoQO ceBasicInfoQO) {
        CeBasicInfoHead ceBasicInfoHead = getCeBasicInfoHeadBy(ceBasicInfoQO.getOrganizationId(),
                ceBasicInfoQO.getYear(), ceBasicInfoQO.getMonth());
        if (Objects.isNull(ceBasicInfoHead)) {
            String key = StrUtil.format(SusDevConsts.DISTRIBUTE_LOCK_KEY_FORMAT, SusDevConsts.PROJECT_PREFIX,
                    "ceBasicInfoHead:getOrInit", ceBasicInfoQO.getOrganizationId(),
                ceBasicInfoQO.getYear(), ceBasicInfoQO.getMonth());
            //超时时间1s
            boolean redisLock = RedisLockUtil.lock(key, lockInitHeadExpireMilis);
            if (!redisLock) {
                //获取锁失败，停顿50ms一次，停顿5次
                int count = lockQueryHeadWaitLoopTimes;
                while(count-- > 0){
                    ceBasicInfoHead = getCeBasicInfoHeadBy(ceBasicInfoQO.getOrganizationId(),
                            ceBasicInfoQO.getYear(), ceBasicInfoQO.getMonth());
                    if(ceBasicInfoHead != null){
                        break;
                    }
                    try {
                        Thread.sleep(lockQueryHeadWaitSleepMilis);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }else{
                try{
                    ceBasicInfoHead = createCeBasicInfoHead(ceBasicInfoQO);
                    // 初始化明细数据
                    CeBasicInfoHead prevHead = getPrevHead(ceBasicInfoQO.getOrganizationId(),
                            ceBasicInfoQO.getYear(), ceBasicInfoQO.getMonth());
                    if (ObjectUtils.isNotEmpty(prevHead)) {
                        initDetailsWithPrevData(prevHead.getId(), ceBasicInfoHead.getId());
                    }
                }
                finally {
                    RedisLockUtil.unlock(key);
                }
            }
        }
        //如果最终没拿到数据，直接返回
        if(ceBasicInfoHead == null){
            throw new ServiceException("生成记录失败, 请稍后再尝试！");
        }
        return ceBasicInfoHead;
    }

    public CeBasicInfoHead getPrevHead(String organizationId, Integer year, Integer month) {
        CeBasicInfoHeadExample example = new CeBasicInfoHeadExample();
        example.or().andIsActiveEqualTo(true).andOrganizationIdEqualTo(organizationId)
                .andYearEqualTo(year).andMonthLessThan(month);
        example.setOrderByClause("month desc");
        List<CeBasicInfoHead> lst = selectByExample(example);
        if(lst.size() > 0) {
            return lst.get(0);
        } else {
            return null;
        }
    }

    void initDetailsWithPrevData(String prevHeadId, String curHeadId) {
        CeBasicInfoDetailExample example = new CeBasicInfoDetailExample();
        example.or().andHeadIdEqualTo(prevHeadId);
        List<CeBasicInfoDetail> details = ceBasicInfoDetailMapper.selectByExample(example);
        for(CeBasicInfoDetail detail : details) {
            detail.setId(null);
            detail.setHeadId(curHeadId);
            ceBasicInfoDetailMapper.insert(detail);
        }
    }


    /**
     * 根据主表id查询碳排基礎訊息明细信息
     *
     * @param headId 碳排基礎訊息头 id
     * @return
     */
    public List<CeBasicInfoDetailVO> listCeBasicInfoDetail(String headId) {
        checkExist(headId, "碳排基礎訊息主表id不能为空");
        CeBasicInfoDetailExample example = new CeBasicInfoDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq");
        return ceBasicInfoDetailMapper.selectByExample(example).stream().map(new CeBasicInfoDetailConverter()::convert).collect(Collectors.toList());
    }

    /**
     * 根据主表id查询碳排經營數據明细信息
     *
     * @param headId 碳排經營數據头 id
     * @return
     */
    public List<CeOperationDataDetailVO> listCeOperationDataDetail(String headId) {
        checkExist(headId, "碳排經營數據主表id不能为空");
        CeOperationDataDetailExample example = new CeOperationDataDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq");
        return ceOperationDataDetailMapper.selectByExample(example).stream().map(new CeOperationDataDetailConverter()::convert).collect(Collectors.toList());
    }


    /**
     * 查询用于前端页面handsontable使用的数据
     *
     * @param headId 碳排基礎訊息头 id
     * @return
     */
    public List<CeBasicInfoTableDataVO> listCeBasicInfoTable(String headId) {
        checkExist(headId, "碳排基礎訊息主表id不能为空");
        CeBasicInfoDetailExample example = new CeBasicInfoDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq");
        return ceBasicInfoDetailMapper.selectByExample(example).stream().map(CeBasicInfoTableDataConverter::convert).collect(Collectors.toList());
    }

    /**
     * 查询用于前端页面handsontable使用的数据
     *
     * @param headId 碳排經營數據头 id
     * @return
     */
    public List<CeOperationDataTableDataVO> listCeOperationDataTable(String headId) {
        checkExist(headId, "碳排經營數據主表id不能为空");
        CeOperationDataDetailExample example = new CeOperationDataDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq");
        return ceOperationDataDetailMapper.selectByExample(example).stream().map(CeOperationDataTableDataConverter::convert).collect(Collectors.toList());
    }

    /**
     * proxy for mapper method
     *
     * @param record
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertDetailSelective(CeBasicInfoDetail record) {
        return ceBasicInfoDetailMapper.insertSelective(record);
    }

    /**
     * 保存碳排基礎訊息明细信息
     *
     * @param ceBasicInfoHeadVO 碳排基礎訊息头信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCeBasicInfoWithDetail(CeBasicInfoHeadVO ceBasicInfoHeadVO) {
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        if (workflowControlService.isNotAllowEdit(ceBasicInfoHeadVO.getId())) {
            throw new ServiceException("已经发起审批流程的单据不允许进行修改");
        }
        // 更新主表信息
        updateHead(ceBasicInfoHeadVO);
        // 删除明细
        deleteDetail1ByHeadId(ceBasicInfoHeadVO.getId());
        deleteDetail2ByHeadId(ceBasicInfoHeadVO.getId());

        List<CeBasicInfoDetail> lstDetails1 = ceBasicInfoHeadVO.getDetails1().stream().map(CeBasicInfoTableDataConverter::convert).collect(Collectors.toList());
        addDetails1(ceBasicInfoHeadVO.getId(), lstDetails1);

        List<CeOperationDataDetail> lstDetails2 = ceBasicInfoHeadVO.getDetails2().stream().map(CeOperationDataTableDataConverter::convert).collect(Collectors.toList());
        addDetails2(ceBasicInfoHeadVO.getId(), lstDetails2);
    }

    /**
     * 提交碳排基礎訊息
     *
     * @param idVersionVO
     */
    public void submit(IdVersionVO idVersionVO) {
        // 验证逻辑---------
        checkExist(idVersionVO.getId(), "碳排基礎訊息主表id不能为空");
        checkExist(idVersionVO.getLastUpdateVersion(), "碳排基礎訊息主表版本号不能为空");

        CeBasicInfoHead existedRecord = ceBasicInfoHeadMapper.selectByPrimaryKey(idVersionVO.getId());
        Optional.ofNullable(existedRecord).orElseThrow(() -> new ServiceException("指定的碳排基礎訊息记录不存在"));
        // 验证逻辑---------

        CeBasicInfoHeadExample example = new CeBasicInfoHeadExample();
        example.or().andIdEqualTo(idVersionVO.getId()).andLastUpdateVersionEqualTo(idVersionVO.getLastUpdateVersion());

        CeBasicInfoHead record = new CeBasicInfoHead();
        //record.setApproveStatus(ApproveStatus.SUBMIT.getCode());
        record.setLastUpdateVersion(idVersionVO.getLastUpdateVersion());

        int count = ceBasicInfoHeadMapper.updateByExampleSelective(record, example);
        if (count == 0) {
            throw new ServiceException("碳排基礎訊息已经被修改，请刷新后重试");
        }
        // todo 增加流程审批记录
    }

    private void addDetails1(String headId, List<CeBasicInfoDetail> ceBasicInfoDetails) {
        if (CollectionUtils.isEmpty(ceBasicInfoDetails)) {
            throw new ServiceException("碳排基礎訊息明细不能为空");
        }
        int seq = 0;
        CeBasicInfoDetail lastDetail = ceBasicInfoDetails.get(0);
        for (CeBasicInfoDetail ceBasicInfoDetail : ceBasicInfoDetails) {
            /*
            if (StringUtils.isNotBlank(ceBasicInfoDetail.getCategory()) && !StringUtils.equals(ceBasicInfoDetail.getCategory(), lastDetail.getCategory())) {
                lastDetail = ceBasicInfoDetail;
            }
            if (StringUtils.isBlank(ceBasicInfoDetail.getCategory())) {
                ceBasicInfoDetail.setCategory(lastDetail.getCategory());
            }
             */
            ceBasicInfoDetail.setId(UUID.randomUUID().toString());
            ceBasicInfoDetail.setHeadId(headId);
            ceBasicInfoDetail.setSeq(seq++);
            ceBasicInfoDetailMapper.insertSelective(ceBasicInfoDetail);
        }
    }


    private void addDetails2(String headId, List<CeOperationDataDetail> ceOperationDataDetails) {
        if (CollectionUtils.isEmpty(ceOperationDataDetails)) {
            throw new ServiceException("碳排經營數據明细不能为空");
        }
        int seq = 0;
        CeOperationDataDetail lastDetail = ceOperationDataDetails.get(0);
        for (CeOperationDataDetail ceOperationDataDetail : ceOperationDataDetails) {
            /*
            if (StringUtils.isNotBlank(ceOperationDataDetail.getCategory()) && !StringUtils.equals(ceOperationDataDetail.getCategory(), lastDetail.getCategory())) {
                lastDetail = ceOperationDataDetail;
            }
            if (StringUtils.isBlank(ceOperationDataDetail.getCategory())) {
                ceOperationDataDetail.setCategory(lastDetail.getCategory());
            }
             */
            ceOperationDataDetail.setId(UUID.randomUUID().toString());
            ceOperationDataDetail.setHeadId(headId);
            ceOperationDataDetail.setSeq(seq++);
            ceOperationDataDetailMapper.insertSelective(ceOperationDataDetail);
        }
    }

    private void deleteDetail1ByHeadId(String headId) {
        CeBasicInfoDetailExample example = new CeBasicInfoDetailExample();
        example.or().andHeadIdEqualTo(headId);
        ceBasicInfoDetailMapper.deleteByExample(example);
    }

    private void deleteDetail2ByHeadId(String headId) {
        CeOperationDataDetailExample example = new CeOperationDataDetailExample();
        example.or().andHeadIdEqualTo(headId);
        ceOperationDataDetailMapper.deleteByExample(example);
    }

    private void updateHead(CeBasicInfoHeadVO ceBasicInfoHeadVO) {
        checkExist(ceBasicInfoHeadVO.getId(), "碳排基礎訊息主表id不能为空");
        checkExist(ceBasicInfoHeadVO.getOrganizationId(), "组织编号不能为空");
        checkExist(ceBasicInfoHeadVO.getLastUpdateVersion(), "版本号不能为空");

        CeBasicInfoHeadExample headExample = new CeBasicInfoHeadExample();
        headExample.or().andIdEqualTo(ceBasicInfoHeadVO.getId()).andLastUpdateVersionEqualTo(ceBasicInfoHeadVO.getLastUpdateVersion());

        CeBasicInfoHead ceBasicInfoHead = CeBasicInfoHeadConverter.convert(ceBasicInfoHeadVO);
        int updateCount = ceBasicInfoHeadMapper.updateByExampleSelective(ceBasicInfoHead, headExample);
        if (updateCount == 0) {
            throw new ServiceException("碳排基礎訊息主表信息已经被修改，请刷新后重试");
        }
    }

    /**
     * proxy for mapper method
     *
     * @param id 碳排基礎訊息主表id
     * @return
     */
    public CeBasicInfoHead selectByPrimaryKey(String id) {
        return ceBasicInfoHeadMapper.selectByPrimaryKey(id);
    }

    public List<CeBasicInfoHead> selectByExample(CeBasicInfoHeadExample example) {
        return ceBasicInfoHeadMapper.selectByExample(example);
    }

    /**
     * 审核完成，同步省级、市级、详细地址 到  【碳中和】-【信息配置】-【基本信息】的省、市、地盘地址字段
     * <AUTHOR>
     * @date 2025/1/10 14:33
     * @param ceBasicInfoHead
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncBasicInfo(CeBasicInfoHead ceBasicInfoHead) {
        // 1、先查看经营信息是否审核通过
        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(ceBasicInfoHead.getId());
        if (Objects.isNull(workflowControl)) {
            return ;
        }
        if (!Objects.equals(workflowControl.getState(), WorkflowControlState.COMPLETED.getCode())) {
            return ;
        }
        // 2、查找经营信息下的基本信息
        List<CeBasicInfoTableDataVO> ceBasicInfoTableDataVOS = listCeBasicInfoTable(ceBasicInfoHead.getId());
        if (CollectionUtils.isNotEmpty(ceBasicInfoTableDataVOS)) {
            CeBasicInfoTableDataVO ceBasicInfoTableDataVO = ceBasicInfoTableDataVOS.get(0);
            // 3、查询组织名称
            Organization organization = organizationMapper.selectByPrimaryKey(ceBasicInfoHead.getOrganizationId());
            if (Objects.isNull(organization)) {
                return ;
            }
            // 4、根据组织名称查询项目基本信息
            TzhProjectInfoVO tzhProjectInfoVO = tzhProjectInfoCustomMapper.get(organization.getId());
            // 不存在就新增
            if (Objects.isNull(tzhProjectInfoVO)) {
                tzhProjectInfoVO = new TzhProjectInfoVO();
                tzhProjectInfoVO.setName(organization.getName());
                tzhProjectInfoVO.setCode(organization.getCode());
                tzhProjectInfoVO.setSiteid(organization.getId());
                tzhProjectInfoVO.setIsdeleted(false);
            }
            TzhProjectInfo tzhProjectInfo = tzhProjectInfoVO;
            // 省级
            if (StringUtils.isNotEmpty(ceBasicInfoTableDataVO.getCol11())) {
                tzhProjectInfo.setProvince(ceBasicInfoTableDataVO.getCol11());
            }
            // 市级
            if (StringUtils.isNotEmpty(ceBasicInfoTableDataVO.getCol12())) {
                tzhProjectInfo.setCity(ceBasicInfoTableDataVO.getCol12());
            }
            // 详细地址
            if (StringUtils.isNotEmpty(ceBasicInfoTableDataVO.getCol13())) {
                tzhProjectInfo.setAddress(ceBasicInfoTableDataVO.getCol13());
            }
            tzhProjectInfoService.save(tzhProjectInfo);
        }
    }
}


