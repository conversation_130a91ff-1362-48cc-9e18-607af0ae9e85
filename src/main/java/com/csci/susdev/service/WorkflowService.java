package com.csci.susdev.service;

import com.alibaba.fastjson.JSON;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.*;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.WorkflowVOConverter;
import com.csci.susdev.qo.WorkflowQO;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.susdev.vo.WorkflowExportData;
import com.csci.susdev.vo.WorkflowVO;
import com.github.pagehelper.PageHelper;
import net.minidev.json.JSONUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;
import static com.csci.susdev.service.ServiceHelper.listSortedWorkflowNodes;

@Service
@LogMethod
@DS(DatasourceContextEnum.SUSDEV)
public class WorkflowService {

    @Resource
    private WorkflowControlService workflowControlService;

    @Resource
    private WorkflowMapper workflowMapper;

    @Resource
    private WorkflowNodeMapper workflowNodeMapper;

    @Resource
    private WorkflowNodeUserMapper workflowNodeUserMapper;

    @Resource
    private WorkflowCustomMapper workflowCustomMapper;

    @Resource
    private OrganizationMapper organizationMapper;

    @Resource
    private FormMapper formMapper;

    @Resource
    private UserMapper userMapper;

    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(Workflow record) {
        return workflowMapper.insertSelective(record);
    }

    /**
     * 根据流程编码查询流程
     *
     * @param workflowCode 流程编码
     * @return
     */
    public Workflow findWorkflowByCode(String workflowCode) {
        WorkflowExample example = new WorkflowExample();
        example.or().andIsActiveEqualTo(Boolean.TRUE).andCodeEqualTo(workflowCode);
        return workflowMapper.selectByExample(example).stream().findFirst().orElse(null);
    }

    /**
     * 判断指定流程是否存在
     *
     * @param workflowCode 流程编码
     * @return
     */
    public boolean isWorkflowExist(String workflowCode) {
        return findWorkflowByCode(workflowCode) != null;
    }

    /**
     * 查询指定组织机构的表单流程是否已经存在
     *
     * @param organizationId 组织机构ID
     * @param formId         表单ID
     * @return
     */
    public boolean isExistByOrgIdAndFormId(String organizationId, String formId, Integer year, Integer month) {
        WorkflowExample example = new WorkflowExample();
        example.or().andIsActiveEqualTo(Boolean.TRUE).andOrganizationIdEqualTo(organizationId)
                .andFormIdEqualTo(formId).andYearEqualTo(year).andMonthEqualTo(month);
        return workflowMapper.selectByExample(example).stream().findFirst().orElse(null) != null;
    }

    /**
     * 根据组织机构ID和表单ID查询流程
     *
     * @param organizationId 组织机构ID
     * @param formId         表单ID
     * @param year         年份
     * @return
     */
    public Workflow findWorkflowByOrgIdAndFormId(String organizationId, String formId, Integer year, Integer month) {
        WorkflowExample example = new WorkflowExample();
        example.or().andIsActiveEqualTo(Boolean.TRUE).andOrganizationIdEqualTo(organizationId)
                .andFormIdEqualTo(formId).andYearEqualTo(year).andMonthEqualTo(month);
        return workflowMapper.selectByExample(example).stream().findFirst().orElse(null);
    }

    /**
     * proxy for {@link WorkflowMapper#deleteByPrimaryKey(String)}
     *
     * @param id 流程ID
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteByPrimaryKey(String id) {
        return workflowMapper.deleteByPrimaryKey(id);
    }

    /**
     * 删除指定编号的流程信息；连节点以及节点用户信息一起删除
     *
     * @param id 流程ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteWorkflow(String id) {
        deleteByPrimaryKey(id);

        WorkflowNodeExample nodeExample = new WorkflowNodeExample();
        nodeExample.or().andWorkflowIdEqualTo(id);
        List<WorkflowNode> lstNode = workflowNodeMapper.selectByExample(nodeExample);
        List<String> lstNodeId = lstNode.stream().map(WorkflowNode::getId).collect(Collectors.toList());

        // 删除节点
        WorkflowNodeExample deleteNodeExample = new WorkflowNodeExample();
        deleteNodeExample.or().andWorkflowIdEqualTo(id);
        workflowNodeMapper.deleteByExample(deleteNodeExample);

        // 删除节点用户关联关系
        WorkflowNodeUserExample deleteNodeUserExample = new WorkflowNodeUserExample();
        deleteNodeUserExample.or().andNodeIdIn(lstNodeId);
        workflowNodeUserMapper.deleteByExample(deleteNodeUserExample);
    }

    /**
     * 将指定流程设置为不可用
     *
     * @param id 流程ID
     * @return
     */
    public int inactiveWorkflow(String id) {
        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByWorkflowId(id);
        if (workflowControl != null) {
            throw new ServiceException("已發起流程，無法刪除");
        }

        Workflow workflow = new Workflow();
        workflow.setId(id);
        workflow.setIsActive(false);
        return workflowMapper.updateByPrimaryKeySelective(workflow);
    }

    /**
     * 查询流程列表
     *
     * @param workflowQO 查询条件
     * @return
     */
    public ResultPage<WorkflowVO> listWorkflow(WorkflowQO workflowQO) {
        UserInfo userInfo = ContextUtils.getCurrentUser();
        PageHelper.startPage(workflowQO.getCurPage(), workflowQO.getPageSize());
        List<Workflow> lstWorkflow = workflowCustomMapper.selectWorkflow(
                workflowQO.getOrganizationId(), workflowQO.getYear(),
                workflowQO.getMonth(), userInfo.getId());
        ResultPage<WorkflowVO> resultPage = new ResultPage<>(lstWorkflow);
        List<WorkflowVO> lstWorkflowVO = convertWorkflowList(lstWorkflow);
        resultPage.setList(lstWorkflowVO);

        return resultPage;
    }
    /**
     * 查询流程记录;包含节点信息
     *
     * @param id 流程ID
     * @return 流程信息
     */
    public WorkflowVO findWorkflowById(String id) {
        checkExist(id, "id不能为空");
        Workflow workflow = workflowMapper.selectByPrimaryKey(id);
        checkExist(workflow, "流程不存在");

        WorkflowVO workflowVO = WorkflowVOConverter.convert(workflow);
        workflowVO.setWorkflowNodes(listSortedWorkflowNodes(id));

        Organization organization = organizationMapper.selectByPrimaryKey(workflow.getOrganizationId());
        workflowVO.setOrganizationName(Optional.ofNullable(organization).map(Organization::getName).orElse(null));

        if(workflow.getRepresentativeId() != null) {
            User user = userMapper.selectByPrimaryKey(workflow.getRepresentativeId());
            if(user != null) {
                workflowVO.setRepresentativeName(user.getName());
                workflowVO.setRepresentativeRealName(user.getUsername());
                workflowVO.setRepresentativeMobile(user.getMobile());
            }
        }
        return workflowVO;
    }

    /**
     * 转换过程中，将组织机构，流程节点信息一并查出来并放入workflowVO对象种
     *
     * @param lstWorkflow 流程列表
     * @return
     */
    private List<WorkflowVO> convertWorkflowList(List<Workflow> lstWorkflow) {
        if (CollectionUtils.isEmpty(lstWorkflow)) {
            return new ArrayList<>();
        }

        // 查询出所有流程所属的组织机构
        List<String> lstOrgId = lstWorkflow.stream().map(Workflow::getOrganizationId).collect(Collectors.toList());
        OrganizationExample orgExample = new OrganizationExample();
        if(lstOrgId.size() < 2000) {
            orgExample.or().andIdIn(lstOrgId).andIsDeletedEqualTo(Boolean.FALSE);
        } else {
            orgExample.or().andIsDeletedEqualTo(Boolean.FALSE);
        }
        List<Organization> lstOrganizations = organizationMapper.selectByExample(orgExample);
        Map<String, Organization> mapOrgs = lstOrganizations.stream().collect(Collectors.toMap(Organization::getId, o -> o));

        // 查询出表单信息
        List<Form> lstForm = formMapper.selectByExample(new FormExample());
        Map<String, Form> mapForm = lstForm.stream().collect(Collectors.toMap(Form::getId, o -> o));

        List<WorkflowVO> lstWorkflowVO = new ArrayList<>();
        for (Workflow workflow : lstWorkflow) {
            Map<String, User> mapUser = null;
            if(workflow.getRepresentativeId() != null) {
                // 查询出用戶信息
                UserExample userExample = new UserExample();
                userExample.or().andIdEqualTo(workflow.getRepresentativeId());
                List<User> lstUser = userMapper.selectByExample(userExample);
                mapUser = lstUser.stream().collect(Collectors.toMap(User::getId, o -> o));
            }

            WorkflowVO workflowVO = WorkflowVOConverter.convert(workflow);
            workflowVO.setWorkflowNodes(listSortedWorkflowNodes(workflow.getId()));
            workflowVO.setOrganizationName(Optional.ofNullable(mapOrgs.get(workflow.getOrganizationId())).map(Organization::getName).orElse(null));
            workflowVO.setFormName(Optional.ofNullable(mapForm.get(workflow.getFormId())).map(Form::getName).orElse(null));
            if(mapUser != null && mapUser.size() > 0) {
                workflowVO.setRepresentativeName(Optional.ofNullable(mapUser.get(workflow.getRepresentativeId())).map(User::getUsername).orElse(null));
                workflowVO.setRepresentativeRealName(Optional.ofNullable(mapUser.get(workflow.getRepresentativeId())).map(User::getName).orElse(null));
                workflowVO.setRepresentativeMobile(Optional.ofNullable(mapUser.get(workflow.getRepresentativeId())).map(User::getMobile).orElse(null));
            }
            lstWorkflowVO.add(workflowVO);
        }
        return lstWorkflowVO;
    }

    public List<WorkflowExportData> selectWorkflowForExport(WorkflowQO qo) {
        checkExist(qo.getYear(), "年份不能为空");
        if(ObjectUtils.isEmpty(qo.getMonth())) {
            qo.setMonth(12);
        }
        checkExist(qo.getUserId(), "用户id不能为空");
        if("undefined".equals(qo.getFormId())) {
            qo.setFormId(null);
        }
        if("undefined".equals(qo.getOrganizationId())) {
            qo.setOrganizationId(null);
        }
        return workflowCustomMapper.selectWorkflowForExport(qo.getOrganizationId(), qo.getYear(), qo.getMonth(), qo.getUserId());
    }

    /**
     * proxy for mapper method
     *
     * @param record  record
     * @param example example
     * @return
     */
    public int updateByExampleSelective(Workflow record, WorkflowExample example) {
        return workflowMapper.updateByExampleSelective(record, example);
    }

    /**
     * 获取工作流最新配置年月
     * <AUTHOR>
     * @date 2025/2/25 11:48
     * @return java.lang.String
     */
    public String getLatestDate() {
        return workflowCustomMapper.getLatestDate();
    }
}
