package com.csci.susdev.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.csci.cohl.model.MaterialTransDetail;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.constant.FormCodeEnum;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.listener.AmbientInitListener;
import com.csci.susdev.mapper.*;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.AmbientHeadVOConverter;
import com.csci.susdev.modelcovt.AmbientTableDataConverter;
import com.csci.susdev.qo.CeIdentificationQO;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static com.csci.susdev.service.ServiceHelper.*;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
@Slf4j
public class AmbientService {
	/**
	 * 初始化记录的过期时间
	 */
	@Value("${lock.init.head.expire.milis:1000}")
	private long lockInitHeadExpireMilis;
	/**
	 * 查询head记录时，获取锁失败循环等待的次数
	 */
	@Value("${query.head.wait.loop.times:5}")
	private int lockQueryHeadWaitLoopTimes;
	/**
	 * 查询head记录时，获取锁失败循环时每次休眠的时间
	 */
	@Value("${query.head.wait.sleep.milis:50}")
	private int lockQueryHeadWaitSleepMilis;
	@Resource
	private AmbientHeadMapperCustom ambientHeadMapperCustom;

	@Resource
	private AmbientExtService ambientExtService;

	@Resource
	private OrganizationService organizationService;

	@Resource
	private AmbientHeadMapper ambientHeadMapper;

	@Resource
	private AmbientDetailMapper ambientDetailMapper;

	@Resource
	private AmbientEnergyBillMapper ambientEnergyBillMapper;

	@Resource
	private MaterialTransDetailMapper materialTransDetailMapper;

	@Resource
	private FormDetailMapper formDetailMapper;

	@Resource
	private AmbientDetailService ambientDetailService;

	@Resource
	private AmbientDetailCustomMapper ambientDetailCustomMapper;

	/**
	 * 查询环境绩效数据，如果不存在则初始化
	 *
	 * @param organizationId 组织id
	 * @param year           年份
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public AmbientHeadVO getOrInit(String organizationId, Integer year, Integer month) {
		long beginTime = System.currentTimeMillis();
		long startTime;
		long endTime;
		checkExist(organizationId, "组织机构id不能为空");
		checkExist(year, "年份不能为空");
		if(ObjectUtils.isEmpty(month)) {
			month = 12;
		}
		/*
		if (!isUserOrganizationExist(ContextUtils.getCurrentUser().getId(), organizationId)) {
			throw new ServiceException("用户无权限查看该组织机构的数据");
		}
		 */

		AmbientHead ambientHead = getOrInitHeadRecord(organizationId, year, month);

		if (Objects.nonNull(ambientHead) && Objects.nonNull(ambientHead.getId())) {
			//從第三方接中間表單同步明细数据
			syncAmbientDetailFromExt(ambientHead.getId());
		}
		endTime = System.currentTimeMillis();
		log.info("【环境绩效初始化】总耗时:{}秒", ((endTime - (double) beginTime) / 1000));
		return AmbientHeadVOConverter.convert(ambientHead);
	}

	/**
	 * 查询或初始化记录，如果获取失败，直接抛出异常退出
	 * @param
	 * @return
	 */
	private AmbientHead getOrInitHeadRecord(String organizationId, Integer year, Integer month) {
		AmbientHead ambientHead = findAmbientHead(organizationId, year, month);
		if (Objects.isNull(ambientHead)) {
			String key = StrUtil.format(SusDevConsts.DISTRIBUTE_LOCK_KEY_FORMAT, SusDevConsts.PROJECT_PREFIX,
					"ambient:getOrInit", organizationId, year, month);
			//超时时间1s
			boolean redisLock = RedisLockUtil.lock(key, lockInitHeadExpireMilis);
			if (!redisLock) {
				//获取锁失败，停顿50ms一次，停顿5次
				int count = lockQueryHeadWaitLoopTimes;
				while(count-- > 0){
					ambientHead = findAmbientHead(organizationId, year, month);
					if(ambientHead != null){
						break;
					}
					try {
						Thread.sleep(lockQueryHeadWaitSleepMilis);
					} catch (InterruptedException e) {
						throw new RuntimeException(e);
					}
				}
			}else{
				try{
					long startMillis = System.currentTimeMillis();
					ambientHead = initAmbient(organizationId, year, month);
					log.debug("初始化记录耗时:{}ms", (System.currentTimeMillis() - startMillis));
				}
				finally {
					RedisLockUtil.unlock(key);
				}
			}
		}
		//如果最终没拿到数据，直接返回
		if(ambientHead == null){
			throw new ServiceException("生成记录失败, 请稍后再尝试！");
		}
		return ambientHead;
	}


	@Transactional(rollbackFor = Exception.class)
	public AmbientHeadVO get(String organizationId, Integer year, Integer month) {
		checkExist(organizationId, "组织机构id不能为空");
		checkExist(year, "年份不能为空");
		checkExist(month, "月份不能为空");
		/*
		if (!isUserOrganizationExist(ContextUtils.getCurrentUser().getId(), organizationId)) {
			throw new ServiceException("用户无权限查看该组织机构的数据");
		}
		 */
		AmbientHead ambientHead = findAmbientHead(organizationId, year, month);
		if (Objects.isNull(ambientHead)) {
			throw new ServiceException("查無表單");
		}
		return AmbientHeadVOConverter.convert(ambientHead);
	}

	/**
	 * 更新环境绩效数据
	 *
	 * @param ambientHeadVO 环境绩效数据
	 */
	@Transactional(rollbackFor = Exception.class)
	public void updateAmbient(AmbientHeadVO ambientHeadVO) {
		checkExist(ambientHeadVO.getId(), "id不能为空");
		checkExist(ambientHeadVO.getLastUpdateVersion(), "版本号不能为空");
		if (CollectionUtils.isEmpty(ambientHeadVO.getDetails())) {
			throw new ServiceException("表單明细不能为空");
		}

		WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
		if (workflowControlService.isNotAllowEdit(ambientHeadVO.getId())) {
			throw new ServiceException("已经发起审批流程的表單不允许进行修改");
		}

		AmbientHead existHead = ambientHeadMapper.selectByPrimaryKey(ambientHeadVO.getId());
		if (!ServiceHelper.isOrganizationLeaf(existHead.getOrganizationId())) {
			throw new ServiceException("表單数据只能在最末级组织机构下修改");
		}

		// update head
		AmbientHead ambientHead = AmbientHeadVOConverter.convert(ambientHeadVO);
		ambientHead.setId(null);
		AmbientHeadExample headExample = new AmbientHeadExample();
		headExample.or().andIdEqualTo(ambientHeadVO.getId()).andLastUpdateVersionEqualTo(ambientHeadVO.getLastUpdateVersion());
		int updateCount = ambientHeadMapper.updateByExampleSelective(ambientHead, headExample);
		if (updateCount != 1) {
			throw new ServiceException("更新表單数据失败, 请刷新页面重试");
		}

		// delete detail
		deleteAmbientDetailByHeadId(ambientHeadVO.getId());

		// insert detail
		int seq = 1;
		for (AmbientTableDataVO detailVO : ambientHeadVO.getDetails()) {
			AmbientDetail detail = AmbientTableDataConverter.convert(detailVO);
			detail.setId(UUID.randomUUID().toString());
			detail.setHeadId(ambientHeadVO.getId());
			detail.setSeq(seq++);
			detail.setCategoryDigest(DigestUtils.md5Hex(detail.getCategory()));
			ambientDetailMapper.insertSelective(detail);
		}
	}
	/**
	 * 更新更新物料运输数据
	 *
	 * @param ambientHeadVO 环境更新物料运输
	 */
	@Transactional(rollbackFor = Exception.class)
	public void updateAmbientWithMeterTrans(AmbientHeadWithMeterTransVO ambientHeadVO) {
		checkExist(ambientHeadVO.getId(), "id不能为空");
		checkExist(ambientHeadVO.getLastUpdateVersion(), "版本号不能为空");
		List<MaterialTransDetail> details = ambientHeadVO.getDetails();
		if (CollectionUtils.isEmpty(details)) {
			throw new ServiceException("表單明细不能为空");
		}

		WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
		if (workflowControlService.isNotAllowEdit(ambientHeadVO.getId())) {
			throw new ServiceException("已经发起审批流程的表單不允许进行修改");
		}

		AmbientHead existHead = ambientHeadMapper.selectByPrimaryKey(ambientHeadVO.getId());
		if (!ServiceHelper.isOrganizationLeaf(existHead.getOrganizationId())) {
			throw new ServiceException("表單数据只能在最末级组织机构下修改");
		}

		// update head
		AmbientHead ambientHead = AmbientHeadVOConverter.convert(ambientHeadVO);
//		ambientHead.setLastUpdateUserId(ContextUtils.getCurrentUser().getId());
//		ambientHead.setCreateUserId(ContextUtils.getCurrentUser().getId());
//		AmbientHeadExample headExample = new AmbientHeadExample();
//		headExample.or().andIdEqualTo(ambientHeadVO.getId()).andLastUpdateVersionEqualTo(ambientHeadVO.getLastUpdateVersion());
//		int updateCount = ambientHeadMapper.updateByExample(ambientHead, headExample);
		LambdaQueryWrapper<AmbientHead> queryWrapper = Wrappers.<AmbientHead>lambdaQuery().eq(AmbientHead::getId, ambientHeadVO.getId()).eq(AmbientHead::getLastUpdateVersion, ambientHeadVO.getLastUpdateVersion());
		int updateCount = ambientHeadMapper.update(ambientHead, queryWrapper);
		if (updateCount != 1) {
			throw new ServiceException("更新表單数据失败, 请刷新页面重试");
		}

		LambdaQueryWrapper<MaterialTransDetail> wrapper = Wrappers.lambdaQuery();
		materialTransDetailMapper.delete(wrapper.eq(MaterialTransDetail::getHeadId, ambientHeadVO.getId()));
		// insert detail
		int seq = 1;
		for (MaterialTransDetail detail : details) {
			detail.setId(UUID.randomUUID().toString());
			detail.setHeadId(ambientHeadVO.getId());
			detail.setSeq(seq++);
			materialTransDetailMapper.insert(detail);
		}
	}

	@Transactional(rollbackFor = Exception.class)
	public void syncAmbientDetailFromExt(String headId) {
		AmbientHead ambientHead = selectByPrimaryKey(headId);
		//查無HEAD
		if(Objects.isNull(ambientHead)) {
			return;
		}
		// 只有初次生成的表單才會進行第三方同步
		if(ambientHead.getLastUpdateVersion() > 1) {
			return;
		}

		AmbientHeadExtVO ambientHeadExtVO = ambientExtService
				.findAmbientHeadExtVO(ambientHead.getOrganizationId(),ambientHead.getYear(),ambientHead.getMonth());

		if(Objects.nonNull(ambientHeadExtVO) && Objects.nonNull(ambientHeadExtVO.getDetails())) {
			// update detail
			for(AmbientTableDataExtVO ambientTableDataExtVO : ambientHeadExtVO.getDetails()) {
				AmbientDetailExample detailExample = new AmbientDetailExample();
				detailExample.or().andHeadIdEqualTo(headId)
						.andCategoryEqualTo(ambientTableDataExtVO.getCategory())
						.andCategoryDigestEqualTo(ambientTableDataExtVO.getCategoryDigest())
						.andTypeEqualTo(ambientTableDataExtVO.getType())
						.andType2EqualTo(ambientTableDataExtVO.getType2())
						.andUnitEqualTo(ambientTableDataExtVO.getUnit())
						.andUnitCodeEqualTo(ambientTableDataExtVO.getUnitCode());

				AmbientDetail existDetail = ambientDetailMapper.selectByExample(detailExample).stream().findFirst().orElse(null);

				if(Objects.nonNull(existDetail)) {
					AmbientDetail detail = AmbientTableDataConverter.convert(ambientTableDataExtVO);
					detail.setId(existDetail.getId());
					detail.setHeadId(headId);
					ambientDetailMapper.updateByPrimaryKeySelective(detail);
				}
			}
		}
		if(Objects.nonNull(ambientHeadExtVO) && Objects.nonNull(ambientHeadExtVO.getBills())) {
			// update bill
			AmbientEnergyBillExample billExample = new AmbientEnergyBillExample();
			billExample.or().andHeadIdEqualTo(headId);
			ambientEnergyBillMapper.deleteByExample(billExample);
			for(AmbientEnergyBillExtVO billExtVO : ambientHeadExtVO.getBills()) {
				AmbientEnergyBill bill = new AmbientEnergyBill();
				BeanUtils.copyProperties(billExtVO, bill);
				bill.setId(UUID.randomUUID().toString());
				bill.setHeadId(headId);
				ambientEnergyBillMapper.insertSelective(bill);
			}
		}
	}

	/**
	 * 根据组织机构id、年份、月份初始化环境绩效数据
	 * 使用同步的方式防止并发
	 *
	 * @param organizationId 组织机构id
	 * @param year           年份
	 * @return
	 */
	private AmbientHead initAmbient(String organizationId, Integer year, Integer month) {
		AmbientHead existedRecord = findAmbientHead(organizationId, year, month);
		if (Objects.nonNull(existedRecord)) {
			return existedRecord;
		}

		AmbientHead ambientHead = new AmbientHead();
		ambientHead.setOrganizationId(organizationId);
		ambientHead.setYear(year);
		ambientHead.setMonth(month);
		ambientHead.setIsActive(Boolean.TRUE);
		ambientHeadMapper.insertSelective(ambientHead);

		/*
		AmbientHead prevHead = getPrevHead(organizationId, year, month);
		if(ObjectUtils.isNotEmpty(prevHead)) {
			initDetailsWithPrevData(prevHead.getId(), ambientHead.getId());
		} else {
			OrganizationVO orgVO = organizationService.getOrganizationById(organizationId);
			initDetails(ambientHead.getId(), orgVO.getNo());
		}
		*/
		// OrganizationVO orgVO = organizationService.getOrganizationById(organizationId);
//		initDetails(ambientHead.getId(), orgVO.getNo());
		initDetailsByFormDetail(ambientHead.getId(), FormCodeEnum.ambient.getCode(), year, month);


		return ambientHead;
	}


	public AmbientHead getPrevHead(String organizationId, Integer year, Integer month) {
		AmbientHeadExample example = new AmbientHeadExample();
		example.or().andIsActiveEqualTo(true).andOrganizationIdEqualTo(organizationId)
				.andYearEqualTo(year).andMonthLessThan(month);
		example.setOrderByClause("month desc");
		List<AmbientHead> lst = selectByExample(example);
		if(lst.size() > 0) {
			return lst.get(0);
		} else {
			return null;
		}
	}

	void initDetailsWithPrevData(String prevHeadId, String curHeadId) {
		AmbientDetailExample example = new AmbientDetailExample();
		example.or().andHeadIdEqualTo(prevHeadId);
		List<AmbientDetail> details = ambientDetailMapper.selectByExample(example);
		for(AmbientDetail detail : details) {
			detail.setId(null);
			detail.setHeadId(curHeadId);
			ambientDetailMapper.insert(detail);
		}
	}

	void initDetails(String headId, String no) {
		checkExist(headId, "headId不能为空");
		String templatePath = "template/ambient_init_hk.xlsx";
		try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath)) {
			EasyExcel.read(is, AmbientInitDataVO.class, new AmbientInitListener(headId)).sheet().doRead();
		} catch (IOException e) {
			throw new ServiceException(e.getMessage());
		}
	}

	/**
	 * 用表单明细来初始化 环境绩效明细数据
	 * <AUTHOR>
	 * @date 2024/12/24 11:28
	 * @param headId	环境绩效主表ID
	 * @param formCode	表单编码
	 * @param year		年份
	 * @param month		月份
	 */
	@Transactional(rollbackFor = Exception.class)
	public void initDetailsByFormDetail(String headId, String formCode, Integer year, Integer month) {
		checkExist(headId, "headId不能为空");
		// 查询表单明细有无对应年月的数据
		FormDetailExample formDetailExample = new FormDetailExample();
		formDetailExample.or().andFormCodeEqualTo(formCode).andYearEqualTo(year).andMonthEqualTo(month).andIsDeletedEqualTo(Boolean.FALSE);
		formDetailExample.setOrderByClause("seq, type_e");

		List<FormDetail> formDetails = formDetailMapper.selectByExample(formDetailExample);
		if (CollectionUtils.isEmpty(formDetails)) {
			throw new ServiceException("没有对应" + year + "-" + month + "的表单明细数据，请联系管理员维护！");
		}
		List<AmbientDetail> ambientDetails = new ArrayList<>();
		for (FormDetail formDetail : formDetails) {
			AmbientDetail ambientDetail = new AmbientDetail();
			ambientDetail.setHeadId(headId);
			ambientDetail.setCategory(formDetail.getTypeA());
			ambientDetail.setType(formDetail.getTypeB());
			ambientDetail.setType2(formDetail.getTypeC());
			ambientDetail.setUnit(formDetail.getTypeD());
			ambientDetail.setUnitCode(formDetail.getTypeE());
			ambientDetail.setSeq(formDetail.getSeq());
			ambientDetail.setCategoryDigest(DigestUtils.md5Hex(ambientDetail.getCategory()));
			ambientDetails.add(ambientDetail);
		}
		int batchSize = 60;
		for (int i = 0; i < ambientDetails.size(); i += batchSize) {
			List<AmbientDetail> details = ambientDetails.subList(i, Math.min(i + batchSize, ambientDetails.size()));
			ambientDetailCustomMapper.batchInsert(details);
		}

	}

	public AmbientHead findAmbientHead(String organizationId, Integer year, Integer month) {
		AmbientHeadExample example = new AmbientHeadExample();
		example.or().andOrganizationIdEqualTo(organizationId).andYearEqualTo(year).andMonthEqualTo(month).andIsActiveEqualTo(Boolean.TRUE);
		List<AmbientHead> lstHead = ambientHeadMapper.selectByExample(example);
		if (lstHead.size() > 1) {
			throw new ServiceException("环境绩效数据异常,发现该组织机构在同一年份下存在多条数据");
		}
		return lstHead.stream().findFirst().orElse(null);
	}

	/**
	 * proxy for mapper method
	 *
	 * @param id id
	 * @return
	 */
	public AmbientHead selectByPrimaryKey(String id) {
		return ambientHeadMapper.selectByPrimaryKey(id);
	}

	public List<AmbientHead> selectByExample(AmbientHeadExample example) {
		return ambientHeadMapper.selectByExample(example);
	}

	/**
	 * 查询指定组织机构指定年份的环境绩效数据
	 *
	 * @param organizationIds
	 * @param year
	 * @return
	 */
	public List<AmbientHead> listAmbientHeadByOrganizationIdsAndYear(List<String> organizationIds, Integer year, Integer month) {
		AmbientHeadExample example = new AmbientHeadExample();
		example.or().andOrganizationIdIn(organizationIds).andYearEqualTo(year).andMonthEqualTo(month).andIsActiveEqualTo(Boolean.TRUE);
		return ambientHeadMapper.selectByExample(example);
	}

	public List<String> selectAllSubmittedHeadId(List<String> organizationIds, Integer year, Integer month) {
		return ambientHeadMapperCustom.selectAllSubmittedHeadId(organizationIds, year, month);
	}

	/**
	 * 查询指定年份的所有已提交的环境绩效数据
	 *
	 * @param year
	 * @return
	 */
	public List<String> selectAllSubmittedHeadIdByYearMonth(Integer year, Integer month) {
		return ambientHeadMapperCustom.selectAllSubmittedHeadIdByYearMonth(year, month);
	}

	public AmbientHeadOrganizationVO getNameById(String id) {
		return ambientHeadMapperCustom.getNameById(id);
	}
}
