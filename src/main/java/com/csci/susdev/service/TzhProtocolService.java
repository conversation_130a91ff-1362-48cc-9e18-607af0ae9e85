package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.ProtocolCategoryMapper;
import com.csci.susdev.mapper.ProtocolMapper;
import com.csci.susdev.model.Protocol;
import com.csci.susdev.model.ProtocolCategory;
import com.csci.susdev.model.ProtocolCategoryExample;
import com.csci.susdev.model.ProtocolExample;
import com.csci.tzh.mapper.TzhProtocolCategoryMapper;
import com.csci.tzh.mapper.TzhProtocolMapper;
import com.csci.tzh.mapper.TzhProtocolSubCategoryCustomMapper;
import com.csci.tzh.mapper.TzhProtocolSubCategoryMapper;
import com.csci.tzh.model.*;
import com.csci.tzh.vo.TzhProtocolSubCategoryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhProtocolService {

	@Autowired
	private TzhProtocolMapper mapper;

	@Autowired
	private TzhProtocolCategoryMapper pCMapper;

	@Autowired
	private TzhProtocolSubCategoryMapper pSCMapper;

	@Autowired
	private TzhProtocolSubCategoryCustomMapper pSCCustomMapper;
	@Resource
	private ProtocolCategoryMapper protocolCategoryMapper;
	@Resource
	private ProtocolMapper protocolMapper;

	public List<TzhProtocol> selectByExample(TzhProtocolExample example) {
		return mapper.selectByExample(example);
	}

	public List<TzhProtocol> list() {
		TzhProtocolExample example = new TzhProtocolExample();

		TzhProtocolExample.Criteria criteria = example.or();
		example.setOrderByClause("Name");

		List<TzhProtocol> lst = mapper.selectByExample(example);
		return lst;
	}

	public List<TzhProtocolSubCategoryVO> listSubCategoryDetail(String sProtocol) {
		return pSCCustomMapper.list(sProtocol);
	}

	public List<TzhProtocolCategory> listCategory(String sProtocol) {
		TzhProtocol protocol = this.get(sProtocol);

		ProtocolCategoryExample example = new ProtocolCategoryExample();

		ProtocolCategoryExample.Criteria criteria = example.or();
		criteria.andProtocolIdEqualTo(protocol.getId());
		criteria.andIsDeletedEqualTo(false);
		example.setOrderByClause("category_name");

		List<ProtocolCategory> lst = protocolCategoryMapper.selectByExample(example);
		ArrayList<TzhProtocolCategory> tzhProtocolCategories = new ArrayList<>();
		for (ProtocolCategory protocolCategory : lst) {
			TzhProtocolCategory tzhProtocolCategory = new TzhProtocolCategory();
			tzhProtocolCategory.setId(protocolCategory.getId());
			tzhProtocolCategory.setCategoryname(protocolCategory.getCategoryName());
			tzhProtocolCategory.setCategorynameen(protocolCategory.getCategoryNameEn());
			tzhProtocolCategory.setCategorynamesc(protocolCategory.getCategoryNameSc());
			tzhProtocolCategory.setProtocolid(protocolCategory.getProtocolId());
			tzhProtocolCategories.add(tzhProtocolCategory);
		}
		return tzhProtocolCategories;
	}

	public List<TzhProtocolSubCategory> listSubCategory(String sProtocol) {
		List<TzhProtocolCategory> listCategory = this.listCategory(sProtocol);
		List<String> listCategoryId = listCategory.stream().map(object -> object.getId()).toList();

		TzhProtocolSubCategoryExample example = new TzhProtocolSubCategoryExample();

		TzhProtocolSubCategoryExample.Criteria criteria = example.or();
		criteria.andCategoryidIn(listCategoryId);
		example.setOrderByClause("SubCategoryName");

		List<TzhProtocolSubCategory> lst = pSCMapper.selectByExample(example);
		return lst;
	}

	public TzhProtocol get(String protocol) {
		ProtocolExample example = new ProtocolExample();

		ProtocolExample.Criteria criteria = example.or();
//		TzhProtocolExample example = new TzhProtocolExample();
//
//		TzhProtocolExample.Criteria criteria = example.or();
		criteria.andNameEnEqualTo(protocol);
		criteria.andIsDeletedEqualTo(false);

		List<Protocol> lst = protocolMapper.selectByExample(example);
		if(lst.size() > 0) {
			Protocol prot = lst.get(0);
			TzhProtocol tzhProtocol=new TzhProtocol();
			tzhProtocol.setId(prot.getId());
			tzhProtocol.setName(prot.getName());
			tzhProtocol.setNameen(prot.getNameEn());
			tzhProtocol.setNamesc(prot.getNameSc());
			tzhProtocol.setDescription(prot.getDescription());
			return tzhProtocol;
		} else {
			return null;
		}
	}
}
