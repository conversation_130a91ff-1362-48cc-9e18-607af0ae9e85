package com.csci.susdev.service;

import com.alibaba.fastjson.JSON;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.mapper.TzhEquipmentUsageCustomMapper;
import com.csci.tzh.mapper.TzhEquipmentUsageMapper;
import com.csci.tzh.model.TzhEquipmentUsage;
import com.csci.tzh.model.TzhEquipmentUsageExample;
import com.csci.tzh.qo.SiteNameProtocolPageableQO;
import com.csci.tzh.vo.TzhEquipmentUsageVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhEquipmentUsageService {

	@Autowired
	private TzhEquipmentUsageMapper mapper;

	@Autowired
	private TzhEquipmentUsageCustomMapper customMapper;

	public List<TzhEquipmentUsage> selectByExample(TzhEquipmentUsageExample example) {
		return mapper.selectByExample(example);
	}

	public ResultPage<TzhEquipmentUsageVO> list(SiteNameProtocolPageableQO qo) {
		PageHelper.startPage(qo.getCurPage(), qo.getPageSize(), "P.NameEN, EU.SiteName, EU.Name, EU.Model");

		List<TzhEquipmentUsageVO> lst = customMapper.list(qo.getSiteName(), qo.getProtocol());
		ResultPage<TzhEquipmentUsageVO> resultPage = new ResultPage<>(lst, true);

		return resultPage;
	}


	@Transactional(rollbackFor = Exception.class)
	public TzhEquipmentUsage save(TzhEquipmentUsage newModel) {
		UserInfo currentUser = ContextUtils.getCurrentUser();

		if(newModel.getIsdeleted() == false) {
			if(StringUtils.isNotBlank(newModel.getId())) {
				// 備份已刪除數據
				TzhEquipmentUsageExample originalExample = new TzhEquipmentUsageExample();
				TzhEquipmentUsageExample.Criteria originalCriteria = originalExample.or();
				originalCriteria.andIdEqualTo(newModel.getId());
				List<TzhEquipmentUsage> lstOriginalModel = mapper.selectByExample(originalExample);
				if(lstOriginalModel.size() > 0) {
					TzhEquipmentUsage originalModel = lstOriginalModel.get(0);
					originalModel.setId(UUID.randomUUID().toString());
					originalModel.setIsdeleted(true);
					originalModel.setDeletedby(currentUser.getUsername());
					originalModel.setDeletedtime(LocalDateTime.now());
					mapper.insertSelective(originalModel);
				}

				// 新增數據(保留舊ID)
				TzhEquipmentUsageExample newExample = new TzhEquipmentUsageExample();
				TzhEquipmentUsageExample.Criteria newCriteria = newExample.or();
				newCriteria.andIdEqualTo(newModel.getId());
				newCriteria.andIsdeletedEqualTo(false);
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				newModel.setDeletedby(null);
				newModel.setDeletedtime(null);
				mapper.deleteByExample(newExample);
				mapper.insertSelective(newModel);
			} else {
				// 新增數據(新建ID)
				newModel.setId(UUID.randomUUID().toString());
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				mapper.insertSelective(newModel);
			}
		} else {
			// 刪除數據
			TzhEquipmentUsageExample example = new TzhEquipmentUsageExample();
			TzhEquipmentUsageExample.Criteria criteria = example.or();
			criteria.andIdEqualTo(newModel.getId());
			newModel.setIsdeleted(true);
			newModel.setDeletedby(currentUser.getUsername());
			newModel.setDeletedtime(LocalDateTime.now());
			mapper.updateByExampleSelective(newModel, example);
		}

		return newModel;
	}

	@Transactional(rollbackFor = Exception.class)
	public List<TzhEquipmentUsage> saveAll(List<TzhEquipmentUsage> newModelList) {
		UserInfo currentUser = ContextUtils.getCurrentUser();

		for(TzhEquipmentUsage newModel : newModelList) {
			try {
				if(newModel.getIsdeleted() == false) {
					if(StringUtils.isNotBlank(newModel.getId())) {
						// 備份已刪除數據
						TzhEquipmentUsageExample originalExample = new TzhEquipmentUsageExample();
						TzhEquipmentUsageExample.Criteria originalCriteria = originalExample.or();
						originalCriteria.andIdEqualTo(newModel.getId());
						List<TzhEquipmentUsage> lstOriginalModel = mapper.selectByExample(originalExample);
						if(lstOriginalModel.size() > 0) {
							TzhEquipmentUsage originalModel = lstOriginalModel.get(0);
							originalModel.setId(UUID.randomUUID().toString());
							originalModel.setIsdeleted(true);
							originalModel.setDeletedby(currentUser.getUsername());
							originalModel.setDeletedtime(LocalDateTime.now());
							mapper.insertSelective(originalModel);
						}

						// 新增數據(保留舊ID)
						TzhEquipmentUsageExample newExample = new TzhEquipmentUsageExample();
						TzhEquipmentUsageExample.Criteria newCriteria = newExample.or();
						newCriteria.andIdEqualTo(newModel.getId());
						newCriteria.andIsdeletedEqualTo(false);
						newModel.setCreatedby(currentUser.getUsername());
						newModel.setCreatedtime(LocalDateTime.now());
						newModel.setDeletedby(null);
						newModel.setDeletedtime(null);
						mapper.deleteByExample(newExample);
						mapper.insertSelective(newModel);
					} else {
						// 新增數據(新建ID)
						newModel.setId(UUID.randomUUID().toString());
						newModel.setCreatedby(currentUser.getUsername());
						newModel.setCreatedtime(LocalDateTime.now());
						mapper.insertSelective(newModel);
					}
				} else {
					// 刪除數據
					TzhEquipmentUsageExample example = new TzhEquipmentUsageExample();
					TzhEquipmentUsageExample.Criteria criteria = example.or();
					criteria.andIdEqualTo(newModel.getId());
					newModel.setIsdeleted(true);
					newModel.setDeletedby(currentUser.getUsername());
					newModel.setDeletedtime(LocalDateTime.now());
					mapper.updateByExampleSelective(newModel, example);
				}
			} catch(Exception ex) {
				throw new ServiceException("錄入數據出問題:" + JSON.toJSONString(newModel));
			}
		}

		return newModelList;
	}
}
