package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.SocialPerfTwoActivityCustomMapper;
import com.csci.susdev.mapper.SocialPerfTwoActivityMapper;
import com.csci.susdev.model.SocialPerfTwoActivity;
import com.csci.susdev.model.SocialPerfTwoActivityExample;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.modelcovt.SocialPerfTwoActivityConverter;
import com.csci.susdev.qo.SocialPerfTwoActivityQO;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.susdev.vo.SocialPerfTwoActivityVO;
import com.csci.tzh.vo.DCdmsMaterialCarbonFactorVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.swing.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class SocialPerfTwoActivityService {

    @Resource
    private SocialPerfTwoActivityMapper socialPerfTwoActivityMapper;

    @Resource
    private SocialPerfTwoActivityCustomMapper socialPerfTwoActivityCustomMapper;

    /**
     * 保存记录
     *
     * @param socialPerfTwoActivityVO
     * @return
     */
    public String saveSocialPerfTwoActivity(SocialPerfTwoActivityVO socialPerfTwoActivityVO) {

        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        workflowControlService.throwIfHasWorkflowControl(socialPerfTwoActivityVO.getHeadId(), "本期数据已经提交，不能进行新增或修改");

        if (StringUtils.isBlank(socialPerfTwoActivityVO.getId())) {
            // 新增
            return doAdd(socialPerfTwoActivityVO);
        } else {
            checkExist(socialPerfTwoActivityVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(socialPerfTwoActivityVO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String integrateSocialPerfTwoActivity(SocialPerfTwoActivityQO socialPerfTwoActivityQO) {
        UserInfo currentUser = ContextUtils.getCurrentUser();

        //刪除舊數據
        SocialPerfTwoActivityExample example = new SocialPerfTwoActivityExample();
        example.or().andHeadIdEqualTo(socialPerfTwoActivityQO.getHeadId());
        socialPerfTwoActivityMapper.deleteByExample(example);

        //集合最新數據
        List<SocialPerfTwoActivity> lst = socialPerfTwoActivityCustomMapper.getIntegratedData(socialPerfTwoActivityQO.getHeadId());
        for(SocialPerfTwoActivity x : lst) {
            x.setId(UUID.randomUUID().toString());
            x.setHeadId(socialPerfTwoActivityQO.getHeadId());
            x.setCreateUserId(currentUser.getId());
            x.setCreateUsername(currentUser.getUsername());
            x.setLastUpdateVersion(1);
            socialPerfTwoActivityMapper.insert(x);
        }
        return socialPerfTwoActivityQO.getHeadId();
    }

    /**
     * 复制指定的记录
     *
     * @param id
     * @return
     */
    public SocialPerfTwoActivity duplicateSocialPerfTwoActivity(String id) {
        SocialPerfTwoActivity socialPerfTwoActivity = socialPerfTwoActivityMapper.selectByPrimaryKey(id);
        checkExist(socialPerfTwoActivity, "未找到对应的记录");

        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        workflowControlService.throwIfHasWorkflowControl(socialPerfTwoActivity.getHeadId(), "本期数据已经提交，不能进行复制操作");

        socialPerfTwoActivity.setId(null);
        socialPerfTwoActivity.setCreationTime(null);
        socialPerfTwoActivity.setCreateUserId(null);
        socialPerfTwoActivity.setCreateUsername(null);
        socialPerfTwoActivity.setLastUpdateVersion(0);
        socialPerfTwoActivity.setLastUpdateTime(null);
        socialPerfTwoActivity.setLastUpdateUserId(null);
        socialPerfTwoActivity.setLastUpdateUsername(null);
        socialPerfTwoActivityMapper.insert(socialPerfTwoActivity);
        return socialPerfTwoActivity;
    }

    /**
     * 查询记录
     *
     * @return
     */
    public ResultPage<SocialPerfTwoActivityVO> listSocialPerfTwoActivity(SocialPerfTwoActivityQO qo) {
        checkExist(qo.getHeadId(), "主表记录ID不能为空");
        PageHelper.startPage(qo.getCurPage(), qo.getPageSize(), "spta.type, o.no, spta.date");
        List<SocialPerfTwoActivityVO> lstVO = socialPerfTwoActivityCustomMapper.listVO(qo.getHeadId());
        ResultPage<SocialPerfTwoActivityVO> resultPage = new ResultPage<>(lstVO, true);
        return resultPage;
    }

    private String getColumnName(String sortName) {
        if (StringUtils.isBlank(sortName)) {
            return null;
        }
        switch (sortName) {
            case "type":
                return "type";
            case "name":
                return "name";
            case "date":
                return "date";
            case "staffCount":
                return "staff_count";
            case "hourCount":
                return "hour_count";
            case "serviceTarget":
                return "service_target";
            case "beneficiaryOrganization":
                return "beneficiary_organization";
            case "noOfBeneficiaries":
                return "no_of_beneficiaries";
            case "totalDonation":
                return "total_donation";
            case "donationType":
                return "donation_type";
            case "donationQty":
                return "donation_qty";
            default:
                return null;
        }
    }

    private String doAdd(SocialPerfTwoActivityVO socialPerfTwoActivityVO) {
        SocialPerfTwoActivity socialPerfTwoActivity = SocialPerfTwoActivityConverter.convert(socialPerfTwoActivityVO);
        socialPerfTwoActivityMapper.insertSelective(socialPerfTwoActivity);
        return socialPerfTwoActivity.getId();
    }

    private String doUpdate(SocialPerfTwoActivityVO socialPerfTwoActivityVO) {
        SocialPerfTwoActivity socialPerfTwoActivity = SocialPerfTwoActivityConverter.convert(socialPerfTwoActivityVO);
        SocialPerfTwoActivityExample example = new SocialPerfTwoActivityExample();
        example.or().andIdEqualTo(socialPerfTwoActivityVO.getId()).andLastUpdateVersionEqualTo(socialPerfTwoActivityVO.getLastUpdateVersion());
        int updateCount = socialPerfTwoActivityMapper.updateByExampleSelective(socialPerfTwoActivity, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return socialPerfTwoActivity.getId();
    }

    public SocialPerfTwoActivityVO getSocialPerfTwoActivityById(String id) {
        SocialPerfTwoActivity socialPerfTwoActivity = socialPerfTwoActivityMapper.selectByPrimaryKey(id);
        if (socialPerfTwoActivity == null) {
            throw new ServiceException("数据不存在");
        }
        return SocialPerfTwoActivityConverter.convert(socialPerfTwoActivity);
    }

    public void deleteSocialPerfTwoActivityById(String id) {
        checkExist(id, "id不能为空");
        socialPerfTwoActivityMapper.deleteByPrimaryKey(id);
    }

    public List<SocialPerfTwoActivity> selectByExample(SocialPerfTwoActivityExample example) {
        return socialPerfTwoActivityMapper.selectByExample(example);
    }

    // 根据组织机构id及年份查询记录
    public List<SocialPerfTwoActivity> listSocialPerfTwoActivityByOrgIdAndYear(String orgId, Integer year) {
        // first, query head record by org id and year
        /*AmbientHeadExample headExample = new AmbientHeadExample();
        headExample.or().and

        SocialPerfTwoActivityExample example = new SocialPerfTwoActivityExample();
        example.or().andOrgIdEqualTo(orgId).andYearValueEqualTo(year);
        return socialPerfTwoActivityMapper.selectByExample(example);*/
        throw new ServiceException("未实现");
    }
}
