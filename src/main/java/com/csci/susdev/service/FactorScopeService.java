package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.NamingConventionEnum;
import com.csci.susdev.mapper.FactorScopeCustomMapper;
import com.csci.susdev.mapper.FactorScopeMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.FactorScopeConverter;
import com.csci.susdev.qo.FactorScopeQO;
import com.csci.susdev.util.MybatisHelper;
import com.csci.susdev.vo.FactorScopeVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class FactorScopeService {

    @Resource
    private FactorScopeMapper factorScopeMapper;

    @Resource
    private FactorScopeCustomMapper factorScopeCustomMapper;

    @Resource
    private FactorSelectionService factorSelectionService;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FactorScope record) {
        return factorScopeMapper.insertSelective(record);
    }

    public FactorScope selectByPrimaryKey(String id) {
        return factorScopeMapper.selectByPrimaryKey(id);
    }

    public FactorScopeVO getFactorScope(String id) {
        checkExist(id, "id不能为空");
        FactorScope factorScope = selectByPrimaryKey(id);
        checkExist(factorScope, "未找到对应的记录");
        if(factorScope.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return FactorScopeConverter.convertToVO(factorScope);
    }

    @Transactional(rollbackFor = Exception.class)
    public String saveFactorScope(FactorScopeVO factorScopeVO) {
        if (StringUtils.isBlank(factorScopeVO.getId())) {
            // 新增
            return doAdd(factorScopeVO);
        } else {
            checkExist(factorScopeVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(factorScopeVO);
        }

    }

    public ResultPage<FactorScopeVO> listFactorScope(FactorScopeQO factorScopeQO) {
        if (StringUtils.isNotBlank(factorScopeQO.getOrderBy())) {
            List<String> fieldNames = Arrays.stream(FactorScopeVO.class.getDeclaredFields()).map(field -> field.getName()).collect(Collectors.toList());
            if(MybatisHelper.checkSqlInjectionForOrderBy(factorScopeQO.getOrderBy(), fieldNames, NamingConventionEnum.CAMO)) {
                throw new ServiceException("orderBy 有SQL注入行為");
            }
        }

        PageHelper.startPage(factorScopeQO.getCurPage(), factorScopeQO.getPageSize());

        List<FactorScopeVO> factorScopeVOS = new ArrayList<>();

                factorScopeVOS = factorScopeCustomMapper.list(
                        factorScopeQO.getProtocolId(),
                        factorScopeQO.getCarbonEmissionLocationId(),
                        factorScopeQO.getYear(),
                        factorScopeQO.getOrderBy()
                ,factorScopeQO.getFormCode());
        return new ResultPage<>(factorScopeVOS, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFactorScope(String id) {
        FactorScope record = selectByPrimaryKey(id);

        FactorSelectionExample example = new FactorSelectionExample();
        example.or().andFactorScopeIdEqualTo(record.getId()).andIsDeletedEqualTo(false);
        List<FactorSelection> factorSelectionList = factorSelectionService.selectByExample(example);
        if(factorSelectionList.size() > 0) {
            throw new ServiceException("已在因子選擇中被使用，不能刪除");
        }

        record.setIsDeleted(true);
        factorScopeMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(FactorScopeVO factorScopeVO) {
        FactorScope factorScope = FactorScopeConverter.convertToModel(factorScopeVO);
        factorScopeMapper.insertSelective(factorScope);
        return factorScope.getId();
    }

    private String doUpdate(FactorScopeVO factorScopeVO) {
        FactorScope originalRecord = selectByPrimaryKey(factorScopeVO.getId());
        FactorScope factorScope = FactorScopeConverter.convertToModelWithBase(factorScopeVO, originalRecord);

        FactorScopeExample example = new FactorScopeExample();
        example.or().andIdEqualTo(factorScopeVO.getId()).andLastUpdateVersionEqualTo(factorScopeVO.getLastUpdateVersion());
        int updateCount = factorScopeMapper.updateByExample(factorScope, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return factorScope.getId();
    }

    public List<FactorScope> selectByExample(FactorScopeExample example) {
        return factorScopeMapper.selectByExample(example);
    }
}
