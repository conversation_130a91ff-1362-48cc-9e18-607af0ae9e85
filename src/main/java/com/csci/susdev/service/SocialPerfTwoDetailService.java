package com.csci.susdev.service;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.SocialPerfTwoDetailMapper;
import com.csci.susdev.model.SocialPerfTwoDetail;
import com.csci.susdev.model.SocialPerfTwoDetailExample;
import com.csci.susdev.modelcovt.SocialPerfTwoDetailConverter;
import com.csci.susdev.modelcovt.SocialPerfTwoTableDataConverter;
import com.csci.susdev.vo.SocialPerfTwoDetailVO;
import com.csci.susdev.vo.SocialPerfTwoTableDataVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class SocialPerfTwoDetailService {

    @Resource
    private SocialPerfTwoDetailMapper socialPerfTwoDetailMapper;

    /**
     * 查询指定社会绩效二主表下的明细数据
     *
     * @param headId 社会绩效二主表ID
     * @return
     */
    public List<SocialPerfTwoDetailVO> listSocialPerfTwoDetail(String headId) {
        checkExist(headId, "社会绩效二主表ID不能为空");

        List<SocialPerfTwoDetail> lstDetails = selectSocialPerfTwoDetailList(headId);
        return lstDetails.stream().map(SocialPerfTwoDetailConverter::convert).collect(Collectors.toList());
    }

    /**
     * 查询指定社会绩效二主表下的明细数据
     *
     * @param headId 社会绩效二主表ID
     * @return
     */
    public List<SocialPerfTwoTableDataVO> listSocialPerfTwoTableData(String headId) {
        checkExist(headId, "社会绩效二主表ID不能为空");
        List<SocialPerfTwoDetail> lstDetails = selectSocialPerfTwoDetailList(headId);
        return lstDetails.stream().map(SocialPerfTwoTableDataConverter::convert).collect(Collectors.toList());
    }

    private List<SocialPerfTwoDetail> selectSocialPerfTwoDetailList(String headId) {
        SocialPerfTwoDetailExample example = new SocialPerfTwoDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq");

        return socialPerfTwoDetailMapper.selectByExample(example);
    }
}
