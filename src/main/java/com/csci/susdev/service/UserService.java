package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.common.util.CustomGsonBuilder;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.*;
import com.csci.susdev.model.*;
import com.csci.susdev.model.UserExample.Criteria;
import com.csci.susdev.modelcovt.UserHistoryConverter;
import com.csci.susdev.modelcovt.UserVOConverter;
import com.csci.susdev.qo.*;
import com.csci.susdev.util.DemoUtils;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.susdev.util.userinit.UserData;
import com.csci.susdev.vo.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.*;

@Service
@LogMethod
@DS(DatasourceContextEnum.SUSDEV)
public class UserService {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    private final Gson gson = CustomGsonBuilder.createGson();

    @Resource
    private UserMapper userMapper;

    @Resource
    private UserCustomMapper userCustomMapper;

    @Resource
    private OrganizationMapperCustom organizationMapperCustom;

    @Autowired
    private OrganizationService organizationService;
    
    @Autowired
    private UserOrganizationService userOrganizationService;
    
    @Autowired
    private RoleService roleService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private MenuService menuService;

    @Resource
    private UserHistoryMapper userHistoryMapper;

    @Resource
    private UserRoleMapper userRoleMapper;

    /**
     * proxy for mapper method
     *
     * @param record
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(User record) {
        return userMapper.insertSelective(record);
    }

    /**
     * proxy for mapper method
     *
     * @param record
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKeySelective(User record) {
        return userMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 查询指定用户名的用户信息
     * 为查询到返回null
     *
     * @param username
     * @return
     */
    public User getUserByUsername(String username) {
        return ServiceHelper.getUserByUsername(username, userMapper);
    }


    public void checkIsReadOnly() {
        UserInfo userInfo = ContextUtils.getCurrentUser();
        if(userInfo == null) {
            throw new ServiceException("找不到當前用戶");
        }
        UserBasicVO curUser = getUserBasicVOByUsername(userInfo.getUsername());
        if(curUser != null && curUser.getIsReadonly() == true) {
            throw new ServiceException("沒有權限");
        }
    }

    /**
     * 获取当前用户的菜單信息
     *
     * @return
     */
    public List<MenuVO> currentUserMenu() {
        List<MenuVO> resultList = new ArrayList<>();
        List<Menu> lstMenu = new ArrayList<>();

        UserInfo userInfo = ContextUtils.getCurrentUser();

        // 取得同一個用戶存在多個角色下的不重覆菜單
        if (ContextUtils.isSuperAdmin()) {
            lstMenu = listAllMenu();
        } else {
            lstMenu = userCustomMapper.listMenuByUsernameSql(userInfo.getUsername());
        }

        if (!CollectionUtils.isEmpty(lstMenu)) {
            //将菜单下对应的父节点也一并全部查询出来
            Set<String> allMenuIds = new HashSet<>();
            for (Menu menu : lstMenu) {
                allMenuIds.add(menu.getId());
                if (StringUtils.isNotEmpty(menu.getPath())) {
                    String[] pathIds = StringUtils.split(",", menu.getPath());
                    for (String pathId : pathIds) {
                        allMenuIds.add(pathId);
                    }
                }
            }
            //生成包含父類的菜單列表
        	lstMenu = new ArrayList<>();
            for(String menuId : allMenuIds) {
            	Menu menu = menuService.getById(menuId);
            	if(menu != null) {
                    lstMenu.add(menu);
                }
            }

            // 排序
            lstMenu.sort(new Comparator<Menu>() {
                public int compare(Menu o1, Menu o2) {
                    return o1.getSeq().compareTo(o2.getSeq());
                }
            });

            // 獲取樹狀結構
            resultList = transferMenuVO(lstMenu, "");
        }
        return resultList;
    }

    public List<MenuVO> getUserMenuByUserName(String username) {
        List<MenuVO> resultList = new ArrayList<>();
        List<Menu> lstMenu = new ArrayList<>();

        // 取得同一個用戶存在多個角色下的不重覆菜單
        if (ContextUtils.isSuperAdmin()) {
            lstMenu = listAllMenu();
        } else {
            lstMenu = userCustomMapper.listMenuByUsernameSql(username);
        }

        if (!CollectionUtils.isEmpty(lstMenu)) {
            //将菜单下对应的父节点也一并全部查询出来
            Set<String> allMenuIds = new HashSet<>();
            for (Menu menu : lstMenu) {
                allMenuIds.add(menu.getId());
                if (StringUtils.isNotEmpty(menu.getPath())) {
                    String[] pathIds = StringUtils.split(",", menu.getPath());
                    for (String pathId : pathIds) {
                        allMenuIds.add(pathId);
                    }
                }
            }
            //生成包含父類的菜單列表
            lstMenu = new ArrayList<>();
            for (String menuId : allMenuIds) {
                Menu menu = menuService.getById(menuId);
                if (menu != null) {
                    lstMenu.add(menu);
                }
            }

            // 排序
            lstMenu.sort(new Comparator<Menu>() {
                public int compare(Menu o1, Menu o2) {
                    return o1.getSeq().compareTo(o2.getSeq());
                }
            });

            // 獲取樹狀結構
            resultList = transferMenuVO(lstMenu, "");
        }
        return resultList;
    }

    /**
     * 封装菜单视图
     *
     * @param allMenu
     * @param parentId
     * @return
     */
    private List<MenuVO> transferMenuVO(List<Menu> allMenu, String parentId) {
        List<MenuVO> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(allMenu)) {
            for (Menu source : allMenu) {
                if (parentId.compareTo(source.getParentId()) == 0) {
                    MenuVO menuVO = new MenuVO();
                    BeanUtils.copyProperties(source, menuVO);
                    //递归查询子菜单，并封装信息
                    List<MenuVO> childList = transferMenuVO(allMenu, source.getId());
                    if (!CollectionUtils.isEmpty(childList)) {
                        menuVO.setMenuVOList(childList);
                    }
                    resultList.add(menuVO);
	            }
	        }
	    }
	    return resultList;
	}

    public UserBasicVO getUserBasicVOByUsername(String username) {
    	//Set User
        User user = this.getUserByUsername(username);
        if(user == null)  return null;
        user.setPassword("");
        UserBasicVO userVO = new UserBasicVO();
        BeanUtils.copyProperties(user, userVO);
        
        return userVO;
    }

    public UserVO getUserVOByUsername(String username) {
    	//Set User
        User user = this.getUserByUsername(username);
        if(user == null)  return null;
        user.setPassword("");
        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(user, userVO);
        
        return userVO;
    }
    
    public UserBasicVO getUserBasicVODetailByUsername(String username) {
    	UserBasicVO userVO = this.getUserBasicVOByUsername(username);
        
        // Set Organization List
        OrganizationPageableQO organizationQO = new OrganizationPageableQO();
        organizationQO.setPageSize(0);
        organizationQO.setCurPage(0);
        organizationQO.setUsername(username);
        List<OrganizationVO> lstOrganization = organizationService.listOrganization(organizationQO).getList();
        List<OrganizationBasicVO> lstOrganizationVO = lstOrganization.stream().map(x -> {
            OrganizationBasicVO vo = new OrganizationBasicVO();
            BeanUtils.copyProperties(x, vo);
            return vo;
        }).collect(Collectors.toList());
        userVO.setLstOrganization(lstOrganizationVO);
        
        // Set Role List
        RolePageableQO roleQO = new RolePageableQO();
        roleQO.setPageSize(0);
        roleQO.setCurPage(0);
        roleQO.setUsername(username);
        List<RoleBasicVO> lstRoleVO = roleService.listRoleBasic(roleQO).getList();
        if(lstRoleVO != null) {
        	lstRoleVO.stream().map(role -> {
	        	RoleBasicVO roleVO = new RoleBasicVO();
	            BeanUtils.copyProperties(role, roleVO);
	            return roleVO;
	        }).collect(Collectors.toList());
        }
        userVO.setLstRoleVO(lstRoleVO);
        
        return userVO;
    }

    /**
     * 用户 数据
     *
     * @param id
     * @return
     */
    public User getUser(String id) {
        return userMapper.selectByPrimaryKey(id);
    }


    /**
     * 用户 数据列表
     *
     * @param userPageableQO
     * @return
     */
    public ResultPage<UserVO> listUser(KeywordPageQO userPageableQO) {
        UserExample example = new UserExample();

        if (StringUtils.isNotBlank(userPageableQO.getKeyword())) {
            example.or().andUsernameLike("%" + userPageableQO.getKeyword() + "%");
            example.or().andNameLike("%" + userPageableQO.getKeyword() + "%");
        }
        example.setOrderByClause("last_update_time desc");

        PageHelper.startPage(userPageableQO.getCurPage(), userPageableQO.getPageSize());
        List<User> lstUser = userMapper.selectByExample(example);
        ResultPage<UserVO> resultPage = new ResultPage<>(lstUser);
        resultPage.setList(lstUser.stream().map(UserVOConverter::convert).collect(Collectors.toList()));
        return resultPage;
    }


    public ResultPage<UserVO> listUserDetail(UserPageableQO userPageableQO) {
        UserExample example = new UserExample();

        if (StringUtils.isNotBlank(userPageableQO.getKeyword())) {
            example.or().andUsernameLike("%" + userPageableQO.getKeyword() + "%");
            example.or().andNameLike("%" + userPageableQO.getKeyword() + "%");
        }
        example.setOrderByClause("last_update_time desc");

        PageHelper.startPage(userPageableQO.getCurPage(), userPageableQO.getPageSize());
        List<User> lstUser = userMapper.selectByExample(example);
        ResultPage<UserVO> resultPage = new ResultPage<>(lstUser);
        List<UserVO> lstUserVO = lstUser.stream().map(UserVOConverter::convert).collect(Collectors.toList());
        for(UserVO userVO : lstUserVO) {
            userVO.setRoleIds(listRolesOfUser(userVO.getId()).stream().map(Role::getId).collect(Collectors.toList()));
            userVO.setBsRoleNames(listBsRoleNamesOfUser(userVO.getUsername()).stream().map(TzhBsRole::getRolename).collect(Collectors.toList()));
            userVO.setOrganizationIds(listOrganizationsOfUser(userVO.getId()).stream().map(Organization::getId).collect(Collectors.toList()));
            userVO.setBsOrganizationIds(listBsOrganizationsOfUser(userVO.getUsername()).stream().map(Organization::getId).collect(Collectors.toList()));
        }
        resultPage.setList(lstUserVO);

        return resultPage;
    }


    public ResultPage<UserBasicVO> listUserDetail2(UserPageableQO userPageableQO) {
    	UserExample example = new UserExample();
    	/*
        Criteria criteria = example.or();
		if (StringUtils.isNotBlank(userPageableQO.getUsername())) {
			criteria.andUsernameLike("%" + userPageableQO.getUsername() + "%");
		}
    	 */
        example.setOrderByClause("last_update_time desc");

        PageHelper.startPage(userPageableQO.getCurPage(), userPageableQO.getPageSize());
        List<User> lstUser = userMapper.selectByExample(example);

        Page<UserBasicVO> lstResultVO = new Page<>();
        for(User user: lstUser) {
        	UserBasicVO resultVO = this.getUserBasicVODetailByUsername(user.getUsername());
            lstResultVO.add(resultVO);
        }
        ResultPage<UserBasicVO> resultPage = new ResultPage<>(lstUser);
        resultPage.setList(lstResultVO);

        return resultPage;
    }


    /**
     * 驗證 用户 信息, 如有錯誤返回相關資訊
     *
     * @param checkUser
     * @return
     */
    public void checkUser(UserVO checkUser) {
        boolean bIsNew = StringUtils.isBlank(checkUser.getId());
        checkExist(checkUser.getIsAdAccount(), "請選擇是否AD帳號");
        checkExist(checkUser.getIsEnabled(), "請選擇是否啟用");

        User user = this.getUserByUsername(checkUser.getUsername());
        if (user != null && !user.getId().equals(checkUser.getId())) {
            throw new ServiceException("用户名已存在");
        }

        if (Objects.equals(checkUser.getIsAdAccount(), false)) {
            // 如果不是域账号
            if (bIsNew) {
                // 如果是新增用户，则必须填密码
                checkExist(checkUser.getPassword(), "請輸入密碼");
            }
            if (StringUtils.isNotBlank(checkUser.getPassword())) {
                // 如果密码不为空，则必须符合密码规则
                boolean isMatch = Pattern.compile("^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\\W_!@#$%^&*`~()-+=]+$)(?![0-9\\W_!@#$%^&*`~()-+=]+$)[a-zA-Z0-9\\W_!@#$%^&*`~()-+=]{8,36}$")
                        .matcher(checkUser.getPassword())
                        .find();
                if(!isMatch) {
                    throw new ServiceException("必须符合密码规则");
                }
                checkUser.setPassword(DigestUtils.md5Hex(checkUser.getPassword()));
            }
        }
    }

    public Map<String, String> checkUserRetMap(UserVO checkUser) {
        Map<String, String> result = new HashMap<>();
        boolean bIsNew = StringUtils.isBlank(checkUser.getId());
        if (Objects.isNull(checkUser.getIsAdAccount())) {
            result.put(checkUser.getUsername(), "請選擇是否AD帳號");
        }
        if (Objects.isNull(checkUser.getIsEnabled())) {
            result.put(checkUser.getUsername(), "請選擇是否啟用");
        }

        if(StringUtils.isBlank(checkUser.getUsername())) {
            result.put(checkUser.getUsername(), "請移入用户名");
        }

        User user = this.getUserByUsername(checkUser.getUsername());
        if (user != null && !user.getId().equals(checkUser.getId())) {
            result.put(checkUser.getUsername(), "用户名已存在");
        }

        if (Objects.equals(checkUser.getIsAdAccount(), false)) {
            // 如果不是域账号
            if (bIsNew) {
                // 如果是新增用户，则必须填密码
                if (Objects.isNull(checkUser.getPassword())) {
                    result.put(checkUser.getUsername(), "請輸入密碼");
                }
            }
            if (StringUtils.isNotBlank(checkUser.getPassword())) {
                // 如果密码不为空，则必须符合密码规则
                checkUser.setPassword(DigestUtils.md5Hex(checkUser.getPassword()));
            }
        }
        return result;
    }

    /**
     * 保存 用户 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param userVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveUser(UserVO userVO) {
        checkUser(userVO);

        User user = UserVOConverter.convert(userVO);

        if (StringUtils.isBlank(userVO.getId())) {
            try {
                userMapper.insertSelective(user);
            } catch (DuplicateKeyException e) {
                throw new ServiceException("用户名已存在");
            }
        } else {
            checkExist(userVO.getLastUpdateVersion(), "更新时版本号不能为空");
            UserExample example = new UserExample();
            example.or().andIdEqualTo(user.getId()).andLastUpdateVersionEqualTo(user.getLastUpdateVersion());
            if(StringUtils.isEmpty(user.getPassword())) {
                user.setPassword(null);
            }
            int updateCount = userMapper.updateByExampleSelective(user, example);
            if (updateCount == 0) {
                throw new ServiceException("数据已被修改，请刷新后重试");
            }
        }

        userRoleService.deleteUserRoleByUserId(user.getId());
        for (String roleId : userVO.getRoleIds()) {
            UserRole userRole = new UserRole();
            userRole.setRoleId(roleId);
            userRole.setUserId(user.getId());
            userRole.setIsDeleted(false);
            userRoleService.saveUserRole(userRole);
        }
        userRoleService.deleteBsUserRoleByUsername(user.getUsername());
        for (String bsRoleName : userVO.getBsRoleNames()) {
            TzhBsUserRole tzhBsUserRole = new TzhBsUserRole();
            tzhBsUserRole.setRolename(bsRoleName);
            tzhBsUserRole.setUsername(user.getUsername());
            userRoleService.saveBsUserRole(tzhBsUserRole);
        }

        userOrganizationService.deleteUserOrganizationByUserId(user.getId());
        for (String organizationId : userVO.getOrganizationIds()) {
            UserOrganization userOrganization = new UserOrganization();
            userOrganization.setOrganizationId(organizationId);
            userOrganization.setUserId(user.getId());
            userOrganization.setIsDeleted(false);
            userOrganizationService.saveUserOrganization(userOrganization);
        }
        userOrganizationService.deleteBsUserSiteByUsername(user.getUsername());
        for (String bsOrganizationId : userVO.getBsOrganizationIds()) {
            TzhBsUserSite tzhBsUserSite = new TzhBsUserSite();
            tzhBsUserSite.setSiteid(bsOrganizationId);
            tzhBsUserSite.setUsername(user.getUsername());
            userOrganizationService.saveTzhBsUserSite(tzhBsUserSite);
        }
        return user.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> saveUserList(List<UserVO> userVOList) {
        Map<String, String> result = new HashMap<>();
        for(UserVO userVO : userVOList) {
            try {
                result.putAll(checkUserRetMap(userVO));

                User user = UserVOConverter.convert(userVO);

                User oldUser = getUserByUsername(user.getUsername());
                if(oldUser != null) {
                    UserExample example = new UserExample();
                    example.or().andUsernameEqualTo(user.getUsername());
                    if(StringUtils.isEmpty(user.getPassword())) {
                        user.setPassword(null);
                    }
                    userMapper.updateByExampleSelective(user, example);
                }
                userMapper.insertSelective(user);

                user = getUserByUsername(user.getUsername());

                userRoleService.deleteUserRoleByUserId(user.getId());
                for (String roleId : userVO.getRoleIds()) {
                    UserRole userRole = new UserRole();
                    userRole.setRoleId(roleId);
                    userRole.setUserId(user.getId());
                    userRole.setIsDeleted(false);
                    userRoleService.saveUserRole(userRole);
                }
                userRoleService.deleteBsUserRoleByUsername(user.getUsername());
                for (String bsRoleName : userVO.getBsRoleNames()) {
                    TzhBsUserRole tzhBsUserRole = new TzhBsUserRole();
                    tzhBsUserRole.setRolename(bsRoleName);
                    tzhBsUserRole.setUsername(user.getUsername());
                    userRoleService.saveBsUserRole(tzhBsUserRole);
                }
                userOrganizationService.deleteUserOrganizationByUserId(user.getId());
                for (String organizationId : userVO.getOrganizationIds()) {
                    UserOrganization userOrganization = new UserOrganization();
                    userOrganization.setOrganizationId(organizationId);
                    userOrganization.setUserId(user.getId());
                    userOrganization.setIsDeleted(false);
                    userOrganizationService.saveUserOrganization(userOrganization);
                }
                userOrganizationService.deleteBsUserSiteByUsername(user.getUsername());
                for (String organizationId : userVO.getBsOrganizationIds()) {
                    TzhBsUserSite tzhBsUserSite = new TzhBsUserSite();
                    tzhBsUserSite.setSiteid(organizationId);
                    tzhBsUserSite.setUsername(user.getUsername());
                    userOrganizationService.saveTzhBsUserSite(tzhBsUserSite);
                }

            } catch (Exception ex) {
                throw new ServiceException("更新失敗，請刷新頁面再嘗試。");
            }
        }
        return result;
    }

    /**
     * 根据id删除用户
     *
     * @param id 用户id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteUserById(String id) {
        checkExist(id, "用户id不能为空");
        User user = userMapper.selectByPrimaryKey(id);
        if (Objects.isNull(user)) {
            throw new ServiceException("用户不存在");
        }
        // 删除角色用户关联表
        userRoleService.deleteUserRoleByUserId(user.getId());
        userRoleService.deleteBsUserRoleByUsername(user.getUsername());
        // 删除组织用户关联表
        userOrganizationService.deleteUserOrganizationByUserId(user.getId());
        userOrganizationService.deleteBsUserSiteByUsername(user.getUsername());

        userHistoryMapper.insertSelective(UserHistoryConverter.convert(user));
        userMapper.deleteByPrimaryKey(id);
    }

    /**
     * 根据id获取用户信息
     *
     * @param id 用户id
     * @return
     */
    public UserVO getUserById(String id) {
        checkExist(id, "用户id不能为空");
        User user = userMapper.selectByPrimaryKey(id);
        if (user == null) {
            throw new ServiceException("未找到该用户");
        }
        UserVO userVO = UserVOConverter.convert(user);
        userVO.setRoleIds(listRolesOfUser(id).stream().map(Role::getId).collect(Collectors.toList()));
        userVO.setBsRoleNames(listBsRoleNamesOfUser(userVO.getUsername()).stream().map(TzhBsRole::getRolename).collect(Collectors.toList()));
        userVO.setOrganizationIds(organizationMapperCustom.selectOrgIdsByUserId(id));
        userVO.setBsOrganizationIds(organizationMapperCustom.selectBsOrgIdsByUserId(id));
        return userVO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void createLocalAdmin(String username) {
        User admin = getUserByUsername(username);
        if (Objects.nonNull(admin)) {
            return;
        }

        User user = new User();
        user.setUsername(username);
        user.setPassword(DigestUtils.md5Hex("admin3311"));
        user.setIsAdAccount(false);
        user.setIsEnabled(true);
        userMapper.insertSelective(user);

        Role role = getRoleByCode("SuperAdmin");
        UserRole userRole = new UserRole();
        userRole.setUserId(user.getId());
        userRole.setRoleId(role.getId());
        userRole.setIsDeleted(false);
        userRoleMapper.insertSelective(userRole);
    }

    @Transactional(rollbackFor = Exception.class)
    public void initUserByEmailAndCompanyName(String email, String organizationName) {
        checkExist(email, "邮箱不能为空");
        checkExist(organizationName, "组织机构不能为空");
        Organization organization = ServiceHelper.getOrganizationByName(organizationName);
        checkExist(organization, "未找到该组织机构");

        // 验证email格式
        if (!DemoUtils.isMatch(email, "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$")) {
            logger.error("email格式不正确");
            return;
        }

        String username;
        boolean bIsAdAccount = false;
        if (StringUtils.endsWith(email, "@cohl.com")) {
            username = RegExUtils.replaceAll(email, "@cohl.com", "");
            bIsAdAccount = true;
        } else {
            username = StringUtils.substring(email, 0, StringUtils.indexOf(email, "@"));
        }
        logger.info("username: {}", username);

        // 根据username查询用户
        User user = ServiceHelper.getUserByUsername(username);
        if (user != null) {
            logger.info("user already exists");
            return;
        }

        // 创建用户
        UserVO userVO = new UserVO();
        userVO.setName(username);
        // userVO.setGender();
        // userVO.setDomainPrefix();
        userVO.setUsername(username);
        if (!bIsAdAccount) {
            userVO.setPassword("********");
        }
        userVO.setEmail(email);
        userVO.setRoleIds(defaultRoleIds());
        userVO.setBsRoleNames(defaultBsRoleNames());
        userVO.setOrganizationIds(Arrays.asList(organization.getId()));
        userVO.setBsOrganizationIds(Arrays.asList(organization.getId()));
        userVO.setIsEnabled(Boolean.TRUE);
        userVO.setIsDeleted(Boolean.FALSE);
        userVO.setIsAdAccount(bIsAdAccount);

        logger.info("user: {}", gson.toJson(userVO));

        saveUser(userVO);
    }

    public void initUserByUserData(UserData userData) {
        if (StringUtils.isBlank(userData.getEmail())) {
            logger.error("email不能为空");
            return;
        }
        if (StringUtils.isBlank(userData.getCompanyName())) {
            logger.error("公司名称不能为空");
            return;
        }
        if (StringUtils.isBlank(userData.getDeptName())) {
            logger.error("部门名称不能为空");
            return;
        }

        // 验证email格式
        if (!DemoUtils.isMatch(userData.getEmail(), "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$")) {
            logger.error("email格式不正确");
            return;
        }

        Organization organization = ServiceHelper.getOrganizationByNameAndCompanyName(userData.getDeptName(), userData.getCompanyName());
        checkExist(organization, "未找到该组织机构");

        String email = userData.getEmail();
        String username;
        boolean bIsAdAccount = false;
        if (StringUtils.endsWith(email, "@cohl.com")) {
            username = RegExUtils.replaceAll(email, "@cohl.com", "");
            bIsAdAccount = true;
        } else {
            username = StringUtils.substring(email, 0, StringUtils.indexOf(email, "@"));
        }
        logger.info("username: {}", username);

        // 根据username查询用户
        User user = ServiceHelper.getUserByUsername(username);
        if (user != null) {
            // 用户已经存在，更新用户信息
            logger.info("user already exists");
            User record = new User();
            record.setId(user.getId());
            record.setName(userData.getName());
            record.setUsername(username);
            record.setEmail(email);
            record.setMobile(userData.getMobile());
            record.setPosition(userData.getPosition());
            record.setIsAdAccount(bIsAdAccount);
            updateByPrimaryKeySelective(record);
            // 添加用户的组织机构权限
            ServiceHelper.addOrgToUser(organization.getId(), user.getId());
            return;
        }

        // 创建用户
        UserVO userVO = new UserVO();
        userVO.setName(username);
        userVO.setUsername(username);
        if (!bIsAdAccount) {
            userVO.setPassword("********");
        }
        userVO.setEmail(email);
        userVO.setRoleIds(defaultRoleIds());
        userVO.setBsRoleNames(defaultBsRoleNames());
        userVO.setOrganizationIds(Arrays.asList(organization.getId()));
        userVO.setBsOrganizationIds(Arrays.asList(organization.getId()));
        userVO.setIsEnabled(Boolean.TRUE);
        userVO.setIsDeleted(Boolean.FALSE);
        userVO.setIsAdAccount(bIsAdAccount);
        userVO.setMobile(userData.getMobile());
        userVO.setPosition(userData.getPosition());
        userVO.setName(userData.getName());

        logger.info("user: {}", gson.toJson(userVO));

        saveUser(userVO);

    }

    private List<String> defaultRoleIds() {
        List<String> roleIds = new ArrayList<>();
        Role role = ServiceHelper.getRoleByCode("esg");
        roleIds.add(role.getId());
        role = ServiceHelper.getRoleByCode("cnt");
        roleIds.add(role.getId());

        return roleIds;
    }

    private List<String> defaultBsRoleNames() {
        List<String> roleNames = new ArrayList<>();
        roleNames.add("leaderUser");

        return roleNames;
    }

    public void addOrgToUser(String orgId, String userId) {
        // 先判断是否已经存在
        ServiceHelper.addOrgToUser(orgId, userId);
    }

    /**
     * proxy for mapper method
     *
     * @param example 查询条件
     * @return
     */
    public List<User> selectByExample(UserExample example) {
        return userMapper.selectByExample(example);
    }

    public ResultPage<UserVO> listUserByRoleId(UserRolePageableQO userRolePageableQO) {

        if (StringUtils.isBlank(userRolePageableQO.getRoleId())) {
            throw new ServiceException("角色ID不能为空！");
        }

        UserRoleExample userRoleExample = new UserRoleExample();
        userRoleExample.or().andIsDeletedEqualTo(Boolean.FALSE).andRoleIdEqualTo(userRolePageableQO.getRoleId());
        List<String> userIds = userRoleMapper.selectByExample(userRoleExample).stream().map(UserRole::getUserId).collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(userIds)) {
            List<UserVO> lstUser = new ArrayList<>();
            ResultPage<UserVO> resultPage = new ResultPage<>(lstUser);
            resultPage.setPageNum(userRolePageableQO.getCurPage());
            resultPage.setPageSize(userRolePageableQO.getPageSize());
            resultPage.setList(lstUser);
            return resultPage;
        }

        UserExample example = new UserExample();
        if (StringUtils.isNotBlank(userRolePageableQO.getKeyword())) {
            example.or().andUsernameLike("%" + userRolePageableQO.getKeyword() + "%").andIdIn(userIds);
            example.or().andNameLike("%" + userRolePageableQO.getKeyword() + "%").andIdIn(userIds);
        }else {
            example.or().andIdIn(userIds);
        }
        example.setOrderByClause("last_update_time desc");

        PageHelper.startPage(userRolePageableQO.getCurPage(), userRolePageableQO.getPageSize());
        List<User> lstUser = userMapper.selectByExample(example);
        ResultPage<UserVO> resultPage = new ResultPage<>(lstUser);
        resultPage.setList(lstUser.stream().map(UserVOConverter::convert).collect(Collectors.toList()));
        return resultPage;
    }
}
