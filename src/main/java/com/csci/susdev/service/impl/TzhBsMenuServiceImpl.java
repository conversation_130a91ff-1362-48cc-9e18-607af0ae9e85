package com.csci.susdev.service.impl;

import com.csci.susdev.controller.menu.PageDTO;
import com.csci.susdev.mapper.TzhBsMenuMapper;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.model.TzhBsMenu;
import com.csci.susdev.qo.TzhBsMenuQO;
import com.csci.susdev.service.ITzhBsMenuService;
import com.github.pagehelper.PageHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

/**
 * @description: 菜单操作
 * 从功能层面而言，整理如下：
 * 1. 搜索，支持根据页签名称和组织机构进行搜索。
 * 2. 新增，支持添加页面配置和展厅配置
 * 3. 编辑，支持修改页面配置或者展厅配置记录。
 * 4. 删除，支持删除页面配置或者展厅配置的某条记录，软删除。
 * 相关操作整理如下：
 * 显示：界面显示，根据展厅模块，页签名称（英文）进行排序即可。
 *
 * 搜索：根据页签名称进行搜索，支持简繁英三种搜索。
 * 删除，找到某条记录，点击右侧的删除，即可直接进行删除，删除是软删除。
 * 删除的成功判断：不能够删除通用模板的记录。
 * 如果用户删除通用模板则提醒：通用模板，不允许删除。
 *

 */
@Service
public class TzhBsMenuServiceImpl implements ITzhBsMenuService {
    @Resource
    private TzhBsMenuMapper tzhBsMenuMapper;

    @Override
    public ResultPage search(PageDTO<TzhBsMenuQO> pageDTO) {
        PageHelper.startPage(pageDTO.getPageNum(), pageDTO.getPageSize());
        List<TzhBsMenu> tzhBsMenus = tzhBsMenuMapper.selectByNameLike(pageDTO.getQueryForm().getPageName());
        return new ResultPage<>(tzhBsMenus, tzhBsMenus);
    }

    /**
     * 新增：点击新增按钮，直接弹窗，直接新增一条页面配置，如左侧截图。其中除了页签备注外，都是必填项。
     * 相关注释整理如下：
     * 页签类别：通用模板是云平台抽象业务活动，不允许新增。
     * 页签名称（英文）：页签名称，唯一标识，不允许重复。
     * 页签路径：url路径。
     * 点击确定后，新增写入数据库的判断：页签类别只能够是自定义模板；英文名重复
     * @param tzhBsMenu
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBean save(TzhBsMenu tzhBsMenu) {
        Integer type = tzhBsMenu.getType();
        if (type == 0) {
            return ResultBean.fail("通用模板是云平台抽象业务活动，不允许新增。");
        }
        // 英文名重复
        int count = tzhBsMenuMapper.selectByTitleEn(tzhBsMenu.getTitleEN());
        if (count>0) {
            return ResultBean.fail("英文名重复");
        }
        int insert = tzhBsMenuMapper.insertSelective(tzhBsMenu);
        return ResultBean.ok();
    }

    @Override
    public ResultPage<TzhBsMenu> getAll(PageDTO<TzhBsMenuQO> pageDTO) {
        if (pageDTO == null) {
            pageDTO = new PageDTO<>();
        }
        PageHelper.startPage(pageDTO.getPageNum(), pageDTO.getPageSize());
        List<TzhBsMenu> tzhBsMenus = tzhBsMenuMapper.queryAll(pageDTO.getQueryForm());
        return new ResultPage<>(tzhBsMenus, tzhBsMenus);

    }

    /**
     * 编辑：找到某条记录，点击右侧的删除，即可直接进行编辑操作，但是无法编辑通用版本的记录。
     * 编辑过程中，无法修改展厅模块，页签类别，页签名称（英文）这三列数据，其余数据可以修改。
     * 如果用户编辑通用模板则提醒：通用模板，不允许编辑。
     * @param tzhBsMenu
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBean edit(TzhBsMenu tzhBsMenu) {

        String id = tzhBsMenu.getId();
        TzhBsMenu menu = tzhBsMenuMapper.selectByPrimaryKey(id);
        if (menu == null) {
            return ResultBean.fail("记录不存在。");
        }
        if (menu.getType() == 0) {
            return ResultBean.fail("通用模板，不允许编辑。");
        }
        if (ObjectUtils.isEmpty(tzhBsMenu.getTitleEN())) {
            return ResultBean.fail("页签名称（英文）不能为空。");
        }
        // 英文名重复
        int count = tzhBsMenuMapper.selectByTitleEn(tzhBsMenu.getTitleEN());
        if (count>1) {
            return ResultBean.fail("英文名重复");
        }
        tzhBsMenuMapper.updateByPrimaryKeySelective(tzhBsMenu);
        return ResultBean.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBean delete(TzhBsMenu tzhBsMenu) {
        String id = tzhBsMenu.getId();
        TzhBsMenu menu = tzhBsMenuMapper.selectByPrimaryKey(id);
        if (menu == null) {
            return ResultBean.fail("记录不存在。");
        }
        if (menu.getType() == 0) {
            return ResultBean.fail("通用模板，不允许删除。");
        }
        return ResultBean.ok(tzhBsMenuMapper.logicallyDelete(tzhBsMenu.getId()));
    }

}
