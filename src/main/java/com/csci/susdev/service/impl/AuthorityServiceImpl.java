package com.csci.susdev.service.impl;

import com.csci.susdev.model.Operation;
import com.csci.susdev.service.IAuthService;
import com.csci.susdev.service.OperationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: barry
 * @create: 2024-06-04 18:24
 */
@Service
public class AuthorityServiceImpl implements IAuthService {
    @Resource
    private OperationService operationService;
    @Override
    public List<Operation> getOperationsByRoles(List<String> roleList) {
        List<Operation> operations = operationService.getOperationsByRoles(roleList);
        return operations;
    }
}
