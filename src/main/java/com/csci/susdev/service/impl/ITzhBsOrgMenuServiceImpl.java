package com.csci.susdev.service.impl;

import com.csci.susdev.controller.menu.PageDTO;
import com.csci.susdev.mapper.TzhBsOrgMenuMapper;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.model.TzhBsOrgMenu;
import com.csci.susdev.qo.TzhBsOrgMenuQO;
import com.csci.susdev.service.ITzhBsOrgMenuService;
import com.csci.susdev.vo.TzhBsOrgMenuVO;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

/**
 * @description:
 *
 * 展厅配置，主要是结合组织机构，配置展厅的页签，主要字段整理如下：
 * 组织机构：保存组织机构的id字段。
 * 协议：枚举值，包括：ISO 14064，GBT 51366， ESG，无废协议。
 * 页签名称（英文）：枚举值，是页面配置中页签名称（英文），支持搜索。
 * 展厅模块：不可输入字段，根据页签名称（英文）选择后关联体现。
 * 排序：数字，说明展厅中界面的展现顺序。
 * 备注：补充说明相关描述。
 * <p>
 * 相关操作整理如下：
 * 显示：界面显示，根据组织机构，展厅模块，页签名称（英文）进行排序即可。
 * <p>
 * 搜索：根据组织机构进行搜索，支持简繁体和地盘编码的搜索。
 * <p>
 * <p>
 * 删除，找到某条记录，点击右侧的删除，即可直接进行删除，删除是软删除。
 * <p>
 * 编辑：找到某条记录，点击右侧的删除，即可直接进行编辑操作。
 * 编辑过程中，无法修改组织机构，展厅模块，协议，页签名称（英文）这司列数据，其余数据可以修改。
 * 编辑写入数据库的判断：组织机构+展厅模块+协议+排序，不能够重复；组织机构+展厅模块+协议+页签路径（通过页签名称（英文）关联获取），不能够重复。
 * @author: barry
 * @create: 2024-05-16 00:35
 */
@Service
@Slf4j
public class ITzhBsOrgMenuServiceImpl implements ITzhBsOrgMenuService {

    @Resource
    private TzhBsOrgMenuMapper tzhBsOrgMenuMapper;

    /**
     *
     * 新增：点击新增按钮，直接弹窗，直接新增某组织机构的一条页面配置，如左侧截图。其中除了备注外，都是必填项。
     * 点击确定后，新增写入数据库的判断：组织机构+展厅模块+协议+排序，不能够重复；组织机构+展厅模块+协议+页签路径（通过页签名称（英文）关联获取），不能够重复。
     * @param tzhBsOrgMenu
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBean save(TzhBsOrgMenu tzhBsOrgMenu) {
        try {
            int insert = tzhBsOrgMenuMapper.insertSelective(tzhBsOrgMenu);
        } catch (DuplicateKeyException e) {
            log.error("[ITzhBsOrgMenuServiceImpl] Duplicate key exception: ", e);
            return ResultBean.fail("组织机构+展厅模块+协议+页签路径重复");
        }
        return ResultBean.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBean edit(TzhBsOrgMenu tzhBsOrgMenu) {
        int rows = tzhBsOrgMenuMapper.updateByPrimaryKeySelective(tzhBsOrgMenu);
        return rows > 0 ? ResultBean.ok() : ResultBean.fail("修改失败");
    }

    @Override
    public ResultBean delete(TzhBsOrgMenu tzhBsOrgMenu) {
        throw new UnsupportedOperationException();
    }


    @Override
    public ResultPage getAll(PageDTO<TzhBsOrgMenuQO> pageDTO) {
        if (pageDTO == null) {
            pageDTO = new PageDTO<>();
        }
        PageHelper.startPage(pageDTO.getPageNum(), pageDTO.getPageSize());
        List<TzhBsOrgMenuVO> tzhBsOrgMenus = tzhBsOrgMenuMapper.queryAll(pageDTO.getQueryForm());
        return new ResultPage<>(tzhBsOrgMenus,tzhBsOrgMenus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultBean deleteById(String id) {
        tzhBsOrgMenuMapper.logicallyDelete(id);
        return ResultBean.ok();
    }
}
