package com.csci.susdev.service;

import cn.hutool.core.util.ObjectUtil;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.*;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.VehicleFuelUsageConverter;
import com.csci.susdev.qo.AttachmentListQO;
import com.csci.susdev.qo.SyncAmbientEnergyBillQO;
import com.csci.susdev.qo.VehicleFuelUsageQO;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.susdev.vo.*;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;
import static com.csci.susdev.service.ServiceHelper.validateMonth;

@Service
@LogMethod
public class VehicleFuelUsageService {

    @Resource
    private VehicleFuelUsageMapper vehicleFuelUsageMapper;
    @Resource
    private VehicleFuelUsageCustomMapper vehicleFuelUsageCustomMapper;
    @Resource
    private AmbientHeadMapper ambientHeadMapper;
    @Resource
    private AmbientService ambientService;
    @Resource
    private AttachmentService attachmentService;
    @Resource
    private MinioAttachmentMapper minioAttachmentMapper;
    @Resource
    private AttachmentMapper attachmentMapper;


    /**
     * 保存车辆油耗记录
     *
     * @param vehicleFuelUsageVO
     * @return
     */
    public String saveVehicleFuelUsage(VehicleFuelUsageVO vehicleFuelUsageVO) {
        validateMonth(vehicleFuelUsageVO.getMonthValue());
        checkExist(vehicleFuelUsageVO.getAmbientHeadId(), "环境绩效id不能为空");
        checkExist(vehicleFuelUsageVO.getFuelType(), "燃料类型不能为空");
        checkExist(vehicleFuelUsageVO.getVehicleType(), "车辆类型不能为空");
        checkExist(vehicleFuelUsageVO.getVehicleEmissionStandard(), "车辆排放标准不能为空");
        checkExist(vehicleFuelUsageVO.getVehicleLicense(), "车牌號碼不能为空");
        checkExist(vehicleFuelUsageVO.getFuelUseAmount(), "燃料使用量不能为空");
        checkExist(vehicleFuelUsageVO.getMonthValue(), "月份不能为空");
        checkExist(vehicleFuelUsageVO.getMileage(), "里程数不能为空");
        //checkExist(vehicleFuelUsageVO.getWeight(), "汽车重量不能为空");
        //checkExist(vehicleFuelUsageVO.getCylinderCapacity(), "汽车排量不能为空");
        //checkExist(vehicleFuelUsageVO.getRatedPower(), "汽车额定功率不能为空");

        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        workflowControlService.throwIfHasWorkflowControl(vehicleFuelUsageVO.getAmbientHeadId(), "本期环境绩效数据已经提交，不能进行新增或修改");

        if (StringUtils.isBlank(vehicleFuelUsageVO.getId())) {
            // 新增
            return doAdd(vehicleFuelUsageVO);
        } else {
            checkExist(vehicleFuelUsageVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(vehicleFuelUsageVO);
        }
    }

    /**
     * 复制指定的车辆油耗记录
     *
     * @param id
     * @return
     */
    public VehicleFuelUsage duplicateVehicleFuelUsage(String id) {
        VehicleFuelUsage vehicleFuelUsage = vehicleFuelUsageMapper.selectByPrimaryKey(id);
        checkExist(vehicleFuelUsage, "未找到对应的车辆油耗记录");

        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        workflowControlService.throwIfHasWorkflowControl(vehicleFuelUsage.getAmbientHeadId(), "本期环境绩效数据已经提交，不能进行复制操作");

        //由于前端点击复制返回后，还是有选中前端无法处理，因此将原来记录更新为新的id，解决前端复制后依然选中的问题
        vehicleFuelUsageCustomMapper.updateNewIdByOldId(vehicleFuelUsage.getId(), UUID.randomUUID().toString());

        vehicleFuelUsage.setId(null);
        vehicleFuelUsage.setCreationTime(null);
        vehicleFuelUsage.setCreateUserId(null);
        vehicleFuelUsage.setCreateUsername(null);
        vehicleFuelUsage.setLastUpdateVersion(0);
        vehicleFuelUsage.setLastUpdateTime(null);
        vehicleFuelUsage.setLastUpdateUserId(null);
        vehicleFuelUsage.setLastUpdateUsername(null);
        vehicleFuelUsageMapper.insert(vehicleFuelUsage);
        return vehicleFuelUsage;
    }

    /**
     *
     * 批量复制
     * @param ids
     * @return
     */
    public String batchDuplicateVehicleFuelUsage(List<String> ids) {
        ids.forEach(id -> {
            duplicateVehicleFuelUsage(id);
        });
        return "";
    }

    /**
     * 查询车辆油耗记录
     *
     * @return
     */
    public ResultPage<VehicleFuelUsageVO> listVehicleFuelUsage(VehicleFuelUsageQO vehicleFuelUsageQO) {
        checkExist(vehicleFuelUsageQO.getAmbientHeadId(), "环境绩效主表记录ID不能为空");
        VehicleFuelUsageExample example = new VehicleFuelUsageExample();
        VehicleFuelUsageExample.Criteria criteria = example.or().andAmbientHeadIdEqualTo(vehicleFuelUsageQO.getAmbientHeadId());

        if(vehicleFuelUsageQO.getFilter() != null) {
            if(vehicleFuelUsageQO.getFilter().get("monthValue") != null && vehicleFuelUsageQO.getFilter().get("monthValue").size() > 0) {
                criteria.andMonthValueIn(vehicleFuelUsageQO.getFilter().get("monthValue"));
            }
            if(vehicleFuelUsageQO.getFilter().get("vehicleEmissionStandard") != null && vehicleFuelUsageQO.getFilter().get("vehicleEmissionStandard").size() > 0) {
                criteria.andVehicleEmissionStandardIn(vehicleFuelUsageQO.getFilter().get("vehicleEmissionStandard"));
            }
            if(vehicleFuelUsageQO.getFilter().get("vehicleType") != null && vehicleFuelUsageQO.getFilter().get("vehicleType").size() > 0) {
                criteria.andVehicleTypeIn(vehicleFuelUsageQO.getFilter().get("vehicleType"));
            }
        }

        if (StringUtils.isBlank(vehicleFuelUsageQO.getSortName())) {
            example.setOrderByClause("creation_time desc");
        } else {
            String columnName = getColumnName(vehicleFuelUsageQO.getSortName());
            if (StringUtils.isBlank(columnName)) {
                throw new ServiceException("排序字段不正确");
            }
            if (StringUtils.equals("asc", vehicleFuelUsageQO.getSortOrder())) {
                example.setOrderByClause(columnName + " asc");
            } else {
                example.setOrderByClause(columnName + " desc");
            }
        }

        PageHelper.startPage(vehicleFuelUsageQO.getCurPage(), vehicleFuelUsageQO.getPageSize());
        List<VehicleFuelUsage> vehicleFuelUsages = vehicleFuelUsageMapper.selectByExample(example);
        return new ResultPage<>(vehicleFuelUsages, vehicleFuelUsages.stream().map(VehicleFuelUsageConverter::convert).collect(Collectors.toList()));
    }

    private String getColumnName(String sortName) {
        if (StringUtils.isBlank(sortName)) {
            return null;
        }
        switch (sortName) {
            case "fuelType":
                return "fuel_type";
            case "vehicleType":
                return "vehicle_type";
            case "vehicleEmissionStandard":
                return "vehicle_emission_standard";
            case "vehicleLicense":
                return "vehicle_license";
            case "fuelUseAmount":
                return "fuel_use_amount";
            case "monthValue":
                return "month_value";
            case "mileage":
                return "mileage";
            case "weight":
                return "weight";
            case "cylinderCapacity":
                return "cylinder_capacity";
            case "ratedPower":
                return "rated_power";
            default:
                return null;
        }
    }

    private String doAdd(VehicleFuelUsageVO vehicleFuelUsageVO) {
        VehicleFuelUsage vehicleFuelUsage = VehicleFuelUsageConverter.convert(vehicleFuelUsageVO);
        vehicleFuelUsageMapper.insertSelective(vehicleFuelUsage);
        return vehicleFuelUsage.getId();
    }

    private String doUpdate(VehicleFuelUsageVO vehicleFuelUsageVO) {
        VehicleFuelUsage vehicleFuelUsage = VehicleFuelUsageConverter.convert(vehicleFuelUsageVO);
        VehicleFuelUsageExample example = new VehicleFuelUsageExample();
        example.or().andIdEqualTo(vehicleFuelUsageVO.getId()).andLastUpdateVersionEqualTo(vehicleFuelUsageVO.getLastUpdateVersion());
        int updateCount = vehicleFuelUsageMapper.updateByExampleSelective(vehicleFuelUsage, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return vehicleFuelUsage.getId();
    }

    public VehicleFuelUsageVO getVehicleFuelUsageById(String id) {
        VehicleFuelUsage vehicleFuelUsage = vehicleFuelUsageMapper.selectByPrimaryKey(id);
        if (vehicleFuelUsage == null) {
            throw new ServiceException("数据不存在");
        }
        return VehicleFuelUsageConverter.convert(vehicleFuelUsage);
    }

    public void deleteVehicleFuelUsageById(String id) {
        checkExist(id, "id不能为空");
        vehicleFuelUsageMapper.deleteByPrimaryKey(id);
    }

    /**
     * 批量删除车辆用油记录
     * @param vehicleFuelUsageBatchDeleteVO
     */
    public String batchDeleteVehicleFuelUsageById(VehicleFuelUsageBatchDeleteVO vehicleFuelUsageBatchDeleteVO) {
        if(vehicleFuelUsageBatchDeleteVO == null || CollectionUtils.isEmpty(vehicleFuelUsageBatchDeleteVO.getDeleteIdList())){
            return "";
        }
        vehicleFuelUsageCustomMapper.batchDeleteByIdList(vehicleFuelUsageBatchDeleteVO.getDeleteIdList());
        return "";
    }

    public List<VehicleFuelUsage> selectByExample(VehicleFuelUsageExample example) {
        return vehicleFuelUsageMapper.selectByExample(example);
    }

    // 根据组织机构id及年份查询车辆油耗记录
    public List<VehicleFuelUsage> listVehicleFuelUsageByOrgIdAndYear(String orgId, Integer year) {
        // first, query head record by org id and year
        /*AmbientHeadExample headExample = new AmbientHeadExample();
        headExample.or().and

        VehicleFuelUsageExample example = new VehicleFuelUsageExample();
        example.or().andOrgIdEqualTo(orgId).andYearValueEqualTo(year);
        return vehicleFuelUsageMapper.selectByExample(example);*/
        throw new ServiceException("未实现");
    }

    /**
     * 批量保存车辆用油记录
     * <AUTHOR>
     * @date 2025/3/5
     * @param vehicleFuelUsageVOList
     * @return java.util.List<java.lang.String>
     */
    public List<String> saveVehicleFuelUsageList(List<VehicleFuelUsageVO> vehicleFuelUsageVOList) {
        if (CollectionUtils.isEmpty(vehicleFuelUsageVOList)) {
            throw new ServiceException("数据为空！");
        }
        if(vehicleFuelUsageVOList.size() > 5000) {
            throw new ServiceException("每次只能新增5000條數據，請分開兩個檔案處理");
        }
        VehicleFuelUsageVO vehicleFuelUsage = vehicleFuelUsageVOList.get(0);
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        workflowControlService.throwIfHasWorkflowControl(vehicleFuelUsage.getAmbientHeadId(), "本期数据已经提交，不能进行导入");

        AmbientHead ambientHead = ambientHeadMapper.selectByPrimaryKey(vehicleFuelUsage.getAmbientHeadId());
        if (ObjectUtil.isNull(ambientHead)) {
            throw new ServiceException("环境绩效主表信息不存在！");
        }
        List<String> ids = new ArrayList<>();
        for(int i = 0; i < vehicleFuelUsageVOList.size(); i++) {
            VehicleFuelUsageVO vehicleFuelUsageVO = vehicleFuelUsageVOList.get(i);
            validateMonth(vehicleFuelUsageVO.getMonthValue());
            checkExist(vehicleFuelUsageVO.getAmbientHeadId(), "[第" + (i + 1) + "行]" +"环境绩效id不能为空");
            checkExist(vehicleFuelUsageVO.getFuelType(), "[第" + (i + 1) + "行]" +"燃料类型不能为空");
            checkExist(vehicleFuelUsageVO.getVehicleType(), "[第" + (i + 1) + "行]" +"车辆类型不能为空");
            checkExist(vehicleFuelUsageVO.getVehicleEmissionStandard(), "[第" + (i + 1) + "行]" +"车辆排放标准不能为空");
            checkExist(vehicleFuelUsageVO.getVehicleLicense(), "[第" + (i + 1) + "行]" +"车牌號碼不能为空");
            checkExist(vehicleFuelUsageVO.getFuelUseAmount(), "[第" + (i + 1) + "行]" +"燃料使用量不能为空");
            checkExist(vehicleFuelUsageVO.getMonthValue(), "[第" + (i + 1) + "行]" +"月份不能为空");
            checkExist(vehicleFuelUsageVO.getMileage(), "[第" + (i + 1) + "行]" +"里程数不能为空");
            if (vehicleFuelUsageVO.getMonthValue() > ambientHead.getMonth()) {
                throw new ServiceException("[第" + (i + 1) + "行]" +"月份不能大于本期环境绩效填报的月份！");
            }
            if (StringUtils.isBlank(vehicleFuelUsageVO.getId())) {
                // 新增
                ids.add(doAdd(vehicleFuelUsageVO));
            } else {
                checkExist(vehicleFuelUsageVO.getLastUpdateVersion(), "更新时版本号不能为空");
                // 修改
                ids.add(doUpdate(vehicleFuelUsageVO));
            }
        }
        return ids;
    }

    /**
     * 同步车辆用油
     * <AUTHOR>
     * @date 2025/3/5 11:11
     * @param syncAmbientEnergyBillQO
     */
    @Transactional(rollbackFor = Exception.class)
    public void synchronizationData(SyncAmbientEnergyBillQO syncAmbientEnergyBillQO) {
        // 1、先查询当前的环境绩效数据
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        workflowControlService.throwIfHasWorkflowControl(syncAmbientEnergyBillQO.getHeadId(), "本期数据已经提交，不能进行同步");
        // 2、根据组织架构id、年份、月份查询环境绩效
        AmbientHeadVO syncAmbientHeadVO = ambientService.get(syncAmbientEnergyBillQO.getOrganizationId(), syncAmbientEnergyBillQO.getYear(), syncAmbientEnergyBillQO.getMonth());
        // 3、查出要同步的车辆用油
        VehicleFuelUsageExample example = new VehicleFuelUsageExample();
        VehicleFuelUsageExample.Criteria criteria = example.or().andAmbientHeadIdEqualTo(syncAmbientHeadVO.getId());
        example.setOrderByClause("creation_time asc");
        List<VehicleFuelUsage> syncVehicleFuelUsages = vehicleFuelUsageMapper.selectByExample(example);
        UserInfo userInfo = ContextUtils.getCurrentUser();
        if (!CollectionUtils.isEmpty(syncVehicleFuelUsages)) {
            AttachmentListQO qo = new AttachmentListQO();
            qo.setSection("車輛用油");
            for (VehicleFuelUsage oldUsage : syncVehicleFuelUsages) {
                qo.setRefId(oldUsage.getAmbientHeadId());
                qo.setCategory(oldUsage.getId());
                // 新增新的车辆用油
                VehicleFuelUsage newVehicleFuelUsage = new VehicleFuelUsage();
                BeanUtils.copyProperties(oldUsage, newVehicleFuelUsage);
                newVehicleFuelUsage.setId(null);
                newVehicleFuelUsage.setAmbientHeadId(syncAmbientEnergyBillQO.getHeadId());
                vehicleFuelUsageMapper.insertSelective(newVehicleFuelUsage);
                // 查询是否有附件
                List<AttachmentVO> attachmentVOS = attachmentService.newList(qo);
                if (!CollectionUtils.isEmpty(attachmentVOS)) {
                    // 有附件复制车辆用油附件
                    for (AttachmentVO oldAttachment : attachmentVOS) {
                        // 生成模型
                        if (StringUtils.isNotEmpty(oldAttachment.getMinioFileName())) {
                            MinioAttachment x = new MinioAttachment();
                            BeanUtils.copyProperties(oldAttachment, x);
                            x.setId(null);
                            x.setRefId(newVehicleFuelUsage.getAmbientHeadId());
                            x.setCategory(newVehicleFuelUsage.getId());
                            x.setCreateUserId(userInfo.getId());
                            x.setCreateUsername(userInfo.getUsername());
                            x.setCreationTime(LocalDateTime.now());
                            minioAttachmentMapper.insert(x);
                        }else {
                            Attachment x = new Attachment();
                            BeanUtils.copyProperties(oldAttachment, x);
                            Attachment primaryKey = attachmentMapper.selectByPrimaryKey(oldAttachment.getId());
                            x.setId(null);
                            x.setRefId(newVehicleFuelUsage.getAmbientHeadId());
                            x.setCategory(newVehicleFuelUsage.getId());
                            x.setData(primaryKey.getData());
                            x.setCreateUserId(userInfo.getId());
                            x.setCreateUsername(userInfo.getUsername());
                            x.setCreationTime(LocalDateTime.now());
                            attachmentMapper.insert(x);
                        }
                    }
                }
            }
        }
    }
}
