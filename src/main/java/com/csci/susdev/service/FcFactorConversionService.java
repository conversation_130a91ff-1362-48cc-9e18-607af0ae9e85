package com.csci.susdev.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.FcFactorConversionMapper;
import com.csci.susdev.model.FcFactorConversion;
import com.csci.susdev.model.FcFactorConversionExample;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.modelcovt.FcFactorConversionConverter;
import com.csci.susdev.qo.FcFactorConversionQO;
import com.csci.susdev.util.ConvertBeanUtils;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.vo.ExcelFcFactorConversionVO;
import com.csci.susdev.vo.ExcleFcCarbonFactorHkFactorSelectionVO;
import com.csci.susdev.vo.FcFactorConversionVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class FcFactorConversionService {

    @Resource
    private FcFactorConversionMapper fcFactorConversionMapper;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FcFactorConversion record) {
        return fcFactorConversionMapper.insertSelective(record);
    }

    public FcFactorConversion selectByPrimaryKey(String id) {
        return fcFactorConversionMapper.selectByPrimaryKey(id);
    }

    public FcFactorConversionVO getFcFactorConversion(String id) {
        checkExist(id, "id不能为空");
        FcFactorConversion fcFactorConversion = selectByPrimaryKey(id);
        checkExist(fcFactorConversion, "未找到对应的记录");
        if(fcFactorConversion.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return FcFactorConversionConverter.convertToVO(fcFactorConversion);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveFcFactorConversion(FcFactorConversionVO fcFactorConversionVO) {
        if (StringUtils.isBlank(fcFactorConversionVO.getId())) {
            // 新增
            return doAdd(fcFactorConversionVO);
        } else {
            checkExist(fcFactorConversionVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(fcFactorConversionVO);
        }

    }

    public ResultPage<FcFactorConversionVO> listFcFactorConversion(FcFactorConversionQO fcFactorConversionQO) {
        FcFactorConversionExample example = new FcFactorConversionExample();

        SimpTradUtil simpTradUtil = new SimpTradUtil();
        String greenhouseGasTypesTc = simpTradUtil.convert2Trad(fcFactorConversionQO.getGreenhouseGasTypes());
        String greenhouseGasTypesSc = simpTradUtil.convert2Simp(fcFactorConversionQO.getGreenhouseGasTypes());

        if(StringUtils.isNotBlank(fcFactorConversionQO.getGreenhouseGasTypes())) {
            example.or().andGreenhouseGasTypesLike("%" + greenhouseGasTypesTc + "%").andIsDeletedEqualTo(false);
            example.or().andGreenhouseGasTypesLike("%" + greenhouseGasTypesSc + "%").andIsDeletedEqualTo(false);
        } else {
            example.or().andIsDeletedEqualTo(false);
        }

        if (StringUtils.isNotBlank(fcFactorConversionQO.getOrderBy())) {
            example.setOrderByClause(NamingConventionUtils.snakeCaseToCamelCase(fcFactorConversionQO.getOrderBy()));
        }else {
            example.setOrderByClause("creation_time desc");
        }

        PageHelper.startPage(fcFactorConversionQO.getCurPage(), fcFactorConversionQO.getPageSize());
        List<FcFactorConversion> fcFactorConversions = fcFactorConversionMapper.selectByExample(example);
        return new ResultPage<>(fcFactorConversions, fcFactorConversions.stream().map(FcFactorConversionConverter::convertToVO).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFcFactorConversion(String id) {
        FcFactorConversion record = selectByPrimaryKey(id);
        record.setIsDeleted(true);
        fcFactorConversionMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(FcFactorConversionVO fcFactorConversionVO) {
        FcFactorConversion fcFactorConversion = FcFactorConversionConverter.convertToModel(fcFactorConversionVO);
        fcFactorConversionMapper.insertSelective(fcFactorConversion);
        return fcFactorConversion.getId();
    }

    private String doUpdate(FcFactorConversionVO fcFactorConversionVO) {
        FcFactorConversion originalRecord = selectByPrimaryKey(fcFactorConversionVO.getId());
        FcFactorConversion fcFactorConversion = FcFactorConversionConverter.convertToModelWithBase(fcFactorConversionVO, originalRecord);

        FcFactorConversionExample example = new FcFactorConversionExample();
        example.or().andIdEqualTo(fcFactorConversionVO.getId()).andLastUpdateVersionEqualTo(fcFactorConversionVO.getLastUpdateVersion());
        int updateCount = fcFactorConversionMapper.updateByExample(fcFactorConversion, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return fcFactorConversion.getId();
    }

    public List<FcFactorConversion> selectByExample(FcFactorConversionExample example) {
        return fcFactorConversionMapper.selectByExample(example);
    }

    public List<String> saveFcFactorConversionList(List<FcFactorConversionVO> fcFactorConversionVOList) {
        if (CollectionUtils.isEmpty(fcFactorConversionVOList)) {
            throw new ServiceException("数据不能为空！");
        }
        if(fcFactorConversionVOList.size() > 5000) {
            throw new ServiceException("每次只能新增5000條數據，請分開兩個檔案處理");
        }
        List<String> ids = new ArrayList<>();
        for(int i = 0; i < fcFactorConversionVOList.size(); i++) {
            FcFactorConversionVO fcFactorConversionVO = fcFactorConversionVOList.get(i);
            checkExist(fcFactorConversionVO.getGreenhouseGasTypes(), "[第" + (i + 1) + "行]" + "温室气体类型不能为空");
            checkExist(fcFactorConversionVO.getGwpValue(), "[第" + (i + 1) + "行]" + "GWP数值不能为空");
            checkExist(fcFactorConversionVO.getUnit(), "[第" + (i + 1) + "行]" + "单位名称不能为空");
            checkExist(fcFactorConversionVO.getDataSource(), "[第" + (i + 1) + "行]" + "数据来源不能为空");
            checkExist(fcFactorConversionVO.getVersion(), "[第" + (i + 1) + "行]" + "版本不能为空");

            if (StringUtils.isBlank(fcFactorConversionVO.getId())) {
                // 新增
                ids.add(doAdd(fcFactorConversionVO));
            } else {
                checkExist(fcFactorConversionVO.getLastUpdateVersion(), "[第" + (i + 1) + "行]" + "更新时版本号不能为空");
                // 修改
                ids.add(doUpdate(fcFactorConversionVO));
            }

        }
        return ids;
    }

    public byte[] exportFcFactorConversion(FcFactorConversionQO fcFactorConversionQO, HttpHeaders headers) {
        List<FcFactorConversionVO> list = this.listFcFactorConversion(fcFactorConversionQO).getList();
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("无数据可导出！");
        }
        String filename = new StringBuilder()
                .append("因子换算")
                .append("_")
                .append(DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()))
                .append(".xlsx").toString();
        String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8);
        headers.setContentDispositionFormData("attachment", encodedFilename);

        try (ByteArrayOutputStream out = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcel.write(out).build()) {
            List<ExcelFcFactorConversionVO> excelFcFactorConversionVOS = new ArrayList<>();
            for (FcFactorConversionVO fcFactorConversionVO : list) {
                ExcelFcFactorConversionVO excelFcFactorConversionVO = ConvertBeanUtils.convert(fcFactorConversionVO, ExcelFcFactorConversionVO.class);
                excelFcFactorConversionVOS.add(excelFcFactorConversionVO);
            }
            WriteSheet hkSheet = EasyExcel.writerSheet("因子换算").head(ExcelFcFactorConversionVO.class).build();
            excelWriter.write(excelFcFactorConversionVOS, hkSheet);

            excelWriter.finish();
            return out.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
