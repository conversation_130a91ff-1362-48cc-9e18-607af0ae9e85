package com.csci.susdev.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.AccountingManagementCustomMapper;
import com.csci.susdev.mapper.AccountingManagementMapper;
import com.csci.susdev.mapper.ProtocolConfigurationCustomMapper;
import com.csci.susdev.model.AccountingManagement;
import com.csci.susdev.model.AccountingManagementExample;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.modelcovt.AccountingManagementConverter;
import com.csci.susdev.qo.AccountingManagementQO;
import com.csci.susdev.qo.ProtocolConfigurationQO;
import com.csci.susdev.util.ConvertBeanUtils;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.vo.*;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class AccountingManagementService {

    @Resource
    private AccountingManagementMapper accountingManagementMapper;
    @Resource
    private AccountingManagementCustomMapper accountingManagementCustomMapper;
    @Resource
    private ProtocolConfigurationService protocolConfigurationService;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(AccountingManagement record) {
        return accountingManagementMapper.insertSelective(record);
    }

    public AccountingManagement selectByPrimaryKey(String id) {
        return accountingManagementMapper.selectByPrimaryKey(id);
    }

    public AccountingManagementVO getAccountingManagement(String id) {
        checkExist(id, "id不能为空");
        AccountingManagement accountingManagement = selectByPrimaryKey(id);
        checkExist(accountingManagement, "未找到对应的记录");
        if(accountingManagement.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return AccountingManagementConverter.convertToVO(accountingManagement);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveAccountingManagement(AccountingManagementVO accountingManagementVO) {
        if (StringUtils.isBlank(accountingManagementVO.getId())) {
            // 新增
            return doAdd(accountingManagementVO);
        } else {
            checkExist(accountingManagementVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(accountingManagementVO);
        }

    }

    public ResultPage<AccountingManagementVO> listAccountingManagement(AccountingManagementQO accountingManagementQO) {
        AccountingManagementExample example = new AccountingManagementExample();

        SimpTradUtil simpTradUtil = new SimpTradUtil();
        String chineseNameTc = simpTradUtil.convert2Trad(accountingManagementQO.getChineseName());
        String chineseNameSc = simpTradUtil.convert2Simp(accountingManagementQO.getChineseName());

        if(StringUtils.isNotBlank(accountingManagementQO.getChineseName())) {
            example.or().andChineseNameLike("%" + chineseNameTc + "%").andIsDeletedEqualTo(false);
            example.or().andChineseNameLike("%" + chineseNameSc + "%").andIsDeletedEqualTo(false);
        } else {
            example.or().andIsDeletedEqualTo(false);
        }

        if (StringUtils.isNotBlank(accountingManagementQO.getOrderBy())) {
            example.setOrderByClause(NamingConventionUtils.snakeCaseToCamelCase(accountingManagementQO.getOrderBy()));
        }else {
            example.setOrderByClause("creation_time desc");
        }

        PageHelper.startPage(accountingManagementQO.getCurPage(), accountingManagementQO.getPageSize());
        List<AccountingManagement> accountingManagements = accountingManagementMapper.selectByExample(example);
        return new ResultPage<>(accountingManagements, accountingManagements.stream().map(AccountingManagementConverter::convertToVO).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteAccountingManagement(String id) {
        AccountingManagement record = selectByPrimaryKey(id);
        record.setIsDeleted(true);
        accountingManagementMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(AccountingManagementVO accountingManagementVO) {
        AccountingManagement accountingManagement = AccountingManagementConverter.convertToModel(accountingManagementVO);
        accountingManagementMapper.insertSelective(accountingManagement);
        return accountingManagement.getId();
    }

    private String doUpdate(AccountingManagementVO accountingManagementVO) {
        AccountingManagement originalRecord = selectByPrimaryKey(accountingManagementVO.getId());
        AccountingManagement accountingManagement = AccountingManagementConverter.convertToModelWithBase(accountingManagementVO, originalRecord);

        AccountingManagementExample example = new AccountingManagementExample();
        example.or().andIdEqualTo(accountingManagementVO.getId()).andLastUpdateVersionEqualTo(accountingManagementVO.getLastUpdateVersion());
        int updateCount = accountingManagementMapper.updateByExample(accountingManagement, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return accountingManagement.getId();
    }

    public List<AccountingManagement> selectByExample(AccountingManagementExample example) {
        return accountingManagementMapper.selectByExample(example);
    }

    @Transactional(rollbackFor = Exception.class)
    public String batchCalculation(AccountingManagementCalculationVO accountingManagementCalculationVO) {
        List<String> idList = accountingManagementCalculationVO.getCalculationIdList();
        if (CollectionUtils.isEmpty(idList)) {
            throw new ServiceException("请选择数据！");
        }
        List<AccountingManagement> accountingManagements = accountingManagementCustomMapper.listCalculation(idList);
        if (CollectionUtils.isNotEmpty(accountingManagements)) {
            for (AccountingManagement accountingManagement : accountingManagements) {
                AccountingManagementExample example = new AccountingManagementExample();
                example.or().andIdEqualTo(accountingManagement.getId()).andLastUpdateVersionEqualTo(accountingManagement.getLastUpdateVersion());
                int updateCount = accountingManagementMapper.updateByExample(accountingManagement, example);
                if (updateCount == 0) {
                    throw new ServiceException("计算失败，可能是数据已被修改，请刷新后重试");
                }
            }
        }
        return "";
    }

    public List<String> saveAccountingManagementList(List<AccountingManagementVO> accountingManagementVOList) {
        if (CollectionUtils.isEmpty(accountingManagementVOList)) {
            throw new ServiceException("数据不能为空！");
        }
        if(accountingManagementVOList.size() > 5000) {
            throw new ServiceException("每次只能新增5000條數據，請分開兩個檔案處理");
        }
        ProtocolConfigurationQO protocolConfigurationQO = new ProtocolConfigurationQO();
        protocolConfigurationQO.setCurPage(1);
        protocolConfigurationQO.setPageSize(99999);
        List<ProtocolConfigurationVO> configurationVOS = protocolConfigurationService.listProtocolConfiguration(protocolConfigurationQO).getList();
        if (CollectionUtils.isEmpty(configurationVOS)) {
            throw new ServiceException("协议配置数据为空，请先添加协议配置数据！");
        }
        List<AccountingManagementImportVO> accountingManagementImportVOS = new ArrayList<>();
        for (ProtocolConfigurationVO configurationVO : configurationVOS) {
            AccountingManagementImportVO managementImportVO = new AccountingManagementImportVO();
            managementImportVO.setProtocolConfigurationId(configurationVO.getId());
            BeanUtils.copyProperties(configurationVO.getFcFactorVO(), managementImportVO);
            accountingManagementImportVOS.add(managementImportVO);
        }
        Map<String, List<AccountingManagementImportVO>> managementImportMap = accountingManagementImportVOS.stream().collect(Collectors.groupingBy(AccountingManagementImportVO::getChineseName));


        List<String> ids = new ArrayList<>();
        for(int i = 0; i < accountingManagementVOList.size(); i++) {
            AccountingManagementVO accountingManagementVO = accountingManagementVOList.get(i);
            if (!managementImportMap.containsKey(accountingManagementVO.getChineseName())) {
                throw new ServiceException("协议配置数据为空，请先添加协议配置数据！");
            }
            AccountingManagementImportVO managementImportVO = managementImportMap.get(accountingManagementVO.getChineseName()).get(0);
            accountingManagementVO.setProtocolConfigurationId(managementImportVO.getProtocolConfigurationId());
            checkExist(accountingManagementVO.getChineseName(), "[第" + (i + 1) + "行]" + "物料名称不能为空");
            checkExist(accountingManagementVO.getCountOne(), "[第" + (i + 1) + "行]" + "用量不能为空");
            checkExist(accountingManagementVO.getCountTwo(), "[第" + (i + 1) + "行]" + "因子数据不能为空");
            checkExist(accountingManagementVO.getComputeSymbol(), "[第" + (i + 1) + "行]" + "计算符号不能为空");

            if (StringUtils.isBlank(accountingManagementVO.getId())) {
                // 新增
                ids.add(doAdd(accountingManagementVO));
            } else {
                checkExist(accountingManagementVO.getLastUpdateVersion(), "[第" + (i + 1) + "行]" + "更新时版本号不能为空");
                // 修改
                ids.add(doUpdate(accountingManagementVO));
            }
        }
        return ids;
    }


    public byte[] exportAccountingManagement(AccountingManagementQO accountingManagementQO, HttpHeaders headers) {
        List<AccountingManagementVO> list = this.listAccountingManagement(accountingManagementQO).getList();
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("无数据可导出！");
        }
        String filename = new StringBuilder()
                .append("核算管理")
                .append("_")
                .append(DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()))
                .append(".xlsx").toString();
        String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8);
        headers.setContentDispositionFormData("attachment", encodedFilename);

        try (ByteArrayOutputStream out = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcel.write(out).build()) {
            List<ExcelAccountingManagementVO> excelAccountingManagementVOS = new ArrayList<>();
            for (AccountingManagementVO accountingManagementVO : list) {
                ExcelAccountingManagementVO excelAccountingManagementVO = ConvertBeanUtils.convert(accountingManagementVO, ExcelAccountingManagementVO.class);
                excelAccountingManagementVOS.add(excelAccountingManagementVO);
            }
            WriteSheet accountingSheet = EasyExcel.writerSheet("核算管理").head(ExcelAccountingManagementVO.class).build();
            excelWriter.write(excelAccountingManagementVOS, accountingSheet);

            excelWriter.finish();
            return out.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
