package com.csci.susdev.service;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.SocialPerformanceDetailMapper;
import com.csci.susdev.model.SocialPerformanceDetail;
import com.csci.susdev.model.SocialPerformanceDetailExample;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@LogMethod
public class SocialPerformanceDetailService {

    @Resource
    private SocialPerformanceDetailMapper socialPerformanceDetailMapper;

    public List<SocialPerformanceDetail> selectByExample(SocialPerformanceDetailExample example) {
        return socialPerformanceDetailMapper.selectByExample(example);
    }

    public int updateByPrimaryKeySelective(SocialPerformanceDetail record) {
        return socialPerformanceDetailMapper.updateByPrimaryKeySelective(record);
    }
}
