package com.csci.susdev.service;

import com.csci.cohl.epidemic.utils.JsonUtil;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.facade.HiAgentFacade;
import com.csci.susdev.mapper.AiAgentConversationMapper;
import com.csci.susdev.mapper.AiAgentOpenQuestionCustomMapper;
import com.csci.susdev.model.AiAgentConversation;
import com.csci.susdev.model.AiAgentConversationExample;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.util.StringUtil;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.vo.AiAgentConversationVO;
import com.csci.susdev.vo.AiAgentOpenQuestionVO;
import com.csci.susdev.vo.HiAgentParamVO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@LogMethod
public class AiAgentConversationService {

	@Autowired
	private AiAgentConversationMapper aiAgentConversationMapper;
	@Autowired
	private AiAgentOpenQuestionCustomMapper aiAgentOpenQuestionCustomMapper;
	@Resource
	private HiAgentFacade hiAgentFacade;

	@Transactional(rollbackFor = Exception.class)
	public String saveConversation(String conversation) {
		try {
			if (StringUtil.isNotEmpty(conversation) && conversation.contains("AppConversationID")) {
				ObjectMapper objectMapper = JsonUtil.getObjectMapper();
				Map<String, Object> map = null;
				map = objectMapper.readValue(conversation, new TypeReference<Map<String, Object>>(){});
				AiAgentConversation aiAgentConversation = new AiAgentConversation();
				Map<String, Object> conversationMap = (Map<String, Object>) map.get("Conversation");
				aiAgentConversation.setAppConversationId(conversationMap.get("AppConversationID").toString());
				aiAgentConversation.setConversationName(conversationMap.get("ConversationName").toString());
				aiAgentConversation.setUsername(ContextUtils.getCurrentUser().getUsername());
				aiAgentConversation.setIsDeleted(false);
				aiAgentConversation.setIsManualUpdate(false);
				aiAgentConversationMapper.insert(aiAgentConversation);
			}
		} catch (Exception e) {
			throw new ServiceException(e.getMessage());
		}
		return "";
	}

	public List<AiAgentConversation> getConversationList(HiAgentParamVO vo) {


		AiAgentConversationExample example = new AiAgentConversationExample();

		Map<String, Object> data = vo.getData();
		String username = ContextUtils.getCurrentUser().getUsername();
		if (data.containsKey("keyword")) {
			String keyword = StringUtil.valueOf(data.get("keyword"));
			if (StringUtil.isNotEmpty(keyword)) {
				SimpTradUtil simpTradUtil = new SimpTradUtil();
				String conversationNameTc = simpTradUtil.convert2Trad(keyword);
				String conversationNameSc = simpTradUtil.convert2Simp(keyword);
				example.or().andConversationNameLike("%" + conversationNameTc + "%").andUsernameEqualTo(username).andIsDeletedEqualTo(false);
				example.or().andConversationNameLike("%" + conversationNameSc + "%").andUsernameEqualTo(username).andIsDeletedEqualTo(false);
			} else {
				example.or().andUsernameEqualTo(username).andIsDeletedEqualTo(false);
			}
			example.setOrderByClause("last_update_time desc");
		}else {
			example.or().andUsernameEqualTo(username).andIsDeletedEqualTo(false);
			example.setOrderByClause("last_update_time desc");
		}
		return aiAgentConversationMapper.selectByExample(example);
	}

	@Transactional(rollbackFor = Exception.class)
	public String updateConversationName(AiAgentConversationVO vo) {
		if (StringUtil.isEmpty(vo.getConversationName())) {
			throw new ServiceException("名称不能为空！");
		}
		AiAgentConversationExample example = new AiAgentConversationExample();
		example.or().andUsernameEqualTo(vo.getUsername()).andAppConversationIdEqualTo(vo.getAppConversationId()).andIsDeletedEqualTo(false);
		List<AiAgentConversation> agentConversations = aiAgentConversationMapper.selectByExample(example);
		if (CollectionUtils.isNotEmpty(agentConversations)) {
			AiAgentConversation aiAgentConversation = agentConversations.get(0);
			if (!BooleanUtils.isTrue(vo.getManualUpdateFlag()) && aiAgentConversation.getIsManualUpdate()) {
				return null;
			}
			aiAgentConversation.setConversationName(vo.getConversationName());
			if (BooleanUtils.isTrue(vo.getManualUpdateFlag())) {
				aiAgentConversation.setIsManualUpdate(vo.getManualUpdateFlag());
			}
			aiAgentConversationMapper.updateByPrimaryKey(aiAgentConversation);
			// 更新HiAgent上的会话名称
			HiAgentParamVO hiAgentParamVO = new HiAgentParamVO();
			Map<String, Object> data = new HashMap<>();
			data.put("AppConversationId", vo.getAppConversationId());
			data.put("ConversationName", vo.getConversationName());
			hiAgentParamVO.setData(data);
			hiAgentFacade.updateConversation(hiAgentParamVO);
		}
		return null;
	}

	@Transactional(rollbackFor = Exception.class)
	public String batchRemoveConversation(AiAgentConversationVO vo) {
		AiAgentConversationExample example = new AiAgentConversationExample();
		example.or().andUsernameEqualTo(vo.getUsername()).
				andAppConversationIdIn(vo.getAppConversationIdList()).
				andIsDeletedEqualTo(false);
		List<AiAgentConversation> agentConversations = aiAgentConversationMapper.selectByExample(example);
		if (CollectionUtils.isNotEmpty(agentConversations)) {
			for (AiAgentConversation aiAgentConversation : agentConversations) {
				aiAgentConversation.setIsDeleted(true);
				aiAgentConversationMapper.updateByPrimaryKey(aiAgentConversation);
			}
		}
		return null;
	}

	public List<AiAgentOpenQuestionVO> queryOpenQuestion() {
		return aiAgentOpenQuestionCustomMapper.list();
	}
}
