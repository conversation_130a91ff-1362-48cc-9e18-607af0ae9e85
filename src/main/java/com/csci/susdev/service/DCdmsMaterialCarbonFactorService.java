package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;

import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.mapper.*;
import com.csci.tzh.model.*;
import com.csci.tzh.model.DCdmsMaterialCarbonFactorExample.Criteria;
import com.csci.susdev.model.ResultPage;
import com.csci.tzh.qo.*;
import com.csci.tzh.vo.*;
import com.github.pagehelper.PageHelper;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class DCdmsMaterialCarbonFactorService {

	@Autowired
	private DCdmsMaterialCarbonFactorMapper mapper;

	@Autowired
	private DCdmsMaterialCarbonFactorCustomMapper customMapper;

	public List<DCdmsMaterialCarbonFactor> selectByExample(DCdmsMaterialCarbonFactorExample example) {
		return mapper.selectByExample(example);
	}

	public ResultPage<DCdmsMaterialCarbonFactorVO> list(SiteNameProtocolPageableQO qo) {
		PageHelper.startPage(qo.getCurPage(), qo.getPageSize(), "MCF.MaterialCode");
		List<DCdmsMaterialCarbonFactorVO> lst = customMapper.list(qo.getSiteName(), qo.getProtocol());
		ResultPage<DCdmsMaterialCarbonFactorVO> resultPage = new ResultPage<>(lst, true);
		return resultPage;
	}

	@Transactional(rollbackFor = Exception.class)
	public DCdmsMaterialCarbonFactor save(DCdmsMaterialCarbonFactor newModel) {
		UserInfo currentUser = ContextUtils.getCurrentUser();


		/*
		if(checkExist(newModel)) {
			throw new ServiceException("數據已存在，請更新舊數據。");
		}
		 */

		if(newModel.getIsdeleted() == false) {
			if(StringUtils.isNotBlank(newModel.getId())) {
				// 備份已刪除數據
				DCdmsMaterialCarbonFactorExample originalExample = new DCdmsMaterialCarbonFactorExample();
				DCdmsMaterialCarbonFactorExample.Criteria originalCriteria = originalExample.or();
				originalCriteria.andIdEqualTo(newModel.getId());
				List<DCdmsMaterialCarbonFactor> lstOriginalModel = mapper.selectByExample(originalExample);
				if(lstOriginalModel.size() > 0) {
					DCdmsMaterialCarbonFactor originalModel = lstOriginalModel.get(0);
					originalModel.setId(UUID.randomUUID().toString());
					originalModel.setIsdeleted(true);
					originalModel.setDeletedby(currentUser.getUsername());
					originalModel.setDeletedtime(LocalDateTime.now());
					mapper.insertSelective(originalModel);
				}

				// 新增數據(保留舊ID)
				DCdmsMaterialCarbonFactorExample newExample = new DCdmsMaterialCarbonFactorExample();
				DCdmsMaterialCarbonFactorExample.Criteria newCriteria = newExample.or();
				newCriteria.andIdEqualTo(newModel.getId());
				newCriteria.andIsdeletedEqualTo(false);
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				newModel.setDeletedby(null);
				newModel.setDeletedtime(null);
				mapper.deleteByExample(newExample);
				mapper.insertSelective(newModel);
			} else {
				// 新增數據(新建ID)
				newModel.setId(UUID.randomUUID().toString());
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				mapper.insertSelective(newModel);
			}
		} else {
			// 刪除數據
			DCdmsMaterialCarbonFactorExample example = new DCdmsMaterialCarbonFactorExample();
			DCdmsMaterialCarbonFactorExample.Criteria criteria = example.or();
			criteria.andIdEqualTo(newModel.getId());
			newModel.setIsdeleted(true);
			newModel.setDeletedby(currentUser.getUsername());
			newModel.setDeletedtime(LocalDateTime.now());
			mapper.updateByExampleSelective(newModel, example);
		}

		return newModel;
	}

	public boolean checkExist(DCdmsMaterialCarbonFactor model) {
		if(model.getIsdeleted() == false) {
			DCdmsMaterialCarbonFactorExample example = new DCdmsMaterialCarbonFactorExample();
			Criteria criteria = example.or();
			if(model.getId() != null) {
				criteria.andIdNotEqualTo(model.getId());
			}
			criteria.andSitenameEqualTo(model.getSitename());
			criteria.andMaterialcodeEqualTo(model.getMaterialcode());
			if(model.getMaterialattribute() != null) {
				criteria.andMaterialattributeEqualTo(model.getMaterialattribute());
			}
			if(model.getMaterialtype() != null) {
				criteria.andMaterialtypeEqualTo(model.getMaterialtype());
			}
			criteria.andIsdeletedEqualTo(false);
			return this.selectByExample(example).size() > 0;
		}
		else {
			return false;
		}
	}
}
