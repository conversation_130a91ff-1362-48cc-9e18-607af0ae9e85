package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.NamingConventionEnum;
import com.csci.susdev.mapper.BDictCustomMapper;
import com.csci.susdev.mapper.BDictMapper;
import com.csci.susdev.model.BDict;
import com.csci.susdev.model.BDictExample;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.modelcovt.BDictConverter;
import com.csci.susdev.qo.BDictQO;
import com.csci.susdev.util.MybatisHelper;
import com.csci.susdev.vo.BDictVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class BDictService {

    @Resource
    private BDictMapper bDictMapper;
    @Resource
    private BDictCustomMapper bDictCustomMapper;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(BDict record) {
        return bDictMapper.insertSelective(record);
    }

    public BDict selectByPrimaryKey(String id) {
        return bDictMapper.selectByPrimaryKey(id);
    }

    public BDictVO getBDict(String id) {
        checkExist(id, "id不能为空");
        BDict bDict = selectByPrimaryKey(id);
        checkExist(bDict, "未找到对应的记录");
        if(bDict.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return BDictConverter.convertToVO(bDict);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveBDict(BDictVO bDictVO) {
        checkExist(bDictVO.getCode(), "编码不能为空");
        checkExist(bDictVO.getName(), "名称不能为空");
        checkExist(bDictVO.getValue(), "值不能为空");
        checkExist(bDictVO.getDictType(), "字典类型不能为空");
        if (StringUtils.isBlank(bDictVO.getId())) {
            // 新增
            return doAdd(bDictVO);
        } else {
            checkExist(bDictVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(bDictVO);
        }

    }

    public ResultPage<BDictVO> listBDict(BDictQO bDictQO) {
        if (StringUtils.isNotBlank(bDictQO.getOrderBy())) {
            List<String> fieldNames = Arrays.stream(BDictVO.class.getDeclaredFields()).map(field -> field.getName()).collect(Collectors.toList());
            if(MybatisHelper.checkSqlInjectionForOrderBy(bDictQO.getOrderBy(), fieldNames, NamingConventionEnum.CAMO)) {
                throw new ServiceException("orderBy 有SQL注入行為");
            }
        }
        PageHelper.startPage(bDictQO.getCurPage(), bDictQO.getPageSize());
        List<BDictVO> bDictVOList = bDictCustomMapper.lisBDict(bDictQO);
        return new ResultPage<>(bDictVOList, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteBDict(String id) {
        BDict record = selectByPrimaryKey(id);
        record.setIsDeleted(true);
        bDictMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(BDictVO bDictVO) {
        BDict bDict = BDictConverter.convertToModel(bDictVO);
        bDictMapper.insertSelective(bDict);
        return bDict.getId();
    }

    private String doUpdate(BDictVO bDictVO) {
        BDict originalRecord = selectByPrimaryKey(bDictVO.getId());
        BDict bDict = BDictConverter.convertToModelWithBase(bDictVO, originalRecord);

        BDictExample example = new BDictExample();
        example.or().andIdEqualTo(bDictVO.getId()).andLastUpdateVersionEqualTo(bDictVO.getLastUpdateVersion());
        int updateCount = bDictMapper.updateByExample(bDict, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return bDict.getId();
    }

    public List<BDict> selectByExample(BDictExample example) {
        return bDictMapper.selectByExample(example);
    }

    public List<String> saveBDictList(List<BDictVO> bDictVOList) {
        if (CollectionUtils.isEmpty(bDictVOList)) {
            throw new ServiceException("数据不能为空！");
        }
        if(bDictVOList.size() > 5000) {
            throw new ServiceException("每次只能新增5000條數據，請分開兩個檔案處理");
        }
        List<String> ids = new ArrayList<>();
        for(int i = 0; i < bDictVOList.size(); i++) {
            BDictVO bDictVO = bDictVOList.get(i);
            checkExist(bDictVO.getCode(), "[第" + (i + 1) + "行]" + "编码不能为空");
            checkExist(bDictVO.getName(), "[第" + (i + 1) + "行]" + "名称不能为空");
            checkExist(bDictVO.getValue(), "[第" + (i + 1) + "行]" + "值不能为空");
            checkExist(bDictVO.getDictType(), "[第" + (i + 1) + "行]" + "字典类型不能为空");

            if (StringUtils.isBlank(bDictVO.getId())) {
                // 新增
                ids.add(doAdd(bDictVO));
            } else {
                checkExist(bDictVO.getLastUpdateVersion(), "[第" + (i + 1) + "行]" + "更新时版本号不能为空");
                // 修改
                ids.add(doUpdate(bDictVO));
            }
        }
        return ids;
    }

}
