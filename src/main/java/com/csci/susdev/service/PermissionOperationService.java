package com.csci.susdev.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.*;
import com.csci.susdev.model.*;
import com.csci.susdev.model.Permission;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class PermissionOperationService {
    
    @Autowired
    private PermissionOperationMapper mapper;
    
    @Autowired
    private PermissionMapper permissionMapper;

    public List<PermissionOperation> selectByExample(PermissionOperationExample example) {
        return mapper.selectByExample(example);
    }
	
    /**
     * 權限操作關係 数据列表
     *
     * @param 
     * @return
     */
    public List<PermissionOperation> listPermissionOperationByCode(String permissionname) {
    	Permission permission = ServiceHelper.getPermissionByCode(permissionname, permissionMapper);
    	if(permission == null)
    		throw new ServiceException("找不到權限");
    	
        return ServiceHelper.listPermissionOperation(permission.getId(), mapper);
    }
    
    

	
    /**
     * 權限操作關係 数据列表
     *
     * @param permission
     * @return
     */
    public List<PermissionOperation> listPermissionOperation(String permissionId) {
        return ServiceHelper.listPermissionOperation(permissionId, mapper);
    }

    /**
     * 保存 權限操作關係 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param permissionOperation
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> savePermissionOperationList(List<PermissionOperation> permissionOperationLst) {
    	List<String> idLst = new ArrayList<>();
    	for(PermissionOperation permissionOperation : permissionOperationLst) {
    		this.savePermissionOperation(permissionOperation);
            idLst.add(permissionOperation.getId());
    	}
        return idLst;
    }

    /**
     * 保存 權限操作關係 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param permissionOperation
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String savePermissionOperation(PermissionOperation permissionOperation) {
    	if(permissionOperation.getIsDeleted() == true) {
    		this.deletePermissionOperation(permissionOperation.getId());
    	} else {
	    	UserInfo currentUser = ContextUtils.getCurrentUser();
	    	LocalDateTime now = LocalDateTime.now();
	        if (StringUtils.isBlank(permissionOperation.getId())) {
	        	permissionOperation.setCreationTime(now);
	        	permissionOperation.setCreateUsername(currentUser.getUsername());
	        	permissionOperation.setLastUpdateTime(now);
	        	permissionOperation.setLastUpdateUsername(currentUser.getUsername());
	        	mapper.insertSelective(permissionOperation);
	        } else {
	        	permissionOperation.setLastUpdateTime(now);
	        	permissionOperation.setLastUpdateUsername(currentUser.getUsername());
	        	mapper.updateByPrimaryKeySelective(permissionOperation);
	        }
    	}
        return permissionOperation.getId();
    }

    /**
     * 刪除 角色權限關係 信息
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int deletePermissionOperation(String id) {
        return mapper.deleteByPrimaryKey(id);
    }
}
