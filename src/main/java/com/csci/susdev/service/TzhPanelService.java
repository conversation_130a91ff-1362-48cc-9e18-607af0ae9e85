package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.tzh.mapper.*;
import com.csci.tzh.model.*;
import com.csci.tzh.model.FResultExample.Criteria;
import com.csci.susdev.model.ResultPage;
import com.csci.tzh.qo.*;
import com.csci.tzh.vo.*;
import com.github.pagehelper.PageHelper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhPanelService {
	@Autowired
	private FResultCustomMapper customMapper;
	public List<CarbonAmountByScopeMainVO> listCarbonAmountByScopeMain(TzhPanelQO tzhPanelQO) {
		return customMapper.listCarbonAmountByScopeMain(tzhPanelQO.getRegion(), tzhPanelQO.getSiteName(), tzhPanelQO.getCarbonEmissionLocation(), tzhPanelQO.getRecordYearMonthFrom(), tzhPanelQO.getRecordYearMonthTo());
	}

	public List<CarbonAmountByScopeDetailVO> listCarbonAmountByScopeDetail(TzhPanelQO tzhPanelQO) {
		return customMapper.listCarbonAmountByScopeDetail(tzhPanelQO.getRegion(), tzhPanelQO.getSiteName(), tzhPanelQO.getCarbonEmissionLocation(), tzhPanelQO.getRecordYearMonthFrom(), tzhPanelQO.getRecordYearMonthTo());
	}

	public List<CarbonAmountPercentageByScopeDetailVO> listCarbonAmountPercentageByScopeDetail(TzhPanelQO tzhPanelQO) {
		return customMapper.listCarbonAmountPercentageByScopeDetail(tzhPanelQO.getRegion(), tzhPanelQO.getSiteName(), tzhPanelQO.getCarbonEmissionLocation(), tzhPanelQO.getRecordYearMonthFrom(), tzhPanelQO.getRecordYearMonthTo());
	}
}
