package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.tzh.mapper.*;
import com.csci.tzh.model.*;
import com.csci.tzh.vo.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class BlockChainSetValueByKeyLogService {

	@Autowired
	private BlockChainSetValueByKeyLogMapper mapper;
	
	@Autowired
	private BlockChainSetValueByKeyLogCustomMapper customMapper;

	public List<BlockChainSetValueByKeyLog> selectByExample(BlockChainSetValueByKeyLogExample example) {
		return mapper.selectByExample(example);
	}

	/**
	 * 區塊鏈MP5計算 最新数据
	 *
	 * @param 
	 * @return
	 */
	public BlockChainSetValueByKeyLogVO getBlockChainSetValueByKeyLog() {
		List<BlockChainSetValueByKeyLogVO> lst = customMapper.listBlockChainSetValueByKeyLog();
		if(lst.size() > 0) {
			return lst.get(0);
		} else {
			return null;
		}
	}
	
}
