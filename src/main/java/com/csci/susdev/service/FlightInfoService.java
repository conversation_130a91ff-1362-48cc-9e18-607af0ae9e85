package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.AirportCustomMapper;
import com.csci.susdev.mapper.AirportMapper;
import com.csci.susdev.mapper.FlightInfoCustomMapper;
import com.csci.susdev.mapper.FlightInfoMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.qo.FlightInfoQO;
import com.csci.susdev.util.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import io.swagger.v3.core.util.Json;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
@Slf4j
public class FlightInfoService {
	@Autowired
	private FlightInfoMapper flightInfoMapper;
	@Autowired
	private FlightInfoCustomMapper flightInfoCustomMapper;
	@Autowired
	private AirportMapper airportMapper;
	@Autowired
	private AirportCustomMapper airportCustomMapper;
	private List<String> listCabinClass;
	@PostConstruct
	public void init(){
		listCabinClass = new ArrayList<>();
		listCabinClass.add("Economy");
	}

	/**
	 * 新版本的抓取数据
	 * @throws Exception
	 */
	public void downloadDataNew() {
		log.info("开始抓取航班信息");
		long start = System.currentTimeMillis();

		List<String> listAirPortCode = airportCustomMapper.getAllAirPortCode();
		//删除当年所有航班信息
		FlightInfoExample example = new FlightInfoExample();
		FlightInfoExample.Criteria criteria = example.or();
		criteria.andRecordYearEqualTo(LocalDateTime.now().getYear());
		flightInfoMapper.deleteByExample(example);

		List<FlightInfo> listFlightInfo = new ArrayList<>();
		log.info("出发地机场数量:{}", listFlightInfo.size());
		for(String airportCodeStart : listAirPortCode) {
			log.info("开始抓取出发机场编码:{}", airportCodeStart);
			try {
				//根据出发机场编码查找所有的目的机场信息
				Connection conn0 = Jsoup.connect("https://applications.icao.int/icec/Home/GetAirportsByDep");
				Document doc0 = conn0
						.data("depCode", airportCodeStart)
						.data("type", "passenger")
						.ignoreContentType(true)
						.post();
				List<Map<String, String>> lstAirportEndMap = JsonUtils.getObjectMapper().readValue(doc0.text(), new TypeReference<List<Map<String, String>>>(){});
				log.info("出发机场机场编码:{},目标机场数量:{}", airportCodeStart, lstAirportEndMap.size());
				for(Map<String, String> airAportEndMap : lstAirportEndMap) {
					for(String cabinClass: listCabinClass){
						//计算航线的碳排信息
						Connection connCompute = Jsoup.connect("https://applications.icao.int/icec/Home/ComputePassenger");
						Document doc1 = connCompute
								.data("triptype", "One Way")
								.data("cabinclass", cabinClass)
								.data("noOfpassenger", "1")
								.data("depCode", airportCodeStart)
								.data("arrCode1", airAportEndMap.get("AirportCode"))
								.data("arrCode2", "")
								.data("arrCode3", "")
								.ignoreContentType(true)
								.post();
						Map<String, String> cookies = connCompute.response().cookies();
						//根据显示的页面抓取相关信息
						Document doc2 = Jsoup.connect("https://applications.icao.int/icec/Home/ResultPassengerTotalView")
								.data("type", "Metric")
								.cookies(cookies)
								.post();
						List<String> lstFlightDetail = doc2.getElementsByClass("lblResultDetail").eachText();
						if(lstFlightDetail.size() < 5) {
							log.info("无效的航班信息:出发地:{}, 目的地:{}, 级别:{}", airportCodeStart,  airAportEndMap.get("AirportCode"),
									cabinClass);
							continue;
						}
						FlightInfo flightInfo = new FlightInfo();
						flightInfo.setId(UUID.randomUUID().toString());
						flightInfo.setCreationTime(LocalDateTime.now());
						flightInfo.setRecordYear(LocalDateTime.now().getYear());
						//获取航线距离
						flightInfo.setFlightDistance(BigDecimal.valueOf(getNumberExceptUnit(lstFlightDetail.get(5))));
						//获取航线碳排放量
						flightInfo.setCarbonEmission(BigDecimal.valueOf(getNumberExceptUnit(lstFlightDetail.get(2))));
						flightInfo.setStartPlace(airportCodeStart);
						flightInfo.setDestination(airAportEndMap.get("AirportCode"));
						flightInfo.setPersonCount(1);
						flightInfo.setLevel(cabinClass);
						flightInfo.setTicketType(2);
						flightInfo.setCreationTime(LocalDateTime.now());
						flightInfo.setLastUpdateTime(LocalDateTime.now());
						listFlightInfo.add(flightInfo);
						if(listFlightInfo.size() >= 100){
							flightInfoCustomMapper.batchInsert(listFlightInfo);
							listFlightInfo.clear();
						}
					}
				}
			} catch (Exception ex) {
				log.error("更新航班信息异常:", ex);
			}
		}
		if(listFlightInfo.size() > 0){
			flightInfoCustomMapper.batchInsert(listFlightInfo);
			listFlightInfo.clear();
		}
		log.info("结束航班抓取信息,耗时:{}", System.currentTimeMillis() - start);
	}

	/**
	 * 字符串，取数字，去除单位，如 171 KM,取171
	 * @param inputStr
	 * @return
	 */
	private Double getNumberExceptUnit(String inputStr){
		try{
			// 使用空格分割字符串
			String[] parts = inputStr.split(" ");
			// 假定数字总是在第一个位置
			if (parts.length > 0) {
				String numberPart = parts[0];
				numberPart = numberPart.replaceAll(",", "");
				return Double.valueOf(numberPart);
			}
		}catch(Exception ex){
			log.error("数字解析异常,数字字符串:{},异常:", inputStr, ex);
		}
		return Double.valueOf(0);
	}

	/**
	 * 旧的处理逻辑，暂时保留
	 * @throws Exception
	 */
	public void downloadData() throws Exception {
		List<Airport> lstAirport = airportMapper.selectByExample(new AirportExample());

		FlightInfoExample example = new FlightInfoExample();
		FlightInfoExample.Criteria criteria = example.or();
		criteria.andRecordYearEqualTo(LocalDateTime.now().getYear());
		flightInfoMapper.deleteByExample(example);

		for(Airport airportStart : lstAirport) {
			try {
				Connection conn0 = Jsoup.connect("https://applications.icao.int/icec/Home/GetAirportsByDep");
				Document doc0 = conn0
						.data("depCode", airportStart.getCode())
						.ignoreContentType(true)
						.post();
				List<Map<String, String>> lstAirportEndMap = JsonUtils.getObjectMapper().readValue(doc0.text(), new TypeReference<List<Map<String, String>>>(){});

				for(Map<String, String> airAportEndMap : lstAirportEndMap) {
					Connection conn1 = Jsoup.connect("https://applications.icao.int/icec/Home/Compute");
					Document doc1 = conn1
							.data("userID", "ICEC")
							.data("unitofMeasureTag", "1")
							.data("triptype", "One Way")
							.data("cabinclass", "Economy")
							.data("noofpassenger", "1")
							.data("noofArrAirport", "1")
							.data("depCode", airportStart.getCode())
							.data("arrCode1", airAportEndMap.get("airport_code"))
							.data("arrCode2", "#")
							.data("arrCode3", "#")
							.data("TypeCargoPassenger", "Passenger")
							.data("NumberKg", "1")
							.data("BellyFreighter", "Freighter")
							.ignoreContentType(true)
							.post();
					Map<String, String> cookies = conn1.response().cookies();
					Document doc2 = Jsoup.connect("https://applications.icao.int/icec/Home/ChildResultDetailView")
							.data("type", "Metric")
							.cookies(cookies)
							.post();
					List<String> lstFlightDetail = doc2.getElementsByClass("lblResultDetail").eachText();
					if(lstFlightDetail.size() < 4) {
						System.out.println(airportStart.getCode() + airAportEndMap.get("airport_code"));
						System.out.println(Json.pretty(lstFlightDetail));
						continue;
					}
					FlightInfo flightInfo = new FlightInfo();
					flightInfo.setId(UUID.randomUUID().toString());
					flightInfo.setCreationTime(LocalDateTime.now());
					flightInfo.setRecordYear(LocalDateTime.now().getYear());
					flightInfo.setFlightDistance(BigDecimal.valueOf(Double.valueOf(lstFlightDetail.get(0))));
					flightInfo.setCarbonEmission(BigDecimal.valueOf(Double.valueOf(lstFlightDetail.get(3))));
					flightInfo.setStartPlace(airportStart.getCode());
					flightInfo.setDestination(airAportEndMap.get("airport_code"));
					flightInfo.setPersonCount(1);
					flightInfo.setLevel("Economy");
					flightInfo.setTicketType(2);

					flightInfoMapper.insert(flightInfo);

				}
			} catch (Exception ex) {
				ex.printStackTrace();
			}
		}

		for(Airport airportStart : lstAirport) {
			try {
				Connection conn0 = Jsoup.connect("https://applications.icao.int/icec/Home/GetAirportsByDep");
				Document doc0 = conn0
						.data("depCode", airportStart.getCode())
						.ignoreContentType(true)
						.post();
				List<Map<String, String>> lstAirportEndMap = JsonUtils.getObjectMapper().readValue(doc0.text(), new TypeReference<List<Map<String, String>>>(){});

				for(Map<String, String> airAportEndMap : lstAirportEndMap) {
					Connection conn1 = Jsoup.connect("https://applications.icao.int/icec/Home/Compute");
					Document doc1 = conn1
							.data("userID", "ICEC")
							.data("unitofMeasureTag", "1")
							.data("triptype", "One Way")
							.data("cabinclass", "Premium")
							.data("noofpassenger", "1")
							.data("noofArrAirport", "1")
							.data("depCode", airportStart.getCode())
							.data("arrCode1", airAportEndMap.get("airport_code"))
							.data("arrCode2", "#")
							.data("arrCode3", "#")
							.data("TypeCargoPassenger", "Passenger")
							.data("NumberKg", "1")
							.data("BellyFreighter", "Freighter")
							.ignoreContentType(true)
							.post();
					Map<String, String> cookies = conn1.response().cookies();
					Document doc2 = Jsoup.connect("https://applications.icao.int/icec/Home/ChildResultDetailView")
							.data("type", "Metric")
							.cookies(cookies)
							.post();
					List<String> lstFlightDetail = doc2.getElementsByClass("lblResultDetail").eachText();
					if(lstFlightDetail.size() < 4) {
						System.out.println(airportStart.getCode() + airAportEndMap.get("airport_code"));
						System.out.println(Json.pretty(lstFlightDetail));
						continue;
					}
					FlightInfo flightInfo = new FlightInfo();
					flightInfo.setId(UUID.randomUUID().toString());
					flightInfo.setCreationTime(LocalDateTime.now());
					flightInfo.setRecordYear(LocalDateTime.now().getYear());
					flightInfo.setFlightDistance(BigDecimal.valueOf(Double.valueOf(lstFlightDetail.get(0))));
					flightInfo.setCarbonEmission(BigDecimal.valueOf(Double.valueOf(lstFlightDetail.get(3))));
					flightInfo.setStartPlace(airportStart.getCode());
					flightInfo.setDestination(airAportEndMap.get("airport_code"));
					flightInfo.setPersonCount(1);
					flightInfo.setLevel("Premium");
					flightInfo.setTicketType(2);

					flightInfoMapper.insert(flightInfo);

				}
			} catch (Exception ex) {
				ex.printStackTrace();
			}
		}
	}

	public FlightInfo get(FlightInfoQO qo) {
		FlightInfo flightInfo = flightInfoCustomMapper.get(qo.getLevel(), 2,
				1, qo.getStartPlace(), qo.getDestination());
		if(flightInfo != null) {
			flightInfo.setTicketType(qo.getTicketType());
			flightInfo.setPersonCount(qo.getPersonCount());
			// 計算人數和來回 里數和碳排
			flightInfo.setCarbonEmission(flightInfo.getCarbonEmission().multiply(new BigDecimal(qo.getPersonCount())));
			if(qo.getTicketType() == 1) {
				flightInfo.setFlightDistance(flightInfo.getFlightDistance().multiply(new BigDecimal(2)));
				flightInfo.setCarbonEmission(flightInfo.getCarbonEmission().multiply(new BigDecimal(2)));
			}
		}
		return flightInfo;
	}
}
