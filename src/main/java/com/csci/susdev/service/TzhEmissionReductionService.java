package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.dto.TzhEmissionReductionDTO;
import com.csci.tzh.mapper.TzhEmissionReductionCustomMapper;
import com.csci.tzh.mapper.TzhEmissionReductionMapper;
import com.csci.tzh.model.TzhEmissionReduction;
import com.csci.tzh.model.TzhEmissionReductionExample;
import com.csci.tzh.model.TzhEmissionReductionExample.Criteria;
import com.csci.tzh.qo.TzhEmissionReductionQO;
import com.csci.tzh.vo.TzhEmissionReductionVO;
import com.csci.tzh.qo.TzhEmissionReductionSummaryQO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhEmissionReductionService {

	@Autowired
	private TzhEmissionReductionMapper mapper;
	@Autowired
	private TzhEmissionReductionCustomMapper customMapper;

	@Resource
	private UserService userService;

	public List<TzhEmissionReduction> selectByExample(TzhEmissionReductionExample example) {
		return mapper.selectByExample(example);
	}

	public List<TzhEmissionReductionVO> list(TzhEmissionReductionQO qo) {
		if(qo.getRecordYearMonthFrom() == null) {
			qo.setRecordYearMonthFrom(0);
		}
		if(qo.getRecordYearMonthTo() == null) {
			qo.setRecordYearMonthTo(999999);
		}
		return customMapper.list(qo.getSiteName(), qo.getProtocol(), qo.getRecordYearMonthFrom(), qo.getRecordYearMonthTo());
	}

	public Map summary(TzhEmissionReductionSummaryQO qo) {
		return customMapper.summary(qo.getSiteName());
	}

	@Transactional(rollbackFor = Exception.class)
	public TzhEmissionReduction save(TzhEmissionReductionDTO dto) {
		UserInfo currentUser = ContextUtils.getCurrentUser();

		TzhEmissionReduction model = new TzhEmissionReduction();
		BeanUtils.copyProperties(dto, model);
		model.setCreatedtime(LocalDateTime.now());
		model.setCreatedby(currentUser.getUsername());

		if(model.getId() != null) {
			this.delete(model.getId());
		}

		if(model.getIsdeleted() == false) {
			model.setId(UUID.randomUUID().toString());
			model.setCarbonunit("千克");
			model.setCreatedby(currentUser.getUsername());
			model.setCreatedtime(LocalDateTime.now());
			mapper.insertSelective(model);
		}

		return model;
	}

	@Transactional(rollbackFor = Exception.class)
	public List<TzhEmissionReduction> saveList(List<TzhEmissionReductionDTO> lstDto) {
		userService.checkIsReadOnly();

		List<TzhEmissionReduction> lst = new ArrayList<>();
		for(TzhEmissionReductionDTO dto: lstDto) {
			lst.add(save(dto));
		}
		return lst;
	}

	@Transactional(rollbackFor = Exception.class)
	public int delete(String id) {
		userService.checkIsReadOnly();

		int result = 0;

		TzhEmissionReductionExample example = new TzhEmissionReductionExample();
		Criteria criteria = example.or();
		criteria.andIdEqualTo(id);

		TzhEmissionReduction x = new TzhEmissionReduction();
		x.setIsdeleted(true);

		result += mapper.updateByExampleSelective(x, example);

		return result;
	}
}
