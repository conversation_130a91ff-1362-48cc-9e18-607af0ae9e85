package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.tzh.mapper.TzhCarbonEmissionLocationMapper;
import com.csci.tzh.model.TzhCarbonEmissionLocation;
import com.csci.tzh.model.TzhCarbonEmissionLocationExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhCarbonEmissionLocationService {

	@Autowired
	private TzhCarbonEmissionLocationMapper mapper;

	public List<TzhCarbonEmissionLocation> selectByExample(TzhCarbonEmissionLocationExample example) {
		return mapper.selectByExample(example);
	}

	public List<TzhCarbonEmissionLocation> list() {
		TzhCarbonEmissionLocationExample example = new TzhCarbonEmissionLocationExample();

		TzhCarbonEmissionLocationExample.Criteria criteria = example.or();
		example.setOrderByClause("Name");

		List<TzhCarbonEmissionLocation> lst = mapper.selectByExample(example);
		return lst;
	}
}
