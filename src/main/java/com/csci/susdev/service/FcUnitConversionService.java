package com.csci.susdev.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.FcUnitConversionMapper;
import com.csci.susdev.model.FcUnitConversion;
import com.csci.susdev.model.FcUnitConversionExample;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.modelcovt.FcUnitConversionConverter;
import com.csci.susdev.qo.FcUnitConversionQO;
import com.csci.susdev.util.ConvertBeanUtils;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.vo.ExcelFcUnitConversionVO;
import com.csci.susdev.vo.FcUnitConversionVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class FcUnitConversionService {

    @Resource
    private FcUnitConversionMapper fcUnitConversionMapper;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FcUnitConversion record) {
        return fcUnitConversionMapper.insertSelective(record);
    }

    public FcUnitConversion selectByPrimaryKey(String id) {
        return fcUnitConversionMapper.selectByPrimaryKey(id);
    }

    public FcUnitConversionVO getFcUnitConversion(String id) {
        checkExist(id, "id不能为空");
        FcUnitConversion fcUnitConversion = selectByPrimaryKey(id);
        checkExist(fcUnitConversion, "未找到对应的记录");
        if(fcUnitConversion.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return FcUnitConversionConverter.convertToVO(fcUnitConversion);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveFcUnitConversion(FcUnitConversionVO fcUnitConversionVO) {
        if (StringUtils.isBlank(fcUnitConversionVO.getId())) {
            // 新增
            return doAdd(fcUnitConversionVO);
        } else {
            checkExist(fcUnitConversionVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(fcUnitConversionVO);
        }

    }

    public ResultPage<FcUnitConversionVO> listFcUnitConversion(FcUnitConversionQO fcUnitConversionQO) {
        FcUnitConversionExample example = new FcUnitConversionExample();
        FcUnitConversionExample.Criteria criteria = example.createCriteria();
        criteria.andIsDeletedEqualTo(false);
        SimpTradUtil simpTradUtil = new SimpTradUtil();
        if (StringUtils.isNotEmpty(fcUnitConversionQO.getUnitGroup())) {
            String unitGroupTc = simpTradUtil.convert2Trad(fcUnitConversionQO.getUnitGroup());
            String unitGroupSc = simpTradUtil.convert2Simp(fcUnitConversionQO.getUnitGroup());
            //criteria.andUnitGroupLike(unitGroupTc);
            criteria.andUnitGroupLike(unitGroupSc);
        }

        if (StringUtils.isNotEmpty(fcUnitConversionQO.getUnitCollect())) {
            String unitCollectTc = simpTradUtil.convert2Trad(fcUnitConversionQO.getUnitCollect());
            String unitCollectSc = simpTradUtil.convert2Simp(fcUnitConversionQO.getUnitCollect());
            //criteria.andUnitCollectLike(unitCollectTc);
            criteria.andUnitCollectLike(unitCollectSc);
        }

        if (StringUtils.isNotEmpty(fcUnitConversionQO.getUnit())) {
            String unitTc = simpTradUtil.convert2Trad(fcUnitConversionQO.getUnit());
            String unitSc = simpTradUtil.convert2Simp(fcUnitConversionQO.getUnit());
            //criteria.andUnitLike(unitTc);
            criteria.andUnitLike(unitSc);
        }
        if (StringUtils.isNotBlank(fcUnitConversionQO.getOrderBy())) {
            example.setOrderByClause(NamingConventionUtils.snakeCaseToCamelCase(fcUnitConversionQO.getOrderBy()));
        }else {
            example.setOrderByClause("unit_group, unit_collect, unit");
        }

        PageHelper.startPage(fcUnitConversionQO.getCurPage(), fcUnitConversionQO.getPageSize());
        List<FcUnitConversion> fcUnitConversions = fcUnitConversionMapper.selectByExample(example);
        return new ResultPage<>(fcUnitConversions, fcUnitConversions.stream().map(FcUnitConversionConverter::convertToVO).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFcUnitConversion(String id) {
        FcUnitConversion record = selectByPrimaryKey(id);
        record.setIsDeleted(true);
        fcUnitConversionMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(FcUnitConversionVO fcUnitConversionVO) {
        FcUnitConversion fcUnitConversion = FcUnitConversionConverter.convertToModel(fcUnitConversionVO);
        fcUnitConversionMapper.insertSelective(fcUnitConversion);
        return fcUnitConversion.getId();
    }

    private String doUpdate(FcUnitConversionVO fcUnitConversionVO) {
        FcUnitConversion originalRecord = selectByPrimaryKey(fcUnitConversionVO.getId());
        FcUnitConversion fcUnitConversion = FcUnitConversionConverter.convertToModelWithBase(fcUnitConversionVO, originalRecord);

        FcUnitConversionExample example = new FcUnitConversionExample();
        example.or().andIdEqualTo(fcUnitConversionVO.getId()).andLastUpdateVersionEqualTo(fcUnitConversionVO.getLastUpdateVersion());
        int updateCount = fcUnitConversionMapper.updateByExample(fcUnitConversion, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return fcUnitConversion.getId();
    }

    public List<FcUnitConversion> selectByExample(FcUnitConversionExample example) {
        return fcUnitConversionMapper.selectByExample(example);
    }

    public List<String> saveFcUnitConversionList(List<FcUnitConversionVO> fcUnitConversionVOList) {
        if (CollectionUtils.isEmpty(fcUnitConversionVOList)) {
            throw new ServiceException("数据不能为空！");
        }
        if(fcUnitConversionVOList.size() > 5000) {
            throw new ServiceException("每次只能新增5000條數據，請分開兩個檔案處理");
        }
        List<String> ids = new ArrayList<>();
        for(int i = 0; i < fcUnitConversionVOList.size(); i++) {
            FcUnitConversionVO fcUnitConversionVO = fcUnitConversionVOList.get(i);
            checkExist(fcUnitConversionVO.getUnitGroup(), "[第" + (i + 1) + "行]" + "单位组不能为空");
            checkExist(fcUnitConversionVO.getUnitCollect(), "[第" + (i + 1) + "行]" + "单位集不能为空");
            checkExist(fcUnitConversionVO.getUnit(), "[第" + (i + 1) + "行]" + "单位不能为空");
            checkExist(fcUnitConversionVO.getUnitSymbol(), "[第" + (i + 1) + "行]" + "单位符号不能为空");

            if (StringUtils.isBlank(fcUnitConversionVO.getId())) {
                // 新增
                ids.add(doAdd(fcUnitConversionVO));
            } else {
                checkExist(fcUnitConversionVO.getLastUpdateVersion(), "[第" + (i + 1) + "行]" + "更新时版本号不能为空");
                // 修改
                ids.add(doUpdate(fcUnitConversionVO));
            }
        }
        return ids;
    }

    public byte[] exportFcUnitConversion(FcUnitConversionQO fcUnitConversionQO, HttpHeaders headers) {
        List<FcUnitConversionVO> list = this.listFcUnitConversion(fcUnitConversionQO).getList();
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("无数据可导出！");
        }
        String filename = new StringBuilder()
                .append("单位换算")
                .append("_")
                .append(DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()))
                .append(".xlsx").toString();
        String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8);
        headers.setContentDispositionFormData("attachment", encodedFilename);

        try (ByteArrayOutputStream out = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcel.write(out).build()) {
            List<ExcelFcUnitConversionVO> excelFcUnitConversionVOS = new ArrayList<>();
            for (FcUnitConversionVO fcUnitConversionVO : list) {
                ExcelFcUnitConversionVO excelFcUnitConversionVO = ConvertBeanUtils.convert(fcUnitConversionVO, ExcelFcUnitConversionVO.class);
                excelFcUnitConversionVOS.add(excelFcUnitConversionVO);
            }
            WriteSheet hkSheet = EasyExcel.writerSheet("单位换算").head(ExcelFcUnitConversionVO.class).build();
            excelWriter.write(excelFcUnitConversionVOS, hkSheet);

            excelWriter.finish();
            return out.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
