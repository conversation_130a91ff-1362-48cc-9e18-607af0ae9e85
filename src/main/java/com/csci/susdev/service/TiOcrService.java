package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.CabinCodeEnum;
import com.csci.susdev.constant.CacheConstants;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.exception.FileProcessingException;
import com.csci.susdev.exception.OcrAnalysisException;
import com.csci.susdev.exception.OcrErrorCodes;
import com.csci.susdev.facade.HiAgentFacade;
import com.csci.susdev.model.response.AgentCreateConversationResponse;
import com.csci.susdev.model.response.AgentMessageInfoResponse;
import com.csci.susdev.util.DateUtils;
import com.csci.susdev.util.JsonUtils;
import com.csci.susdev.util.StringUtil;
import com.csci.susdev.util.TiOcrUtil;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.redis.RedisUtil;
import com.csci.susdev.vo.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class TiOcrService {


    @Resource
    private TiOcrUtil tiOcrUtil;
    @Resource
    private HiAgentFacade hiAgentFacade;
    @Resource
    private RedisUtil redisUtil;
    @Value("${app.esg.redisKey}")
    private String esgRedisKey;

    public String convertFileToStr(MultipartFile file) throws FileProcessingException {
        try {
            // Validate file
            validateFile(file);

            String base64 = tiOcrUtil.convertFile2Base64(file);
            String ocrResult = tiOcrUtil.ocrStructureInfo(base64);

            if (StringUtil.isEmptyNotTrim(ocrResult)) {
                throw new FileProcessingException.InvalidFileException("OCR未能识别文件内容，请确保文件清晰且格式正确");
            }

            return ocrResult;
        } catch (IOException e) {
            throw new FileProcessingException("文件处理失败: " + e.getMessage(), e);
        } catch (Exception e) {
            if (e instanceof FileProcessingException) {
                throw e;
            }
            throw new FileProcessingException("文件转换过程中发生未知错误: " + e.getMessage(), e);
        }
    }

    /**
     * Validate uploaded file
     */
    private void validateFile(MultipartFile file) throws FileProcessingException {
        if (file == null || file.isEmpty()) {
            throw new FileProcessingException.InvalidFileException("文件不能为空");
        }

        // Check file size (25MB limit as per application.properties)
        long maxSize = 25 * 1024 * 1024; // 25MB in bytes
        if (file.getSize() > maxSize) {
            throw new FileProcessingException.FileSizeExceededException(file.getSize(), maxSize);
        }

        // Additional validation can be added here
    }

    /**
     * 解析水费数据
     * <AUTHOR>
     * @param strResult ocr解析后的字符串结果
     * @return com.csci.susdev.vo.TiOcrEnergyBillListVO
     */
    public TiOcrEnergyBillListVO analysisWaterBillInfo(String strResult) throws JsonProcessingException {
        if (StringUtil.isEmptyNotTrim(strResult)) {
            return null;
        }
        TiOcrEnergyBillListVO tiOcrEnergyBillListVO = new TiOcrEnergyBillListVO();
        TiOcrEnergyBillVO tiOcrEnergyBillVO = new TiOcrEnergyBillVO();
        tiOcrEnergyBillListVO.getList().add(tiOcrEnergyBillVO);
        if (strResult.contains("水務署") && strResult.contains("用戶編號") && strResult.contains("付款通知書")) {
            // 水单号
            String billNo = extractValue(strResult, "用戶編[号號][:：]\\s*(\\d+(?:\\s+\\d+)*)", 1);
            tiOcrEnergyBillVO.setBillNo(billNo.replaceAll(" ", ""));
            String fromDate = extractValue(strResult, "(?<!\\d)(\\d{2}/\\d{2}/\\d{4})\\s*-\\s*(\\d{2}/\\d{2}/\\d{4})(?!\\d)", 1);
            String toDate = extractValue(strResult, "(?<!\\d)(\\d{2}/\\d{2}/\\d{4})\\s*-\\s*(\\d{2}/\\d{2}/\\d{4})(?!\\d)", 2);
            // 转换日期格式
            String fromTime = DateUtils.convertDateForTypeToType(fromDate, "dd/MM/yyyy","yyyy-MM-dd");
            String toTime = DateUtils.convertDateForTypeToType(toDate, "dd/MM/yyyy","yyyy-MM-dd");
            tiOcrEnergyBillVO.setFromTime(fromTime);
            tiOcrEnergyBillVO.setToTime(toTime);

            String consumption = extractValue(strResult, "(\\d{1,3}(?:[,\\uFF0C]\\d{3})*)\\s*立方米/\\d+日", 1);
            if (StringUtil.isNotEmpty(consumption)) {
                tiOcrEnergyBillVO.setConsumption(consumption.replaceAll("[,，]", ""));
            }

        }else if (strResult.contains("水務署") && strResult.contains("Consumption")) {
            // 水单号
            String billNo = extractValue(strResult, "Account\\s*Number[:：]\\s*(\\d+(?:\\s+\\d+)*)", 1);
            tiOcrEnergyBillVO.setBillNo(billNo.replaceAll(" ", ""));
            String fromDate = extractValue(strResult, "(?<!\\d)(\\d{2}/\\d{2}/\\d{4})\\s*-\\s*(\\d{2}/\\d{2}/\\d{4})(?!\\d)", 1);
            String toDate = extractValue(strResult, "(?<!\\d)(\\d{2}/\\d{2}/\\d{4})\\s*-\\s*(\\d{2}/\\d{2}/\\d{4})(?!\\d)", 2);
            // 转换日期格式
            String fromTime = DateUtils.convertDateForTypeToType(fromDate, "dd/MM/yyyy","yyyy-MM-dd");
            String toTime = DateUtils.convertDateForTypeToType(toDate, "dd/MM/yyyy","yyyy-MM-dd");
            tiOcrEnergyBillVO.setFromTime(fromTime);
            tiOcrEnergyBillVO.setToTime(toTime);
            // 用量
            String consumption = extractValue(strResult, "(\\d{1,3}(?:[,\\uFF0C]\\d{3})*)\\s*cu\\s*.\\s*m\\s*.\\s*in\\s*\\d+\\s*days", 1);
            if (StringUtil.isNotEmpty(consumption)) {
                tiOcrEnergyBillVO.setConsumption(consumption.replaceAll("[,，]", ""));
            }
        }else if (strResult.contains("澳門自来水")) {
            tiOcrEnergyBillVO.setBillNo("N/A");
            String fromDate = extractValue(strResult, "上次[\\s\\S]*?(\\d{2}/\\d{2}/\\d{4})", 1);
            if (StringUtil.isEmptyNotTrim(fromDate)) {
                fromDate = extractValue(strResult, "(\\d{2}/\\d{2}/\\d{4})[\\s\\S]*?[\\s\\S]上次", 1);
            }
            String toDate = extractValue(strResult, "本次[\\s\\S]*?(\\d{2}/\\d{2}/\\d{4})", 1);
            // 转换日期格式
            String fromTime = DateUtils.convertDateForTypeToType(fromDate, "dd/MM/yyyy","yyyy-MM-dd");
            String toTime = DateUtils.convertDateForTypeToType(toDate, "dd/MM/yyyy","yyyy-MM-dd");
            tiOcrEnergyBillVO.setFromTime(fromTime);
            tiOcrEnergyBillVO.setToTime(toTime);
            // 用量
            String consumption = extractValue(strResult, "總用量.*\\s+(\\d+)\\b", 1);
            String consumption2 = extractValue(strResult, "總用量\\s\\n.*\\s+(\\d+)\\b", 1);
            if (StringUtil.isNotEmpty(consumption)) {
                tiOcrEnergyBillVO.setConsumption(consumption.replaceAll("[,，]", ""));
            }else if (StringUtil.isNotEmpty(consumption2)) {
                tiOcrEnergyBillVO.setConsumption(consumption2.replaceAll("[,，]", ""));
            }
        }else {
            tiOcrEnergyBillListVO = analysisTiOcrEnergyBillInfoByAi(strResult, "水費單");
        }
        return tiOcrEnergyBillListVO;
    }

    /**
     * 解析电费数据
     * <AUTHOR>
     * @param strResult ocr解析后的字符串结果
     * @return com.csci.susdev.vo.TiOcrEnergyBillListVO
     */
    public TiOcrEnergyBillListVO analysisElectricityBillInfo(String strResult) throws JsonProcessingException {
        if (StringUtil.isEmptyNotTrim(strResult)) {
            return null;
        }
        TiOcrEnergyBillListVO tiOcrEnergyBillListVO = new TiOcrEnergyBillListVO();
        TiOcrEnergyBillVO tiOcrEnergyBillVO = new TiOcrEnergyBillVO();
        tiOcrEnergyBillListVO.getList().add(tiOcrEnergyBillVO);
        if (strResult.contains("中電") && (strResult.contains("編賬號碼") || strResult.contains("Account Number")) && strResult.contains("應缴總數")) {
            // 水单号
            String billNo = extractValue(strResult, "\\s*Account\\s*Number\\s*\\n(\\d+-\\d+-\\d+)", 1);
            tiOcrEnergyBillVO.setBillNo(billNo);
            // 日期
            String fromDate = extractValue(strResult, "由.*?(\\d{2}-\\d{2}-\\d{2})", 1);
            String toDate = extractValue(strResult, "至.*?(\\d{2}-\\d{2}-\\d{2})", 1);
            tiOcrEnergyBillVO.setFromTime(formatDate(fromDate));
            tiOcrEnergyBillVO.setToTime(formatDate(toDate));
            // 用量
            String consumption = extractValue(strResult, "(?<=小計\\(\\s*).+?(?=\\s*度\\))", 0);
            if (StringUtil.isNotEmpty(consumption)) {
                tiOcrEnergyBillVO.setConsumption(consumption.replaceAll("[,，]", ""));
            }
        }else if (strResult.contains("中電") && (strResult.contains("編賬號碼") || strResult.contains("Account Number")) && strResult.contains("Total Amount")) {
            // 水单号
            String billNo = extractValue(strResult, "號碼\\s*Account\\s*Number\\s*\\n(\\d+-\\d+-\\d+)", 1);
            tiOcrEnergyBillVO.setBillNo(billNo);
            // 日期
            String fromDate = extractValue(strResult, "From.*?(\\d{2}-\\d{2}-\\d{2})", 1);
            String toDate = extractValue(strResult, "to.*?(\\d{2}-\\d{2}-\\d{2})", 1);
            tiOcrEnergyBillVO.setFromTime(formatDate(fromDate));
            tiOcrEnergyBillVO.setToTime(formatDate(toDate));
            // 用量
            String consumption = extractValue(strResult, "Sub-total\\s*\\(\\s*(\\d+)\\s+units\\s*\\)", 1);
            if (StringUtil.isNotEmpty(consumption)) {
                tiOcrEnergyBillVO.setConsumption(consumption.replaceAll("[,，]", ""));
            }
        }else if (strResult.contains("港燈")) {
            // 水单号
            String billNo = extractValue(strResult, "號碼\\s*\\n\\s*(\\d+)", 1);
            tiOcrEnergyBillVO.setBillNo(billNo);
            String fromDate = extractValue(strResult, "由[\\s\\S]*?(\\d{2}/\\d{2}/\\d{4})", 1);
            String toDate = extractValue(strResult, "至[\\s\\S]*?(\\d{2}/\\d{2}/\\d{4})", 1);
            // 转换日期格式
            String fromTime = DateUtils.convertDateForTypeToType(fromDate, "dd/MM/yyyy","yyyy-MM-dd");
            String toTime = DateUtils.convertDateForTypeToType(toDate, "dd/MM/yyyy","yyyy-MM-dd");
            tiOcrEnergyBillVO.setFromTime(fromTime);
            tiOcrEnergyBillVO.setToTime(toTime);
            // 用量
            String consumption = extractValue(strResult, "(\\d+)度", 1);
            if (StringUtil.isNotEmpty(consumption)) {
                tiOcrEnergyBillVO.setConsumption(consumption.replaceAll("[,，]", ""));
            }
        }else if (strResult.contains("澳門電力") || strResult.contains("澳電")) {
            // 水单号
            String billNo = extractValue(strResult, "賬單編號:\\s*(\\d+)", 1);
            tiOcrEnergyBillVO.setBillNo(billNo);
            String fromDate = extractValue(strResult, "(?<!\\d)(\\d{2}/\\d{2}/\\d{4})\\s*-\\s*(\\d{2}/\\d{2}/\\d{4})(?!\\d)", 1);
            String toDate = extractValue(strResult, "(?<!\\d)(\\d{2}/\\d{2}/\\d{4})\\s*-\\s*(\\d{2}/\\d{2}/\\d{4})(?!\\d)", 2);
            // 转换日期格式
            String fromTime = DateUtils.convertDateForTypeToType(fromDate, "dd/MM/yyyy","yyyy-MM-dd");
            String toTime = DateUtils.convertDateForTypeToType(toDate, "dd/MM/yyyy","yyyy-MM-dd");
            tiOcrEnergyBillVO.setFromTime(fromTime);
            tiOcrEnergyBillVO.setToTime(toTime);
            // 用量
            String consumption = extractValue(strResult, "訂定功率\\s*\\(最新\\)\\s*[\\s\\S]*?\\b(\\d{1,3}(?:,\\d{3})+)\\b(?=[\\s\\S]*?A1收費)", 1);
            if (StringUtil.isNotEmpty(consumption)) {
                tiOcrEnergyBillVO.setConsumption(consumption.replaceAll("[,，]", ""));
            }else {
                String consumption2 = extractValue(strResult, "本月讀數\\s*[\\s\\S]*?\\b(\\d{1,3}(?:,\\d{3})+)\\b(?=[\\s\\S]*?A1收費)", 1);
                if (StringUtil.isNotEmpty(consumption2)) {
                    tiOcrEnergyBillVO.setConsumption(consumption2.replaceAll("[,，]", ""));
                }else {
                    String consumption3 = extractValue(strResult, "(\\d{4}年\\d+月):\\s*(\\d{1,3}(?:[,\\uFF0C]\\d{3})*)\\s*度", 2);
                    if (StringUtil.isNotEmpty(consumption3)) {
                        tiOcrEnergyBillVO.setConsumption(consumption3.replaceAll("[,，]", ""));
                    }
                }
            }
        }else {
            tiOcrEnergyBillListVO = analysisTiOcrEnergyBillInfoByAi(strResult, "電費單");
        }

        return tiOcrEnergyBillListVO;
    }


    /**
     * 解析商务旅行-火车数据
     * <AUTHOR>
     * @param strResult ocr解析后的字符串结果
     * @return com.csci.susdev.vo.TiOcrBusinessTripVO
     */
    public TiOcrBusinessTripListVO analysisBusinessTripTrainInfo(String strResult) throws JsonProcessingException {
        if (StringUtil.isEmptyNotTrim(strResult)) {
            return null;
        }
        TiOcrBusinessTripListVO tiOcrBusinessTripListVO = new TiOcrBusinessTripListVO();
        TiOcrBusinessTripVO tiOcrBusinessTripVO = new TiOcrBusinessTripVO();
        tiOcrBusinessTripListVO.getList().add(tiOcrBusinessTripVO);
        if (strResult.contains("仅供报销使用") || strResult.contains("退票改签")) {
            // 起始地点 目的地 班次号
            String startPlace = extractValue(strResult, "([\\u4e00-\\u9fa5]+)\\s*站\\s*([\\u4e00-\\u9fa5]+)\\s*站\\s*([A-Z]\\d+)", 1);
            String destination = extractValue(strResult, "([\\u4e00-\\u9fa5]+)\\s*站\\s*([\\u4e00-\\u9fa5]+)\\s*站\\s*([A-Z]\\d+)", 2);
            String routeNo = extractValue(strResult, "([\\u4e00-\\u9fa5]+)\\s*站\\s*([\\u4e00-\\u9fa5]+)\\s*站\\s*([A-Z]\\d+)", 3);
            tiOcrBusinessTripVO.setStartPlace(startPlace);
            tiOcrBusinessTripVO.setDestination(destination);
            tiOcrBusinessTripVO.setRouteNo(routeNo);
            if (StringUtil.isEmptyNotTrim(startPlace)) {
                String startPlace2 = extractValue(strResult, "([\\u4e00-\\u9fa5]+)\\s*站\\s*([A-Z]\\d+)\\s*([\\u4e00-\\u9fa5]+)\\s*站", 1);
                String destination2 = extractValue(strResult, "([\\u4e00-\\u9fa5]+)\\s*站\\s*([A-Z]\\d+)\\s*([\\u4e00-\\u9fa5]+)\\s*站", 3);
                String routeNo2 = extractValue(strResult, "([\\u4e00-\\u9fa5]+)\\s*站\\s*([A-Z]\\d+)\\s*([\\u4e00-\\u9fa5]+)\\s*站", 2);
                tiOcrBusinessTripVO.setStartPlace(startPlace2);
                tiOcrBusinessTripVO.setDestination(destination2);
                tiOcrBusinessTripVO.setRouteNo(routeNo2);
                if (StringUtil.isEmptyNotTrim(startPlace2)) {
                    String startPlace3 = extractValue(strResult, "([A-Z]\\d+)\\s*([\\u4e00-\\u9fa5]+)\\s*站\\s*([\\u4e00-\\u9fa5]+)\\s*站", 3);
                    String destination3 = extractValue(strResult, "([A-Z]\\d+)\\s*([\\u4e00-\\u9fa5]+)\\s*站\\s*([\\u4e00-\\u9fa5]+)\\s*站", 2);
                    String routeNo3 = extractValue(strResult, "([A-Z]\\d+)\\s*([\\u4e00-\\u9fa5]+)\\s*站\\s*([\\u4e00-\\u9fa5]+)\\s*站", 1);
                    tiOcrBusinessTripVO.setStartPlace(startPlace3);
                    tiOcrBusinessTripVO.setDestination(destination3);
                    tiOcrBusinessTripVO.setRouteNo(routeNo3);
                }
            }
            // 月份
            String monthValue = extractValue(strResult, "\\d{4}年(\\d{2})月", 1);
            tiOcrBusinessTripVO.setMonthValue(StringUtil.isEmpty(monthValue) ? null : Integer.parseInt(monthValue));
            // 等级
            String level = extractValue(strResult, "([\\u4e00-\\u9fa5]+[座卧])", 1);
            tiOcrBusinessTripVO.setLevel(level);
        } else {
            tiOcrBusinessTripListVO = analysisBusinessTripInfoByAi(strResult, "車票");
        }
        return tiOcrBusinessTripListVO;
    }


    /**
     * 解析水費單/電費單
     * @param strResult ocr解析后的字符串结果
     * @return com.csci.susdev.vo.TiOcrBusinessTripVO
     */
    public TiOcrEnergyBillListVO analysisTiOcrEnergyBillInfoByAi(String strResult, String type)
            throws OcrAnalysisException, JsonProcessingException {
        if (StringUtil.isEmptyNotTrim(strResult)) {
            throw new OcrAnalysisException.DataExtractionException("OCR结果为空，无法进行" + type + "分析");
        }
        TiOcrEnergyBillListVO tiOcrEnergyBillListVO = new TiOcrEnergyBillListVO();
        Object cacheObj = redisUtil.get(esgRedisKey + CacheConstants.OCR_AGENT_CONVERSATION + ContextUtils.getCurrentUser().getUsername());
        String appConversationID = StringUtil.cast(cacheObj);
        if (StringUtil.isEmpty(appConversationID)) {
            HiAgentParamVO hiAgentParamVO = new HiAgentParamVO();
            hiAgentParamVO.setApp(3);
            Map<String, Object> data = new HashMap<>();
            hiAgentParamVO.setData(data);
            String conversation = hiAgentFacade.createConversation(hiAgentParamVO);
            AgentCreateConversationResponse createConversationResponse = null;
            try {
                createConversationResponse = JsonUtils.getObjectMapper().readValue(conversation, AgentCreateConversationResponse.class);
                redisUtil.set(esgRedisKey + CacheConstants.OCR_AGENT_CONVERSATION + ContextUtils.getCurrentUser().getUsername(), createConversationResponse.getConversation().getAppConversationID(), 300);
                appConversationID = createConversationResponse.getConversation().getAppConversationID();
            } catch (Exception e) {
                throw new OcrAnalysisException.AiAnalysisException("创建AI对话失败: " + e.getMessage(), e);
            }
        }
        HiAgentParamVO paramVO = new HiAgentParamVO();
        paramVO.setApp(3);
        Map<String, Object> data = new HashMap<>();
        data.put("AppConversationID", appConversationID);
        String query = "提取" + type + "帳單號、起始日期、結束日期、用量";
        data.put("Query", query + "\n" + strResult);
        data.put("ResponseMode", "streaming");
        paramVO.setData(data);
        try {
            String queryResult = hiAgentFacade.chatQuery(paramVO);
            String messageID = extractTaskId(queryResult);

            if (StringUtil.isEmptyNotTrim(messageID)) {
                throw new OcrAnalysisException.AiAnalysisException("AI查询失败，未获取到有效的消息ID");
            }

            data.clear();
            data.put("MessageID", messageID);
            paramVO.setData(data);
            String messageInfo = hiAgentFacade.getMessageInfo(paramVO);

            AgentMessageInfoResponse messageInfoResponse = JsonUtils.getObjectMapper().readValue(messageInfo, AgentMessageInfoResponse.class);
            String answer = messageInfoResponse.getMessageInfo().getAnswerInfo().getAnswer().replace("```","").replace("json", "");
            tiOcrEnergyBillListVO = JsonUtils.getObjectMapper().readValue(answer, TiOcrEnergyBillListVO.class);

            // Validate the result
            if (tiOcrEnergyBillListVO == null || tiOcrEnergyBillListVO.getList() == null || tiOcrEnergyBillListVO.getList().isEmpty()) {
                throw new OcrAnalysisException.DocumentParsingException(type);
            }

        } catch (Exception e) {
            if (e instanceof OcrAnalysisException) {
                throw e;
            }
            throw new OcrAnalysisException.AiAnalysisException("AI分析" + type + "失败: " + e.getMessage(), e);
        }
        return tiOcrEnergyBillListVO;
    }

    /**
     * 解析商务旅行
     * <AUTHOR>
     * @param strResult ocr解析后的字符串结果
     * @return com.csci.susdev.vo.TiOcrBusinessTripVO
     */
    public TiOcrBusinessTripListVO analysisBusinessTripInfoByAi(String strResult, String type)
            throws OcrAnalysisException, JsonProcessingException {
        if (StringUtil.isEmptyNotTrim(strResult)) {
            throw new OcrAnalysisException.DataExtractionException("OCR结果为空，无法进行" + type + "分析");
        }
        TiOcrBusinessTripListVO tiOcrBusinessTripListVO = new TiOcrBusinessTripListVO();
        Object cacheObj = redisUtil.get(esgRedisKey + CacheConstants.OCR_AGENT_CONVERSATION + ContextUtils.getCurrentUser().getUsername());
        String appConversationID = StringUtil.cast(cacheObj);
        if (StringUtil.isEmpty(appConversationID)) {
            HiAgentParamVO hiAgentParamVO = new HiAgentParamVO();
            hiAgentParamVO.setApp(2);
            Map<String, Object> data = new HashMap<>();
            hiAgentParamVO.setData(data);
            String conversation = hiAgentFacade.createConversation(hiAgentParamVO);
            AgentCreateConversationResponse createConversationResponse = null;
            try {
                createConversationResponse = JsonUtils.getObjectMapper().readValue(conversation, AgentCreateConversationResponse.class);
                redisUtil.set(esgRedisKey + CacheConstants.OCR_AGENT_CONVERSATION + ContextUtils.getCurrentUser().getUsername(), createConversationResponse.getConversation().getAppConversationID(), 300);
                appConversationID = createConversationResponse.getConversation().getAppConversationID();
            } catch (Exception e) {
                throw new OcrAnalysisException.AiAnalysisException("创建AI对话失败: " + e.getMessage(), e);
            }
        }
        HiAgentParamVO paramVO = new HiAgentParamVO();
        paramVO.setApp(2);
        Map<String, Object> data = new HashMap<>();
        data.put("AppConversationID", appConversationID);
        String query = "提取" + type + "起始地、目的地、座位等级、月份";
        data.put("Query", query + "\n" + strResult);
        data.put("ResponseMode", "streaming");
        paramVO.setData(data);
        try {
            String queryResult = hiAgentFacade.chatQuery(paramVO);
            String messageID = extractTaskId(queryResult);

            if (StringUtil.isEmptyNotTrim(messageID)) {
                throw new OcrAnalysisException.AiAnalysisException("AI查询失败，未获取到有效的消息ID");
            }

            data.clear();
            data.put("MessageID", messageID);
            paramVO.setData(data);
            String messageInfo = hiAgentFacade.getMessageInfo(paramVO);

            AgentMessageInfoResponse messageInfoResponse = JsonUtils.getObjectMapper().readValue(messageInfo, AgentMessageInfoResponse.class);
            String answer = messageInfoResponse.getMessageInfo().getAnswerInfo().getAnswer().replace("```","").replace("json", "");
            tiOcrBusinessTripListVO = JsonUtils.getObjectMapper().readValue(answer, TiOcrBusinessTripListVO.class);

            // Validate the result
            if (tiOcrBusinessTripListVO == null || tiOcrBusinessTripListVO.getList() == null || tiOcrBusinessTripListVO.getList().isEmpty()) {
                throw new OcrAnalysisException.DocumentParsingException(type);
            }

        } catch (Exception e) {
            if (e instanceof OcrAnalysisException) {
                throw e;
            }
            throw new OcrAnalysisException.AiAnalysisException("AI分析" + type + "失败: " + e.getMessage(), e);
        }
        for(TiOcrBusinessTripVO vo : tiOcrBusinessTripListVO.getList()) {
            vo.setLevel(CabinCodeEnum.getChineseName(vo.getLevel()));
        }
        return tiOcrBusinessTripListVO;
    }

    public String extractTaskId(String rawData) {
        ObjectMapper mapper = new ObjectMapper();
        String[] lines = rawData.split("\\r?\\n"); // 兼容不同系统的换行符

        for (String line : lines) {
            // 匹配有效数据行
            if (line.startsWith("data:data: ")) {
                try {
                    String jsonStr = line.substring("data:data: ".length()).trim();
                    JsonNode node = mapper.readTree(jsonStr);
                    if (node.has("task_id")) {
                        return node.get("task_id").asText();
                    }
                } catch (Exception e) {
                    System.err.println("JSON解析失败: " + e.getMessage());
                }
            }
        }
        return null;
    }

    private String extractValue(String content, String regex, int group) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        return matcher.find() ? matcher.group(group) : "";
    }

    private static String formatDate(String rawDate) {
        if (rawDate == null || rawDate.equals("N/A") || StringUtil.isEmptyNotTrim(rawDate)) return rawDate;
        String[] parts = rawDate.split("-");
        return "20" + parts[2] + "-" + parts[1] + "-" + parts[0];
    }



}
