package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.RoleMapper;
import com.csci.susdev.mapper.RolePermissionMapper;
import com.csci.susdev.model.Role;
import com.csci.susdev.model.RolePermission;
import com.csci.susdev.model.RolePermissionExample;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class RolePermissionService {

    @Autowired
    private RolePermissionMapper rolePermissionMapper;
    
    @Autowired
    private RoleMapper roleMapper;

    public List<RolePermission> selectByExample(RolePermissionExample example) {
        return rolePermissionMapper.selectByExample(example);
    }
	
    /**
     * 角色權限關係 数据列表
     *
     * @param rolename
     * @return
     */
    public List<RolePermission> listRolePermissionByCode(String rolename) {
        Role role = ServiceHelper.getRoleByCode(rolename);
        if (role == null)
            throw new ServiceException("找不到角色");

        return ServiceHelper.listRolePermission(role.getId(), rolePermissionMapper);
    }
    
    /**
     * 角色權限關係 数据列表
     *
     * @param roleId
     * @return
     */
    public List<RolePermission> listRolePermissionByRoleId(String roleId) {
        return ServiceHelper.listRolePermission(roleId, rolePermissionMapper);
    }

    /**
     * 保存 菜單 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param rolePermissionLst
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> saveRolePermissionList(List<RolePermission> rolePermissionLst) {
    	List<String> idLst = new ArrayList<>();
    	for(RolePermission rolePermission : rolePermissionLst) {
    		this.saveRolePermission(rolePermission);
            idLst.add(rolePermission.getId());
    	}
        return idLst;
    }

    /**
     * 保存 角色權限關係 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param rolePermission
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveRolePermission(RolePermission rolePermission) {
        UserInfo currentUser = ContextUtils.getCurrentUser();
        LocalDateTime now = LocalDateTime.now();
        if (StringUtils.isBlank(rolePermission.getId())) {
            rolePermission.setCreationTime(now);
            rolePermission.setCreateUsername(currentUser.getUsername());
            rolePermission.setLastUpdateTime(now);
            rolePermission.setLastUpdateUsername(currentUser.getUsername());
            rolePermissionMapper.insertSelective(rolePermission);
        } else {
            rolePermission.setLastUpdateTime(now);
            rolePermission.setLastUpdateUsername(currentUser.getUsername());
            rolePermissionMapper.updateByPrimaryKeySelective(rolePermission);
        }
        return rolePermission.getId();
    }

    /**
     * 刪除 角色權限關係 信息
     *
     * @param roleId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteRolePermissionByRoleId(String roleId) {
        RolePermissionExample example = new RolePermissionExample();
        example.or().andRoleIdEqualTo(roleId);
        return rolePermissionMapper.deleteByExample(example);
    }

    /**
     * 刪除 角色權限關係 信息
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteRolePermission(String id) {
        return rolePermissionMapper.deleteByPrimaryKey(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(RolePermission record) {
        return rolePermissionMapper.insertSelective(record);
    }
}
