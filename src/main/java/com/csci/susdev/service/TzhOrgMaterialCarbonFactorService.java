package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.mapper.TzhOrgMaterialCarbonFactorCustomMapper;
import com.csci.tzh.mapper.TzhOrgMaterialCarbonFactorMapper;
import com.csci.tzh.model.*;
import com.csci.tzh.qo.TzhOrgMaterialCarbonFactorPageableQO;
import com.csci.tzh.vo.TzhOrgMaterialCarbonFactorVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhOrgMaterialCarbonFactorService {

	@Autowired
	private TzhOrgMaterialCarbonFactorMapper mapper;

	@Autowired
	private TzhOrgMaterialCarbonFactorCustomMapper customMapper;

	public List<TzhOrgMaterialCarbonFactor> selectByExample(TzhOrgMaterialCarbonFactorExample example) {
		return mapper.selectByExample(example);
	}

	public ResultPage<TzhOrgMaterialCarbonFactorVO> list(TzhOrgMaterialCarbonFactorPageableQO qo) {
		PageHelper.startPage(qo.getCurPage(), qo.getPageSize(), "OMCF.MaterialCode");

		List<TzhOrgMaterialCarbonFactorVO> lst = customMapper.list(qo.getChineseName());
		ResultPage<TzhOrgMaterialCarbonFactorVO> resultPage = new ResultPage<>(lst, true);

		return resultPage;
	}

	@Transactional(rollbackFor = Exception.class)
	public TzhOrgMaterialCarbonFactor save(TzhOrgMaterialCarbonFactor newModel) {
		UserInfo currentUser = ContextUtils.getCurrentUser();

		if(newModel.getIsdeleted() == false) {
			if(StringUtils.isNotBlank(newModel.getId())) {
				// 備份已刪除數據
				TzhOrgMaterialCarbonFactorExample originalExample = new TzhOrgMaterialCarbonFactorExample();
				TzhOrgMaterialCarbonFactorExample.Criteria originalCriteria = originalExample.or();
				originalCriteria.andIdEqualTo(newModel.getId());
				List<TzhOrgMaterialCarbonFactor> lstOriginalModel = mapper.selectByExample(originalExample);
				if(lstOriginalModel.size() > 0) {
					TzhOrgMaterialCarbonFactor originalModel = lstOriginalModel.get(0);
					originalModel.setId(UUID.randomUUID().toString());
					originalModel.setIsdeleted(true);
					originalModel.setDeletedby(currentUser.getUsername());
					originalModel.setDeletedtime(LocalDateTime.now());
					mapper.insertSelective(originalModel);
				}

				// 新增數據(保留舊ID)
				TzhOrgMaterialCarbonFactorExample newExample = new TzhOrgMaterialCarbonFactorExample();
				TzhOrgMaterialCarbonFactorExample.Criteria newCriteria = newExample.or();
				newCriteria.andIdEqualTo(newModel.getId());
				newCriteria.andIsdeletedEqualTo(false);
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				newModel.setDeletedby(null);
				newModel.setDeletedtime(null);
				mapper.deleteByExample(newExample);
				mapper.insertSelective(newModel);
			} else {
				// 新增數據(新建ID)
				newModel.setId(UUID.randomUUID().toString());
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				mapper.insertSelective(newModel);
			}
		} else {
			// 刪除數據
			TzhOrgMaterialCarbonFactorExample example = new TzhOrgMaterialCarbonFactorExample();
			TzhOrgMaterialCarbonFactorExample.Criteria criteria = example.or();
			criteria.andIdEqualTo(newModel.getId());
			newModel.setIsdeleted(true);
			newModel.setDeletedby(currentUser.getUsername());
			newModel.setDeletedtime(LocalDateTime.now());
			mapper.updateByExampleSelective(newModel, example);
		}

		return newModel;
	}
}
