package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.AmbientDetailExtMapper;
import com.csci.susdev.mapper.AmbientHeadExtMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.AmbientEnergyBillExtVOConverter;
import com.csci.susdev.modelcovt.AmbientHeadExtVOConverter;
import com.csci.susdev.modelcovt.AmbientTableDataExtConverter;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.AmbientEnergyBillExtVO;
import com.csci.susdev.vo.AmbientHeadExtVO;
import com.csci.susdev.vo.AmbientTableDataExtVO;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class AmbientExtService {

	@Resource
	private OrganizationService organizationService;

	@Resource
	private AmbientHeadExtMapper ambientHeadExtMapper;

	@Resource
	private AmbientDetailExtMapper ambientDetailExtMapper;

	@Resource
	private AmbientEnergyBillExtService ambientEnergyBillExtService;

	@Transactional(rollbackFor = Exception.class)
	public void insertAmbient(AmbientHeadExtVO ambientHeadExtVO) {

		ambientHeadExtVO.setId(UUID.randomUUID().toString());
		ambientHeadExtVO.setLastUpdateVersion(0);

		if (CollectionUtils.isEmpty(ambientHeadExtVO.getDetails())) {
			throw new ServiceException("表單明细不能为空");
		}

		if (!ServiceHelper.isOrganizationLeaf(ambientHeadExtVO.getOrganizationId())) {
			throw new ServiceException("表單数据只能在最末级组织机构下修改");
		}

		// insert head
		AmbientHeadExt ambientHead = AmbientHeadExtVOConverter.convert(ambientHeadExtVO);
		int updateCount = ambientHeadExtMapper.insertSelective(ambientHead);
		if (updateCount != 1) {
			throw new ServiceException("更新表單数据失败, 请刷新页面重试");
		}

		// delete detail
		deleteAmbientDetailExtByHeadId(ambientHeadExtVO.getId());

		// insert detail
		int seq = 1;
		for (AmbientTableDataExtVO detailVO : ambientHeadExtVO.getDetails()) {
			AmbientDetailExt detail = AmbientTableDataExtConverter.convert(detailVO);
			detail.setId(UUID.randomUUID().toString());
			detail.setHeadId(ambientHeadExtVO.getId());
			detail.setSeq(seq++);
			detail.setCategoryDigest(DigestUtils.md5Hex(detail.getCategory()));
			ambientDetailExtMapper.insertSelective(detail);
		}

		// insert bill
		if(!CollectionUtils.isEmpty(ambientHeadExtVO.getBills())) {
			for (AmbientEnergyBillExtVO billExtVO : ambientHeadExtVO.getBills()) {
				billExtVO.setHeadId(ambientHeadExtVO.getId());
				ambientEnergyBillExtService.saveAmbientEnergyBillExt(billExtVO);
			}
		}
 	}
	
	public static void deleteAmbientDetailExtByHeadId(String headId) {
		checkExist(headId, "headId is null");
		AmbientDetailExtMapper ambientDetailExtMapper = SpringContextUtil.getBean(AmbientDetailExtMapper.class);
		AmbientDetailExtExample example = new AmbientDetailExtExample();
		example.or().andHeadIdEqualTo(headId);
		ambientDetailExtMapper.deleteByExample(example);
	}

	public AmbientHeadExtVO findAmbientHeadExtVO(String organizationId, Integer year, Integer month) {
		//查找符合條件的最新的數據
		AmbientHeadExtExample example = new AmbientHeadExtExample();
		example.or().andOrganizationIdEqualTo(organizationId).andYearEqualTo(year).andMonthEqualTo(month).andIsActiveEqualTo(Boolean.TRUE);
		example.setOrderByClause("creation_time desc");
		List<AmbientHeadExt> lstHead = ambientHeadExtMapper.selectByExample(example);
		AmbientHeadExt ambientHeadExt = lstHead.stream().findFirst().orElse(null);
		if(Objects.isNull(ambientHeadExt)) {
			return null;
		}

		AmbientHeadExtVO ambientHeadExtVO = AmbientHeadExtVOConverter.convert(ambientHeadExt);

		//查找明細表
		AmbientDetailExtExample detailExample = new AmbientDetailExtExample();
		detailExample.or().andHeadIdEqualTo(ambientHeadExt.getId());
		List<AmbientDetailExt> lstDetail = ambientDetailExtMapper.selectByExample(detailExample);
		List<AmbientTableDataExtVO> lstDetailVO = lstDetail.stream().map(AmbientTableDataExtConverter::convert).toList();
		ambientHeadExtVO.setDetails(lstDetailVO);

		//查找水電計費表
		AmbientEnergyBillExtExample billExample = new AmbientEnergyBillExtExample();
		billExample.or().andHeadIdEqualTo(ambientHeadExt.getId());
		List<AmbientEnergyBillExt> lstBill = ambientEnergyBillExtService.selectByExample(billExample);
		List<AmbientEnergyBillExtVO> lstBillVO = lstBill.stream().map(AmbientEnergyBillExtVOConverter::convert).toList();
		ambientHeadExtVO.setBills(lstBillVO);

		return ambientHeadExtVO;
	}

	public AmbientHeadExt selectByPrimaryKey(String id) {
		return ambientHeadExtMapper.selectByPrimaryKey(id);
	}

	public List<AmbientHeadExt> selectByExample(AmbientHeadExtExample example) {
		return ambientHeadExtMapper.selectByExample(example);
	}
}