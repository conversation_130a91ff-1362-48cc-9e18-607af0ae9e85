package com.csci.susdev.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.OperationMapper;
import com.csci.susdev.mapper.PermissionMapper;
import com.csci.susdev.mapper.PermissionOperationMapper;
import com.csci.susdev.model.Operation;
import com.csci.susdev.model.OperationExample;
import com.csci.susdev.model.Permission;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.OperationPageableQO;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class OperationService {
    @Autowired
    private OperationMapper operationMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    @Autowired
    private PermissionOperationMapper permissionOperationMapper;
    public List<Operation> getOperationsByRoles(List<String> roleList) {
        List<Permission> permissionList = permissionMapper.getPermissionByRoleList(roleList);
        if (permissionList == null || permissionList.isEmpty()) {
            return List.of();
        }
        List<String> permissionIds = permissionList.stream().map(Permission::getId).toList();
        List<Operation> operationByPermissionList = operationMapper.getOperationByPermissionList(permissionIds);
        return operationByPermissionList;
    }
    public List<Operation> selectByExample(OperationExample example) {
        return null;
    }

    /**
     * 操作 数据列表
     *
     * @param operationQO
     * @return
     */
    public ResultPage<Operation> listOperation(OperationPageableQO operationQO) {

        return null;
    }

    /**
     * 保存 操作 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param operationLst
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> saveOperationList(List<Operation> operationLst) {
    	List<String> idLst = new ArrayList<>();
    	for(Operation operation : operationLst) {
    		this.saveOperation(operation);
            idLst.add(operation.getId());
    	}
        return idLst;
    }

    /**
     * 保存 操作 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param operation
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveOperation(Operation operation) {
    	UserInfo currentUser = ContextUtils.getCurrentUser();
    	LocalDateTime now = LocalDateTime.now();
        if (StringUtils.isBlank(operation.getId())) {
        	operation.setCreationTime(now);
        	operation.setCreateUsername(currentUser.getUsername());
        	operation.setLastUpdateTime(now);
        	operation.setLastUpdateUsername(currentUser.getUsername());
        	operationMapper.insert(operation);
        } else {
        	operation.setLastUpdateTime(now);
        	operation.setLastUpdateUsername(currentUser.getUsername());
        	operationMapper.updateById(operation);
        }
        return operation.getId();
    }

    /**
     * 刪除 操作 信息
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteOperation(String id) {
        return operationMapper.deleteById(id);
    }

    public List<Operation> getIgnoreOperations() {
        QueryWrapper<Operation> wrapper = new QueryWrapper<Operation>().eq("code", "1");
        return operationMapper.selectList(wrapper);
    }
}
