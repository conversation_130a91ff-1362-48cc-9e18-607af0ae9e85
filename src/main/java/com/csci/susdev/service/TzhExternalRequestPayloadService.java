package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.tzh.mapper.TzhExternalRequestPayloadMapper;
import com.csci.tzh.model.TzhExternalRequestPayload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhExternalRequestPayloadService {

	@Autowired
	private TzhExternalRequestPayloadMapper mapper;

	@Transactional(rollbackFor = Exception.class)
	public TzhExternalRequestPayload save(TzhExternalRequestPayload model) {
		model.setId(UUID.randomUUID().toString());
		model.setCreatedtime(LocalDateTime.now());
		model.setCreatedby("system");
		mapper.insertSelective(model);

		return model;
	}

}
