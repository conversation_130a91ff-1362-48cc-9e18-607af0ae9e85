package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.mapper.TzhBsSiteProtocolCustomMapper;
import java.math.BigDecimal;
import com.csci.tzh.mapper.TzhProjectInfoCustomMapper;
import com.csci.tzh.mapper.TzhProjectInfoMapper;
import com.csci.tzh.model.TzhProjectInfo;
import com.csci.tzh.model.TzhProjectInfoExample;
import com.csci.tzh.qo.TzhProjectInfoQO;
import com.csci.tzh.vo.TzhProjectInfoVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhProjectInfoService {

	@Autowired
	private TzhProjectInfoMapper mapper;

	@Autowired
	private TzhProjectInfoCustomMapper customMapper;

	@Autowired
	private TzhBsSiteProtocolCustomMapper sPCustomMapper;

	public List<TzhProjectInfo> selectByExample(TzhProjectInfoExample example) {
		return mapper.selectByExample(example);
	}

	public TzhProjectInfoVO get(TzhProjectInfoQO qo) {
		return customMapper.get(qo.getSiteid());
	}

	@Transactional(rollbackFor = Exception.class)
	public TzhProjectInfo save(TzhProjectInfo newModel) {
		UserInfo currentUser = ContextUtils.getCurrentUser();
		// 校验并处理经纬度
		if (StringUtils.isNotEmpty(newModel.getLongitude()) || StringUtils.isNotEmpty(newModel.getLatitude())) {
			String validatedLongitude = validateAndFormatLongitude(newModel.getLongitude());
			String validatedLatitude = validateAndFormatLatitude(newModel.getLatitude());
			if (validatedLongitude == null || validatedLatitude == null) {
				throw new IllegalArgumentException("经纬度数据不合法");
			}
			newModel.setLongitude(validatedLongitude);
			newModel.setLatitude(validatedLatitude);
		}

		if(newModel.getIsdeleted() == false) {
			if(StringUtils.isNotBlank(newModel.getId())) {
				// 備份已刪除數據
				TzhProjectInfoExample originalExample = new TzhProjectInfoExample();
				TzhProjectInfoExample.Criteria originalCriteria = originalExample.or();
				originalCriteria.andIdEqualTo(newModel.getId());
				List<TzhProjectInfo> lstOriginalModel = mapper.selectByExample(originalExample);
				if(lstOriginalModel.size() > 0) {
					TzhProjectInfo originalModel = lstOriginalModel.get(0);
					originalModel.setId(UUID.randomUUID().toString());
					originalModel.setIsdeleted(true);
					originalModel.setDeletedby(currentUser.getUsername());
					originalModel.setDeletedtime(LocalDateTime.now());
					mapper.insertSelective(originalModel);
				}

				// 新增數據(保留舊ID)
				TzhProjectInfoExample newExample = new TzhProjectInfoExample();
				TzhProjectInfoExample.Criteria newCriteria = newExample.or();
				newCriteria.andIdEqualTo(newModel.getId());
				newCriteria.andIsdeletedEqualTo(false);
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				newModel.setDeletedby(null);
				newModel.setDeletedtime(null);
				mapper.deleteByExample(newExample);
				mapper.insertSelective(newModel);
			} else {
				// 新增數據(新建ID)
				newModel.setId(UUID.randomUUID().toString());
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				mapper.insertSelective(newModel);
			}
		} else {
			// 刪除數據
			TzhProjectInfoExample example = new TzhProjectInfoExample();
			TzhProjectInfoExample.Criteria criteria = example.or();
			criteria.andIdEqualTo(newModel.getId());
			newModel.setIsdeleted(true);
			newModel.setDeletedby(currentUser.getUsername());
			newModel.setDeletedtime(LocalDateTime.now());
			mapper.updateByExampleSelective(newModel, example);
		}

		newModel.setId(newModel.getId());
		return newModel;
	}

	private String validateAndFormatLongitude(String longitude) {
		if (longitude == null) return null;
		try {
			double value = Double.parseDouble(longitude);
			if (value < -180 || value > 180) return null;

			// 保留两位小数
			BigDecimal longitudeBD = new BigDecimal(longitude);
			longitudeBD = longitudeBD.setScale(2, RoundingMode.HALF_UP);
			return longitudeBD.toString();
		} catch (NumberFormatException e) {
			return null;
		}
	}

	private String validateAndFormatLatitude(String latitude) {
		if (latitude == null) return null;
		try {
			double value = Double.parseDouble(latitude);
			if (value < -90 || value > 90) return null;

			// 保留两位小数
			BigDecimal latitudeBD = new BigDecimal(latitude);
			latitudeBD = latitudeBD.setScale(2, RoundingMode.HALF_UP);
			return latitudeBD.toString();
		} catch (NumberFormatException e) {
			return null;
		}
	}
}
