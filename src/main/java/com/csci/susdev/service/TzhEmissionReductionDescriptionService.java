package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.mapper.TzhEmissionReductionDescriptionCustomMapper;
import com.csci.tzh.mapper.TzhEmissionReductionDescriptionMapper;
import com.csci.tzh.model.TzhEmissionReductionDescription;
import com.csci.tzh.model.TzhEmissionReductionDescriptionExample;
import com.csci.tzh.model.TzhOrgMaterialCarbonFactor;
import com.csci.tzh.model.TzhOrgMaterialCarbonFactorExample;
import com.csci.tzh.qo.TzhEmissionReductionDescriptionQO;
import com.csci.tzh.qo.TzhOrgMaterialCarbonFactorPageableQO;
import com.csci.tzh.vo.TzhEmissionReductionDescriptionVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhEmissionReductionDescriptionService {

	@Autowired
	private TzhEmissionReductionDescriptionMapper mapper;

	@Autowired
	private TzhEmissionReductionDescriptionCustomMapper customMapper;

	public List<TzhEmissionReductionDescription> selectByExample(TzhEmissionReductionDescriptionExample example) {
		return mapper.selectByExample(example);
	}

	public List<TzhEmissionReductionDescriptionVO> list(TzhEmissionReductionDescriptionQO qo) {
		List<TzhEmissionReductionDescriptionVO> lst = customMapper.list(qo.getSiteName(), qo.getProtocol());
		return lst;
	}

	@Transactional(rollbackFor = Exception.class)
	public TzhEmissionReductionDescription save(TzhEmissionReductionDescription newModel) {
		UserInfo currentUser = ContextUtils.getCurrentUser();

		if(newModel.getIsdeleted() == false) {
			if(StringUtils.isNotBlank(newModel.getId())) {
				// 備份已刪除數據
				TzhEmissionReductionDescriptionExample originalExample = new TzhEmissionReductionDescriptionExample();
				TzhEmissionReductionDescriptionExample.Criteria originalCriteria = originalExample.or();
				originalCriteria.andIdEqualTo(newModel.getId());
				List<TzhEmissionReductionDescription> lstOriginalModel = mapper.selectByExample(originalExample);
				if(lstOriginalModel.size() > 0) {
					TzhEmissionReductionDescription originalModel = lstOriginalModel.get(0);
					originalModel.setId(UUID.randomUUID().toString());
					originalModel.setIsdeleted(true);
					originalModel.setDeletedby(currentUser.getUsername());
					originalModel.setDeletedtime(LocalDateTime.now());
					mapper.insertSelective(originalModel);
				}

				// 新增數據(保留舊ID)
				TzhEmissionReductionDescriptionExample newExample = new TzhEmissionReductionDescriptionExample();
				TzhEmissionReductionDescriptionExample.Criteria newCriteria = newExample.or();
				newCriteria.andIdEqualTo(newModel.getId());
				newCriteria.andIsdeletedEqualTo(false);
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				newModel.setDeletedby(null);
				newModel.setDeletedtime(null);
				mapper.deleteByExample(newExample);
				mapper.insertSelective(newModel);
			} else {
				// 新增數據(新建ID)
				newModel.setId(UUID.randomUUID().toString());
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				mapper.insertSelective(newModel);
			}
		} else {
			// 刪除數據
			TzhEmissionReductionDescriptionExample example = new TzhEmissionReductionDescriptionExample();
			TzhEmissionReductionDescriptionExample.Criteria criteria = example.or();
			criteria.andIdEqualTo(newModel.getId());
			newModel.setIsdeleted(true);
			newModel.setDeletedby(currentUser.getUsername());
			newModel.setDeletedtime(LocalDateTime.now());
			mapper.updateByExampleSelective(newModel, example);
		}

		return newModel;
	}
}
