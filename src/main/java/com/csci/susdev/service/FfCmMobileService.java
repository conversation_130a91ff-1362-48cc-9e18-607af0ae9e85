package com.csci.susdev.service;

import cn.hutool.core.util.StrUtil;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.constant.WorkflowControlState;
import com.csci.susdev.mapper.FfCmMobileDetailMapper;
import com.csci.susdev.mapper.FfCmMobileHeadMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.FfCmMobileDetailConverter;
import com.csci.susdev.modelcovt.FfCmMobileHeadConverter;
import com.csci.susdev.modelcovt.FfCmMobileTableDataConverter;
import com.csci.susdev.qo.FfCmFixedQO;
import com.csci.susdev.qo.FfCmMobileQO;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.FfCmMobileDetailVO;
import com.csci.susdev.vo.FfCmMobileHeadVO;
import com.csci.susdev.vo.FfCmMobileTableDataVO;
import com.csci.susdev.vo.IdVersionVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class FfCmMobileService {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(FfCmMobileService.class);
    /**
     * 初始化记录的过期时间
     */
    @Value("${lock.init.head.expire.milis:1000}")
    private long lockInitHeadExpireMilis;
    /**
     * 查询head记录时，获取锁失败循环等待的次数
     */
    @Value("${query.head.wait.loop.times:5}")
    private int lockQueryHeadWaitLoopTimes;
    /**
     * 查询head记录时，获取锁失败循环时每次休眠的时间
     */
    @Value("${query.head.wait.sleep.milis:50}")
    private int lockQueryHeadWaitSleepMilis;

    @Resource
    private FfCmMobileHeadMapper ffCmMobileHeadMapper;

    @Resource
    private FfCmMobileDetailMapper ffCmMobileDetailMapper;

    /**
     * 根据查询条件查询分判商移動源信息
     * 只会查询 active = true 的记录
     *
     * @param organizationId 组织 id
     * @param year           年份
     * @param month          月份
     * @return
     */
    FfCmMobileHead getFfCmMobileHeadBy(String organizationId, Integer year, Integer month) {
        FfCmMobileHeadExample example = new FfCmMobileHeadExample();
        example.or().andOrganizationIdEqualTo(organizationId).andYearEqualTo(year)
                .andMonthEqualTo(month).andIsActiveEqualTo(Boolean.TRUE);
        return ffCmMobileHeadMapper.selectByExample(example).stream().findFirst().orElse(null);
    }

    /**
     * @param qo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public FfCmMobileHead createFfCmMobileHead(FfCmMobileQO qo) {
        FfCmMobileHead ffCmMobileHead = getFfCmMobileHeadBy(qo.getOrganizationId(), qo.getYear(),
                qo.getMonth());
        if (ffCmMobileHead == null) {
            ffCmMobileHead = new FfCmMobileHead();
            ffCmMobileHead.setOrganizationId(qo.getOrganizationId());
            ffCmMobileHead.setYear(qo.getYear());
            ffCmMobileHead.setMonth(qo.getMonth());
            ffCmMobileHead.setIsActive(Boolean.TRUE);
            ffCmMobileHead.setLastUpdateVersion(0);
            ffCmMobileHeadMapper.insertSelective(ffCmMobileHead);
        }
        return ffCmMobileHead;
    }

    /**
     * 根据查询条件查询分判商移動源信息，如果不存在则创建
     *
     * @param ffCmMobileQO 查询条件
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public FfCmMobileHeadVO getOrInit(FfCmMobileQO ffCmMobileQO) {
        checkExist(ffCmMobileQO.getOrganizationId(), "组织编号不能为空");
        checkExist(ffCmMobileQO.getYear(), "年份不能为空");
        if(ObjectUtils.isEmpty(ffCmMobileQO.getMonth())) {
            ffCmMobileQO.setMonth(12);
        }

        if (ffCmMobileQO.getYear() < 1900 || ffCmMobileQO.getYear() > 2100) {
            throw new ServiceException("年份必须在 1900-2100 之间");
        }

        //查询或初始化记录，如果获取失败，直接抛出异常退出
        FfCmMobileHead ffCmMobileHead = getOrInitHeadRecord(ffCmMobileQO);

        // 查询流程审批状态
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(ffCmMobileHead.getId());
        FfCmMobileHeadVO ffCmMobileHeadVO = FfCmMobileHeadConverter.convert(ffCmMobileHead);
        if (Objects.nonNull(workflowControl)) {
            ffCmMobileHeadVO.setWorkflowControlState(workflowControl.getState());
            ffCmMobileHeadVO.setWorkflowControlStateName(Optional.ofNullable(WorkflowControlState.getWorkflowControlState(workflowControl.getState())).map(WorkflowControlState::getName).orElse(null));
        }
        return ffCmMobileHeadVO;
    }

    /**
     * 查询或初始化记录，如果获取失败，直接抛出异常退出
     * @param
     * @return
     */
    private FfCmMobileHead getOrInitHeadRecord(FfCmMobileQO ffCmMobileQO) {
        FfCmMobileHead ffCmMobileHead = getFfCmMobileHeadBy(ffCmMobileQO.getOrganizationId(),
                ffCmMobileQO.getYear(), ffCmMobileQO.getMonth());
        if (Objects.isNull(ffCmMobileHead)) {
            //分布式锁的key：分别表示前缀:业务:组织ID:年:月
            String key = StrUtil.format(SusDevConsts.DISTRIBUTE_LOCK_KEY_FORMAT, SusDevConsts.PROJECT_PREFIX,
                    "ffCmMobileHead:getOrInit", ffCmMobileQO.getOrganizationId(),
                    ffCmMobileQO.getYear(), ffCmMobileQO.getMonth());
            //超时时间1s
            boolean redisLock = RedisLockUtil.lock(key, lockInitHeadExpireMilis);
            if (!redisLock) {
                //获取锁失败，停顿50ms一次，停顿5次
                int count = lockQueryHeadWaitLoopTimes;
                while(count-- > 0){
                    ffCmMobileHead = getFfCmMobileHeadBy(ffCmMobileQO.getOrganizationId(),
                            ffCmMobileQO.getYear(), ffCmMobileQO.getMonth());
                    if(ffCmMobileHead != null){
                        break;
                    }
                    try {
                        Thread.sleep(lockQueryHeadWaitSleepMilis);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }else{
                try{
                    ffCmMobileHead = createFfCmMobileHead(ffCmMobileQO);
                    FfCmMobileHead prevHead = getPrevHead(ffCmMobileQO.getOrganizationId(), ffCmMobileQO.getYear(),
                            ffCmMobileQO.getMonth());
                    if (ObjectUtils.isNotEmpty(prevHead)) {
                        initDetailsWithPrevData(prevHead.getId(), ffCmMobileHead.getId());
                    }
                }
                finally {
                    RedisLockUtil.unlock(key);
                }
            }
        }
        //如果最终没拿到数据，直接返回
        if(ffCmMobileHead == null){
            throw new ServiceException("生成记录失败, 请稍后再尝试！");
        }
        return ffCmMobileHead;
    }

    public FfCmMobileHead getPrevHead(String organizationId, Integer year, Integer month) {
        FfCmMobileHeadExample example = new FfCmMobileHeadExample();
        example.or().andIsActiveEqualTo(true).andOrganizationIdEqualTo(organizationId)
                .andYearEqualTo(year).andMonthLessThan(month);
        example.setOrderByClause("month desc");
        List<FfCmMobileHead> lst = selectByExample(example);
        if(lst.size() > 0) {
            return lst.get(0);
        } else {
            return null;
        }
    }

    void initDetailsWithPrevData(String prevHeadId, String curHeadId) {
        FfCmMobileDetailExample example = new FfCmMobileDetailExample();
        example.or().andHeadIdEqualTo(prevHeadId);
        List<FfCmMobileDetail> details = ffCmMobileDetailMapper.selectByExample(example);
        for(FfCmMobileDetail detail : details) {
            detail.setId(null);
            detail.setHeadId(curHeadId);
            ffCmMobileDetailMapper.insert(detail);
        }
    }

    /**
     * 根据主表id查询分判商移動源明细信息
     *
     * @param headId 分判商移動源头 id
     * @return
     */
    public List<FfCmMobileDetailVO> listFfCmMobileDetail(String headId) {
        checkExist(headId, "分判商移動源主表id不能为空");
        FfCmMobileDetailExample example = new FfCmMobileDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq");
        return ffCmMobileDetailMapper.selectByExample(example).stream().map(new FfCmMobileDetailConverter()::convert).collect(Collectors.toList());
    }

    /**
     * 查询用于前端页面handsontable使用的数据
     *
     * @param headId 分判商移動源头 id
     * @return
     */
    public List<FfCmMobileTableDataVO> listFfCmMobileTable(String headId) {
        checkExist(headId, "分判商移動源主表id不能为空");
        FfCmMobileDetailExample example = new FfCmMobileDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq");
        return ffCmMobileDetailMapper.selectByExample(example).stream().map(FfCmMobileTableDataConverter::convert).collect(Collectors.toList());
    }


    /**
     * proxy for mapper method
     *
     * @param record
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertDetailSelective(FfCmMobileDetail record) {
        return ffCmMobileDetailMapper.insertSelective(record);
    }

    /**
     * 保存分判商移動源明细信息
     *
     * @param ffCmMobileHeadVO 分判商移動源头信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateFfCmMobileWithDetail(FfCmMobileHeadVO ffCmMobileHeadVO) {
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        if (workflowControlService.isNotAllowEdit(ffCmMobileHeadVO.getId())) {
            throw new ServiceException("已经发起审批流程的单据不允许进行修改");
        }
        // 更新主表信息
        updateHead(ffCmMobileHeadVO);
        // 删除明细
        deleteDetailByHeadId(ffCmMobileHeadVO.getId());
        List<FfCmMobileDetail> lstDetails = ffCmMobileHeadVO.getDetails().stream().map(FfCmMobileTableDataConverter::convert).collect(Collectors.toList());

        addDetails(ffCmMobileHeadVO.getId(), lstDetails);
    }

    /**
     * 提交分判商移動源
     *
     * @param idVersionVO
     */
    public void submit(IdVersionVO idVersionVO) {
        // 验证逻辑---------
        checkExist(idVersionVO.getId(), "分判商移動源主表id不能为空");
        checkExist(idVersionVO.getLastUpdateVersion(), "分判商移動源主表版本号不能为空");

        FfCmMobileHead existedRecord = ffCmMobileHeadMapper.selectByPrimaryKey(idVersionVO.getId());
        Optional.ofNullable(existedRecord).orElseThrow(() -> new ServiceException("指定的分判商移動源记录不存在"));
        // 验证逻辑---------

        FfCmMobileHeadExample example = new FfCmMobileHeadExample();
        example.or().andIdEqualTo(idVersionVO.getId()).andLastUpdateVersionEqualTo(idVersionVO.getLastUpdateVersion());

        FfCmMobileHead record = new FfCmMobileHead();
        //record.setApproveStatus(ApproveStatus.SUBMIT.getCode());
        record.setLastUpdateVersion(idVersionVO.getLastUpdateVersion());

        int count = ffCmMobileHeadMapper.updateByExampleSelective(record, example);
        if (count == 0) {
            throw new ServiceException("分判商移動源已经被修改，请刷新后重试");
        }
        // todo 增加流程审批记录
    }

    private void addDetails(String headId, List<FfCmMobileDetail> ffCmMobileDetails) {
        if (CollectionUtils.isEmpty(ffCmMobileDetails)) {
            throw new ServiceException("分判商移動源明细不能为空");
        }
        int seq = 0;
        FfCmMobileDetail lastDetail = ffCmMobileDetails.get(0);
        for (FfCmMobileDetail ffCmMobileDetail : ffCmMobileDetails) {
            /*
            if (StringUtils.isNotBlank(ffCmMobileDetail.getCategory()) && !StringUtils.equals(ffCmMobileDetail.getCategory(), lastDetail.getCategory())) {
                lastDetail = ffCmMobileDetail;
            }
            if (StringUtils.isBlank(ffCmMobileDetail.getCategory())) {
                ffCmMobileDetail.setCategory(lastDetail.getCategory());
            }
             */
            ffCmMobileDetail.setId(UUID.randomUUID().toString());
            ffCmMobileDetail.setHeadId(headId);
            ffCmMobileDetail.setSeq(seq++);
            ffCmMobileDetailMapper.insertSelective(ffCmMobileDetail);
        }
    }

    private void deleteDetailByHeadId(String headId) {
        FfCmMobileDetailExample example = new FfCmMobileDetailExample();
        example.or().andHeadIdEqualTo(headId);
        ffCmMobileDetailMapper.deleteByExample(example);
    }

    private void updateHead(FfCmMobileHeadVO ffCmMobileHeadVO) {
        checkExist(ffCmMobileHeadVO.getId(), "分判商移動源主表id不能为空");
        checkExist(ffCmMobileHeadVO.getOrganizationId(), "组织编号不能为空");
        checkExist(ffCmMobileHeadVO.getLastUpdateVersion(), "版本号不能为空");

        FfCmMobileHeadExample headExample = new FfCmMobileHeadExample();
        headExample.or().andIdEqualTo(ffCmMobileHeadVO.getId()).andLastUpdateVersionEqualTo(ffCmMobileHeadVO.getLastUpdateVersion());

        FfCmMobileHead ffCmMobileHead = FfCmMobileHeadConverter.convert(ffCmMobileHeadVO);
        int updateCount = ffCmMobileHeadMapper.updateByExampleSelective(ffCmMobileHead, headExample);
        if (updateCount == 0) {
            throw new ServiceException("分判商移動源主表信息已经被修改，请刷新后重试");
        }
    }

    /**
     * proxy for mapper method
     *
     * @param id 分判商移動源主表id
     * @return
     */
    public FfCmMobileHead selectByPrimaryKey(String id) {
        return ffCmMobileHeadMapper.selectByPrimaryKey(id);
    }

    public List<FfCmMobileHead> selectByExample(FfCmMobileHeadExample example) {
        return ffCmMobileHeadMapper.selectByExample(example);
    }

}


