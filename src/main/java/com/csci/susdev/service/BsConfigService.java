package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.BsConfigMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.BsConfigConverter;
import com.csci.susdev.qo.BsConfigQO;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.vo.BsConfigVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class BsConfigService {

    @Resource
    private BsConfigMapper bsConfigMapper;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(BsConfig record) {
        return bsConfigMapper.insertSelective(record);
    }

    public BsConfig selectByPrimaryKey(String id) {
        return bsConfigMapper.selectByPrimaryKey(id);
    }

    public BsConfigVO getBsConfig(String id) {
        checkExist(id, "id不能为空");
        BsConfig bsConfig = selectByPrimaryKey(id);
        checkExist(bsConfig, "未找到对应的记录");
        if(bsConfig.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return BsConfigConverter.convertToVO(bsConfig);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveBsConfig(BsConfigVO bsConfigVO) {
        if (StringUtils.isBlank(bsConfigVO.getId())) {
            // 新增

            try {
                return doAdd(bsConfigVO);
            } catch (DuplicateKeyException e) {
                throw new ServiceException("新增失败，可能是数据已存在");
            }
        } else {
            // 修改
            checkExist(bsConfigVO.getLastUpdateVersion(), "更新时版本号不能为空");
            try {
                return doUpdate(bsConfigVO);
            } catch (DuplicateKeyException e) {
                throw new ServiceException("更新失败，可能是数据已存在");
            }
        }

    }

    public ResultPage<BsConfigVO> listBsConfig(BsConfigQO bsConfigQO) {
        BsConfigExample example = new BsConfigExample();

        if(StringUtils.isNotBlank(bsConfigQO.getOrganizationId())) {
            example.or().andOrganizationIdEqualTo(bsConfigQO.getOrganizationId()).andIsDeletedEqualTo(false);
        } else {
            example.or().andIsDeletedEqualTo(false);
        }

        if (StringUtils.isNotBlank(bsConfigQO.getOrderBy())) {
            example.setOrderByClause(NamingConventionUtils.snakeCaseToCamelCase(bsConfigQO.getOrderBy()));
        }

        PageHelper.startPage(bsConfigQO.getCurPage(), bsConfigQO.getPageSize());
        List<BsConfig> bsConfigs = bsConfigMapper.selectByExample(example);
        return new ResultPage<>(bsConfigs, bsConfigs.stream().map(BsConfigConverter::convertToVO).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteBsConfig(String id) {
        BsConfig record = selectByPrimaryKey(id);

        ProtocolDetailExample example = new ProtocolDetailExample();
        example.or().andCarbonEmissionLocationIdEqualTo(id).andIsDeletedEqualTo(false);

        record.setIsDeleted(true);
        bsConfigMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(BsConfigVO bsConfigVO) {
        BsConfig bsConfig = BsConfigConverter.convertToModel(bsConfigVO);
        bsConfigMapper.insertSelective(bsConfig);
        return bsConfig.getId();
    }

    private String doUpdate(BsConfigVO bsConfigVO) {
        BsConfig originalRecord = selectByPrimaryKey(bsConfigVO.getId());
        BsConfig bsConfig = BsConfigConverter.convertToModelWithBase(bsConfigVO, originalRecord);

        BsConfigExample example = new BsConfigExample();
        example.or().andIdEqualTo(bsConfigVO.getId()).andLastUpdateVersionEqualTo(bsConfigVO.getLastUpdateVersion());
        int updateCount = bsConfigMapper.updateByExample(bsConfig, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return bsConfig.getId();
    }

    public List<BsConfig> selectByExample(BsConfigExample example) {
        return bsConfigMapper.selectByExample(example);
    }
}
