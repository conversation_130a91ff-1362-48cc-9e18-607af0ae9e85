package com.csci.susdev.service;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.UserHistoryMapper;
import com.csci.susdev.model.UserHistory;
import com.csci.susdev.model.UserHistoryExample;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
@LogMethod
public class UserHistoryService {

    @Resource
    private UserHistoryMapper userHistoryMapper;

    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(UserHistory record) {
        return userHistoryMapper.insertSelective(record);
    }

    public List<UserHistory> selectByExampleWithBLOBs(UserHistoryExample example) {
        return userHistoryMapper.selectByExampleWithBLOBs(example);
    }

    public List<UserHistory> selectByExample(UserHistoryExample example) {
        return userHistoryMapper.selectByExample(example);
    }
}
