package com.csci.susdev.model;

import java.time.LocalDateTime;

public class SocialPerformanceDetail {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_detail.id
     *
     * @mbg.generated
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_detail.head_id
     *
     * @mbg.generated
     */
    private String headId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_detail.category
     *
     * @mbg.generated
     */
    private String category;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_detail.report_item
     *
     * @mbg.generated
     */
    private String reportItem;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_detail.classification1
     *
     * @mbg.generated
     */
    private String classification1;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_detail.classification2
     *
     * @mbg.generated
     */
    private String classification2;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_detail.classification3
     *
     * @mbg.generated
     */
    private String classification3;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_detail.classification4
     *
     * @mbg.generated
     */
    private String classification4;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_detail.classification5
     *
     * @mbg.generated
     */
    private String classification5;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_detail.classification6
     *
     * @mbg.generated
     */
    private String classification6;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_detail.classification7
     *
     * @mbg.generated
     */
    private String classification7;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_detail.remark
     *
     * @mbg.generated
     */
    private String remark;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_detail.is_header
     *
     * @mbg.generated
     */
    private Boolean header;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_detail.seq
     *
     * @mbg.generated
     */
    private Integer seq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_social_performance_detail.creation_time
     *
     * @mbg.generated
     */
    private LocalDateTime creationTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_detail.id
     *
     * @return the value of t_social_performance_detail.id
     *
     * @mbg.generated
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_detail.id
     *
     * @param id the value for t_social_performance_detail.id
     *
     * @mbg.generated
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_detail.head_id
     *
     * @return the value of t_social_performance_detail.head_id
     *
     * @mbg.generated
     */
    public String getHeadId() {
        return headId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_detail.head_id
     *
     * @param headId the value for t_social_performance_detail.head_id
     *
     * @mbg.generated
     */
    public void setHeadId(String headId) {
        this.headId = headId == null ? null : headId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_detail.category
     *
     * @return the value of t_social_performance_detail.category
     *
     * @mbg.generated
     */
    public String getCategory() {
        return category;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_detail.category
     *
     * @param category the value for t_social_performance_detail.category
     *
     * @mbg.generated
     */
    public void setCategory(String category) {
        this.category = category == null ? null : category.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_detail.report_item
     *
     * @return the value of t_social_performance_detail.report_item
     *
     * @mbg.generated
     */
    public String getReportItem() {
        return reportItem;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_detail.report_item
     *
     * @param reportItem the value for t_social_performance_detail.report_item
     *
     * @mbg.generated
     */
    public void setReportItem(String reportItem) {
        this.reportItem = reportItem == null ? null : reportItem.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_detail.classification1
     *
     * @return the value of t_social_performance_detail.classification1
     *
     * @mbg.generated
     */
    public String getClassification1() {
        return classification1;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_detail.classification1
     *
     * @param classification1 the value for t_social_performance_detail.classification1
     *
     * @mbg.generated
     */
    public void setClassification1(String classification1) {
        this.classification1 = classification1 == null ? null : classification1.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_detail.classification2
     *
     * @return the value of t_social_performance_detail.classification2
     *
     * @mbg.generated
     */
    public String getClassification2() {
        return classification2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_detail.classification2
     *
     * @param classification2 the value for t_social_performance_detail.classification2
     *
     * @mbg.generated
     */
    public void setClassification2(String classification2) {
        this.classification2 = classification2 == null ? null : classification2.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_detail.classification3
     *
     * @return the value of t_social_performance_detail.classification3
     *
     * @mbg.generated
     */
    public String getClassification3() {
        return classification3;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_detail.classification3
     *
     * @param classification3 the value for t_social_performance_detail.classification3
     *
     * @mbg.generated
     */
    public void setClassification3(String classification3) {
        this.classification3 = classification3 == null ? null : classification3.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_detail.classification4
     *
     * @return the value of t_social_performance_detail.classification4
     *
     * @mbg.generated
     */
    public String getClassification4() {
        return classification4;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_detail.classification4
     *
     * @param classification4 the value for t_social_performance_detail.classification4
     *
     * @mbg.generated
     */
    public void setClassification4(String classification4) {
        this.classification4 = classification4 == null ? null : classification4.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_detail.classification5
     *
     * @return the value of t_social_performance_detail.classification5
     *
     * @mbg.generated
     */
    public String getClassification5() {
        return classification5;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_detail.classification5
     *
     * @param classification5 the value for t_social_performance_detail.classification5
     *
     * @mbg.generated
     */
    public void setClassification5(String classification5) {
        this.classification5 = classification5 == null ? null : classification5.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_detail.classification6
     *
     * @return the value of t_social_performance_detail.classification6
     *
     * @mbg.generated
     */
    public String getClassification6() {
        return classification6;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_detail.classification6
     *
     * @param classification6 the value for t_social_performance_detail.classification6
     *
     * @mbg.generated
     */
    public void setClassification6(String classification6) {
        this.classification6 = classification6 == null ? null : classification6.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_detail.classification7
     *
     * @return the value of t_social_performance_detail.classification7
     *
     * @mbg.generated
     */
    public String getClassification7() {
        return classification7;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_detail.classification7
     *
     * @param classification7 the value for t_social_performance_detail.classification7
     *
     * @mbg.generated
     */
    public void setClassification7(String classification7) {
        this.classification7 = classification7 == null ? null : classification7.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_detail.remark
     *
     * @return the value of t_social_performance_detail.remark
     *
     * @mbg.generated
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_detail.remark
     *
     * @param remark the value for t_social_performance_detail.remark
     *
     * @mbg.generated
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_detail.is_header
     *
     * @return the value of t_social_performance_detail.is_header
     *
     * @mbg.generated
     */
    public Boolean getHeader() {
        return header;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_detail.is_header
     *
     * @param header the value for t_social_performance_detail.is_header
     *
     * @mbg.generated
     */
    public void setHeader(Boolean header) {
        this.header = header;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_detail.seq
     *
     * @return the value of t_social_performance_detail.seq
     *
     * @mbg.generated
     */
    public Integer getSeq() {
        return seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_detail.seq
     *
     * @param seq the value for t_social_performance_detail.seq
     *
     * @mbg.generated
     */
    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_social_performance_detail.creation_time
     *
     * @return the value of t_social_performance_detail.creation_time
     *
     * @mbg.generated
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_social_performance_detail.creation_time
     *
     * @param creationTime the value for t_social_performance_detail.creation_time
     *
     * @mbg.generated
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }
}