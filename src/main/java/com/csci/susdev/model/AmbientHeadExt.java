package com.csci.susdev.model;

import java.time.LocalDateTime;

public class AmbientHeadExt {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_head_ext.id
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_head_ext.organization_id
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    private String organizationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_head_ext.year
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    private Integer year;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_head_ext.month
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    private Integer month;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_head_ext.is_active
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    private Boolean isActive;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_head_ext.creation_time
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_head_ext.create_username
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_head_ext.create_user_id
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_head_ext.last_update_time
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_head_ext.last_update_username
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_head_ext.last_update_user_id
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_head_ext.last_update_version
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    private Integer lastUpdateVersion;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_head_ext.id
     *
     * @return the value of t_ambient_head_ext.id
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_head_ext.id
     *
     * @param id the value for t_ambient_head_ext.id
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_head_ext.organization_id
     *
     * @return the value of t_ambient_head_ext.organization_id
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public String getOrganizationId() {
        return organizationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_head_ext.organization_id
     *
     * @param organizationId the value for t_ambient_head_ext.organization_id
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId == null ? null : organizationId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_head_ext.year
     *
     * @return the value of t_ambient_head_ext.year
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public Integer getYear() {
        return year;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_head_ext.year
     *
     * @param year the value for t_ambient_head_ext.year
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public void setYear(Integer year) {
        this.year = year;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_head_ext.month
     *
     * @return the value of t_ambient_head_ext.month
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public Integer getMonth() {
        return month;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_head_ext.month
     *
     * @param month the value for t_ambient_head_ext.month
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public void setMonth(Integer month) {
        this.month = month;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_head_ext.is_active
     *
     * @return the value of t_ambient_head_ext.is_active
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public Boolean getIsActive() {
        return isActive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_head_ext.is_active
     *
     * @param isActive the value for t_ambient_head_ext.is_active
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_head_ext.creation_time
     *
     * @return the value of t_ambient_head_ext.creation_time
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_head_ext.creation_time
     *
     * @param creationTime the value for t_ambient_head_ext.creation_time
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_head_ext.create_username
     *
     * @return the value of t_ambient_head_ext.create_username
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_head_ext.create_username
     *
     * @param createUsername the value for t_ambient_head_ext.create_username
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_head_ext.create_user_id
     *
     * @return the value of t_ambient_head_ext.create_user_id
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_head_ext.create_user_id
     *
     * @param createUserId the value for t_ambient_head_ext.create_user_id
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_head_ext.last_update_time
     *
     * @return the value of t_ambient_head_ext.last_update_time
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_head_ext.last_update_time
     *
     * @param lastUpdateTime the value for t_ambient_head_ext.last_update_time
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_head_ext.last_update_username
     *
     * @return the value of t_ambient_head_ext.last_update_username
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_head_ext.last_update_username
     *
     * @param lastUpdateUsername the value for t_ambient_head_ext.last_update_username
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_head_ext.last_update_user_id
     *
     * @return the value of t_ambient_head_ext.last_update_user_id
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_head_ext.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_ambient_head_ext.last_update_user_id
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_head_ext.last_update_version
     *
     * @return the value of t_ambient_head_ext.last_update_version
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_head_ext.last_update_version
     *
     * @param lastUpdateVersion the value for t_ambient_head_ext.last_update_version
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }
}