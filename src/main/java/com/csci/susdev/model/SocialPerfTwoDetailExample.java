package com.csci.susdev.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class SocialPerfTwoDetailExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    public SocialPerfTwoDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andHeadIdIsNull() {
            addCriterion("head_id is null");
            return (Criteria) this;
        }

        public Criteria andHeadIdIsNotNull() {
            addCriterion("head_id is not null");
            return (Criteria) this;
        }

        public Criteria andHeadIdEqualTo(String value) {
            addCriterion("head_id =", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotEqualTo(String value) {
            addCriterion("head_id <>", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdGreaterThan(String value) {
            addCriterion("head_id >", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdGreaterThanOrEqualTo(String value) {
            addCriterion("head_id >=", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLessThan(String value) {
            addCriterion("head_id <", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLessThanOrEqualTo(String value) {
            addCriterion("head_id <=", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLike(String value) {
            addCriterion("head_id like", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotLike(String value) {
            addCriterion("head_id not like", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdIn(List<String> values) {
            addCriterion("head_id in", values, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotIn(List<String> values) {
            addCriterion("head_id not in", values, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdBetween(String value1, String value2) {
            addCriterion("head_id between", value1, value2, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotBetween(String value1, String value2) {
            addCriterion("head_id not between", value1, value2, "headId");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNull() {
            addCriterion("category is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNotNull() {
            addCriterion("category is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryEqualTo(String value) {
            addCriterion("category =", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotEqualTo(String value) {
            addCriterion("category <>", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThan(String value) {
            addCriterion("category >", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("category >=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThan(String value) {
            addCriterion("category <", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThanOrEqualTo(String value) {
            addCriterion("category <=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLike(String value) {
            addCriterion("category like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotLike(String value) {
            addCriterion("category not like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryIn(List<String> values) {
            addCriterion("category in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotIn(List<String> values) {
            addCriterion("category not in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryBetween(String value1, String value2) {
            addCriterion("category between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotBetween(String value1, String value2) {
            addCriterion("category not between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andReportItemIsNull() {
            addCriterion("report_item is null");
            return (Criteria) this;
        }

        public Criteria andReportItemIsNotNull() {
            addCriterion("report_item is not null");
            return (Criteria) this;
        }

        public Criteria andReportItemEqualTo(String value) {
            addCriterion("report_item =", value, "reportItem");
            return (Criteria) this;
        }

        public Criteria andReportItemNotEqualTo(String value) {
            addCriterion("report_item <>", value, "reportItem");
            return (Criteria) this;
        }

        public Criteria andReportItemGreaterThan(String value) {
            addCriterion("report_item >", value, "reportItem");
            return (Criteria) this;
        }

        public Criteria andReportItemGreaterThanOrEqualTo(String value) {
            addCriterion("report_item >=", value, "reportItem");
            return (Criteria) this;
        }

        public Criteria andReportItemLessThan(String value) {
            addCriterion("report_item <", value, "reportItem");
            return (Criteria) this;
        }

        public Criteria andReportItemLessThanOrEqualTo(String value) {
            addCriterion("report_item <=", value, "reportItem");
            return (Criteria) this;
        }

        public Criteria andReportItemLike(String value) {
            addCriterion("report_item like", value, "reportItem");
            return (Criteria) this;
        }

        public Criteria andReportItemNotLike(String value) {
            addCriterion("report_item not like", value, "reportItem");
            return (Criteria) this;
        }

        public Criteria andReportItemIn(List<String> values) {
            addCriterion("report_item in", values, "reportItem");
            return (Criteria) this;
        }

        public Criteria andReportItemNotIn(List<String> values) {
            addCriterion("report_item not in", values, "reportItem");
            return (Criteria) this;
        }

        public Criteria andReportItemBetween(String value1, String value2) {
            addCriterion("report_item between", value1, value2, "reportItem");
            return (Criteria) this;
        }

        public Criteria andReportItemNotBetween(String value1, String value2) {
            addCriterion("report_item not between", value1, value2, "reportItem");
            return (Criteria) this;
        }

        public Criteria andClassification1IsNull() {
            addCriterion("classification1 is null");
            return (Criteria) this;
        }

        public Criteria andClassification1IsNotNull() {
            addCriterion("classification1 is not null");
            return (Criteria) this;
        }

        public Criteria andClassification1EqualTo(String value) {
            addCriterion("classification1 =", value, "classification1");
            return (Criteria) this;
        }

        public Criteria andClassification1NotEqualTo(String value) {
            addCriterion("classification1 <>", value, "classification1");
            return (Criteria) this;
        }

        public Criteria andClassification1GreaterThan(String value) {
            addCriterion("classification1 >", value, "classification1");
            return (Criteria) this;
        }

        public Criteria andClassification1GreaterThanOrEqualTo(String value) {
            addCriterion("classification1 >=", value, "classification1");
            return (Criteria) this;
        }

        public Criteria andClassification1LessThan(String value) {
            addCriterion("classification1 <", value, "classification1");
            return (Criteria) this;
        }

        public Criteria andClassification1LessThanOrEqualTo(String value) {
            addCriterion("classification1 <=", value, "classification1");
            return (Criteria) this;
        }

        public Criteria andClassification1Like(String value) {
            addCriterion("classification1 like", value, "classification1");
            return (Criteria) this;
        }

        public Criteria andClassification1NotLike(String value) {
            addCriterion("classification1 not like", value, "classification1");
            return (Criteria) this;
        }

        public Criteria andClassification1In(List<String> values) {
            addCriterion("classification1 in", values, "classification1");
            return (Criteria) this;
        }

        public Criteria andClassification1NotIn(List<String> values) {
            addCriterion("classification1 not in", values, "classification1");
            return (Criteria) this;
        }

        public Criteria andClassification1Between(String value1, String value2) {
            addCriterion("classification1 between", value1, value2, "classification1");
            return (Criteria) this;
        }

        public Criteria andClassification1NotBetween(String value1, String value2) {
            addCriterion("classification1 not between", value1, value2, "classification1");
            return (Criteria) this;
        }

        public Criteria andClassification2IsNull() {
            addCriterion("classification2 is null");
            return (Criteria) this;
        }

        public Criteria andClassification2IsNotNull() {
            addCriterion("classification2 is not null");
            return (Criteria) this;
        }

        public Criteria andClassification2EqualTo(String value) {
            addCriterion("classification2 =", value, "classification2");
            return (Criteria) this;
        }

        public Criteria andClassification2NotEqualTo(String value) {
            addCriterion("classification2 <>", value, "classification2");
            return (Criteria) this;
        }

        public Criteria andClassification2GreaterThan(String value) {
            addCriterion("classification2 >", value, "classification2");
            return (Criteria) this;
        }

        public Criteria andClassification2GreaterThanOrEqualTo(String value) {
            addCriterion("classification2 >=", value, "classification2");
            return (Criteria) this;
        }

        public Criteria andClassification2LessThan(String value) {
            addCriterion("classification2 <", value, "classification2");
            return (Criteria) this;
        }

        public Criteria andClassification2LessThanOrEqualTo(String value) {
            addCriterion("classification2 <=", value, "classification2");
            return (Criteria) this;
        }

        public Criteria andClassification2Like(String value) {
            addCriterion("classification2 like", value, "classification2");
            return (Criteria) this;
        }

        public Criteria andClassification2NotLike(String value) {
            addCriterion("classification2 not like", value, "classification2");
            return (Criteria) this;
        }

        public Criteria andClassification2In(List<String> values) {
            addCriterion("classification2 in", values, "classification2");
            return (Criteria) this;
        }

        public Criteria andClassification2NotIn(List<String> values) {
            addCriterion("classification2 not in", values, "classification2");
            return (Criteria) this;
        }

        public Criteria andClassification2Between(String value1, String value2) {
            addCriterion("classification2 between", value1, value2, "classification2");
            return (Criteria) this;
        }

        public Criteria andClassification2NotBetween(String value1, String value2) {
            addCriterion("classification2 not between", value1, value2, "classification2");
            return (Criteria) this;
        }

        public Criteria andClassification3IsNull() {
            addCriterion("classification3 is null");
            return (Criteria) this;
        }

        public Criteria andClassification3IsNotNull() {
            addCriterion("classification3 is not null");
            return (Criteria) this;
        }

        public Criteria andClassification3EqualTo(String value) {
            addCriterion("classification3 =", value, "classification3");
            return (Criteria) this;
        }

        public Criteria andClassification3NotEqualTo(String value) {
            addCriterion("classification3 <>", value, "classification3");
            return (Criteria) this;
        }

        public Criteria andClassification3GreaterThan(String value) {
            addCriterion("classification3 >", value, "classification3");
            return (Criteria) this;
        }

        public Criteria andClassification3GreaterThanOrEqualTo(String value) {
            addCriterion("classification3 >=", value, "classification3");
            return (Criteria) this;
        }

        public Criteria andClassification3LessThan(String value) {
            addCriterion("classification3 <", value, "classification3");
            return (Criteria) this;
        }

        public Criteria andClassification3LessThanOrEqualTo(String value) {
            addCriterion("classification3 <=", value, "classification3");
            return (Criteria) this;
        }

        public Criteria andClassification3Like(String value) {
            addCriterion("classification3 like", value, "classification3");
            return (Criteria) this;
        }

        public Criteria andClassification3NotLike(String value) {
            addCriterion("classification3 not like", value, "classification3");
            return (Criteria) this;
        }

        public Criteria andClassification3In(List<String> values) {
            addCriterion("classification3 in", values, "classification3");
            return (Criteria) this;
        }

        public Criteria andClassification3NotIn(List<String> values) {
            addCriterion("classification3 not in", values, "classification3");
            return (Criteria) this;
        }

        public Criteria andClassification3Between(String value1, String value2) {
            addCriterion("classification3 between", value1, value2, "classification3");
            return (Criteria) this;
        }

        public Criteria andClassification3NotBetween(String value1, String value2) {
            addCriterion("classification3 not between", value1, value2, "classification3");
            return (Criteria) this;
        }

        public Criteria andClassification4IsNull() {
            addCriterion("classification4 is null");
            return (Criteria) this;
        }

        public Criteria andClassification4IsNotNull() {
            addCriterion("classification4 is not null");
            return (Criteria) this;
        }

        public Criteria andClassification4EqualTo(String value) {
            addCriterion("classification4 =", value, "classification4");
            return (Criteria) this;
        }

        public Criteria andClassification4NotEqualTo(String value) {
            addCriterion("classification4 <>", value, "classification4");
            return (Criteria) this;
        }

        public Criteria andClassification4GreaterThan(String value) {
            addCriterion("classification4 >", value, "classification4");
            return (Criteria) this;
        }

        public Criteria andClassification4GreaterThanOrEqualTo(String value) {
            addCriterion("classification4 >=", value, "classification4");
            return (Criteria) this;
        }

        public Criteria andClassification4LessThan(String value) {
            addCriterion("classification4 <", value, "classification4");
            return (Criteria) this;
        }

        public Criteria andClassification4LessThanOrEqualTo(String value) {
            addCriterion("classification4 <=", value, "classification4");
            return (Criteria) this;
        }

        public Criteria andClassification4Like(String value) {
            addCriterion("classification4 like", value, "classification4");
            return (Criteria) this;
        }

        public Criteria andClassification4NotLike(String value) {
            addCriterion("classification4 not like", value, "classification4");
            return (Criteria) this;
        }

        public Criteria andClassification4In(List<String> values) {
            addCriterion("classification4 in", values, "classification4");
            return (Criteria) this;
        }

        public Criteria andClassification4NotIn(List<String> values) {
            addCriterion("classification4 not in", values, "classification4");
            return (Criteria) this;
        }

        public Criteria andClassification4Between(String value1, String value2) {
            addCriterion("classification4 between", value1, value2, "classification4");
            return (Criteria) this;
        }

        public Criteria andClassification4NotBetween(String value1, String value2) {
            addCriterion("classification4 not between", value1, value2, "classification4");
            return (Criteria) this;
        }

        public Criteria andClassification5IsNull() {
            addCriterion("classification5 is null");
            return (Criteria) this;
        }

        public Criteria andClassification5IsNotNull() {
            addCriterion("classification5 is not null");
            return (Criteria) this;
        }

        public Criteria andClassification5EqualTo(String value) {
            addCriterion("classification5 =", value, "classification5");
            return (Criteria) this;
        }

        public Criteria andClassification5NotEqualTo(String value) {
            addCriterion("classification5 <>", value, "classification5");
            return (Criteria) this;
        }

        public Criteria andClassification5GreaterThan(String value) {
            addCriterion("classification5 >", value, "classification5");
            return (Criteria) this;
        }

        public Criteria andClassification5GreaterThanOrEqualTo(String value) {
            addCriterion("classification5 >=", value, "classification5");
            return (Criteria) this;
        }

        public Criteria andClassification5LessThan(String value) {
            addCriterion("classification5 <", value, "classification5");
            return (Criteria) this;
        }

        public Criteria andClassification5LessThanOrEqualTo(String value) {
            addCriterion("classification5 <=", value, "classification5");
            return (Criteria) this;
        }

        public Criteria andClassification5Like(String value) {
            addCriterion("classification5 like", value, "classification5");
            return (Criteria) this;
        }

        public Criteria andClassification5NotLike(String value) {
            addCriterion("classification5 not like", value, "classification5");
            return (Criteria) this;
        }

        public Criteria andClassification5In(List<String> values) {
            addCriterion("classification5 in", values, "classification5");
            return (Criteria) this;
        }

        public Criteria andClassification5NotIn(List<String> values) {
            addCriterion("classification5 not in", values, "classification5");
            return (Criteria) this;
        }

        public Criteria andClassification5Between(String value1, String value2) {
            addCriterion("classification5 between", value1, value2, "classification5");
            return (Criteria) this;
        }

        public Criteria andClassification5NotBetween(String value1, String value2) {
            addCriterion("classification5 not between", value1, value2, "classification5");
            return (Criteria) this;
        }

        public Criteria andClassification6IsNull() {
            addCriterion("classification6 is null");
            return (Criteria) this;
        }

        public Criteria andClassification6IsNotNull() {
            addCriterion("classification6 is not null");
            return (Criteria) this;
        }

        public Criteria andClassification6EqualTo(String value) {
            addCriterion("classification6 =", value, "classification6");
            return (Criteria) this;
        }

        public Criteria andClassification6NotEqualTo(String value) {
            addCriterion("classification6 <>", value, "classification6");
            return (Criteria) this;
        }

        public Criteria andClassification6GreaterThan(String value) {
            addCriterion("classification6 >", value, "classification6");
            return (Criteria) this;
        }

        public Criteria andClassification6GreaterThanOrEqualTo(String value) {
            addCriterion("classification6 >=", value, "classification6");
            return (Criteria) this;
        }

        public Criteria andClassification6LessThan(String value) {
            addCriterion("classification6 <", value, "classification6");
            return (Criteria) this;
        }

        public Criteria andClassification6LessThanOrEqualTo(String value) {
            addCriterion("classification6 <=", value, "classification6");
            return (Criteria) this;
        }

        public Criteria andClassification6Like(String value) {
            addCriterion("classification6 like", value, "classification6");
            return (Criteria) this;
        }

        public Criteria andClassification6NotLike(String value) {
            addCriterion("classification6 not like", value, "classification6");
            return (Criteria) this;
        }

        public Criteria andClassification6In(List<String> values) {
            addCriterion("classification6 in", values, "classification6");
            return (Criteria) this;
        }

        public Criteria andClassification6NotIn(List<String> values) {
            addCriterion("classification6 not in", values, "classification6");
            return (Criteria) this;
        }

        public Criteria andClassification6Between(String value1, String value2) {
            addCriterion("classification6 between", value1, value2, "classification6");
            return (Criteria) this;
        }

        public Criteria andClassification6NotBetween(String value1, String value2) {
            addCriterion("classification6 not between", value1, value2, "classification6");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andHeaderIsNull() {
            addCriterion("is_header is null");
            return (Criteria) this;
        }

        public Criteria andHeaderIsNotNull() {
            addCriterion("is_header is not null");
            return (Criteria) this;
        }

        public Criteria andHeaderEqualTo(Boolean value) {
            addCriterion("is_header =", value, "header");
            return (Criteria) this;
        }

        public Criteria andHeaderNotEqualTo(Boolean value) {
            addCriterion("is_header <>", value, "header");
            return (Criteria) this;
        }

        public Criteria andHeaderGreaterThan(Boolean value) {
            addCriterion("is_header >", value, "header");
            return (Criteria) this;
        }

        public Criteria andHeaderGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_header >=", value, "header");
            return (Criteria) this;
        }

        public Criteria andHeaderLessThan(Boolean value) {
            addCriterion("is_header <", value, "header");
            return (Criteria) this;
        }

        public Criteria andHeaderLessThanOrEqualTo(Boolean value) {
            addCriterion("is_header <=", value, "header");
            return (Criteria) this;
        }

        public Criteria andHeaderIn(List<Boolean> values) {
            addCriterion("is_header in", values, "header");
            return (Criteria) this;
        }

        public Criteria andHeaderNotIn(List<Boolean> values) {
            addCriterion("is_header not in", values, "header");
            return (Criteria) this;
        }

        public Criteria andHeaderBetween(Boolean value1, Boolean value2) {
            addCriterion("is_header between", value1, value2, "header");
            return (Criteria) this;
        }

        public Criteria andHeaderNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_header not between", value1, value2, "header");
            return (Criteria) this;
        }

        public Criteria andSeqIsNull() {
            addCriterion("seq is null");
            return (Criteria) this;
        }

        public Criteria andSeqIsNotNull() {
            addCriterion("seq is not null");
            return (Criteria) this;
        }

        public Criteria andSeqEqualTo(Integer value) {
            addCriterion("seq =", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotEqualTo(Integer value) {
            addCriterion("seq <>", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqGreaterThan(Integer value) {
            addCriterion("seq >", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("seq >=", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqLessThan(Integer value) {
            addCriterion("seq <", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqLessThanOrEqualTo(Integer value) {
            addCriterion("seq <=", value, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqIn(List<Integer> values) {
            addCriterion("seq in", values, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotIn(List<Integer> values) {
            addCriterion("seq not in", values, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqBetween(Integer value1, Integer value2) {
            addCriterion("seq between", value1, value2, "seq");
            return (Criteria) this;
        }

        public Criteria andSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("seq not between", value1, value2, "seq");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNull() {
            addCriterion("creation_time is null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNotNull() {
            addCriterion("creation_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeEqualTo(LocalDateTime value) {
            addCriterion("creation_time =", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
            addCriterion("creation_time <>", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
            addCriterion("creation_time >", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time >=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThan(LocalDateTime value) {
            addCriterion("creation_time <", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time <=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIn(List<LocalDateTime> values) {
            addCriterion("creation_time in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
            addCriterion("creation_time not in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time not between", value1, value2, "creationTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}