package com.csci.susdev.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class EmailActivityExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	public EmailActivityExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andFromIsNull() {
			addCriterion("from is null");
			return (Criteria) this;
		}

		public Criteria andFromIsNotNull() {
			addCriterion("from is not null");
			return (Criteria) this;
		}

		public Criteria andFromEqualTo(String value) {
			addCriterion("from =", value, "from");
			return (Criteria) this;
		}

		public Criteria andFromNotEqualTo(String value) {
			addCriterion("from <>", value, "from");
			return (Criteria) this;
		}

		public Criteria andFromGreaterThan(String value) {
			addCriterion("from >", value, "from");
			return (Criteria) this;
		}

		public Criteria andFromGreaterThanOrEqualTo(String value) {
			addCriterion("from >=", value, "from");
			return (Criteria) this;
		}

		public Criteria andFromLessThan(String value) {
			addCriterion("from <", value, "from");
			return (Criteria) this;
		}

		public Criteria andFromLessThanOrEqualTo(String value) {
			addCriterion("from <=", value, "from");
			return (Criteria) this;
		}

		public Criteria andFromLike(String value) {
			addCriterion("from like", value, "from");
			return (Criteria) this;
		}

		public Criteria andFromNotLike(String value) {
			addCriterion("from not like", value, "from");
			return (Criteria) this;
		}

		public Criteria andFromIn(List<String> values) {
			addCriterion("from in", values, "from");
			return (Criteria) this;
		}

		public Criteria andFromNotIn(List<String> values) {
			addCriterion("from not in", values, "from");
			return (Criteria) this;
		}

		public Criteria andFromBetween(String value1, String value2) {
			addCriterion("from between", value1, value2, "from");
			return (Criteria) this;
		}

		public Criteria andFromNotBetween(String value1, String value2) {
			addCriterion("from not between", value1, value2, "from");
			return (Criteria) this;
		}

		public Criteria andToIsNull() {
			addCriterion("to is null");
			return (Criteria) this;
		}

		public Criteria andToIsNotNull() {
			addCriterion("to is not null");
			return (Criteria) this;
		}

		public Criteria andToEqualTo(String value) {
			addCriterion("to =", value, "to");
			return (Criteria) this;
		}

		public Criteria andToNotEqualTo(String value) {
			addCriterion("to <>", value, "to");
			return (Criteria) this;
		}

		public Criteria andToGreaterThan(String value) {
			addCriterion("to >", value, "to");
			return (Criteria) this;
		}

		public Criteria andToGreaterThanOrEqualTo(String value) {
			addCriterion("to >=", value, "to");
			return (Criteria) this;
		}

		public Criteria andToLessThan(String value) {
			addCriterion("to <", value, "to");
			return (Criteria) this;
		}

		public Criteria andToLessThanOrEqualTo(String value) {
			addCriterion("to <=", value, "to");
			return (Criteria) this;
		}

		public Criteria andToLike(String value) {
			addCriterion("to like", value, "to");
			return (Criteria) this;
		}

		public Criteria andToNotLike(String value) {
			addCriterion("to not like", value, "to");
			return (Criteria) this;
		}

		public Criteria andToIn(List<String> values) {
			addCriterion("to in", values, "to");
			return (Criteria) this;
		}

		public Criteria andToNotIn(List<String> values) {
			addCriterion("to not in", values, "to");
			return (Criteria) this;
		}

		public Criteria andToBetween(String value1, String value2) {
			addCriterion("to between", value1, value2, "to");
			return (Criteria) this;
		}

		public Criteria andToNotBetween(String value1, String value2) {
			addCriterion("to not between", value1, value2, "to");
			return (Criteria) this;
		}

		public Criteria andTitleIsNull() {
			addCriterion("title is null");
			return (Criteria) this;
		}

		public Criteria andTitleIsNotNull() {
			addCriterion("title is not null");
			return (Criteria) this;
		}

		public Criteria andTitleEqualTo(String value) {
			addCriterion("title =", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleNotEqualTo(String value) {
			addCriterion("title <>", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleGreaterThan(String value) {
			addCriterion("title >", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleGreaterThanOrEqualTo(String value) {
			addCriterion("title >=", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleLessThan(String value) {
			addCriterion("title <", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleLessThanOrEqualTo(String value) {
			addCriterion("title <=", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleLike(String value) {
			addCriterion("title like", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleNotLike(String value) {
			addCriterion("title not like", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleIn(List<String> values) {
			addCriterion("title in", values, "title");
			return (Criteria) this;
		}

		public Criteria andTitleNotIn(List<String> values) {
			addCriterion("title not in", values, "title");
			return (Criteria) this;
		}

		public Criteria andTitleBetween(String value1, String value2) {
			addCriterion("title between", value1, value2, "title");
			return (Criteria) this;
		}

		public Criteria andTitleNotBetween(String value1, String value2) {
			addCriterion("title not between", value1, value2, "title");
			return (Criteria) this;
		}

		public Criteria andContentIsNull() {
			addCriterion("content is null");
			return (Criteria) this;
		}

		public Criteria andContentIsNotNull() {
			addCriterion("content is not null");
			return (Criteria) this;
		}

		public Criteria andContentEqualTo(String value) {
			addCriterion("content =", value, "content");
			return (Criteria) this;
		}

		public Criteria andContentNotEqualTo(String value) {
			addCriterion("content <>", value, "content");
			return (Criteria) this;
		}

		public Criteria andContentGreaterThan(String value) {
			addCriterion("content >", value, "content");
			return (Criteria) this;
		}

		public Criteria andContentGreaterThanOrEqualTo(String value) {
			addCriterion("content >=", value, "content");
			return (Criteria) this;
		}

		public Criteria andContentLessThan(String value) {
			addCriterion("content <", value, "content");
			return (Criteria) this;
		}

		public Criteria andContentLessThanOrEqualTo(String value) {
			addCriterion("content <=", value, "content");
			return (Criteria) this;
		}

		public Criteria andContentLike(String value) {
			addCriterion("content like", value, "content");
			return (Criteria) this;
		}

		public Criteria andContentNotLike(String value) {
			addCriterion("content not like", value, "content");
			return (Criteria) this;
		}

		public Criteria andContentIn(List<String> values) {
			addCriterion("content in", values, "content");
			return (Criteria) this;
		}

		public Criteria andContentNotIn(List<String> values) {
			addCriterion("content not in", values, "content");
			return (Criteria) this;
		}

		public Criteria andContentBetween(String value1, String value2) {
			addCriterion("content between", value1, value2, "content");
			return (Criteria) this;
		}

		public Criteria andContentNotBetween(String value1, String value2) {
			addCriterion("content not between", value1, value2, "content");
			return (Criteria) this;
		}

		public Criteria andIsSentIsNull() {
			addCriterion("is_sent is null");
			return (Criteria) this;
		}

		public Criteria andIsSentIsNotNull() {
			addCriterion("is_sent is not null");
			return (Criteria) this;
		}

		public Criteria andIsSentEqualTo(Boolean value) {
			addCriterion("is_sent =", value, "isSent");
			return (Criteria) this;
		}

		public Criteria andIsSentNotEqualTo(Boolean value) {
			addCriterion("is_sent <>", value, "isSent");
			return (Criteria) this;
		}

		public Criteria andIsSentGreaterThan(Boolean value) {
			addCriterion("is_sent >", value, "isSent");
			return (Criteria) this;
		}

		public Criteria andIsSentGreaterThanOrEqualTo(Boolean value) {
			addCriterion("is_sent >=", value, "isSent");
			return (Criteria) this;
		}

		public Criteria andIsSentLessThan(Boolean value) {
			addCriterion("is_sent <", value, "isSent");
			return (Criteria) this;
		}

		public Criteria andIsSentLessThanOrEqualTo(Boolean value) {
			addCriterion("is_sent <=", value, "isSent");
			return (Criteria) this;
		}

		public Criteria andIsSentIn(List<Boolean> values) {
			addCriterion("is_sent in", values, "isSent");
			return (Criteria) this;
		}

		public Criteria andIsSentNotIn(List<Boolean> values) {
			addCriterion("is_sent not in", values, "isSent");
			return (Criteria) this;
		}

		public Criteria andIsSentBetween(Boolean value1, Boolean value2) {
			addCriterion("is_sent between", value1, value2, "isSent");
			return (Criteria) this;
		}

		public Criteria andIsSentNotBetween(Boolean value1, Boolean value2) {
			addCriterion("is_sent not between", value1, value2, "isSent");
			return (Criteria) this;
		}

		public Criteria andTryTimesIsNull() {
			addCriterion("try_times is null");
			return (Criteria) this;
		}

		public Criteria andTryTimesIsNotNull() {
			addCriterion("try_times is not null");
			return (Criteria) this;
		}

		public Criteria andTryTimesEqualTo(Integer value) {
			addCriterion("try_times =", value, "tryTimes");
			return (Criteria) this;
		}

		public Criteria andTryTimesNotEqualTo(Integer value) {
			addCriterion("try_times <>", value, "tryTimes");
			return (Criteria) this;
		}

		public Criteria andTryTimesGreaterThan(Integer value) {
			addCriterion("try_times >", value, "tryTimes");
			return (Criteria) this;
		}

		public Criteria andTryTimesGreaterThanOrEqualTo(Integer value) {
			addCriterion("try_times >=", value, "tryTimes");
			return (Criteria) this;
		}

		public Criteria andTryTimesLessThan(Integer value) {
			addCriterion("try_times <", value, "tryTimes");
			return (Criteria) this;
		}

		public Criteria andTryTimesLessThanOrEqualTo(Integer value) {
			addCriterion("try_times <=", value, "tryTimes");
			return (Criteria) this;
		}

		public Criteria andTryTimesIn(List<Integer> values) {
			addCriterion("try_times in", values, "tryTimes");
			return (Criteria) this;
		}

		public Criteria andTryTimesNotIn(List<Integer> values) {
			addCriterion("try_times not in", values, "tryTimes");
			return (Criteria) this;
		}

		public Criteria andTryTimesBetween(Integer value1, Integer value2) {
			addCriterion("try_times between", value1, value2, "tryTimes");
			return (Criteria) this;
		}

		public Criteria andTryTimesNotBetween(Integer value1, Integer value2) {
			addCriterion("try_times not between", value1, value2, "tryTimes");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNull() {
			addCriterion("creation_time is null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNotNull() {
			addCriterion("creation_time is not null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeEqualTo(LocalDateTime value) {
			addCriterion("creation_time =", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
			addCriterion("creation_time <>", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
			addCriterion("creation_time >", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("creation_time >=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThan(LocalDateTime value) {
			addCriterion("creation_time <", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("creation_time <=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIn(List<LocalDateTime> values) {
			addCriterion("creation_time in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
			addCriterion("creation_time not in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("creation_time between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("creation_time not between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNull() {
			addCriterion("last_update_time is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNotNull() {
			addCriterion("last_update_time is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
			addCriterion("last_update_time =", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
			addCriterion("last_update_time <>", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
			addCriterion("last_update_time >", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("last_update_time >=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
			addCriterion("last_update_time <", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("last_update_time <=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
			addCriterion("last_update_time in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
			addCriterion("last_update_time not in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_email_activity
     *
     * @mbg.generated do_not_delete_during_merge Tue Oct 10 13:27:59 HKT 2023
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}