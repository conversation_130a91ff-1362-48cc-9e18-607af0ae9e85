package com.csci.susdev.model;

import com.github.pagehelper.Page;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Generated Comments
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 11/9/2019
 */
@Schema(name = "ResultPage")
public class ResultPage<T> extends ResultList<T> {
    //当前页
    @Schema(description = "当前页")
    private int pageNum;
    //每页的数量
    @Schema(description = "每页的数量")
    private int pageSize;
    //当前页的数量
    @Schema(description = "当前页的数量")
    private int size;
    //总记录数
    @Schema(description = "总记录数")
    private long total;
    //总页数
    @Schema(description = "总页数")
    private int pages;

    public ResultPage(List<?> page) {
        this(page, false);
    }

    public ResultPage(List<?> page, Boolean sameType) {
        initPage(page);
        if (sameType) {
            this.setList((List<T>) page);
        }
    }

    public ResultPage(List<?> page, List<T> list) {
        initPage(page);
        this.setList(list);
    }

    public ResultPage(int code, String msg) {
        setCode(code);
        setMsg(msg);
    }

    public void initPage(List<?> list) {
        if (list instanceof Page) {
            Page<?> page = (Page<?>) list;
            this.pageNum = page.getPageNum();
            this.pageSize = page.getPageSize();
            this.pages = page.getPages();
            this.size = page.size();
            this.total = page.getTotal();
        }
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

}
