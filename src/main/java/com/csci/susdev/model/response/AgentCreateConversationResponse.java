package com.csci.susdev.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class AgentCreateConversationResponse {

    @JsonProperty("BaseResp")
    private String baseResp;

    @JsonProperty("Conversation")
    private Conversation conversation;

    @Data
    public static class Conversation {
        @JsonProperty("AppConversationID")
        private String appConversationID;
        @JsonProperty("ConversationName")
        private String ConversationName;
        @JsonProperty("CreateTime")
        private String CreateTime;
        @JsonProperty("LastChatTime")
        private String LastChatTime;
        @JsonProperty("EmptyConversation")
        private String EmptyConversation;
    }
}
