package com.csci.susdev.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class AgentMessageInfoResponse {

    @JsonProperty("BaseResp")
    private String baseResp;

    @JsonProperty("MessageInfo")
    private MessageInfo messageInfo;

    @Data
    public static class MessageInfo {
        @JsonProperty("ConversationID")
        private String ConversationID;
        @JsonProperty("Inputs")
        private String Inputs;
        @JsonProperty("OtherAnswers")
        private List<String> OtherAnswers;
        @JsonProperty("Query")
        private String Query;
        @JsonProperty("QueryExtends")
        private String QueryExtends;
        @JsonProperty("QueryID")
        private String QueryID;
        @JsonProperty("SendByTrigger")
        private String SendByTrigger;
        @JsonProperty("AnswerInfo")
        private AnswerInfo answerInfo;

        @Data
        public static class AnswerInfo {
            @JsonProperty("Answer")
            private String answer;
            @JsonProperty("CreatedTime")
            private String CreatedTime;
            @JsonProperty("Latency")
            private String Latency;
            @JsonProperty("Like")
            private String Like;
            @JsonProperty("MessageID")
            private String MessageID;
            @JsonProperty("RetrieverResource")
            private String RetrieverResource;
            @JsonProperty("TaskID")
            private String TaskID;
            @JsonProperty("TotalTokens")
            private String TotalTokens;
            @JsonProperty("TracingJsonStr")
            private String TracingJsonStr;


        }
    }
}
