package com.csci.susdev.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class TiOcrResponse {

    /**
     * 错误码
     */
    @JsonProperty("error_code")
    private Integer errorCode;

    /**
     * 错误码消息
     */
    @JsonProperty("error_message")
    private String errorMessage;

    /**
     * 相应请求的session标识符，可用于结果查询
     */
    @JsonProperty("session_id")
    private String sessionId;

    @JsonProperty("recognize_list")
    private List<RecognizeResult> recognizeList;

    @Data
    public static class RecognizeResult {

        /**
         * 图片旋转角度(角度制)，文本的水平方向为0°；顺时针为正，逆时针为负
         */
        private Double angle;

        /**
         * 识别出的OCR信息
         */
        @JsonProperty("item_content")
        private ItemContent itemContent;

        @Data
        public static class ItemContent {

            @JsonProperty("item_list")
            private List<Item> itemList;

            @Data
            public static class Item {

                /**
                 * 实体编号
                 */
                private Integer id;

                /**
                 * 当item_type为key时，该字段为标准key名；其他情况下为字段名称（即key）
                 */
                private String name;

                /**
                 * 当item_type为key时，该字段为原文key名；其他情况下为字段内容（即value）
                 */
                private String content;

                /**
                 * 实体类型(ocr/kv/kv-unhinted/key/row/group)
                 * 1、为ocr时，表示是检测识别结果
                 * 2、为key时，表示是智能结构化的key字段
                 * 3、为kv时，表示是被keyDict命中的智能结构化value字段
                 * 4、为kv-unhinted时，表示是未被keyDict命中的智能结构化value字段
                 * 5、为row时，表示存储的是聚合信息（比如图片中表格里的一行），relation中存储了相关value的id
                 * 6、为group时，表示存储的是组信息（比如图片中的一个表格），relation中存储了相关value的id
                 */
                @JsonProperty("item_type")
                private String itemType;

                /**
                 * 字段内容在原图中的矩形框坐标, 左上角(x,y)，width为框宽，height为框高
                 */
                private Coord coord;

                /**
                 * 字段内容在原图中的四点框坐标，左上角(x1，y1),右上角(x2,y2),右下角(x3,y3),左下角(x4,y4)
                 */
                private Vertex vertex;

                /**
                 * 字段内容的置信度
                 */
                private Double conf;

                /**
                 * 与当前实体有关联的实体id。目前包括两种关联类型
                 * 1、结构化结果中value和key的关联
                 * 2、结构化结果和ocr结果的关联。每种关联相关的实体id需要通过对应实体的item_type区分。
                 */
                private Relation relation;

                /**
                 * 字段内容对应的单字信息
                 */
                @JsonProperty("word_info")
                private WordInfo wordInfo;

                @Data
                public static class Vertex {

                    /**
                     * x1
                     */
                    private Integer x1;

                    /**
                     * y1
                     */
                    private Integer y1;

                    /**
                     * x2
                     */
                    private Integer x2;

                    /**
                     * y2
                     */
                    private Integer y2;

                    /**
                     * x3
                     */
                    private Integer x3;

                    /**
                     * y3
                     */
                    private Integer y3;

                    /**
                     * x4
                     */
                    private Integer x4;

                    /**
                     * y4
                     */
                    private Integer y4;
                }

                @Data
                public static class Coord {

                    /**
                     * x
                     */
                    private Integer x;

                    /**
                     * y
                     */
                    private Integer y;

                    /**
                     * width
                     */
                    private Integer width;

                    /**
                     * height
                     */
                    private Integer height;
                }

                @Data
                public static class WordInfo {

                    @JsonProperty("word_list")
                    private List<Word> wordList;

                    @Data
                    public static class Word {

                        /**
                         * 单字内容
                         */
                        private String content;

                        /**
                         * 单字在原图中的四点框坐标，左上角(x1，y1),右上角(x2,y2),右下角(x3,y3),左下角(x4,y4)
                         */
                        private Vertex vertex;

                        /**
                         * 单字置信度
                         */
                        private Double conf;

                        private List<?> candidates;
                    }
                }

                @Data
                public static class Relation {

                    /**
                     * relations
                     */
                    private List<Integer> relations;
                }
            }
        }
    }
}
