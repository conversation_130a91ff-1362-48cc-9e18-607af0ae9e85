package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FcFactorConversion {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_factor_conversion.id
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_factor_conversion.greenhouse_gas_types
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    private String greenhouseGasTypes;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_factor_conversion.gwp_value
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    private BigDecimal gwpValue;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_factor_conversion.unit
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    private String unit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_factor_conversion.data_source
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    private String dataSource;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_factor_conversion.version
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    private String version;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_factor_conversion.creation_time
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_factor_conversion.create_username
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_factor_conversion.create_user_id
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_factor_conversion.last_update_time
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_factor_conversion.last_update_username
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_factor_conversion.last_update_user_id
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_factor_conversion.last_update_version
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_factor_conversion.is_deleted
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    private Boolean isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_factor_conversion.id
     *
     * @return the value of t_fc_factor_conversion.id
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_factor_conversion.id
     *
     * @param id the value for t_fc_factor_conversion.id
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_factor_conversion.greenhouse_gas_types
     *
     * @return the value of t_fc_factor_conversion.greenhouse_gas_types
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public String getGreenhouseGasTypes() {
        return greenhouseGasTypes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_factor_conversion.greenhouse_gas_types
     *
     * @param greenhouseGasTypes the value for t_fc_factor_conversion.greenhouse_gas_types
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public void setGreenhouseGasTypes(String greenhouseGasTypes) {
        this.greenhouseGasTypes = greenhouseGasTypes == null ? null : greenhouseGasTypes.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_factor_conversion.gwp_value
     *
     * @return the value of t_fc_factor_conversion.gwp_value
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public BigDecimal getGwpValue() {
        return gwpValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_factor_conversion.gwp_value
     *
     * @param gwpValue the value for t_fc_factor_conversion.gwp_value
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public void setGwpValue(BigDecimal gwpValue) {
        this.gwpValue = gwpValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_factor_conversion.unit
     *
     * @return the value of t_fc_factor_conversion.unit
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public String getUnit() {
        return unit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_factor_conversion.unit
     *
     * @param unit the value for t_fc_factor_conversion.unit
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_factor_conversion.data_source
     *
     * @return the value of t_fc_factor_conversion.data_source
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public String getDataSource() {
        return dataSource;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_factor_conversion.data_source
     *
     * @param dataSource the value for t_fc_factor_conversion.data_source
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public void setDataSource(String dataSource) {
        this.dataSource = dataSource == null ? null : dataSource.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_factor_conversion.version
     *
     * @return the value of t_fc_factor_conversion.version
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public String getVersion() {
        return version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_factor_conversion.version
     *
     * @param version the value for t_fc_factor_conversion.version
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public void setVersion(String version) {
        this.version = version == null ? null : version.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_factor_conversion.creation_time
     *
     * @return the value of t_fc_factor_conversion.creation_time
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_factor_conversion.creation_time
     *
     * @param creationTime the value for t_fc_factor_conversion.creation_time
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_factor_conversion.create_username
     *
     * @return the value of t_fc_factor_conversion.create_username
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_factor_conversion.create_username
     *
     * @param createUsername the value for t_fc_factor_conversion.create_username
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_factor_conversion.create_user_id
     *
     * @return the value of t_fc_factor_conversion.create_user_id
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_factor_conversion.create_user_id
     *
     * @param createUserId the value for t_fc_factor_conversion.create_user_id
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_factor_conversion.last_update_time
     *
     * @return the value of t_fc_factor_conversion.last_update_time
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_factor_conversion.last_update_time
     *
     * @param lastUpdateTime the value for t_fc_factor_conversion.last_update_time
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_factor_conversion.last_update_username
     *
     * @return the value of t_fc_factor_conversion.last_update_username
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_factor_conversion.last_update_username
     *
     * @param lastUpdateUsername the value for t_fc_factor_conversion.last_update_username
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_factor_conversion.last_update_user_id
     *
     * @return the value of t_fc_factor_conversion.last_update_user_id
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_factor_conversion.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_fc_factor_conversion.last_update_user_id
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_factor_conversion.last_update_version
     *
     * @return the value of t_fc_factor_conversion.last_update_version
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_factor_conversion.last_update_version
     *
     * @param lastUpdateVersion the value for t_fc_factor_conversion.last_update_version
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_factor_conversion.is_deleted
     *
     * @return the value of t_fc_factor_conversion.is_deleted
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_factor_conversion.is_deleted
     *
     * @param isDeleted the value for t_fc_factor_conversion.is_deleted
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
}