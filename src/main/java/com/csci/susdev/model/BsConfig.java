package com.csci.susdev.model;

import java.time.LocalDateTime;

public class BsConfig {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_bs_config.id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_bs_config.organization_id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    private String organizationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_bs_config.protocol_id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    private String protocolId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_bs_config.carbon_emission_location_id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    private String carbonEmissionLocationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_bs_config.emission_target
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    private String emissionTarget;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_bs_config.creation_time
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_bs_config.create_username
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_bs_config.create_user_id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_bs_config.last_update_time
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_bs_config.last_update_username
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_bs_config.last_update_user_id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_bs_config.last_update_version
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_bs_config.is_deleted
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    private Boolean isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_bs_config.id
     *
     * @return the value of t_bs_config.id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_bs_config.id
     *
     * @param id the value for t_bs_config.id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_bs_config.organization_id
     *
     * @return the value of t_bs_config.organization_id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public String getOrganizationId() {
        return organizationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_bs_config.organization_id
     *
     * @param organizationId the value for t_bs_config.organization_id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId == null ? null : organizationId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_bs_config.protocol_id
     *
     * @return the value of t_bs_config.protocol_id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public String getProtocolId() {
        return protocolId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_bs_config.protocol_id
     *
     * @param protocolId the value for t_bs_config.protocol_id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public void setProtocolId(String protocolId) {
        this.protocolId = protocolId == null ? null : protocolId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_bs_config.carbon_emission_location_id
     *
     * @return the value of t_bs_config.carbon_emission_location_id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public String getCarbonEmissionLocationId() {
        return carbonEmissionLocationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_bs_config.carbon_emission_location_id
     *
     * @param carbonEmissionLocationId the value for t_bs_config.carbon_emission_location_id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public void setCarbonEmissionLocationId(String carbonEmissionLocationId) {
        this.carbonEmissionLocationId = carbonEmissionLocationId == null ? null : carbonEmissionLocationId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_bs_config.emission_target
     *
     * @return the value of t_bs_config.emission_target
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public String getEmissionTarget() {
        return emissionTarget;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_bs_config.emission_target
     *
     * @param emissionTarget the value for t_bs_config.emission_target
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public void setEmissionTarget(String emissionTarget) {
        this.emissionTarget = emissionTarget == null ? null : emissionTarget.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_bs_config.creation_time
     *
     * @return the value of t_bs_config.creation_time
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_bs_config.creation_time
     *
     * @param creationTime the value for t_bs_config.creation_time
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_bs_config.create_username
     *
     * @return the value of t_bs_config.create_username
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_bs_config.create_username
     *
     * @param createUsername the value for t_bs_config.create_username
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_bs_config.create_user_id
     *
     * @return the value of t_bs_config.create_user_id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_bs_config.create_user_id
     *
     * @param createUserId the value for t_bs_config.create_user_id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_bs_config.last_update_time
     *
     * @return the value of t_bs_config.last_update_time
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_bs_config.last_update_time
     *
     * @param lastUpdateTime the value for t_bs_config.last_update_time
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_bs_config.last_update_username
     *
     * @return the value of t_bs_config.last_update_username
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_bs_config.last_update_username
     *
     * @param lastUpdateUsername the value for t_bs_config.last_update_username
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_bs_config.last_update_user_id
     *
     * @return the value of t_bs_config.last_update_user_id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_bs_config.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_bs_config.last_update_user_id
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_bs_config.last_update_version
     *
     * @return the value of t_bs_config.last_update_version
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_bs_config.last_update_version
     *
     * @param lastUpdateVersion the value for t_bs_config.last_update_version
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_bs_config.is_deleted
     *
     * @return the value of t_bs_config.is_deleted
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_bs_config.is_deleted
     *
     * @param isDeleted the value for t_bs_config.is_deleted
     *
     * @mbg.generated Wed Jun 05 12:21:28 HKT 2024
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
}