package com.csci.susdev.model;

import java.time.LocalDateTime;

public class RoleOrganization {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_role_organization.id
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_role_organization.role_id
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    private String roleId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_role_organization.organization_id
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    private String organizationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_role_organization.creation_time
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_role_organization.create_username
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_role_organization.create_user_id
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_role_organization.last_update_time
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_role_organization.last_update_username
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_role_organization.last_update_user_id
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_role_organization.is_deleted
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    private Boolean isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_role_organization.id
     *
     * @return the value of t_role_organization.id
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_role_organization.id
     *
     * @param id the value for t_role_organization.id
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_role_organization.role_id
     *
     * @return the value of t_role_organization.role_id
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public String getRoleId() {
        return roleId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_role_organization.role_id
     *
     * @param roleId the value for t_role_organization.role_id
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public void setRoleId(String roleId) {
        this.roleId = roleId == null ? null : roleId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_role_organization.organization_id
     *
     * @return the value of t_role_organization.organization_id
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public String getOrganizationId() {
        return organizationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_role_organization.organization_id
     *
     * @param organizationId the value for t_role_organization.organization_id
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId == null ? null : organizationId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_role_organization.creation_time
     *
     * @return the value of t_role_organization.creation_time
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_role_organization.creation_time
     *
     * @param creationTime the value for t_role_organization.creation_time
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_role_organization.create_username
     *
     * @return the value of t_role_organization.create_username
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_role_organization.create_username
     *
     * @param createUsername the value for t_role_organization.create_username
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_role_organization.create_user_id
     *
     * @return the value of t_role_organization.create_user_id
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_role_organization.create_user_id
     *
     * @param createUserId the value for t_role_organization.create_user_id
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_role_organization.last_update_time
     *
     * @return the value of t_role_organization.last_update_time
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_role_organization.last_update_time
     *
     * @param lastUpdateTime the value for t_role_organization.last_update_time
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_role_organization.last_update_username
     *
     * @return the value of t_role_organization.last_update_username
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_role_organization.last_update_username
     *
     * @param lastUpdateUsername the value for t_role_organization.last_update_username
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_role_organization.last_update_user_id
     *
     * @return the value of t_role_organization.last_update_user_id
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_role_organization.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_role_organization.last_update_user_id
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_role_organization.is_deleted
     *
     * @return the value of t_role_organization.is_deleted
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_role_organization.is_deleted
     *
     * @param isDeleted the value for t_role_organization.is_deleted
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
}