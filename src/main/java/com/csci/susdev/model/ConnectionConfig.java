package com.csci.susdev.model;

import java.time.LocalDateTime;

public class ConnectionConfig {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_connection_config.id
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_connection_config.user_id
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    private String userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_connection_config.app_id
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    private String appId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_connection_config.app_key
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    private String appKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_connection_config.description
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_connection_config.is_active
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    private Boolean isActive;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_connection_config.creation_time
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_connection_config.create_username
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_connection_config.create_user_id
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_connection_config.last_update_time
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_connection_config.last_update_username
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_connection_config.last_update_user_id
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_connection_config.last_update_version
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_connection_config.is_deleted
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    private Boolean isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_connection_config.id
     *
     * @return the value of t_connection_config.id
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_connection_config.id
     *
     * @param id the value for t_connection_config.id
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_connection_config.user_id
     *
     * @return the value of t_connection_config.user_id
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public String getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_connection_config.user_id
     *
     * @param userId the value for t_connection_config.user_id
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_connection_config.app_id
     *
     * @return the value of t_connection_config.app_id
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public String getAppId() {
        return appId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_connection_config.app_id
     *
     * @param appId the value for t_connection_config.app_id
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public void setAppId(String appId) {
        this.appId = appId == null ? null : appId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_connection_config.app_key
     *
     * @return the value of t_connection_config.app_key
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public String getAppKey() {
        return appKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_connection_config.app_key
     *
     * @param appKey the value for t_connection_config.app_key
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public void setAppKey(String appKey) {
        this.appKey = appKey == null ? null : appKey.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_connection_config.description
     *
     * @return the value of t_connection_config.description
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_connection_config.description
     *
     * @param description the value for t_connection_config.description
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_connection_config.is_active
     *
     * @return the value of t_connection_config.is_active
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public Boolean getIsActive() {
        return isActive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_connection_config.is_active
     *
     * @param isActive the value for t_connection_config.is_active
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_connection_config.creation_time
     *
     * @return the value of t_connection_config.creation_time
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_connection_config.creation_time
     *
     * @param creationTime the value for t_connection_config.creation_time
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_connection_config.create_username
     *
     * @return the value of t_connection_config.create_username
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_connection_config.create_username
     *
     * @param createUsername the value for t_connection_config.create_username
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_connection_config.create_user_id
     *
     * @return the value of t_connection_config.create_user_id
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_connection_config.create_user_id
     *
     * @param createUserId the value for t_connection_config.create_user_id
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_connection_config.last_update_time
     *
     * @return the value of t_connection_config.last_update_time
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_connection_config.last_update_time
     *
     * @param lastUpdateTime the value for t_connection_config.last_update_time
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_connection_config.last_update_username
     *
     * @return the value of t_connection_config.last_update_username
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_connection_config.last_update_username
     *
     * @param lastUpdateUsername the value for t_connection_config.last_update_username
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_connection_config.last_update_user_id
     *
     * @return the value of t_connection_config.last_update_user_id
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_connection_config.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_connection_config.last_update_user_id
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_connection_config.last_update_version
     *
     * @return the value of t_connection_config.last_update_version
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_connection_config.last_update_version
     *
     * @param lastUpdateVersion the value for t_connection_config.last_update_version
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_connection_config.is_deleted
     *
     * @return the value of t_connection_config.is_deleted
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_connection_config.is_deleted
     *
     * @param isDeleted the value for t_connection_config.is_deleted
     *
     * @mbg.generated Wed Apr 17 10:06:38 HKT 2024
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
}