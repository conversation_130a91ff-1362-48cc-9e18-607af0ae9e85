package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class Train {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_train.id
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_train.code
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    private String code;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_train.start_name
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    private String startName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_train.dest_name
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    private String destName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_train.carbon_emission
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    private BigDecimal carbonEmission;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_train.creation_time
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_train.create_username
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_train.create_user_id
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_train.last_update_time
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_train.last_update_username
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_train.last_update_user_id
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    private String lastUpdateUserId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_train.id
     *
     * @return the value of t_train.id
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_train.id
     *
     * @param id the value for t_train.id
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_train.code
     *
     * @return the value of t_train.code
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public String getCode() {
        return code;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_train.code
     *
     * @param code the value for t_train.code
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_train.start_name
     *
     * @return the value of t_train.start_name
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public String getStartName() {
        return startName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_train.start_name
     *
     * @param startName the value for t_train.start_name
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public void setStartName(String startName) {
        this.startName = startName == null ? null : startName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_train.dest_name
     *
     * @return the value of t_train.dest_name
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public String getDestName() {
        return destName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_train.dest_name
     *
     * @param destName the value for t_train.dest_name
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public void setDestName(String destName) {
        this.destName = destName == null ? null : destName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_train.carbon_emission
     *
     * @return the value of t_train.carbon_emission
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public BigDecimal getCarbonEmission() {
        return carbonEmission;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_train.carbon_emission
     *
     * @param carbonEmission the value for t_train.carbon_emission
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public void setCarbonEmission(BigDecimal carbonEmission) {
        this.carbonEmission = carbonEmission;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_train.creation_time
     *
     * @return the value of t_train.creation_time
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_train.creation_time
     *
     * @param creationTime the value for t_train.creation_time
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_train.create_username
     *
     * @return the value of t_train.create_username
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_train.create_username
     *
     * @param createUsername the value for t_train.create_username
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_train.create_user_id
     *
     * @return the value of t_train.create_user_id
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_train.create_user_id
     *
     * @param createUserId the value for t_train.create_user_id
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_train.last_update_time
     *
     * @return the value of t_train.last_update_time
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_train.last_update_time
     *
     * @param lastUpdateTime the value for t_train.last_update_time
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_train.last_update_username
     *
     * @return the value of t_train.last_update_username
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_train.last_update_username
     *
     * @param lastUpdateUsername the value for t_train.last_update_username
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_train.last_update_user_id
     *
     * @return the value of t_train.last_update_user_id
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_train.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_train.last_update_user_id
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }
}