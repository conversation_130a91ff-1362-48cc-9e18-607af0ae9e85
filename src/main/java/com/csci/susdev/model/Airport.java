package com.csci.susdev.model;

import java.time.LocalDateTime;

public class Airport {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_airport.id
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_airport.code
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	private String code;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_airport.name
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	private String name;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_airport.name_sc
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	private String nameSc;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_airport.name_en
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	private String nameEn;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_airport.creation_time
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	private LocalDateTime creationTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_airport.create_username
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	private String createUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_airport.create_user_id
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	private String createUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_airport.last_update_time
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	private LocalDateTime lastUpdateTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_airport.last_update_username
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	private String lastUpdateUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_airport.last_update_user_id
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	private String lastUpdateUserId;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_airport.id
	 * @return  the value of t_airport.id
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_airport.id
	 * @param id  the value for t_airport.id
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_airport.code
	 * @return  the value of t_airport.code
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public String getCode() {
		return code;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_airport.code
	 * @param code  the value for t_airport.code
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public void setCode(String code) {
		this.code = code == null ? null : code.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_airport.name
	 * @return  the value of t_airport.name
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public String getName() {
		return name;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_airport.name
	 * @param name  the value for t_airport.name
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public void setName(String name) {
		this.name = name == null ? null : name.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_airport.name_sc
	 * @return  the value of t_airport.name_sc
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public String getNameSc() {
		return nameSc;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_airport.name_sc
	 * @param nameSc  the value for t_airport.name_sc
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public void setNameSc(String nameSc) {
		this.nameSc = nameSc == null ? null : nameSc.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_airport.name_en
	 * @return  the value of t_airport.name_en
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public String getNameEn() {
		return nameEn;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_airport.name_en
	 * @param nameEn  the value for t_airport.name_en
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public void setNameEn(String nameEn) {
		this.nameEn = nameEn == null ? null : nameEn.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_airport.creation_time
	 * @return  the value of t_airport.creation_time
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public LocalDateTime getCreationTime() {
		return creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_airport.creation_time
	 * @param creationTime  the value for t_airport.creation_time
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public void setCreationTime(LocalDateTime creationTime) {
		this.creationTime = creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_airport.create_username
	 * @return  the value of t_airport.create_username
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public String getCreateUsername() {
		return createUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_airport.create_username
	 * @param createUsername  the value for t_airport.create_username
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public void setCreateUsername(String createUsername) {
		this.createUsername = createUsername == null ? null : createUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_airport.create_user_id
	 * @return  the value of t_airport.create_user_id
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public String getCreateUserId() {
		return createUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_airport.create_user_id
	 * @param createUserId  the value for t_airport.create_user_id
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId == null ? null : createUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_airport.last_update_time
	 * @return  the value of t_airport.last_update_time
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public LocalDateTime getLastUpdateTime() {
		return lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_airport.last_update_time
	 * @param lastUpdateTime  the value for t_airport.last_update_time
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_airport.last_update_username
	 * @return  the value of t_airport.last_update_username
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public String getLastUpdateUsername() {
		return lastUpdateUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_airport.last_update_username
	 * @param lastUpdateUsername  the value for t_airport.last_update_username
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public void setLastUpdateUsername(String lastUpdateUsername) {
		this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_airport.last_update_user_id
	 * @return  the value of t_airport.last_update_user_id
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public String getLastUpdateUserId() {
		return lastUpdateUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_airport.last_update_user_id
	 * @param lastUpdateUserId  the value for t_airport.last_update_user_id
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	public void setLastUpdateUserId(String lastUpdateUserId) {
		this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
	}
}