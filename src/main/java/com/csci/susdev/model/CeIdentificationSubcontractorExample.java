package com.csci.susdev.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class CeIdentificationSubcontractorExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public CeIdentificationSubcontractorExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andCeIdentificationHeadIdIsNull() {
			addCriterion("ce_identification_head_id is null");
			return (Criteria) this;
		}

		public Criteria andCeIdentificationHeadIdIsNotNull() {
			addCriterion("ce_identification_head_id is not null");
			return (Criteria) this;
		}

		public Criteria andCeIdentificationHeadIdEqualTo(String value) {
			addCriterion("ce_identification_head_id =", value, "ceIdentificationHeadId");
			return (Criteria) this;
		}

		public Criteria andCeIdentificationHeadIdNotEqualTo(String value) {
			addCriterion("ce_identification_head_id <>", value, "ceIdentificationHeadId");
			return (Criteria) this;
		}

		public Criteria andCeIdentificationHeadIdGreaterThan(String value) {
			addCriterion("ce_identification_head_id >", value, "ceIdentificationHeadId");
			return (Criteria) this;
		}

		public Criteria andCeIdentificationHeadIdGreaterThanOrEqualTo(String value) {
			addCriterion("ce_identification_head_id >=", value, "ceIdentificationHeadId");
			return (Criteria) this;
		}

		public Criteria andCeIdentificationHeadIdLessThan(String value) {
			addCriterion("ce_identification_head_id <", value, "ceIdentificationHeadId");
			return (Criteria) this;
		}

		public Criteria andCeIdentificationHeadIdLessThanOrEqualTo(String value) {
			addCriterion("ce_identification_head_id <=", value, "ceIdentificationHeadId");
			return (Criteria) this;
		}

		public Criteria andCeIdentificationHeadIdLike(String value) {
			addCriterion("ce_identification_head_id like", value, "ceIdentificationHeadId");
			return (Criteria) this;
		}

		public Criteria andCeIdentificationHeadIdNotLike(String value) {
			addCriterion("ce_identification_head_id not like", value, "ceIdentificationHeadId");
			return (Criteria) this;
		}

		public Criteria andCeIdentificationHeadIdIn(List<String> values) {
			addCriterion("ce_identification_head_id in", values, "ceIdentificationHeadId");
			return (Criteria) this;
		}

		public Criteria andCeIdentificationHeadIdNotIn(List<String> values) {
			addCriterion("ce_identification_head_id not in", values, "ceIdentificationHeadId");
			return (Criteria) this;
		}

		public Criteria andCeIdentificationHeadIdBetween(String value1, String value2) {
			addCriterion("ce_identification_head_id between", value1, value2, "ceIdentificationHeadId");
			return (Criteria) this;
		}

		public Criteria andCeIdentificationHeadIdNotBetween(String value1, String value2) {
			addCriterion("ce_identification_head_id not between", value1, value2, "ceIdentificationHeadId");
			return (Criteria) this;
		}

		public Criteria andTypeIsNull() {
			addCriterion("type is null");
			return (Criteria) this;
		}

		public Criteria andTypeIsNotNull() {
			addCriterion("type is not null");
			return (Criteria) this;
		}

		public Criteria andTypeEqualTo(String value) {
			addCriterion("type =", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeNotEqualTo(String value) {
			addCriterion("type <>", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeGreaterThan(String value) {
			addCriterion("type >", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeGreaterThanOrEqualTo(String value) {
			addCriterion("type >=", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeLessThan(String value) {
			addCriterion("type <", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeLessThanOrEqualTo(String value) {
			addCriterion("type <=", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeLike(String value) {
			addCriterion("type like", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeNotLike(String value) {
			addCriterion("type not like", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeIn(List<String> values) {
			addCriterion("type in", values, "type");
			return (Criteria) this;
		}

		public Criteria andTypeNotIn(List<String> values) {
			addCriterion("type not in", values, "type");
			return (Criteria) this;
		}

		public Criteria andTypeBetween(String value1, String value2) {
			addCriterion("type between", value1, value2, "type");
			return (Criteria) this;
		}

		public Criteria andTypeNotBetween(String value1, String value2) {
			addCriterion("type not between", value1, value2, "type");
			return (Criteria) this;
		}

		public Criteria andNameIsNull() {
			addCriterion("name is null");
			return (Criteria) this;
		}

		public Criteria andNameIsNotNull() {
			addCriterion("name is not null");
			return (Criteria) this;
		}

		public Criteria andNameEqualTo(String value) {
			addCriterion("name =", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotEqualTo(String value) {
			addCriterion("name <>", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameGreaterThan(String value) {
			addCriterion("name >", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameGreaterThanOrEqualTo(String value) {
			addCriterion("name >=", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLessThan(String value) {
			addCriterion("name <", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLessThanOrEqualTo(String value) {
			addCriterion("name <=", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLike(String value) {
			addCriterion("name like", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotLike(String value) {
			addCriterion("name not like", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameIn(List<String> values) {
			addCriterion("name in", values, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotIn(List<String> values) {
			addCriterion("name not in", values, "name");
			return (Criteria) this;
		}

		public Criteria andNameBetween(String value1, String value2) {
			addCriterion("name between", value1, value2, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotBetween(String value1, String value2) {
			addCriterion("name not between", value1, value2, "name");
			return (Criteria) this;
		}

		public Criteria andDutyIsNull() {
			addCriterion("duty is null");
			return (Criteria) this;
		}

		public Criteria andDutyIsNotNull() {
			addCriterion("duty is not null");
			return (Criteria) this;
		}

		public Criteria andDutyEqualTo(String value) {
			addCriterion("duty =", value, "duty");
			return (Criteria) this;
		}

		public Criteria andDutyNotEqualTo(String value) {
			addCriterion("duty <>", value, "duty");
			return (Criteria) this;
		}

		public Criteria andDutyGreaterThan(String value) {
			addCriterion("duty >", value, "duty");
			return (Criteria) this;
		}

		public Criteria andDutyGreaterThanOrEqualTo(String value) {
			addCriterion("duty >=", value, "duty");
			return (Criteria) this;
		}

		public Criteria andDutyLessThan(String value) {
			addCriterion("duty <", value, "duty");
			return (Criteria) this;
		}

		public Criteria andDutyLessThanOrEqualTo(String value) {
			addCriterion("duty <=", value, "duty");
			return (Criteria) this;
		}

		public Criteria andDutyLike(String value) {
			addCriterion("duty like", value, "duty");
			return (Criteria) this;
		}

		public Criteria andDutyNotLike(String value) {
			addCriterion("duty not like", value, "duty");
			return (Criteria) this;
		}

		public Criteria andDutyIn(List<String> values) {
			addCriterion("duty in", values, "duty");
			return (Criteria) this;
		}

		public Criteria andDutyNotIn(List<String> values) {
			addCriterion("duty not in", values, "duty");
			return (Criteria) this;
		}

		public Criteria andDutyBetween(String value1, String value2) {
			addCriterion("duty between", value1, value2, "duty");
			return (Criteria) this;
		}

		public Criteria andDutyNotBetween(String value1, String value2) {
			addCriterion("duty not between", value1, value2, "duty");
			return (Criteria) this;
		}

		public Criteria andDutyTypeIsNull() {
			addCriterion("duty_type is null");
			return (Criteria) this;
		}

		public Criteria andDutyTypeIsNotNull() {
			addCriterion("duty_type is not null");
			return (Criteria) this;
		}

		public Criteria andDutyTypeEqualTo(String value) {
			addCriterion("duty_type =", value, "dutyType");
			return (Criteria) this;
		}

		public Criteria andDutyTypeNotEqualTo(String value) {
			addCriterion("duty_type <>", value, "dutyType");
			return (Criteria) this;
		}

		public Criteria andDutyTypeGreaterThan(String value) {
			addCriterion("duty_type >", value, "dutyType");
			return (Criteria) this;
		}

		public Criteria andDutyTypeGreaterThanOrEqualTo(String value) {
			addCriterion("duty_type >=", value, "dutyType");
			return (Criteria) this;
		}

		public Criteria andDutyTypeLessThan(String value) {
			addCriterion("duty_type <", value, "dutyType");
			return (Criteria) this;
		}

		public Criteria andDutyTypeLessThanOrEqualTo(String value) {
			addCriterion("duty_type <=", value, "dutyType");
			return (Criteria) this;
		}

		public Criteria andDutyTypeLike(String value) {
			addCriterion("duty_type like", value, "dutyType");
			return (Criteria) this;
		}

		public Criteria andDutyTypeNotLike(String value) {
			addCriterion("duty_type not like", value, "dutyType");
			return (Criteria) this;
		}

		public Criteria andDutyTypeIn(List<String> values) {
			addCriterion("duty_type in", values, "dutyType");
			return (Criteria) this;
		}

		public Criteria andDutyTypeNotIn(List<String> values) {
			addCriterion("duty_type not in", values, "dutyType");
			return (Criteria) this;
		}

		public Criteria andDutyTypeBetween(String value1, String value2) {
			addCriterion("duty_type between", value1, value2, "dutyType");
			return (Criteria) this;
		}

		public Criteria andDutyTypeNotBetween(String value1, String value2) {
			addCriterion("duty_type not between", value1, value2, "dutyType");
			return (Criteria) this;
		}

		public Criteria andSsFossilFuelIsNull() {
			addCriterion("ss_fossil_fuel is null");
			return (Criteria) this;
		}

		public Criteria andSsFossilFuelIsNotNull() {
			addCriterion("ss_fossil_fuel is not null");
			return (Criteria) this;
		}

		public Criteria andSsFossilFuelEqualTo(String value) {
			addCriterion("ss_fossil_fuel =", value, "ssFossilFuel");
			return (Criteria) this;
		}

		public Criteria andSsFossilFuelNotEqualTo(String value) {
			addCriterion("ss_fossil_fuel <>", value, "ssFossilFuel");
			return (Criteria) this;
		}

		public Criteria andSsFossilFuelGreaterThan(String value) {
			addCriterion("ss_fossil_fuel >", value, "ssFossilFuel");
			return (Criteria) this;
		}

		public Criteria andSsFossilFuelGreaterThanOrEqualTo(String value) {
			addCriterion("ss_fossil_fuel >=", value, "ssFossilFuel");
			return (Criteria) this;
		}

		public Criteria andSsFossilFuelLessThan(String value) {
			addCriterion("ss_fossil_fuel <", value, "ssFossilFuel");
			return (Criteria) this;
		}

		public Criteria andSsFossilFuelLessThanOrEqualTo(String value) {
			addCriterion("ss_fossil_fuel <=", value, "ssFossilFuel");
			return (Criteria) this;
		}

		public Criteria andSsFossilFuelLike(String value) {
			addCriterion("ss_fossil_fuel like", value, "ssFossilFuel");
			return (Criteria) this;
		}

		public Criteria andSsFossilFuelNotLike(String value) {
			addCriterion("ss_fossil_fuel not like", value, "ssFossilFuel");
			return (Criteria) this;
		}

		public Criteria andSsFossilFuelIn(List<String> values) {
			addCriterion("ss_fossil_fuel in", values, "ssFossilFuel");
			return (Criteria) this;
		}

		public Criteria andSsFossilFuelNotIn(List<String> values) {
			addCriterion("ss_fossil_fuel not in", values, "ssFossilFuel");
			return (Criteria) this;
		}

		public Criteria andSsFossilFuelBetween(String value1, String value2) {
			addCriterion("ss_fossil_fuel between", value1, value2, "ssFossilFuel");
			return (Criteria) this;
		}

		public Criteria andSsFossilFuelNotBetween(String value1, String value2) {
			addCriterion("ss_fossil_fuel not between", value1, value2, "ssFossilFuel");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelQtyIsNull() {
			addCriterion("ms_fossil_fuel_qty is null");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelQtyIsNotNull() {
			addCriterion("ms_fossil_fuel_qty is not null");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelQtyEqualTo(String value) {
			addCriterion("ms_fossil_fuel_qty =", value, "msFossilFuelQty");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelQtyNotEqualTo(String value) {
			addCriterion("ms_fossil_fuel_qty <>", value, "msFossilFuelQty");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelQtyGreaterThan(String value) {
			addCriterion("ms_fossil_fuel_qty >", value, "msFossilFuelQty");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelQtyGreaterThanOrEqualTo(String value) {
			addCriterion("ms_fossil_fuel_qty >=", value, "msFossilFuelQty");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelQtyLessThan(String value) {
			addCriterion("ms_fossil_fuel_qty <", value, "msFossilFuelQty");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelQtyLessThanOrEqualTo(String value) {
			addCriterion("ms_fossil_fuel_qty <=", value, "msFossilFuelQty");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelQtyLike(String value) {
			addCriterion("ms_fossil_fuel_qty like", value, "msFossilFuelQty");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelQtyNotLike(String value) {
			addCriterion("ms_fossil_fuel_qty not like", value, "msFossilFuelQty");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelQtyIn(List<String> values) {
			addCriterion("ms_fossil_fuel_qty in", values, "msFossilFuelQty");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelQtyNotIn(List<String> values) {
			addCriterion("ms_fossil_fuel_qty not in", values, "msFossilFuelQty");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelQtyBetween(String value1, String value2) {
			addCriterion("ms_fossil_fuel_qty between", value1, value2, "msFossilFuelQty");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelQtyNotBetween(String value1, String value2) {
			addCriterion("ms_fossil_fuel_qty not between", value1, value2, "msFossilFuelQty");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelTonKmIsNull() {
			addCriterion("ms_fossil_fuel_ton_km is null");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelTonKmIsNotNull() {
			addCriterion("ms_fossil_fuel_ton_km is not null");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelTonKmEqualTo(String value) {
			addCriterion("ms_fossil_fuel_ton_km =", value, "msFossilFuelTonKm");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelTonKmNotEqualTo(String value) {
			addCriterion("ms_fossil_fuel_ton_km <>", value, "msFossilFuelTonKm");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelTonKmGreaterThan(String value) {
			addCriterion("ms_fossil_fuel_ton_km >", value, "msFossilFuelTonKm");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelTonKmGreaterThanOrEqualTo(String value) {
			addCriterion("ms_fossil_fuel_ton_km >=", value, "msFossilFuelTonKm");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelTonKmLessThan(String value) {
			addCriterion("ms_fossil_fuel_ton_km <", value, "msFossilFuelTonKm");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelTonKmLessThanOrEqualTo(String value) {
			addCriterion("ms_fossil_fuel_ton_km <=", value, "msFossilFuelTonKm");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelTonKmLike(String value) {
			addCriterion("ms_fossil_fuel_ton_km like", value, "msFossilFuelTonKm");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelTonKmNotLike(String value) {
			addCriterion("ms_fossil_fuel_ton_km not like", value, "msFossilFuelTonKm");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelTonKmIn(List<String> values) {
			addCriterion("ms_fossil_fuel_ton_km in", values, "msFossilFuelTonKm");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelTonKmNotIn(List<String> values) {
			addCriterion("ms_fossil_fuel_ton_km not in", values, "msFossilFuelTonKm");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelTonKmBetween(String value1, String value2) {
			addCriterion("ms_fossil_fuel_ton_km between", value1, value2, "msFossilFuelTonKm");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelTonKmNotBetween(String value1, String value2) {
			addCriterion("ms_fossil_fuel_ton_km not between", value1, value2, "msFossilFuelTonKm");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelMachineTeamIsNull() {
			addCriterion("ms_fossil_fuel_machine_team is null");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelMachineTeamIsNotNull() {
			addCriterion("ms_fossil_fuel_machine_team is not null");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelMachineTeamEqualTo(String value) {
			addCriterion("ms_fossil_fuel_machine_team =", value, "msFossilFuelMachineTeam");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelMachineTeamNotEqualTo(String value) {
			addCriterion("ms_fossil_fuel_machine_team <>", value, "msFossilFuelMachineTeam");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelMachineTeamGreaterThan(String value) {
			addCriterion("ms_fossil_fuel_machine_team >", value, "msFossilFuelMachineTeam");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelMachineTeamGreaterThanOrEqualTo(String value) {
			addCriterion("ms_fossil_fuel_machine_team >=", value, "msFossilFuelMachineTeam");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelMachineTeamLessThan(String value) {
			addCriterion("ms_fossil_fuel_machine_team <", value, "msFossilFuelMachineTeam");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelMachineTeamLessThanOrEqualTo(String value) {
			addCriterion("ms_fossil_fuel_machine_team <=", value, "msFossilFuelMachineTeam");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelMachineTeamLike(String value) {
			addCriterion("ms_fossil_fuel_machine_team like", value, "msFossilFuelMachineTeam");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelMachineTeamNotLike(String value) {
			addCriterion("ms_fossil_fuel_machine_team not like", value, "msFossilFuelMachineTeam");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelMachineTeamIn(List<String> values) {
			addCriterion("ms_fossil_fuel_machine_team in", values, "msFossilFuelMachineTeam");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelMachineTeamNotIn(List<String> values) {
			addCriterion("ms_fossil_fuel_machine_team not in", values, "msFossilFuelMachineTeam");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelMachineTeamBetween(String value1, String value2) {
			addCriterion("ms_fossil_fuel_machine_team between", value1, value2, "msFossilFuelMachineTeam");
			return (Criteria) this;
		}

		public Criteria andMsFossilFuelMachineTeamNotBetween(String value1, String value2) {
			addCriterion("ms_fossil_fuel_machine_team not between", value1, value2, "msFossilFuelMachineTeam");
			return (Criteria) this;
		}

		public Criteria andProcessEmissionIsNull() {
			addCriterion("process_emission is null");
			return (Criteria) this;
		}

		public Criteria andProcessEmissionIsNotNull() {
			addCriterion("process_emission is not null");
			return (Criteria) this;
		}

		public Criteria andProcessEmissionEqualTo(String value) {
			addCriterion("process_emission =", value, "processEmission");
			return (Criteria) this;
		}

		public Criteria andProcessEmissionNotEqualTo(String value) {
			addCriterion("process_emission <>", value, "processEmission");
			return (Criteria) this;
		}

		public Criteria andProcessEmissionGreaterThan(String value) {
			addCriterion("process_emission >", value, "processEmission");
			return (Criteria) this;
		}

		public Criteria andProcessEmissionGreaterThanOrEqualTo(String value) {
			addCriterion("process_emission >=", value, "processEmission");
			return (Criteria) this;
		}

		public Criteria andProcessEmissionLessThan(String value) {
			addCriterion("process_emission <", value, "processEmission");
			return (Criteria) this;
		}

		public Criteria andProcessEmissionLessThanOrEqualTo(String value) {
			addCriterion("process_emission <=", value, "processEmission");
			return (Criteria) this;
		}

		public Criteria andProcessEmissionLike(String value) {
			addCriterion("process_emission like", value, "processEmission");
			return (Criteria) this;
		}

		public Criteria andProcessEmissionNotLike(String value) {
			addCriterion("process_emission not like", value, "processEmission");
			return (Criteria) this;
		}

		public Criteria andProcessEmissionIn(List<String> values) {
			addCriterion("process_emission in", values, "processEmission");
			return (Criteria) this;
		}

		public Criteria andProcessEmissionNotIn(List<String> values) {
			addCriterion("process_emission not in", values, "processEmission");
			return (Criteria) this;
		}

		public Criteria andProcessEmissionBetween(String value1, String value2) {
			addCriterion("process_emission between", value1, value2, "processEmission");
			return (Criteria) this;
		}

		public Criteria andProcessEmissionNotBetween(String value1, String value2) {
			addCriterion("process_emission not between", value1, value2, "processEmission");
			return (Criteria) this;
		}

		public Criteria andElectricityIsNull() {
			addCriterion("electricity is null");
			return (Criteria) this;
		}

		public Criteria andElectricityIsNotNull() {
			addCriterion("electricity is not null");
			return (Criteria) this;
		}

		public Criteria andElectricityEqualTo(String value) {
			addCriterion("electricity =", value, "electricity");
			return (Criteria) this;
		}

		public Criteria andElectricityNotEqualTo(String value) {
			addCriterion("electricity <>", value, "electricity");
			return (Criteria) this;
		}

		public Criteria andElectricityGreaterThan(String value) {
			addCriterion("electricity >", value, "electricity");
			return (Criteria) this;
		}

		public Criteria andElectricityGreaterThanOrEqualTo(String value) {
			addCriterion("electricity >=", value, "electricity");
			return (Criteria) this;
		}

		public Criteria andElectricityLessThan(String value) {
			addCriterion("electricity <", value, "electricity");
			return (Criteria) this;
		}

		public Criteria andElectricityLessThanOrEqualTo(String value) {
			addCriterion("electricity <=", value, "electricity");
			return (Criteria) this;
		}

		public Criteria andElectricityLike(String value) {
			addCriterion("electricity like", value, "electricity");
			return (Criteria) this;
		}

		public Criteria andElectricityNotLike(String value) {
			addCriterion("electricity not like", value, "electricity");
			return (Criteria) this;
		}

		public Criteria andElectricityIn(List<String> values) {
			addCriterion("electricity in", values, "electricity");
			return (Criteria) this;
		}

		public Criteria andElectricityNotIn(List<String> values) {
			addCriterion("electricity not in", values, "electricity");
			return (Criteria) this;
		}

		public Criteria andElectricityBetween(String value1, String value2) {
			addCriterion("electricity between", value1, value2, "electricity");
			return (Criteria) this;
		}

		public Criteria andElectricityNotBetween(String value1, String value2) {
			addCriterion("electricity not between", value1, value2, "electricity");
			return (Criteria) this;
		}

		public Criteria andOutsourcedBuildingMaterialIsNull() {
			addCriterion("outsourced_building_material is null");
			return (Criteria) this;
		}

		public Criteria andOutsourcedBuildingMaterialIsNotNull() {
			addCriterion("outsourced_building_material is not null");
			return (Criteria) this;
		}

		public Criteria andOutsourcedBuildingMaterialEqualTo(String value) {
			addCriterion("outsourced_building_material =", value, "outsourcedBuildingMaterial");
			return (Criteria) this;
		}

		public Criteria andOutsourcedBuildingMaterialNotEqualTo(String value) {
			addCriterion("outsourced_building_material <>", value, "outsourcedBuildingMaterial");
			return (Criteria) this;
		}

		public Criteria andOutsourcedBuildingMaterialGreaterThan(String value) {
			addCriterion("outsourced_building_material >", value, "outsourcedBuildingMaterial");
			return (Criteria) this;
		}

		public Criteria andOutsourcedBuildingMaterialGreaterThanOrEqualTo(String value) {
			addCriterion("outsourced_building_material >=", value, "outsourcedBuildingMaterial");
			return (Criteria) this;
		}

		public Criteria andOutsourcedBuildingMaterialLessThan(String value) {
			addCriterion("outsourced_building_material <", value, "outsourcedBuildingMaterial");
			return (Criteria) this;
		}

		public Criteria andOutsourcedBuildingMaterialLessThanOrEqualTo(String value) {
			addCriterion("outsourced_building_material <=", value, "outsourcedBuildingMaterial");
			return (Criteria) this;
		}

		public Criteria andOutsourcedBuildingMaterialLike(String value) {
			addCriterion("outsourced_building_material like", value, "outsourcedBuildingMaterial");
			return (Criteria) this;
		}

		public Criteria andOutsourcedBuildingMaterialNotLike(String value) {
			addCriterion("outsourced_building_material not like", value, "outsourcedBuildingMaterial");
			return (Criteria) this;
		}

		public Criteria andOutsourcedBuildingMaterialIn(List<String> values) {
			addCriterion("outsourced_building_material in", values, "outsourcedBuildingMaterial");
			return (Criteria) this;
		}

		public Criteria andOutsourcedBuildingMaterialNotIn(List<String> values) {
			addCriterion("outsourced_building_material not in", values, "outsourcedBuildingMaterial");
			return (Criteria) this;
		}

		public Criteria andOutsourcedBuildingMaterialBetween(String value1, String value2) {
			addCriterion("outsourced_building_material between", value1, value2, "outsourcedBuildingMaterial");
			return (Criteria) this;
		}

		public Criteria andOutsourcedBuildingMaterialNotBetween(String value1, String value2) {
			addCriterion("outsourced_building_material not between", value1, value2, "outsourcedBuildingMaterial");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNull() {
			addCriterion("creation_time is null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNotNull() {
			addCriterion("creation_time is not null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeEqualTo(LocalDateTime value) {
			addCriterion("creation_time =", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
			addCriterion("creation_time <>", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
			addCriterion("creation_time >", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("creation_time >=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThan(LocalDateTime value) {
			addCriterion("creation_time <", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("creation_time <=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIn(List<LocalDateTime> values) {
			addCriterion("creation_time in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
			addCriterion("creation_time not in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("creation_time between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("creation_time not between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIsNull() {
			addCriterion("create_username is null");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIsNotNull() {
			addCriterion("create_username is not null");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameEqualTo(String value) {
			addCriterion("create_username =", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotEqualTo(String value) {
			addCriterion("create_username <>", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameGreaterThan(String value) {
			addCriterion("create_username >", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
			addCriterion("create_username >=", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLessThan(String value) {
			addCriterion("create_username <", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
			addCriterion("create_username <=", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLike(String value) {
			addCriterion("create_username like", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotLike(String value) {
			addCriterion("create_username not like", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIn(List<String> values) {
			addCriterion("create_username in", values, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotIn(List<String> values) {
			addCriterion("create_username not in", values, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameBetween(String value1, String value2) {
			addCriterion("create_username between", value1, value2, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotBetween(String value1, String value2) {
			addCriterion("create_username not between", value1, value2, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdIsNull() {
			addCriterion("create_user_id is null");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdIsNotNull() {
			addCriterion("create_user_id is not null");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdEqualTo(String value) {
			addCriterion("create_user_id =", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdNotEqualTo(String value) {
			addCriterion("create_user_id <>", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdGreaterThan(String value) {
			addCriterion("create_user_id >", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
			addCriterion("create_user_id >=", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdLessThan(String value) {
			addCriterion("create_user_id <", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
			addCriterion("create_user_id <=", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdLike(String value) {
			addCriterion("create_user_id like", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdNotLike(String value) {
			addCriterion("create_user_id not like", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdIn(List<String> values) {
			addCriterion("create_user_id in", values, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdNotIn(List<String> values) {
			addCriterion("create_user_id not in", values, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdBetween(String value1, String value2) {
			addCriterion("create_user_id between", value1, value2, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdNotBetween(String value1, String value2) {
			addCriterion("create_user_id not between", value1, value2, "createUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNull() {
			addCriterion("last_update_time is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNotNull() {
			addCriterion("last_update_time is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
			addCriterion("last_update_time =", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
			addCriterion("last_update_time <>", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
			addCriterion("last_update_time >", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("last_update_time >=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
			addCriterion("last_update_time <", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("last_update_time <=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
			addCriterion("last_update_time in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
			addCriterion("last_update_time not in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIsNull() {
			addCriterion("last_update_username is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIsNotNull() {
			addCriterion("last_update_username is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameEqualTo(String value) {
			addCriterion("last_update_username =", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotEqualTo(String value) {
			addCriterion("last_update_username <>", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameGreaterThan(String value) {
			addCriterion("last_update_username >", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
			addCriterion("last_update_username >=", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLessThan(String value) {
			addCriterion("last_update_username <", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
			addCriterion("last_update_username <=", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLike(String value) {
			addCriterion("last_update_username like", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotLike(String value) {
			addCriterion("last_update_username not like", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIn(List<String> values) {
			addCriterion("last_update_username in", values, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotIn(List<String> values) {
			addCriterion("last_update_username not in", values, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
			addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
			addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdIsNull() {
			addCriterion("last_update_user_id is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdIsNotNull() {
			addCriterion("last_update_user_id is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdEqualTo(String value) {
			addCriterion("last_update_user_id =", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdNotEqualTo(String value) {
			addCriterion("last_update_user_id <>", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdGreaterThan(String value) {
			addCriterion("last_update_user_id >", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdGreaterThanOrEqualTo(String value) {
			addCriterion("last_update_user_id >=", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdLessThan(String value) {
			addCriterion("last_update_user_id <", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdLessThanOrEqualTo(String value) {
			addCriterion("last_update_user_id <=", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdLike(String value) {
			addCriterion("last_update_user_id like", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdNotLike(String value) {
			addCriterion("last_update_user_id not like", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdIn(List<String> values) {
			addCriterion("last_update_user_id in", values, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdNotIn(List<String> values) {
			addCriterion("last_update_user_id not in", values, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdBetween(String value1, String value2) {
			addCriterion("last_update_user_id between", value1, value2, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdNotBetween(String value1, String value2) {
			addCriterion("last_update_user_id not between", value1, value2, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionIsNull() {
			addCriterion("last_update_version is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionIsNotNull() {
			addCriterion("last_update_version is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionEqualTo(Integer value) {
			addCriterion("last_update_version =", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionNotEqualTo(Integer value) {
			addCriterion("last_update_version <>", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionGreaterThan(Integer value) {
			addCriterion("last_update_version >", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionGreaterThanOrEqualTo(Integer value) {
			addCriterion("last_update_version >=", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionLessThan(Integer value) {
			addCriterion("last_update_version <", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionLessThanOrEqualTo(Integer value) {
			addCriterion("last_update_version <=", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionIn(List<Integer> values) {
			addCriterion("last_update_version in", values, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionNotIn(List<Integer> values) {
			addCriterion("last_update_version not in", values, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionBetween(Integer value1, Integer value2) {
			addCriterion("last_update_version between", value1, value2, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionNotBetween(Integer value1, Integer value2) {
			addCriterion("last_update_version not between", value1, value2, "lastUpdateVersion");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_ce_identification_subcontractor
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_ce_identification_subcontractor
     *
     * @mbg.generated do_not_delete_during_merge Mon Sep 11 00:15:13 HKT 2023
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}