package com.csci.susdev.model;

import java.time.LocalDateTime;

public class MinioAttachment {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_minio_attachment.id
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_minio_attachment.ref_id
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    private String refId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_minio_attachment.category
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    private String category;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_minio_attachment.section
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    private String section;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_minio_attachment.type
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    private String type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_minio_attachment.name
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_minio_attachment.minio_file_name
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    private String minioFileName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_minio_attachment.minio_file_url
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    private String minioFileUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_minio_attachment.creation_time
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_minio_attachment.create_username
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_minio_attachment.create_user_id
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_minio_attachment.last_update_time
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_minio_attachment.last_update_username
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_minio_attachment.last_update_user_id
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    private String lastUpdateUserId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_minio_attachment.id
     *
     * @return the value of t_minio_attachment.id
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_minio_attachment.id
     *
     * @param id the value for t_minio_attachment.id
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_minio_attachment.ref_id
     *
     * @return the value of t_minio_attachment.ref_id
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public String getRefId() {
        return refId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_minio_attachment.ref_id
     *
     * @param refId the value for t_minio_attachment.ref_id
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public void setRefId(String refId) {
        this.refId = refId == null ? null : refId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_minio_attachment.category
     *
     * @return the value of t_minio_attachment.category
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public String getCategory() {
        return category;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_minio_attachment.category
     *
     * @param category the value for t_minio_attachment.category
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public void setCategory(String category) {
        this.category = category == null ? null : category.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_minio_attachment.section
     *
     * @return the value of t_minio_attachment.section
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public String getSection() {
        return section;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_minio_attachment.section
     *
     * @param section the value for t_minio_attachment.section
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public void setSection(String section) {
        this.section = section == null ? null : section.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_minio_attachment.type
     *
     * @return the value of t_minio_attachment.type
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_minio_attachment.type
     *
     * @param type the value for t_minio_attachment.type
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_minio_attachment.name
     *
     * @return the value of t_minio_attachment.name
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_minio_attachment.name
     *
     * @param name the value for t_minio_attachment.name
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_minio_attachment.minio_file_name
     *
     * @return the value of t_minio_attachment.minio_file_name
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public String getMinioFileName() {
        return minioFileName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_minio_attachment.minio_file_name
     *
     * @param minioFileName the value for t_minio_attachment.minio_file_name
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public void setMinioFileName(String minioFileName) {
        this.minioFileName = minioFileName == null ? null : minioFileName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_minio_attachment.minio_file_url
     *
     * @return the value of t_minio_attachment.minio_file_url
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public String getMinioFileUrl() {
        return minioFileUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_minio_attachment.minio_file_url
     *
     * @param minioFileUrl the value for t_minio_attachment.minio_file_url
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public void setMinioFileUrl(String minioFileUrl) {
        this.minioFileUrl = minioFileUrl == null ? null : minioFileUrl.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_minio_attachment.creation_time
     *
     * @return the value of t_minio_attachment.creation_time
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_minio_attachment.creation_time
     *
     * @param creationTime the value for t_minio_attachment.creation_time
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_minio_attachment.create_username
     *
     * @return the value of t_minio_attachment.create_username
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_minio_attachment.create_username
     *
     * @param createUsername the value for t_minio_attachment.create_username
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_minio_attachment.create_user_id
     *
     * @return the value of t_minio_attachment.create_user_id
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_minio_attachment.create_user_id
     *
     * @param createUserId the value for t_minio_attachment.create_user_id
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_minio_attachment.last_update_time
     *
     * @return the value of t_minio_attachment.last_update_time
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_minio_attachment.last_update_time
     *
     * @param lastUpdateTime the value for t_minio_attachment.last_update_time
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_minio_attachment.last_update_username
     *
     * @return the value of t_minio_attachment.last_update_username
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_minio_attachment.last_update_username
     *
     * @param lastUpdateUsername the value for t_minio_attachment.last_update_username
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_minio_attachment.last_update_user_id
     *
     * @return the value of t_minio_attachment.last_update_user_id
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_minio_attachment.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_minio_attachment.last_update_user_id
     *
     * @mbg.generated Wed Dec 18 15:54:10 CST 2024
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }
}