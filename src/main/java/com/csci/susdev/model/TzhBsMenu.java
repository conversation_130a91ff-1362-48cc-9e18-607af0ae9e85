package com.csci.susdev.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema
@Data
public class TzhBsMenu {
    @Schema(description = "")
    private String id;

    @Schema(description = "")
    private String pageName;

    /**
     * 路径url
     */
    @Schema(description = "路径url")
    private String path;

    @Schema(description = "")
    private String name;

    /**
     * 前端组件
     */
    @Schema(description = "前端组件")
    private String component;
    /**
     * 标签类别 0：通用模版 1：自定义模版
     */
    @Schema(description = "模块,ESG:1,碳中和:2")
    private Integer module;
    /**
     * 英文名
     */
    @Schema(description = "英文名")
    private String titleEN;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private String seq;

    /**
     * 逻辑删除 1->删除
     */
    @Schema(description = "逻辑删除 1->删除")
    private boolean isDeleted;

    /**
     * 简体中文名
     */
    @Schema(description = "简体中文名")
    private String titleSC;

    /**
     * 繁体中文名
     */
    @Schema(description = "繁体中文名")
    private String title;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 标签类别 0：通用模版 1：自定义模版
     */
    @Schema(description = "标签类别 0：通用模版 1：自定义模版")
    private Integer type =1;
}
