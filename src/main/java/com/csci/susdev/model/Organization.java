package com.csci.susdev.model;

import java.time.LocalDateTime;

public class Organization {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.id
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.parent_id
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String parentId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.no
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String no;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.name
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String name;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.code
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String code;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.unit_code
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String unitCode;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.title
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String title;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.address
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String address;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.latitude
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String latitude;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.longitude
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String longitude;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.currency
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String currency;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.division
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String division;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.region
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String region;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.power_supply
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String powerSupply;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.start_date
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String startDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.end_date
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String endDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.is_completed
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String isCompleted;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.jv
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String jv;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.total_cfa
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String totalCfa;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.sort
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private Integer sort;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.scene
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String scene;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.area_id
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String areaId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.type
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String type;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.creation_time
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private LocalDateTime creationTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.create_username
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String createUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.create_user_id
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String createUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.last_update_time
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private LocalDateTime lastUpdateTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.last_update_username
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String lastUpdateUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.last_update_user_id
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private String lastUpdateUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.last_update_version
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private Integer lastUpdateVersion;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_organization.is_deleted
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	private Boolean isDeleted;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.id
	 * @return  the value of t_organization.id
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.id
	 * @param id  the value for t_organization.id
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.parent_id
	 * @return  the value of t_organization.parent_id
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getParentId() {
		return parentId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.parent_id
	 * @param parentId  the value for t_organization.parent_id
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setParentId(String parentId) {
		this.parentId = parentId == null ? null : parentId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.no
	 * @return  the value of t_organization.no
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getNo() {
		return no;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.no
	 * @param no  the value for t_organization.no
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setNo(String no) {
		this.no = no == null ? null : no.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.name
	 * @return  the value of t_organization.name
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getName() {
		return name;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.name
	 * @param name  the value for t_organization.name
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setName(String name) {
		this.name = name == null ? null : name.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.code
	 * @return  the value of t_organization.code
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getCode() {
		return code;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.code
	 * @param code  the value for t_organization.code
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setCode(String code) {
		this.code = code == null ? null : code.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.unit_code
	 * @return  the value of t_organization.unit_code
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getUnitCode() {
		return unitCode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.unit_code
	 * @param unitCode  the value for t_organization.unit_code
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setUnitCode(String unitCode) {
		this.unitCode = unitCode == null ? null : unitCode.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.title
	 * @return  the value of t_organization.title
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getTitle() {
		return title;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.title
	 * @param title  the value for t_organization.title
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setTitle(String title) {
		this.title = title == null ? null : title.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.address
	 * @return  the value of t_organization.address
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getAddress() {
		return address;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.address
	 * @param address  the value for t_organization.address
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setAddress(String address) {
		this.address = address == null ? null : address.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.latitude
	 * @return  the value of t_organization.latitude
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getLatitude() {
		return latitude;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.latitude
	 * @param latitude  the value for t_organization.latitude
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setLatitude(String latitude) {
		this.latitude = latitude == null ? null : latitude.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.longitude
	 * @return  the value of t_organization.longitude
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getLongitude() {
		return longitude;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.longitude
	 * @param longitude  the value for t_organization.longitude
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setLongitude(String longitude) {
		this.longitude = longitude == null ? null : longitude.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.currency
	 * @return  the value of t_organization.currency
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getCurrency() {
		return currency;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.currency
	 * @param currency  the value for t_organization.currency
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setCurrency(String currency) {
		this.currency = currency == null ? null : currency.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.division
	 * @return  the value of t_organization.division
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getDivision() {
		return division;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.division
	 * @param division  the value for t_organization.division
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setDivision(String division) {
		this.division = division == null ? null : division.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.region
	 * @return  the value of t_organization.region
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getRegion() {
		return region;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.region
	 * @param region  the value for t_organization.region
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setRegion(String region) {
		this.region = region == null ? null : region.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.power_supply
	 * @return  the value of t_organization.power_supply
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getPowerSupply() {
		return powerSupply;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.power_supply
	 * @param powerSupply  the value for t_organization.power_supply
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setPowerSupply(String powerSupply) {
		this.powerSupply = powerSupply == null ? null : powerSupply.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.start_date
	 * @return  the value of t_organization.start_date
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getStartDate() {
		return startDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.start_date
	 * @param startDate  the value for t_organization.start_date
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setStartDate(String startDate) {
		this.startDate = startDate == null ? null : startDate.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.end_date
	 * @return  the value of t_organization.end_date
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getEndDate() {
		return endDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.end_date
	 * @param endDate  the value for t_organization.end_date
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setEndDate(String endDate) {
		this.endDate = endDate == null ? null : endDate.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.is_completed
	 * @return  the value of t_organization.is_completed
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getIsCompleted() {
		return isCompleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.is_completed
	 * @param isCompleted  the value for t_organization.is_completed
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setIsCompleted(String isCompleted) {
		this.isCompleted = isCompleted == null ? null : isCompleted.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.jv
	 * @return  the value of t_organization.jv
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getJv() {
		return jv;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.jv
	 * @param jv  the value for t_organization.jv
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setJv(String jv) {
		this.jv = jv == null ? null : jv.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.total_cfa
	 * @return  the value of t_organization.total_cfa
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getTotalCfa() {
		return totalCfa;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.total_cfa
	 * @param totalCfa  the value for t_organization.total_cfa
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setTotalCfa(String totalCfa) {
		this.totalCfa = totalCfa == null ? null : totalCfa.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.sort
	 * @return  the value of t_organization.sort
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public Integer getSort() {
		return sort;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.sort
	 * @param sort  the value for t_organization.sort
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setSort(Integer sort) {
		this.sort = sort;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.scene
	 * @return  the value of t_organization.scene
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getScene() {
		return scene;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.scene
	 * @param scene  the value for t_organization.scene
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setScene(String scene) {
		this.scene = scene == null ? null : scene.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.area_id
	 * @return  the value of t_organization.area_id
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getAreaId() {
		return areaId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.area_id
	 * @param areaId  the value for t_organization.area_id
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setAreaId(String areaId) {
		this.areaId = areaId == null ? null : areaId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.type
	 * @return  the value of t_organization.type
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getType() {
		return type;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.type
	 * @param type  the value for t_organization.type
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setType(String type) {
		this.type = type == null ? null : type.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.creation_time
	 * @return  the value of t_organization.creation_time
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public LocalDateTime getCreationTime() {
		return creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.creation_time
	 * @param creationTime  the value for t_organization.creation_time
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setCreationTime(LocalDateTime creationTime) {
		this.creationTime = creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.create_username
	 * @return  the value of t_organization.create_username
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getCreateUsername() {
		return createUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.create_username
	 * @param createUsername  the value for t_organization.create_username
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setCreateUsername(String createUsername) {
		this.createUsername = createUsername == null ? null : createUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.create_user_id
	 * @return  the value of t_organization.create_user_id
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getCreateUserId() {
		return createUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.create_user_id
	 * @param createUserId  the value for t_organization.create_user_id
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId == null ? null : createUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.last_update_time
	 * @return  the value of t_organization.last_update_time
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public LocalDateTime getLastUpdateTime() {
		return lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.last_update_time
	 * @param lastUpdateTime  the value for t_organization.last_update_time
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.last_update_username
	 * @return  the value of t_organization.last_update_username
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getLastUpdateUsername() {
		return lastUpdateUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.last_update_username
	 * @param lastUpdateUsername  the value for t_organization.last_update_username
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setLastUpdateUsername(String lastUpdateUsername) {
		this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.last_update_user_id
	 * @return  the value of t_organization.last_update_user_id
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public String getLastUpdateUserId() {
		return lastUpdateUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.last_update_user_id
	 * @param lastUpdateUserId  the value for t_organization.last_update_user_id
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setLastUpdateUserId(String lastUpdateUserId) {
		this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.last_update_version
	 * @return  the value of t_organization.last_update_version
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public Integer getLastUpdateVersion() {
		return lastUpdateVersion;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.last_update_version
	 * @param lastUpdateVersion  the value for t_organization.last_update_version
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setLastUpdateVersion(Integer lastUpdateVersion) {
		this.lastUpdateVersion = lastUpdateVersion;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_organization.is_deleted
	 * @return  the value of t_organization.is_deleted
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public Boolean getIsDeleted() {
		return isDeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_organization.is_deleted
	 * @param isDeleted  the value for t_organization.is_deleted
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}
}