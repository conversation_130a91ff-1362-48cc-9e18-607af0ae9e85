package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class AccountingManagement {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_accounting_management.id
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_accounting_management.protocol_configuration_id
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    private String protocolConfigurationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_accounting_management.chinese_name
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    private String chineseName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_accounting_management.count_one
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    private BigDecimal countOne;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_accounting_management.count_two
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    private BigDecimal countTwo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_accounting_management.compute_symbol
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    private String computeSymbol;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_accounting_management.calculation_result
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    private BigDecimal calculationResult;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_accounting_management.creation_time
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_accounting_management.create_username
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_accounting_management.create_user_id
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_accounting_management.last_update_time
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_accounting_management.last_update_username
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_accounting_management.last_update_user_id
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_accounting_management.last_update_version
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_accounting_management.is_deleted
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    private Boolean isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_accounting_management.id
     *
     * @return the value of t_accounting_management.id
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_accounting_management.id
     *
     * @param id the value for t_accounting_management.id
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_accounting_management.protocol_configuration_id
     *
     * @return the value of t_accounting_management.protocol_configuration_id
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public String getProtocolConfigurationId() {
        return protocolConfigurationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_accounting_management.protocol_configuration_id
     *
     * @param protocolConfigurationId the value for t_accounting_management.protocol_configuration_id
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setProtocolConfigurationId(String protocolConfigurationId) {
        this.protocolConfigurationId = protocolConfigurationId == null ? null : protocolConfigurationId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_accounting_management.chinese_name
     *
     * @return the value of t_accounting_management.chinese_name
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public String getChineseName() {
        return chineseName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_accounting_management.chinese_name
     *
     * @param chineseName the value for t_accounting_management.chinese_name
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setChineseName(String chineseName) {
        this.chineseName = chineseName == null ? null : chineseName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_accounting_management.count_one
     *
     * @return the value of t_accounting_management.count_one
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public BigDecimal getCountOne() {
        return countOne;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_accounting_management.count_one
     *
     * @param countOne the value for t_accounting_management.count_one
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setCountOne(BigDecimal countOne) {
        this.countOne = countOne;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_accounting_management.count_two
     *
     * @return the value of t_accounting_management.count_two
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public BigDecimal getCountTwo() {
        return countTwo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_accounting_management.count_two
     *
     * @param countTwo the value for t_accounting_management.count_two
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setCountTwo(BigDecimal countTwo) {
        this.countTwo = countTwo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_accounting_management.compute_symbol
     *
     * @return the value of t_accounting_management.compute_symbol
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public String getComputeSymbol() {
        return computeSymbol;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_accounting_management.compute_symbol
     *
     * @param computeSymbol the value for t_accounting_management.compute_symbol
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setComputeSymbol(String computeSymbol) {
        this.computeSymbol = computeSymbol == null ? null : computeSymbol.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_accounting_management.calculation_result
     *
     * @return the value of t_accounting_management.calculation_result
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public BigDecimal getCalculationResult() {
        return calculationResult;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_accounting_management.calculation_result
     *
     * @param calculationResult the value for t_accounting_management.calculation_result
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setCalculationResult(BigDecimal calculationResult) {
        this.calculationResult = calculationResult;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_accounting_management.creation_time
     *
     * @return the value of t_accounting_management.creation_time
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_accounting_management.creation_time
     *
     * @param creationTime the value for t_accounting_management.creation_time
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_accounting_management.create_username
     *
     * @return the value of t_accounting_management.create_username
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_accounting_management.create_username
     *
     * @param createUsername the value for t_accounting_management.create_username
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_accounting_management.create_user_id
     *
     * @return the value of t_accounting_management.create_user_id
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_accounting_management.create_user_id
     *
     * @param createUserId the value for t_accounting_management.create_user_id
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_accounting_management.last_update_time
     *
     * @return the value of t_accounting_management.last_update_time
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_accounting_management.last_update_time
     *
     * @param lastUpdateTime the value for t_accounting_management.last_update_time
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_accounting_management.last_update_username
     *
     * @return the value of t_accounting_management.last_update_username
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_accounting_management.last_update_username
     *
     * @param lastUpdateUsername the value for t_accounting_management.last_update_username
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_accounting_management.last_update_user_id
     *
     * @return the value of t_accounting_management.last_update_user_id
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_accounting_management.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_accounting_management.last_update_user_id
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_accounting_management.last_update_version
     *
     * @return the value of t_accounting_management.last_update_version
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_accounting_management.last_update_version
     *
     * @param lastUpdateVersion the value for t_accounting_management.last_update_version
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_accounting_management.is_deleted
     *
     * @return the value of t_accounting_management.is_deleted
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_accounting_management.is_deleted
     *
     * @param isDeleted the value for t_accounting_management.is_deleted
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
}