package com.csci.susdev.model;

import java.time.LocalDateTime;

public class CarbonEmissionLocation {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_carbon_emission_location.id
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_carbon_emission_location.name
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_carbon_emission_location.name_sc
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    private String nameSc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_carbon_emission_location.name_en
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    private String nameEn;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_carbon_emission_location.description
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_carbon_emission_location.creation_time
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_carbon_emission_location.create_username
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_carbon_emission_location.create_user_id
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_carbon_emission_location.last_update_time
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_carbon_emission_location.last_update_username
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_carbon_emission_location.last_update_user_id
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_carbon_emission_location.last_update_version
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_carbon_emission_location.is_deleted
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    private Boolean isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_carbon_emission_location.id
     *
     * @return the value of t_carbon_emission_location.id
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_carbon_emission_location.id
     *
     * @param id the value for t_carbon_emission_location.id
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_carbon_emission_location.name
     *
     * @return the value of t_carbon_emission_location.name
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_carbon_emission_location.name
     *
     * @param name the value for t_carbon_emission_location.name
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_carbon_emission_location.name_sc
     *
     * @return the value of t_carbon_emission_location.name_sc
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public String getNameSc() {
        return nameSc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_carbon_emission_location.name_sc
     *
     * @param nameSc the value for t_carbon_emission_location.name_sc
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public void setNameSc(String nameSc) {
        this.nameSc = nameSc == null ? null : nameSc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_carbon_emission_location.name_en
     *
     * @return the value of t_carbon_emission_location.name_en
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public String getNameEn() {
        return nameEn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_carbon_emission_location.name_en
     *
     * @param nameEn the value for t_carbon_emission_location.name_en
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public void setNameEn(String nameEn) {
        this.nameEn = nameEn == null ? null : nameEn.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_carbon_emission_location.description
     *
     * @return the value of t_carbon_emission_location.description
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_carbon_emission_location.description
     *
     * @param description the value for t_carbon_emission_location.description
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_carbon_emission_location.creation_time
     *
     * @return the value of t_carbon_emission_location.creation_time
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_carbon_emission_location.creation_time
     *
     * @param creationTime the value for t_carbon_emission_location.creation_time
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_carbon_emission_location.create_username
     *
     * @return the value of t_carbon_emission_location.create_username
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_carbon_emission_location.create_username
     *
     * @param createUsername the value for t_carbon_emission_location.create_username
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_carbon_emission_location.create_user_id
     *
     * @return the value of t_carbon_emission_location.create_user_id
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_carbon_emission_location.create_user_id
     *
     * @param createUserId the value for t_carbon_emission_location.create_user_id
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_carbon_emission_location.last_update_time
     *
     * @return the value of t_carbon_emission_location.last_update_time
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_carbon_emission_location.last_update_time
     *
     * @param lastUpdateTime the value for t_carbon_emission_location.last_update_time
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_carbon_emission_location.last_update_username
     *
     * @return the value of t_carbon_emission_location.last_update_username
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_carbon_emission_location.last_update_username
     *
     * @param lastUpdateUsername the value for t_carbon_emission_location.last_update_username
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_carbon_emission_location.last_update_user_id
     *
     * @return the value of t_carbon_emission_location.last_update_user_id
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_carbon_emission_location.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_carbon_emission_location.last_update_user_id
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_carbon_emission_location.last_update_version
     *
     * @return the value of t_carbon_emission_location.last_update_version
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_carbon_emission_location.last_update_version
     *
     * @param lastUpdateVersion the value for t_carbon_emission_location.last_update_version
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_carbon_emission_location.is_deleted
     *
     * @return the value of t_carbon_emission_location.is_deleted
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_carbon_emission_location.is_deleted
     *
     * @param isDeleted the value for t_carbon_emission_location.is_deleted
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
}