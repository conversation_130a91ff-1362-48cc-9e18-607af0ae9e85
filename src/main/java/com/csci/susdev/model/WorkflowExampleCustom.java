package com.csci.susdev.model;

/**
 * 自定义条件
 * 增加了多个字段逻辑或连接的like条件拼接
 * 如果要增加
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 12/5/2019
 */
public class WorkflowExampleCustom extends WorkflowExample {

    public WorkflowExampleCustom() {
        super();
    }

    @Override
    protected CustomCriteria createCriteriaInternal() {
        return new CustomCriteria();
    }

    @Override
    public CustomCriteria or() {
        return (CustomCriteria) super.or();
    }

    @Override
    public CustomCriteria createCriteria() {
        return (CustomCriteria) super.createCriteria();
    }

    public static class CustomCriteria extends Criteria {
        protected CustomCriteria() {
            super();
        }

        public CustomCriteria andMultiColumnLike(String value, String... columns) {
            StringBuilder sbSQL = new StringBuilder();
            sbSQL.append("(");
            for (int i = 0, iSize = columns.length; i < iSize; i++) {
                String column = columns[i];
                sbSQL.append(column).append(" like ").append("'").append(value).append("'");
                if (i != iSize - 1) {
                    sbSQL.append(" OR ");
                }
            }
            sbSQL.append(")");
            addCriterion(sbSQL.toString());
            return this;
        }

        /**
         * 样例代码，可以通过这种方式添加自定义的sql片段到查询条件中去
         *
         * @param conditionSql 如：exists(select 1 from a where a.table_id = table.id)
         * @return
         */
        public CustomCriteria andExampleCondition(String conditionSql) {
            addCriterion(conditionSql);
            return this;
        }
    }
}