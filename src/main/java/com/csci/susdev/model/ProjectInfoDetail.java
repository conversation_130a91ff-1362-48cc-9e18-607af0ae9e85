package com.csci.susdev.model;

public class ProjectInfoDetail {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.id
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.head_id
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String headId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.display_name
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String displayName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.organization_id
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String organizationId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.job_no
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String jobNo;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.title
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String title;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.address
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String address;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.latitude
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String latitude;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.longidute
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String longidute;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.currency
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String currency;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.division
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String division;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.region
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String region;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.power_supply
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String powerSupply;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.start_date
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String startDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.end_date
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String endDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.is_completed
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String isCompleted;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.jv
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String jv;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_project_info_detail.total_cfa
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	private String totalCfa;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.id
	 * @return  the value of t_project_info_detail.id
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.id
	 * @param id  the value for t_project_info_detail.id
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.head_id
	 * @return  the value of t_project_info_detail.head_id
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getHeadId() {
		return headId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.head_id
	 * @param headId  the value for t_project_info_detail.head_id
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setHeadId(String headId) {
		this.headId = headId == null ? null : headId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.display_name
	 * @return  the value of t_project_info_detail.display_name
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getDisplayName() {
		return displayName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.display_name
	 * @param displayName  the value for t_project_info_detail.display_name
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setDisplayName(String displayName) {
		this.displayName = displayName == null ? null : displayName.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.organization_id
	 * @return  the value of t_project_info_detail.organization_id
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getOrganizationId() {
		return organizationId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.organization_id
	 * @param organizationId  the value for t_project_info_detail.organization_id
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setOrganizationId(String organizationId) {
		this.organizationId = organizationId == null ? null : organizationId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.job_no
	 * @return  the value of t_project_info_detail.job_no
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getJobNo() {
		return jobNo;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.job_no
	 * @param jobNo  the value for t_project_info_detail.job_no
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setJobNo(String jobNo) {
		this.jobNo = jobNo == null ? null : jobNo.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.title
	 * @return  the value of t_project_info_detail.title
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getTitle() {
		return title;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.title
	 * @param title  the value for t_project_info_detail.title
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setTitle(String title) {
		this.title = title == null ? null : title.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.address
	 * @return  the value of t_project_info_detail.address
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getAddress() {
		return address;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.address
	 * @param address  the value for t_project_info_detail.address
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setAddress(String address) {
		this.address = address == null ? null : address.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.latitude
	 * @return  the value of t_project_info_detail.latitude
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getLatitude() {
		return latitude;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.latitude
	 * @param latitude  the value for t_project_info_detail.latitude
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setLatitude(String latitude) {
		this.latitude = latitude == null ? null : latitude.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.longidute
	 * @return  the value of t_project_info_detail.longidute
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getLongidute() {
		return longidute;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.longidute
	 * @param longidute  the value for t_project_info_detail.longidute
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setLongidute(String longidute) {
		this.longidute = longidute == null ? null : longidute.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.currency
	 * @return  the value of t_project_info_detail.currency
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getCurrency() {
		return currency;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.currency
	 * @param currency  the value for t_project_info_detail.currency
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setCurrency(String currency) {
		this.currency = currency == null ? null : currency.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.division
	 * @return  the value of t_project_info_detail.division
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getDivision() {
		return division;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.division
	 * @param division  the value for t_project_info_detail.division
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setDivision(String division) {
		this.division = division == null ? null : division.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.region
	 * @return  the value of t_project_info_detail.region
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getRegion() {
		return region;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.region
	 * @param region  the value for t_project_info_detail.region
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setRegion(String region) {
		this.region = region == null ? null : region.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.power_supply
	 * @return  the value of t_project_info_detail.power_supply
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getPowerSupply() {
		return powerSupply;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.power_supply
	 * @param powerSupply  the value for t_project_info_detail.power_supply
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setPowerSupply(String powerSupply) {
		this.powerSupply = powerSupply == null ? null : powerSupply.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.start_date
	 * @return  the value of t_project_info_detail.start_date
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getStartDate() {
		return startDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.start_date
	 * @param startDate  the value for t_project_info_detail.start_date
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setStartDate(String startDate) {
		this.startDate = startDate == null ? null : startDate.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.end_date
	 * @return  the value of t_project_info_detail.end_date
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getEndDate() {
		return endDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.end_date
	 * @param endDate  the value for t_project_info_detail.end_date
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setEndDate(String endDate) {
		this.endDate = endDate == null ? null : endDate.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.is_completed
	 * @return  the value of t_project_info_detail.is_completed
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getIsCompleted() {
		return isCompleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.is_completed
	 * @param isCompleted  the value for t_project_info_detail.is_completed
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setIsCompleted(String isCompleted) {
		this.isCompleted = isCompleted == null ? null : isCompleted.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.jv
	 * @return  the value of t_project_info_detail.jv
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getJv() {
		return jv;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.jv
	 * @param jv  the value for t_project_info_detail.jv
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setJv(String jv) {
		this.jv = jv == null ? null : jv.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_project_info_detail.total_cfa
	 * @return  the value of t_project_info_detail.total_cfa
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public String getTotalCfa() {
		return totalCfa;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_project_info_detail.total_cfa
	 * @param totalCfa  the value for t_project_info_detail.total_cfa
	 * @mbg.generated  Wed May 04 16:56:59 HKT 2022
	 */
	public void setTotalCfa(String totalCfa) {
		this.totalCfa = totalCfa == null ? null : totalCfa.trim();
	}
}