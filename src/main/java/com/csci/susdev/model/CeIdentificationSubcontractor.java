package com.csci.susdev.model;

import java.time.LocalDateTime;

public class CeIdentificationSubcontractor {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.id
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.ce_identification_head_id
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String ceIdentificationHeadId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.type
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String type;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.name
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String name;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.duty
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String duty;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.duty_type
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String dutyType;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.ss_fossil_fuel
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String ssFossilFuel;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.ms_fossil_fuel_qty
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String msFossilFuelQty;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.ms_fossil_fuel_ton_km
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String msFossilFuelTonKm;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.ms_fossil_fuel_machine_team
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String msFossilFuelMachineTeam;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.process_emission
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String processEmission;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.electricity
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String electricity;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.outsourced_building_material
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String outsourcedBuildingMaterial;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.creation_time
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private LocalDateTime creationTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.create_username
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String createUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.create_user_id
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String createUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.last_update_time
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private LocalDateTime lastUpdateTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.last_update_username
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String lastUpdateUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.last_update_user_id
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private String lastUpdateUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ce_identification_subcontractor.last_update_version
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	private Integer lastUpdateVersion;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.id
	 * @return  the value of t_ce_identification_subcontractor.id
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.id
	 * @param id  the value for t_ce_identification_subcontractor.id
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.ce_identification_head_id
	 * @return  the value of t_ce_identification_subcontractor.ce_identification_head_id
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getCeIdentificationHeadId() {
		return ceIdentificationHeadId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.ce_identification_head_id
	 * @param ceIdentificationHeadId  the value for t_ce_identification_subcontractor.ce_identification_head_id
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setCeIdentificationHeadId(String ceIdentificationHeadId) {
		this.ceIdentificationHeadId = ceIdentificationHeadId == null ? null : ceIdentificationHeadId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.type
	 * @return  the value of t_ce_identification_subcontractor.type
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getType() {
		return type;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.type
	 * @param type  the value for t_ce_identification_subcontractor.type
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setType(String type) {
		this.type = type == null ? null : type.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.name
	 * @return  the value of t_ce_identification_subcontractor.name
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getName() {
		return name;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.name
	 * @param name  the value for t_ce_identification_subcontractor.name
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setName(String name) {
		this.name = name == null ? null : name.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.duty
	 * @return  the value of t_ce_identification_subcontractor.duty
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getDuty() {
		return duty;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.duty
	 * @param duty  the value for t_ce_identification_subcontractor.duty
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setDuty(String duty) {
		this.duty = duty == null ? null : duty.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.duty_type
	 * @return  the value of t_ce_identification_subcontractor.duty_type
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getDutyType() {
		return dutyType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.duty_type
	 * @param dutyType  the value for t_ce_identification_subcontractor.duty_type
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setDutyType(String dutyType) {
		this.dutyType = dutyType == null ? null : dutyType.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.ss_fossil_fuel
	 * @return  the value of t_ce_identification_subcontractor.ss_fossil_fuel
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getSsFossilFuel() {
		return ssFossilFuel;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.ss_fossil_fuel
	 * @param ssFossilFuel  the value for t_ce_identification_subcontractor.ss_fossil_fuel
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setSsFossilFuel(String ssFossilFuel) {
		this.ssFossilFuel = ssFossilFuel == null ? null : ssFossilFuel.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.ms_fossil_fuel_qty
	 * @return  the value of t_ce_identification_subcontractor.ms_fossil_fuel_qty
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getMsFossilFuelQty() {
		return msFossilFuelQty;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.ms_fossil_fuel_qty
	 * @param msFossilFuelQty  the value for t_ce_identification_subcontractor.ms_fossil_fuel_qty
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setMsFossilFuelQty(String msFossilFuelQty) {
		this.msFossilFuelQty = msFossilFuelQty == null ? null : msFossilFuelQty.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.ms_fossil_fuel_ton_km
	 * @return  the value of t_ce_identification_subcontractor.ms_fossil_fuel_ton_km
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getMsFossilFuelTonKm() {
		return msFossilFuelTonKm;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.ms_fossil_fuel_ton_km
	 * @param msFossilFuelTonKm  the value for t_ce_identification_subcontractor.ms_fossil_fuel_ton_km
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setMsFossilFuelTonKm(String msFossilFuelTonKm) {
		this.msFossilFuelTonKm = msFossilFuelTonKm == null ? null : msFossilFuelTonKm.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.ms_fossil_fuel_machine_team
	 * @return  the value of t_ce_identification_subcontractor.ms_fossil_fuel_machine_team
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getMsFossilFuelMachineTeam() {
		return msFossilFuelMachineTeam;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.ms_fossil_fuel_machine_team
	 * @param msFossilFuelMachineTeam  the value for t_ce_identification_subcontractor.ms_fossil_fuel_machine_team
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setMsFossilFuelMachineTeam(String msFossilFuelMachineTeam) {
		this.msFossilFuelMachineTeam = msFossilFuelMachineTeam == null ? null : msFossilFuelMachineTeam.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.process_emission
	 * @return  the value of t_ce_identification_subcontractor.process_emission
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getProcessEmission() {
		return processEmission;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.process_emission
	 * @param processEmission  the value for t_ce_identification_subcontractor.process_emission
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setProcessEmission(String processEmission) {
		this.processEmission = processEmission == null ? null : processEmission.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.electricity
	 * @return  the value of t_ce_identification_subcontractor.electricity
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getElectricity() {
		return electricity;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.electricity
	 * @param electricity  the value for t_ce_identification_subcontractor.electricity
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setElectricity(String electricity) {
		this.electricity = electricity == null ? null : electricity.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.outsourced_building_material
	 * @return  the value of t_ce_identification_subcontractor.outsourced_building_material
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getOutsourcedBuildingMaterial() {
		return outsourcedBuildingMaterial;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.outsourced_building_material
	 * @param outsourcedBuildingMaterial  the value for t_ce_identification_subcontractor.outsourced_building_material
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setOutsourcedBuildingMaterial(String outsourcedBuildingMaterial) {
		this.outsourcedBuildingMaterial = outsourcedBuildingMaterial == null ? null : outsourcedBuildingMaterial.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.creation_time
	 * @return  the value of t_ce_identification_subcontractor.creation_time
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public LocalDateTime getCreationTime() {
		return creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.creation_time
	 * @param creationTime  the value for t_ce_identification_subcontractor.creation_time
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setCreationTime(LocalDateTime creationTime) {
		this.creationTime = creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.create_username
	 * @return  the value of t_ce_identification_subcontractor.create_username
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getCreateUsername() {
		return createUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.create_username
	 * @param createUsername  the value for t_ce_identification_subcontractor.create_username
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setCreateUsername(String createUsername) {
		this.createUsername = createUsername == null ? null : createUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.create_user_id
	 * @return  the value of t_ce_identification_subcontractor.create_user_id
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getCreateUserId() {
		return createUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.create_user_id
	 * @param createUserId  the value for t_ce_identification_subcontractor.create_user_id
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId == null ? null : createUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.last_update_time
	 * @return  the value of t_ce_identification_subcontractor.last_update_time
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public LocalDateTime getLastUpdateTime() {
		return lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.last_update_time
	 * @param lastUpdateTime  the value for t_ce_identification_subcontractor.last_update_time
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.last_update_username
	 * @return  the value of t_ce_identification_subcontractor.last_update_username
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getLastUpdateUsername() {
		return lastUpdateUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.last_update_username
	 * @param lastUpdateUsername  the value for t_ce_identification_subcontractor.last_update_username
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setLastUpdateUsername(String lastUpdateUsername) {
		this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.last_update_user_id
	 * @return  the value of t_ce_identification_subcontractor.last_update_user_id
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public String getLastUpdateUserId() {
		return lastUpdateUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.last_update_user_id
	 * @param lastUpdateUserId  the value for t_ce_identification_subcontractor.last_update_user_id
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setLastUpdateUserId(String lastUpdateUserId) {
		this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ce_identification_subcontractor.last_update_version
	 * @return  the value of t_ce_identification_subcontractor.last_update_version
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public Integer getLastUpdateVersion() {
		return lastUpdateVersion;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ce_identification_subcontractor.last_update_version
	 * @param lastUpdateVersion  the value for t_ce_identification_subcontractor.last_update_version
	 * @mbg.generated  Mon Sep 18 15:40:16 HKT 2023
	 */
	public void setLastUpdateVersion(Integer lastUpdateVersion) {
		this.lastUpdateVersion = lastUpdateVersion;
	}
}