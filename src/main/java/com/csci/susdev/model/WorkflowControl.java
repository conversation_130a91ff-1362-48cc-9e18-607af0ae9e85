package com.csci.susdev.model;

import java.time.LocalDateTime;

public class WorkflowControl {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_workflow_control.id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_workflow_control.workflow_id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	private String workflowId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_workflow_control.business_id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	private String businessId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_workflow_control.current_node_id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	private String currentNodeId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_workflow_control.state
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	private Integer state;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_workflow_control.is_active
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	private Boolean isActive;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_workflow_control.is_urgent_editable
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	private Boolean isUrgentEditable;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_workflow_control.creation_time
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	private LocalDateTime creationTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_workflow_control.create_username
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	private String createUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_workflow_control.create_user_id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	private String createUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_workflow_control.last_update_time
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	private LocalDateTime lastUpdateTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_workflow_control.last_update_username
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	private String lastUpdateUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_workflow_control.last_update_user_id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	private String lastUpdateUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_workflow_control.last_update_version
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	private Integer lastUpdateVersion;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_workflow_control.id
	 * @return  the value of t_workflow_control.id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_workflow_control.id
	 * @param id  the value for t_workflow_control.id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_workflow_control.workflow_id
	 * @return  the value of t_workflow_control.workflow_id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public String getWorkflowId() {
		return workflowId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_workflow_control.workflow_id
	 * @param workflowId  the value for t_workflow_control.workflow_id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public void setWorkflowId(String workflowId) {
		this.workflowId = workflowId == null ? null : workflowId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_workflow_control.business_id
	 * @return  the value of t_workflow_control.business_id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public String getBusinessId() {
		return businessId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_workflow_control.business_id
	 * @param businessId  the value for t_workflow_control.business_id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public void setBusinessId(String businessId) {
		this.businessId = businessId == null ? null : businessId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_workflow_control.current_node_id
	 * @return  the value of t_workflow_control.current_node_id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public String getCurrentNodeId() {
		return currentNodeId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_workflow_control.current_node_id
	 * @param currentNodeId  the value for t_workflow_control.current_node_id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public void setCurrentNodeId(String currentNodeId) {
		this.currentNodeId = currentNodeId == null ? null : currentNodeId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_workflow_control.state
	 * @return  the value of t_workflow_control.state
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public Integer getState() {
		return state;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_workflow_control.state
	 * @param state  the value for t_workflow_control.state
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public void setState(Integer state) {
		this.state = state;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_workflow_control.is_active
	 * @return  the value of t_workflow_control.is_active
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public Boolean getIsActive() {
		return isActive;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_workflow_control.is_active
	 * @param isActive  the value for t_workflow_control.is_active
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public void setIsActive(Boolean isActive) {
		this.isActive = isActive;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_workflow_control.is_urgent_editable
	 * @return  the value of t_workflow_control.is_urgent_editable
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public Boolean getIsUrgentEditable() {
		return isUrgentEditable;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_workflow_control.is_urgent_editable
	 * @param isUrgentEditable  the value for t_workflow_control.is_urgent_editable
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public void setIsUrgentEditable(Boolean isUrgentEditable) {
		this.isUrgentEditable = isUrgentEditable;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_workflow_control.creation_time
	 * @return  the value of t_workflow_control.creation_time
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public LocalDateTime getCreationTime() {
		return creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_workflow_control.creation_time
	 * @param creationTime  the value for t_workflow_control.creation_time
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public void setCreationTime(LocalDateTime creationTime) {
		this.creationTime = creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_workflow_control.create_username
	 * @return  the value of t_workflow_control.create_username
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public String getCreateUsername() {
		return createUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_workflow_control.create_username
	 * @param createUsername  the value for t_workflow_control.create_username
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public void setCreateUsername(String createUsername) {
		this.createUsername = createUsername == null ? null : createUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_workflow_control.create_user_id
	 * @return  the value of t_workflow_control.create_user_id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public String getCreateUserId() {
		return createUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_workflow_control.create_user_id
	 * @param createUserId  the value for t_workflow_control.create_user_id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId == null ? null : createUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_workflow_control.last_update_time
	 * @return  the value of t_workflow_control.last_update_time
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public LocalDateTime getLastUpdateTime() {
		return lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_workflow_control.last_update_time
	 * @param lastUpdateTime  the value for t_workflow_control.last_update_time
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_workflow_control.last_update_username
	 * @return  the value of t_workflow_control.last_update_username
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public String getLastUpdateUsername() {
		return lastUpdateUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_workflow_control.last_update_username
	 * @param lastUpdateUsername  the value for t_workflow_control.last_update_username
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public void setLastUpdateUsername(String lastUpdateUsername) {
		this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_workflow_control.last_update_user_id
	 * @return  the value of t_workflow_control.last_update_user_id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public String getLastUpdateUserId() {
		return lastUpdateUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_workflow_control.last_update_user_id
	 * @param lastUpdateUserId  the value for t_workflow_control.last_update_user_id
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public void setLastUpdateUserId(String lastUpdateUserId) {
		this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_workflow_control.last_update_version
	 * @return  the value of t_workflow_control.last_update_version
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public Integer getLastUpdateVersion() {
		return lastUpdateVersion;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_workflow_control.last_update_version
	 * @param lastUpdateVersion  the value for t_workflow_control.last_update_version
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	public void setLastUpdateVersion(Integer lastUpdateVersion) {
		this.lastUpdateVersion = lastUpdateVersion;
	}
}