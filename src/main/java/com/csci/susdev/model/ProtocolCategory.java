package com.csci.susdev.model;

import java.time.LocalDateTime;

public class ProtocolCategory {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_category.id
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_category.protocol_id
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    private String protocolId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_category.category_name
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    private String categoryName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_category.category_name_sc
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    private String categoryNameSc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_category.category_name_en
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    private String categoryNameEn;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_category.creation_time
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_category.create_username
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_category.create_user_id
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_category.last_update_time
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_category.last_update_username
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_category.last_update_user_id
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_category.last_update_version
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_category.is_deleted
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    private Boolean isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_category.id
     *
     * @return the value of t_protocol_category.id
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_category.id
     *
     * @param id the value for t_protocol_category.id
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_category.protocol_id
     *
     * @return the value of t_protocol_category.protocol_id
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public String getProtocolId() {
        return protocolId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_category.protocol_id
     *
     * @param protocolId the value for t_protocol_category.protocol_id
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public void setProtocolId(String protocolId) {
        this.protocolId = protocolId == null ? null : protocolId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_category.category_name
     *
     * @return the value of t_protocol_category.category_name
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public String getCategoryName() {
        return categoryName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_category.category_name
     *
     * @param categoryName the value for t_protocol_category.category_name
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName == null ? null : categoryName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_category.category_name_sc
     *
     * @return the value of t_protocol_category.category_name_sc
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public String getCategoryNameSc() {
        return categoryNameSc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_category.category_name_sc
     *
     * @param categoryNameSc the value for t_protocol_category.category_name_sc
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public void setCategoryNameSc(String categoryNameSc) {
        this.categoryNameSc = categoryNameSc == null ? null : categoryNameSc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_category.category_name_en
     *
     * @return the value of t_protocol_category.category_name_en
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public String getCategoryNameEn() {
        return categoryNameEn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_category.category_name_en
     *
     * @param categoryNameEn the value for t_protocol_category.category_name_en
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public void setCategoryNameEn(String categoryNameEn) {
        this.categoryNameEn = categoryNameEn == null ? null : categoryNameEn.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_category.creation_time
     *
     * @return the value of t_protocol_category.creation_time
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_category.creation_time
     *
     * @param creationTime the value for t_protocol_category.creation_time
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_category.create_username
     *
     * @return the value of t_protocol_category.create_username
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_category.create_username
     *
     * @param createUsername the value for t_protocol_category.create_username
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_category.create_user_id
     *
     * @return the value of t_protocol_category.create_user_id
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_category.create_user_id
     *
     * @param createUserId the value for t_protocol_category.create_user_id
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_category.last_update_time
     *
     * @return the value of t_protocol_category.last_update_time
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_category.last_update_time
     *
     * @param lastUpdateTime the value for t_protocol_category.last_update_time
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_category.last_update_username
     *
     * @return the value of t_protocol_category.last_update_username
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_category.last_update_username
     *
     * @param lastUpdateUsername the value for t_protocol_category.last_update_username
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_category.last_update_user_id
     *
     * @return the value of t_protocol_category.last_update_user_id
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_category.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_protocol_category.last_update_user_id
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_category.last_update_version
     *
     * @return the value of t_protocol_category.last_update_version
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_category.last_update_version
     *
     * @param lastUpdateVersion the value for t_protocol_category.last_update_version
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_category.is_deleted
     *
     * @return the value of t_protocol_category.is_deleted
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_category.is_deleted
     *
     * @param isDeleted the value for t_protocol_category.is_deleted
     *
     * @mbg.generated Wed Apr 10 09:46:06 HKT 2024
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
}