package com.csci.susdev.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class CeIdentificationRunawayEmissionExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public CeIdentificationRunawayEmissionExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdIsNull() {
            addCriterion("ce_identification_head_id is null");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdIsNotNull() {
            addCriterion("ce_identification_head_id is not null");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdEqualTo(String value) {
            addCriterion("ce_identification_head_id =", value, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdNotEqualTo(String value) {
            addCriterion("ce_identification_head_id <>", value, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdGreaterThan(String value) {
            addCriterion("ce_identification_head_id >", value, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdGreaterThanOrEqualTo(String value) {
            addCriterion("ce_identification_head_id >=", value, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdLessThan(String value) {
            addCriterion("ce_identification_head_id <", value, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdLessThanOrEqualTo(String value) {
            addCriterion("ce_identification_head_id <=", value, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdLike(String value) {
            addCriterion("ce_identification_head_id like", value, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdNotLike(String value) {
            addCriterion("ce_identification_head_id not like", value, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdIn(List<String> values) {
            addCriterion("ce_identification_head_id in", values, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdNotIn(List<String> values) {
            addCriterion("ce_identification_head_id not in", values, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdBetween(String value1, String value2) {
            addCriterion("ce_identification_head_id between", value1, value2, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andCeIdentificationHeadIdNotBetween(String value1, String value2) {
            addCriterion("ce_identification_head_id not between", value1, value2, "ceIdentificationHeadId");
            return (Criteria) this;
        }

        public Criteria andIsOwnSepticTankIsNull() {
            addCriterion("is_own_septic_tank is null");
            return (Criteria) this;
        }

        public Criteria andIsOwnSepticTankIsNotNull() {
            addCriterion("is_own_septic_tank is not null");
            return (Criteria) this;
        }

        public Criteria andIsOwnSepticTankEqualTo(String value) {
            addCriterion("is_own_septic_tank =", value, "isOwnSepticTank");
            return (Criteria) this;
        }

        public Criteria andIsOwnSepticTankNotEqualTo(String value) {
            addCriterion("is_own_septic_tank <>", value, "isOwnSepticTank");
            return (Criteria) this;
        }

        public Criteria andIsOwnSepticTankGreaterThan(String value) {
            addCriterion("is_own_septic_tank >", value, "isOwnSepticTank");
            return (Criteria) this;
        }

        public Criteria andIsOwnSepticTankGreaterThanOrEqualTo(String value) {
            addCriterion("is_own_septic_tank >=", value, "isOwnSepticTank");
            return (Criteria) this;
        }

        public Criteria andIsOwnSepticTankLessThan(String value) {
            addCriterion("is_own_septic_tank <", value, "isOwnSepticTank");
            return (Criteria) this;
        }

        public Criteria andIsOwnSepticTankLessThanOrEqualTo(String value) {
            addCriterion("is_own_septic_tank <=", value, "isOwnSepticTank");
            return (Criteria) this;
        }

        public Criteria andIsOwnSepticTankLike(String value) {
            addCriterion("is_own_septic_tank like", value, "isOwnSepticTank");
            return (Criteria) this;
        }

        public Criteria andIsOwnSepticTankNotLike(String value) {
            addCriterion("is_own_septic_tank not like", value, "isOwnSepticTank");
            return (Criteria) this;
        }

        public Criteria andIsOwnSepticTankIn(List<String> values) {
            addCriterion("is_own_septic_tank in", values, "isOwnSepticTank");
            return (Criteria) this;
        }

        public Criteria andIsOwnSepticTankNotIn(List<String> values) {
            addCriterion("is_own_septic_tank not in", values, "isOwnSepticTank");
            return (Criteria) this;
        }

        public Criteria andIsOwnSepticTankBetween(String value1, String value2) {
            addCriterion("is_own_septic_tank between", value1, value2, "isOwnSepticTank");
            return (Criteria) this;
        }

        public Criteria andIsOwnSepticTankNotBetween(String value1, String value2) {
            addCriterion("is_own_septic_tank not between", value1, value2, "isOwnSepticTank");
            return (Criteria) this;
        }

        public Criteria andRegularEmployeeNumIsNull() {
            addCriterion("regular_employee_num is null");
            return (Criteria) this;
        }

        public Criteria andRegularEmployeeNumIsNotNull() {
            addCriterion("regular_employee_num is not null");
            return (Criteria) this;
        }

        public Criteria andRegularEmployeeNumEqualTo(String value) {
            addCriterion("regular_employee_num =", value, "regularEmployeeNum");
            return (Criteria) this;
        }

        public Criteria andRegularEmployeeNumNotEqualTo(String value) {
            addCriterion("regular_employee_num <>", value, "regularEmployeeNum");
            return (Criteria) this;
        }

        public Criteria andRegularEmployeeNumGreaterThan(String value) {
            addCriterion("regular_employee_num >", value, "regularEmployeeNum");
            return (Criteria) this;
        }

        public Criteria andRegularEmployeeNumGreaterThanOrEqualTo(String value) {
            addCriterion("regular_employee_num >=", value, "regularEmployeeNum");
            return (Criteria) this;
        }

        public Criteria andRegularEmployeeNumLessThan(String value) {
            addCriterion("regular_employee_num <", value, "regularEmployeeNum");
            return (Criteria) this;
        }

        public Criteria andRegularEmployeeNumLessThanOrEqualTo(String value) {
            addCriterion("regular_employee_num <=", value, "regularEmployeeNum");
            return (Criteria) this;
        }

        public Criteria andRegularEmployeeNumLike(String value) {
            addCriterion("regular_employee_num like", value, "regularEmployeeNum");
            return (Criteria) this;
        }

        public Criteria andRegularEmployeeNumNotLike(String value) {
            addCriterion("regular_employee_num not like", value, "regularEmployeeNum");
            return (Criteria) this;
        }

        public Criteria andRegularEmployeeNumIn(List<String> values) {
            addCriterion("regular_employee_num in", values, "regularEmployeeNum");
            return (Criteria) this;
        }

        public Criteria andRegularEmployeeNumNotIn(List<String> values) {
            addCriterion("regular_employee_num not in", values, "regularEmployeeNum");
            return (Criteria) this;
        }

        public Criteria andRegularEmployeeNumBetween(String value1, String value2) {
            addCriterion("regular_employee_num between", value1, value2, "regularEmployeeNum");
            return (Criteria) this;
        }

        public Criteria andRegularEmployeeNumNotBetween(String value1, String value2) {
            addCriterion("regular_employee_num not between", value1, value2, "regularEmployeeNum");
            return (Criteria) this;
        }

        public Criteria andAverageWorkDayYearIsNull() {
            addCriterion("\"average_work_day_year \" is null");
            return (Criteria) this;
        }

        public Criteria andAverageWorkDayYearIsNotNull() {
            addCriterion("\"average_work_day_year \" is not null");
            return (Criteria) this;
        }

        public Criteria andAverageWorkDayYearEqualTo(String value) {
            addCriterion("\"average_work_day_year \" =", value, "averageWorkDayYear");
            return (Criteria) this;
        }

        public Criteria andAverageWorkDayYearNotEqualTo(String value) {
            addCriterion("\"average_work_day_year \" <>", value, "averageWorkDayYear");
            return (Criteria) this;
        }

        public Criteria andAverageWorkDayYearGreaterThan(String value) {
            addCriterion("\"average_work_day_year \" >", value, "averageWorkDayYear");
            return (Criteria) this;
        }

        public Criteria andAverageWorkDayYearGreaterThanOrEqualTo(String value) {
            addCriterion("\"average_work_day_year \" >=", value, "averageWorkDayYear");
            return (Criteria) this;
        }

        public Criteria andAverageWorkDayYearLessThan(String value) {
            addCriterion("\"average_work_day_year \" <", value, "averageWorkDayYear");
            return (Criteria) this;
        }

        public Criteria andAverageWorkDayYearLessThanOrEqualTo(String value) {
            addCriterion("\"average_work_day_year \" <=", value, "averageWorkDayYear");
            return (Criteria) this;
        }

        public Criteria andAverageWorkDayYearLike(String value) {
            addCriterion("\"average_work_day_year \" like", value, "averageWorkDayYear");
            return (Criteria) this;
        }

        public Criteria andAverageWorkDayYearNotLike(String value) {
            addCriterion("\"average_work_day_year \" not like", value, "averageWorkDayYear");
            return (Criteria) this;
        }

        public Criteria andAverageWorkDayYearIn(List<String> values) {
            addCriterion("\"average_work_day_year \" in", values, "averageWorkDayYear");
            return (Criteria) this;
        }

        public Criteria andAverageWorkDayYearNotIn(List<String> values) {
            addCriterion("\"average_work_day_year \" not in", values, "averageWorkDayYear");
            return (Criteria) this;
        }

        public Criteria andAverageWorkDayYearBetween(String value1, String value2) {
            addCriterion("\"average_work_day_year \" between", value1, value2, "averageWorkDayYear");
            return (Criteria) this;
        }

        public Criteria andAverageWorkDayYearNotBetween(String value1, String value2) {
            addCriterion("\"average_work_day_year \" not between", value1, value2, "averageWorkDayYear");
            return (Criteria) this;
        }

        public Criteria andAverageDailyCommutingIsNull() {
            addCriterion("average_daily_commuting is null");
            return (Criteria) this;
        }

        public Criteria andAverageDailyCommutingIsNotNull() {
            addCriterion("average_daily_commuting is not null");
            return (Criteria) this;
        }

        public Criteria andAverageDailyCommutingEqualTo(String value) {
            addCriterion("average_daily_commuting =", value, "averageDailyCommuting");
            return (Criteria) this;
        }

        public Criteria andAverageDailyCommutingNotEqualTo(String value) {
            addCriterion("average_daily_commuting <>", value, "averageDailyCommuting");
            return (Criteria) this;
        }

        public Criteria andAverageDailyCommutingGreaterThan(String value) {
            addCriterion("average_daily_commuting >", value, "averageDailyCommuting");
            return (Criteria) this;
        }

        public Criteria andAverageDailyCommutingGreaterThanOrEqualTo(String value) {
            addCriterion("average_daily_commuting >=", value, "averageDailyCommuting");
            return (Criteria) this;
        }

        public Criteria andAverageDailyCommutingLessThan(String value) {
            addCriterion("average_daily_commuting <", value, "averageDailyCommuting");
            return (Criteria) this;
        }

        public Criteria andAverageDailyCommutingLessThanOrEqualTo(String value) {
            addCriterion("average_daily_commuting <=", value, "averageDailyCommuting");
            return (Criteria) this;
        }

        public Criteria andAverageDailyCommutingLike(String value) {
            addCriterion("average_daily_commuting like", value, "averageDailyCommuting");
            return (Criteria) this;
        }

        public Criteria andAverageDailyCommutingNotLike(String value) {
            addCriterion("average_daily_commuting not like", value, "averageDailyCommuting");
            return (Criteria) this;
        }

        public Criteria andAverageDailyCommutingIn(List<String> values) {
            addCriterion("average_daily_commuting in", values, "averageDailyCommuting");
            return (Criteria) this;
        }

        public Criteria andAverageDailyCommutingNotIn(List<String> values) {
            addCriterion("average_daily_commuting not in", values, "averageDailyCommuting");
            return (Criteria) this;
        }

        public Criteria andAverageDailyCommutingBetween(String value1, String value2) {
            addCriterion("average_daily_commuting between", value1, value2, "averageDailyCommuting");
            return (Criteria) this;
        }

        public Criteria andAverageDailyCommutingNotBetween(String value1, String value2) {
            addCriterion("average_daily_commuting not between", value1, value2, "averageDailyCommuting");
            return (Criteria) this;
        }

        public Criteria andAverageDailyAccommodationIsNull() {
            addCriterion("average_daily_accommodation is null");
            return (Criteria) this;
        }

        public Criteria andAverageDailyAccommodationIsNotNull() {
            addCriterion("average_daily_accommodation is not null");
            return (Criteria) this;
        }

        public Criteria andAverageDailyAccommodationEqualTo(String value) {
            addCriterion("average_daily_accommodation =", value, "averageDailyAccommodation");
            return (Criteria) this;
        }

        public Criteria andAverageDailyAccommodationNotEqualTo(String value) {
            addCriterion("average_daily_accommodation <>", value, "averageDailyAccommodation");
            return (Criteria) this;
        }

        public Criteria andAverageDailyAccommodationGreaterThan(String value) {
            addCriterion("average_daily_accommodation >", value, "averageDailyAccommodation");
            return (Criteria) this;
        }

        public Criteria andAverageDailyAccommodationGreaterThanOrEqualTo(String value) {
            addCriterion("average_daily_accommodation >=", value, "averageDailyAccommodation");
            return (Criteria) this;
        }

        public Criteria andAverageDailyAccommodationLessThan(String value) {
            addCriterion("average_daily_accommodation <", value, "averageDailyAccommodation");
            return (Criteria) this;
        }

        public Criteria andAverageDailyAccommodationLessThanOrEqualTo(String value) {
            addCriterion("average_daily_accommodation <=", value, "averageDailyAccommodation");
            return (Criteria) this;
        }

        public Criteria andAverageDailyAccommodationLike(String value) {
            addCriterion("average_daily_accommodation like", value, "averageDailyAccommodation");
            return (Criteria) this;
        }

        public Criteria andAverageDailyAccommodationNotLike(String value) {
            addCriterion("average_daily_accommodation not like", value, "averageDailyAccommodation");
            return (Criteria) this;
        }

        public Criteria andAverageDailyAccommodationIn(List<String> values) {
            addCriterion("average_daily_accommodation in", values, "averageDailyAccommodation");
            return (Criteria) this;
        }

        public Criteria andAverageDailyAccommodationNotIn(List<String> values) {
            addCriterion("average_daily_accommodation not in", values, "averageDailyAccommodation");
            return (Criteria) this;
        }

        public Criteria andAverageDailyAccommodationBetween(String value1, String value2) {
            addCriterion("average_daily_accommodation between", value1, value2, "averageDailyAccommodation");
            return (Criteria) this;
        }

        public Criteria andAverageDailyAccommodationNotBetween(String value1, String value2) {
            addCriterion("average_daily_accommodation not between", value1, value2, "averageDailyAccommodation");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantTypeIsNull() {
            addCriterion("aircondition_refrigerant_type is null");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantTypeIsNotNull() {
            addCriterion("aircondition_refrigerant_type is not null");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantTypeEqualTo(String value) {
            addCriterion("aircondition_refrigerant_type =", value, "airconditionRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantTypeNotEqualTo(String value) {
            addCriterion("aircondition_refrigerant_type <>", value, "airconditionRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantTypeGreaterThan(String value) {
            addCriterion("aircondition_refrigerant_type >", value, "airconditionRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantTypeGreaterThanOrEqualTo(String value) {
            addCriterion("aircondition_refrigerant_type >=", value, "airconditionRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantTypeLessThan(String value) {
            addCriterion("aircondition_refrigerant_type <", value, "airconditionRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantTypeLessThanOrEqualTo(String value) {
            addCriterion("aircondition_refrigerant_type <=", value, "airconditionRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantTypeLike(String value) {
            addCriterion("aircondition_refrigerant_type like", value, "airconditionRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantTypeNotLike(String value) {
            addCriterion("aircondition_refrigerant_type not like", value, "airconditionRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantTypeIn(List<String> values) {
            addCriterion("aircondition_refrigerant_type in", values, "airconditionRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantTypeNotIn(List<String> values) {
            addCriterion("aircondition_refrigerant_type not in", values, "airconditionRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantTypeBetween(String value1, String value2) {
            addCriterion("aircondition_refrigerant_type between", value1, value2, "airconditionRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantTypeNotBetween(String value1, String value2) {
            addCriterion("aircondition_refrigerant_type not between", value1, value2, "airconditionRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantChargeAmountIsNull() {
            addCriterion("aircondition_refrigerant_charge_amount is null");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantChargeAmountIsNotNull() {
            addCriterion("aircondition_refrigerant_charge_amount is not null");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantChargeAmountEqualTo(String value) {
            addCriterion("aircondition_refrigerant_charge_amount =", value, "airconditionRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantChargeAmountNotEqualTo(String value) {
            addCriterion("aircondition_refrigerant_charge_amount <>", value, "airconditionRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantChargeAmountGreaterThan(String value) {
            addCriterion("aircondition_refrigerant_charge_amount >", value, "airconditionRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantChargeAmountGreaterThanOrEqualTo(String value) {
            addCriterion("aircondition_refrigerant_charge_amount >=", value, "airconditionRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantChargeAmountLessThan(String value) {
            addCriterion("aircondition_refrigerant_charge_amount <", value, "airconditionRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantChargeAmountLessThanOrEqualTo(String value) {
            addCriterion("aircondition_refrigerant_charge_amount <=", value, "airconditionRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantChargeAmountLike(String value) {
            addCriterion("aircondition_refrigerant_charge_amount like", value, "airconditionRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantChargeAmountNotLike(String value) {
            addCriterion("aircondition_refrigerant_charge_amount not like", value, "airconditionRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantChargeAmountIn(List<String> values) {
            addCriterion("aircondition_refrigerant_charge_amount in", values, "airconditionRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantChargeAmountNotIn(List<String> values) {
            addCriterion("aircondition_refrigerant_charge_amount not in", values, "airconditionRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantChargeAmountBetween(String value1, String value2) {
            addCriterion("aircondition_refrigerant_charge_amount between", value1, value2, "airconditionRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andAirconditionRefrigerantChargeAmountNotBetween(String value1, String value2) {
            addCriterion("aircondition_refrigerant_charge_amount not between", value1, value2, "airconditionRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andAirconditionTakePhotoIsNull() {
            addCriterion("aircondition_take_photo is null");
            return (Criteria) this;
        }

        public Criteria andAirconditionTakePhotoIsNotNull() {
            addCriterion("aircondition_take_photo is not null");
            return (Criteria) this;
        }

        public Criteria andAirconditionTakePhotoEqualTo(String value) {
            addCriterion("aircondition_take_photo =", value, "airconditionTakePhoto");
            return (Criteria) this;
        }

        public Criteria andAirconditionTakePhotoNotEqualTo(String value) {
            addCriterion("aircondition_take_photo <>", value, "airconditionTakePhoto");
            return (Criteria) this;
        }

        public Criteria andAirconditionTakePhotoGreaterThan(String value) {
            addCriterion("aircondition_take_photo >", value, "airconditionTakePhoto");
            return (Criteria) this;
        }

        public Criteria andAirconditionTakePhotoGreaterThanOrEqualTo(String value) {
            addCriterion("aircondition_take_photo >=", value, "airconditionTakePhoto");
            return (Criteria) this;
        }

        public Criteria andAirconditionTakePhotoLessThan(String value) {
            addCriterion("aircondition_take_photo <", value, "airconditionTakePhoto");
            return (Criteria) this;
        }

        public Criteria andAirconditionTakePhotoLessThanOrEqualTo(String value) {
            addCriterion("aircondition_take_photo <=", value, "airconditionTakePhoto");
            return (Criteria) this;
        }

        public Criteria andAirconditionTakePhotoLike(String value) {
            addCriterion("aircondition_take_photo like", value, "airconditionTakePhoto");
            return (Criteria) this;
        }

        public Criteria andAirconditionTakePhotoNotLike(String value) {
            addCriterion("aircondition_take_photo not like", value, "airconditionTakePhoto");
            return (Criteria) this;
        }

        public Criteria andAirconditionTakePhotoIn(List<String> values) {
            addCriterion("aircondition_take_photo in", values, "airconditionTakePhoto");
            return (Criteria) this;
        }

        public Criteria andAirconditionTakePhotoNotIn(List<String> values) {
            addCriterion("aircondition_take_photo not in", values, "airconditionTakePhoto");
            return (Criteria) this;
        }

        public Criteria andAirconditionTakePhotoBetween(String value1, String value2) {
            addCriterion("aircondition_take_photo between", value1, value2, "airconditionTakePhoto");
            return (Criteria) this;
        }

        public Criteria andAirconditionTakePhotoNotBetween(String value1, String value2) {
            addCriterion("aircondition_take_photo not between", value1, value2, "airconditionTakePhoto");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantTypeIsNull() {
            addCriterion("fridge_refrigerant_type is null");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantTypeIsNotNull() {
            addCriterion("fridge_refrigerant_type is not null");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantTypeEqualTo(String value) {
            addCriterion("fridge_refrigerant_type =", value, "fridgeRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantTypeNotEqualTo(String value) {
            addCriterion("fridge_refrigerant_type <>", value, "fridgeRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantTypeGreaterThan(String value) {
            addCriterion("fridge_refrigerant_type >", value, "fridgeRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantTypeGreaterThanOrEqualTo(String value) {
            addCriterion("fridge_refrigerant_type >=", value, "fridgeRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantTypeLessThan(String value) {
            addCriterion("fridge_refrigerant_type <", value, "fridgeRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantTypeLessThanOrEqualTo(String value) {
            addCriterion("fridge_refrigerant_type <=", value, "fridgeRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantTypeLike(String value) {
            addCriterion("fridge_refrigerant_type like", value, "fridgeRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantTypeNotLike(String value) {
            addCriterion("fridge_refrigerant_type not like", value, "fridgeRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantTypeIn(List<String> values) {
            addCriterion("fridge_refrigerant_type in", values, "fridgeRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantTypeNotIn(List<String> values) {
            addCriterion("fridge_refrigerant_type not in", values, "fridgeRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantTypeBetween(String value1, String value2) {
            addCriterion("fridge_refrigerant_type between", value1, value2, "fridgeRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantTypeNotBetween(String value1, String value2) {
            addCriterion("fridge_refrigerant_type not between", value1, value2, "fridgeRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantChargeAmountIsNull() {
            addCriterion("fridge_refrigerant_charge_amount is null");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantChargeAmountIsNotNull() {
            addCriterion("fridge_refrigerant_charge_amount is not null");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantChargeAmountEqualTo(String value) {
            addCriterion("fridge_refrigerant_charge_amount =", value, "fridgeRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantChargeAmountNotEqualTo(String value) {
            addCriterion("fridge_refrigerant_charge_amount <>", value, "fridgeRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantChargeAmountGreaterThan(String value) {
            addCriterion("fridge_refrigerant_charge_amount >", value, "fridgeRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantChargeAmountGreaterThanOrEqualTo(String value) {
            addCriterion("fridge_refrigerant_charge_amount >=", value, "fridgeRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantChargeAmountLessThan(String value) {
            addCriterion("fridge_refrigerant_charge_amount <", value, "fridgeRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantChargeAmountLessThanOrEqualTo(String value) {
            addCriterion("fridge_refrigerant_charge_amount <=", value, "fridgeRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantChargeAmountLike(String value) {
            addCriterion("fridge_refrigerant_charge_amount like", value, "fridgeRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantChargeAmountNotLike(String value) {
            addCriterion("fridge_refrigerant_charge_amount not like", value, "fridgeRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantChargeAmountIn(List<String> values) {
            addCriterion("fridge_refrigerant_charge_amount in", values, "fridgeRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantChargeAmountNotIn(List<String> values) {
            addCriterion("fridge_refrigerant_charge_amount not in", values, "fridgeRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantChargeAmountBetween(String value1, String value2) {
            addCriterion("fridge_refrigerant_charge_amount between", value1, value2, "fridgeRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andFridgeRefrigerantChargeAmountNotBetween(String value1, String value2) {
            addCriterion("fridge_refrigerant_charge_amount not between", value1, value2, "fridgeRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andFridgeTakePhotoIsNull() {
            addCriterion("fridge_take_photo is null");
            return (Criteria) this;
        }

        public Criteria andFridgeTakePhotoIsNotNull() {
            addCriterion("fridge_take_photo is not null");
            return (Criteria) this;
        }

        public Criteria andFridgeTakePhotoEqualTo(String value) {
            addCriterion("fridge_take_photo =", value, "fridgeTakePhoto");
            return (Criteria) this;
        }

        public Criteria andFridgeTakePhotoNotEqualTo(String value) {
            addCriterion("fridge_take_photo <>", value, "fridgeTakePhoto");
            return (Criteria) this;
        }

        public Criteria andFridgeTakePhotoGreaterThan(String value) {
            addCriterion("fridge_take_photo >", value, "fridgeTakePhoto");
            return (Criteria) this;
        }

        public Criteria andFridgeTakePhotoGreaterThanOrEqualTo(String value) {
            addCriterion("fridge_take_photo >=", value, "fridgeTakePhoto");
            return (Criteria) this;
        }

        public Criteria andFridgeTakePhotoLessThan(String value) {
            addCriterion("fridge_take_photo <", value, "fridgeTakePhoto");
            return (Criteria) this;
        }

        public Criteria andFridgeTakePhotoLessThanOrEqualTo(String value) {
            addCriterion("fridge_take_photo <=", value, "fridgeTakePhoto");
            return (Criteria) this;
        }

        public Criteria andFridgeTakePhotoLike(String value) {
            addCriterion("fridge_take_photo like", value, "fridgeTakePhoto");
            return (Criteria) this;
        }

        public Criteria andFridgeTakePhotoNotLike(String value) {
            addCriterion("fridge_take_photo not like", value, "fridgeTakePhoto");
            return (Criteria) this;
        }

        public Criteria andFridgeTakePhotoIn(List<String> values) {
            addCriterion("fridge_take_photo in", values, "fridgeTakePhoto");
            return (Criteria) this;
        }

        public Criteria andFridgeTakePhotoNotIn(List<String> values) {
            addCriterion("fridge_take_photo not in", values, "fridgeTakePhoto");
            return (Criteria) this;
        }

        public Criteria andFridgeTakePhotoBetween(String value1, String value2) {
            addCriterion("fridge_take_photo between", value1, value2, "fridgeTakePhoto");
            return (Criteria) this;
        }

        public Criteria andFridgeTakePhotoNotBetween(String value1, String value2) {
            addCriterion("fridge_take_photo not between", value1, value2, "fridgeTakePhoto");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantTypeIsNull() {
            addCriterion("service_car_refrigerant_type is null");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantTypeIsNotNull() {
            addCriterion("service_car_refrigerant_type is not null");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantTypeEqualTo(String value) {
            addCriterion("service_car_refrigerant_type =", value, "serviceCarRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantTypeNotEqualTo(String value) {
            addCriterion("service_car_refrigerant_type <>", value, "serviceCarRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantTypeGreaterThan(String value) {
            addCriterion("service_car_refrigerant_type >", value, "serviceCarRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantTypeGreaterThanOrEqualTo(String value) {
            addCriterion("service_car_refrigerant_type >=", value, "serviceCarRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantTypeLessThan(String value) {
            addCriterion("service_car_refrigerant_type <", value, "serviceCarRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantTypeLessThanOrEqualTo(String value) {
            addCriterion("service_car_refrigerant_type <=", value, "serviceCarRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantTypeLike(String value) {
            addCriterion("service_car_refrigerant_type like", value, "serviceCarRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantTypeNotLike(String value) {
            addCriterion("service_car_refrigerant_type not like", value, "serviceCarRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantTypeIn(List<String> values) {
            addCriterion("service_car_refrigerant_type in", values, "serviceCarRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantTypeNotIn(List<String> values) {
            addCriterion("service_car_refrigerant_type not in", values, "serviceCarRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantTypeBetween(String value1, String value2) {
            addCriterion("service_car_refrigerant_type between", value1, value2, "serviceCarRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantTypeNotBetween(String value1, String value2) {
            addCriterion("service_car_refrigerant_type not between", value1, value2, "serviceCarRefrigerantType");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantChargeAmountIsNull() {
            addCriterion("service_car_refrigerant_charge_amount is null");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantChargeAmountIsNotNull() {
            addCriterion("service_car_refrigerant_charge_amount is not null");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantChargeAmountEqualTo(String value) {
            addCriterion("service_car_refrigerant_charge_amount =", value, "serviceCarRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantChargeAmountNotEqualTo(String value) {
            addCriterion("service_car_refrigerant_charge_amount <>", value, "serviceCarRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantChargeAmountGreaterThan(String value) {
            addCriterion("service_car_refrigerant_charge_amount >", value, "serviceCarRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantChargeAmountGreaterThanOrEqualTo(String value) {
            addCriterion("service_car_refrigerant_charge_amount >=", value, "serviceCarRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantChargeAmountLessThan(String value) {
            addCriterion("service_car_refrigerant_charge_amount <", value, "serviceCarRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantChargeAmountLessThanOrEqualTo(String value) {
            addCriterion("service_car_refrigerant_charge_amount <=", value, "serviceCarRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantChargeAmountLike(String value) {
            addCriterion("service_car_refrigerant_charge_amount like", value, "serviceCarRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantChargeAmountNotLike(String value) {
            addCriterion("service_car_refrigerant_charge_amount not like", value, "serviceCarRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantChargeAmountIn(List<String> values) {
            addCriterion("service_car_refrigerant_charge_amount in", values, "serviceCarRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantChargeAmountNotIn(List<String> values) {
            addCriterion("service_car_refrigerant_charge_amount not in", values, "serviceCarRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantChargeAmountBetween(String value1, String value2) {
            addCriterion("service_car_refrigerant_charge_amount between", value1, value2, "serviceCarRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceCarRefrigerantChargeAmountNotBetween(String value1, String value2) {
            addCriterion("service_car_refrigerant_charge_amount not between", value1, value2, "serviceCarRefrigerantChargeAmount");
            return (Criteria) this;
        }

        public Criteria andServiceCarTakePhotoIsNull() {
            addCriterion("service_car_take_photo is null");
            return (Criteria) this;
        }

        public Criteria andServiceCarTakePhotoIsNotNull() {
            addCriterion("service_car_take_photo is not null");
            return (Criteria) this;
        }

        public Criteria andServiceCarTakePhotoEqualTo(String value) {
            addCriterion("service_car_take_photo =", value, "serviceCarTakePhoto");
            return (Criteria) this;
        }

        public Criteria andServiceCarTakePhotoNotEqualTo(String value) {
            addCriterion("service_car_take_photo <>", value, "serviceCarTakePhoto");
            return (Criteria) this;
        }

        public Criteria andServiceCarTakePhotoGreaterThan(String value) {
            addCriterion("service_car_take_photo >", value, "serviceCarTakePhoto");
            return (Criteria) this;
        }

        public Criteria andServiceCarTakePhotoGreaterThanOrEqualTo(String value) {
            addCriterion("service_car_take_photo >=", value, "serviceCarTakePhoto");
            return (Criteria) this;
        }

        public Criteria andServiceCarTakePhotoLessThan(String value) {
            addCriterion("service_car_take_photo <", value, "serviceCarTakePhoto");
            return (Criteria) this;
        }

        public Criteria andServiceCarTakePhotoLessThanOrEqualTo(String value) {
            addCriterion("service_car_take_photo <=", value, "serviceCarTakePhoto");
            return (Criteria) this;
        }

        public Criteria andServiceCarTakePhotoLike(String value) {
            addCriterion("service_car_take_photo like", value, "serviceCarTakePhoto");
            return (Criteria) this;
        }

        public Criteria andServiceCarTakePhotoNotLike(String value) {
            addCriterion("service_car_take_photo not like", value, "serviceCarTakePhoto");
            return (Criteria) this;
        }

        public Criteria andServiceCarTakePhotoIn(List<String> values) {
            addCriterion("service_car_take_photo in", values, "serviceCarTakePhoto");
            return (Criteria) this;
        }

        public Criteria andServiceCarTakePhotoNotIn(List<String> values) {
            addCriterion("service_car_take_photo not in", values, "serviceCarTakePhoto");
            return (Criteria) this;
        }

        public Criteria andServiceCarTakePhotoBetween(String value1, String value2) {
            addCriterion("service_car_take_photo between", value1, value2, "serviceCarTakePhoto");
            return (Criteria) this;
        }

        public Criteria andServiceCarTakePhotoNotBetween(String value1, String value2) {
            addCriterion("service_car_take_photo not between", value1, value2, "serviceCarTakePhoto");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNull() {
            addCriterion("creation_time is null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNotNull() {
            addCriterion("creation_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeEqualTo(LocalDateTime value) {
            addCriterion("creation_time =", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
            addCriterion("creation_time <>", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
            addCriterion("creation_time >", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time >=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThan(LocalDateTime value) {
            addCriterion("creation_time <", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time <=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIn(List<LocalDateTime> values) {
            addCriterion("creation_time in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
            addCriterion("creation_time not in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time not between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNull() {
            addCriterion("create_username is null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNotNull() {
            addCriterion("create_username is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameEqualTo(String value) {
            addCriterion("create_username =", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotEqualTo(String value) {
            addCriterion("create_username <>", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThan(String value) {
            addCriterion("create_username >", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("create_username >=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThan(String value) {
            addCriterion("create_username <", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
            addCriterion("create_username <=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLike(String value) {
            addCriterion("create_username like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotLike(String value) {
            addCriterion("create_username not like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIn(List<String> values) {
            addCriterion("create_username in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotIn(List<String> values) {
            addCriterion("create_username not in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameBetween(String value1, String value2) {
            addCriterion("create_username between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotBetween(String value1, String value2) {
            addCriterion("create_username not between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNull() {
            addCriterion("last_update_time is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNotNull() {
            addCriterion("last_update_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("last_update_time =", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <>", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("last_update_time >", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time >=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("last_update_time <", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("last_update_time in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("last_update_time not in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNull() {
            addCriterion("last_update_username is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNotNull() {
            addCriterion("last_update_username is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameEqualTo(String value) {
            addCriterion("last_update_username =", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotEqualTo(String value) {
            addCriterion("last_update_username <>", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThan(String value) {
            addCriterion("last_update_username >", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_username >=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThan(String value) {
            addCriterion("last_update_username <", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
            addCriterion("last_update_username <=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLike(String value) {
            addCriterion("last_update_username like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotLike(String value) {
            addCriterion("last_update_username not like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIn(List<String> values) {
            addCriterion("last_update_username in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotIn(List<String> values) {
            addCriterion("last_update_username not in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
            addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
            addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNull() {
            addCriterion("last_update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNotNull() {
            addCriterion("last_update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdEqualTo(String value) {
            addCriterion("last_update_user_id =", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotEqualTo(String value) {
            addCriterion("last_update_user_id <>", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThan(String value) {
            addCriterion("last_update_user_id >", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_user_id >=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThan(String value) {
            addCriterion("last_update_user_id <", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThanOrEqualTo(String value) {
            addCriterion("last_update_user_id <=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLike(String value) {
            addCriterion("last_update_user_id like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotLike(String value) {
            addCriterion("last_update_user_id not like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIn(List<String> values) {
            addCriterion("last_update_user_id in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotIn(List<String> values) {
            addCriterion("last_update_user_id not in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdBetween(String value1, String value2) {
            addCriterion("last_update_user_id between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotBetween(String value1, String value2) {
            addCriterion("last_update_user_id not between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNull() {
            addCriterion("last_update_version is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNotNull() {
            addCriterion("last_update_version is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionEqualTo(Integer value) {
            addCriterion("last_update_version =", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotEqualTo(Integer value) {
            addCriterion("last_update_version <>", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThan(Integer value) {
            addCriterion("last_update_version >", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("last_update_version >=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThan(Integer value) {
            addCriterion("last_update_version <", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThanOrEqualTo(Integer value) {
            addCriterion("last_update_version <=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIn(List<Integer> values) {
            addCriterion("last_update_version in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotIn(List<Integer> values) {
            addCriterion("last_update_version not in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version not between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated do_not_delete_during_merge Tue Apr 01 14:16:37 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_ce_identification_runaway_emission
     *
     * @mbg.generated Tue Apr 01 14:16:37 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}