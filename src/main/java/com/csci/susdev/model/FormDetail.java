package com.csci.susdev.model;

import java.time.LocalDateTime;

public class FormDetail {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.id
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.form_code
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    private String formCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.year
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    private Integer year;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.type_a
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    private String typeA;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.type_b
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    private String typeB;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.type_c
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    private String typeC;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.type_d
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    private String typeD;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.type_e
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    private String typeE;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.creation_time
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.create_username
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.create_user_id
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.last_update_time
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.last_update_username
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.last_update_user_id
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.last_update_version
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.is_deleted
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    private Boolean isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.seq
     *
     * @mbg.generated Tue Dec 24 15:20:10 CST 2024
     */
    private Integer seq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_form_detail.month
     *
     * @mbg.generated Tue Dec 24 15:20:10 CST 2024
     */
    private Integer month;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.id
     *
     * @return the value of t_form_detail.id
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.id
     *
     * @param id the value for t_form_detail.id
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.form_code
     *
     * @return the value of t_form_detail.form_code
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public String getFormCode() {
        return formCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.form_code
     *
     * @param formCode the value for t_form_detail.form_code
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public void setFormCode(String formCode) {
        this.formCode = formCode == null ? null : formCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.year
     *
     * @return the value of t_form_detail.year
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public Integer getYear() {
        return year;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.year
     *
     * @param year the value for t_form_detail.year
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public void setYear(Integer year) {
        this.year = year;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.type_a
     *
     * @return the value of t_form_detail.type_a
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public String getTypeA() {
        return typeA;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.type_a
     *
     * @param typeA the value for t_form_detail.type_a
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public void setTypeA(String typeA) {
        this.typeA = typeA == null ? null : typeA.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.type_b
     *
     * @return the value of t_form_detail.type_b
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public String getTypeB() {
        return typeB;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.type_b
     *
     * @param typeB the value for t_form_detail.type_b
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public void setTypeB(String typeB) {
        this.typeB = typeB == null ? null : typeB.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.type_c
     *
     * @return the value of t_form_detail.type_c
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public String getTypeC() {
        return typeC;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.type_c
     *
     * @param typeC the value for t_form_detail.type_c
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public void setTypeC(String typeC) {
        this.typeC = typeC == null ? null : typeC.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.type_d
     *
     * @return the value of t_form_detail.type_d
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public String getTypeD() {
        return typeD;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.type_d
     *
     * @param typeD the value for t_form_detail.type_d
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public void setTypeD(String typeD) {
        this.typeD = typeD == null ? null : typeD.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.type_e
     *
     * @return the value of t_form_detail.type_e
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public String getTypeE() {
        return typeE;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.type_e
     *
     * @param typeE the value for t_form_detail.type_e
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public void setTypeE(String typeE) {
        this.typeE = typeE == null ? null : typeE.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.creation_time
     *
     * @return the value of t_form_detail.creation_time
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.creation_time
     *
     * @param creationTime the value for t_form_detail.creation_time
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.create_username
     *
     * @return the value of t_form_detail.create_username
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.create_username
     *
     * @param createUsername the value for t_form_detail.create_username
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.create_user_id
     *
     * @return the value of t_form_detail.create_user_id
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.create_user_id
     *
     * @param createUserId the value for t_form_detail.create_user_id
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.last_update_time
     *
     * @return the value of t_form_detail.last_update_time
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.last_update_time
     *
     * @param lastUpdateTime the value for t_form_detail.last_update_time
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.last_update_username
     *
     * @return the value of t_form_detail.last_update_username
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.last_update_username
     *
     * @param lastUpdateUsername the value for t_form_detail.last_update_username
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.last_update_user_id
     *
     * @return the value of t_form_detail.last_update_user_id
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_form_detail.last_update_user_id
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.last_update_version
     *
     * @return the value of t_form_detail.last_update_version
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.last_update_version
     *
     * @param lastUpdateVersion the value for t_form_detail.last_update_version
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.is_deleted
     *
     * @return the value of t_form_detail.is_deleted
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.is_deleted
     *
     * @param isDeleted the value for t_form_detail.is_deleted
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.seq
     *
     * @return the value of t_form_detail.seq
     *
     * @mbg.generated Tue Dec 24 15:20:10 CST 2024
     */
    public Integer getSeq() {
        return seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.seq
     *
     * @param seq the value for t_form_detail.seq
     *
     * @mbg.generated Tue Dec 24 15:20:10 CST 2024
     */
    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_form_detail.month
     *
     * @return the value of t_form_detail.month
     *
     * @mbg.generated Tue Dec 24 15:20:10 CST 2024
     */
    public Integer getMonth() {
        return month;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_form_detail.month
     *
     * @param month the value for t_form_detail.month
     *
     * @mbg.generated Tue Dec 24 15:20:10 CST 2024
     */
    public void setMonth(Integer month) {
        this.month = month;
    }
}