package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FcEnergyFactorEsgDataset {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.id
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.platform
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private String platform;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.chinese_name
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private String chineseName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.category
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private String category;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.emission_source
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private String emissionSource;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.unit
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private String unit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.energy_factor
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private BigDecimal energyFactor;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.energy_factor_unit
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private String energyFactorUnit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.description
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.source_description
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private String sourceDescription;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.creation_time
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.create_username
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.create_user_id
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.last_update_time
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.last_update_username
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.last_update_user_id
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.last_update_version
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.is_deleted
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private Boolean isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor_esg_dataset.record_year
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    private Integer recordYear;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.id
     *
     * @return the value of t_fc_energy_factor_esg_dataset.id
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.id
     *
     * @param id the value for t_fc_energy_factor_esg_dataset.id
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.platform
     *
     * @return the value of t_fc_energy_factor_esg_dataset.platform
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.platform
     *
     * @param platform the value for t_fc_energy_factor_esg_dataset.platform
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setPlatform(String platform) {
        this.platform = platform == null ? null : platform.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.chinese_name
     *
     * @return the value of t_fc_energy_factor_esg_dataset.chinese_name
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public String getChineseName() {
        return chineseName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.chinese_name
     *
     * @param chineseName the value for t_fc_energy_factor_esg_dataset.chinese_name
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setChineseName(String chineseName) {
        this.chineseName = chineseName == null ? null : chineseName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.category
     *
     * @return the value of t_fc_energy_factor_esg_dataset.category
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public String getCategory() {
        return category;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.category
     *
     * @param category the value for t_fc_energy_factor_esg_dataset.category
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setCategory(String category) {
        this.category = category == null ? null : category.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.emission_source
     *
     * @return the value of t_fc_energy_factor_esg_dataset.emission_source
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public String getEmissionSource() {
        return emissionSource;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.emission_source
     *
     * @param emissionSource the value for t_fc_energy_factor_esg_dataset.emission_source
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setEmissionSource(String emissionSource) {
        this.emissionSource = emissionSource == null ? null : emissionSource.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.unit
     *
     * @return the value of t_fc_energy_factor_esg_dataset.unit
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public String getUnit() {
        return unit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.unit
     *
     * @param unit the value for t_fc_energy_factor_esg_dataset.unit
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.energy_factor
     *
     * @return the value of t_fc_energy_factor_esg_dataset.energy_factor
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public BigDecimal getEnergyFactor() {
        return energyFactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.energy_factor
     *
     * @param energyFactor the value for t_fc_energy_factor_esg_dataset.energy_factor
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setEnergyFactor(BigDecimal energyFactor) {
        this.energyFactor = energyFactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.energy_factor_unit
     *
     * @return the value of t_fc_energy_factor_esg_dataset.energy_factor_unit
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public String getEnergyFactorUnit() {
        return energyFactorUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.energy_factor_unit
     *
     * @param energyFactorUnit the value for t_fc_energy_factor_esg_dataset.energy_factor_unit
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setEnergyFactorUnit(String energyFactorUnit) {
        this.energyFactorUnit = energyFactorUnit == null ? null : energyFactorUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.description
     *
     * @return the value of t_fc_energy_factor_esg_dataset.description
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.description
     *
     * @param description the value for t_fc_energy_factor_esg_dataset.description
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.source_description
     *
     * @return the value of t_fc_energy_factor_esg_dataset.source_description
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public String getSourceDescription() {
        return sourceDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.source_description
     *
     * @param sourceDescription the value for t_fc_energy_factor_esg_dataset.source_description
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setSourceDescription(String sourceDescription) {
        this.sourceDescription = sourceDescription == null ? null : sourceDescription.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.creation_time
     *
     * @return the value of t_fc_energy_factor_esg_dataset.creation_time
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.creation_time
     *
     * @param creationTime the value for t_fc_energy_factor_esg_dataset.creation_time
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.create_username
     *
     * @return the value of t_fc_energy_factor_esg_dataset.create_username
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.create_username
     *
     * @param createUsername the value for t_fc_energy_factor_esg_dataset.create_username
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.create_user_id
     *
     * @return the value of t_fc_energy_factor_esg_dataset.create_user_id
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.create_user_id
     *
     * @param createUserId the value for t_fc_energy_factor_esg_dataset.create_user_id
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.last_update_time
     *
     * @return the value of t_fc_energy_factor_esg_dataset.last_update_time
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.last_update_time
     *
     * @param lastUpdateTime the value for t_fc_energy_factor_esg_dataset.last_update_time
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.last_update_username
     *
     * @return the value of t_fc_energy_factor_esg_dataset.last_update_username
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.last_update_username
     *
     * @param lastUpdateUsername the value for t_fc_energy_factor_esg_dataset.last_update_username
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.last_update_user_id
     *
     * @return the value of t_fc_energy_factor_esg_dataset.last_update_user_id
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_fc_energy_factor_esg_dataset.last_update_user_id
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.last_update_version
     *
     * @return the value of t_fc_energy_factor_esg_dataset.last_update_version
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.last_update_version
     *
     * @param lastUpdateVersion the value for t_fc_energy_factor_esg_dataset.last_update_version
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.is_deleted
     *
     * @return the value of t_fc_energy_factor_esg_dataset.is_deleted
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.is_deleted
     *
     * @param isDeleted the value for t_fc_energy_factor_esg_dataset.is_deleted
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor_esg_dataset.record_year
     *
     * @return the value of t_fc_energy_factor_esg_dataset.record_year
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public Integer getRecordYear() {
        return recordYear;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor_esg_dataset.record_year
     *
     * @param recordYear the value for t_fc_energy_factor_esg_dataset.record_year
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    public void setRecordYear(Integer recordYear) {
        this.recordYear = recordYear;
    }
}