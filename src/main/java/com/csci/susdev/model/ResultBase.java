package com.csci.susdev.model;

import com.csci.susdev.constant.SusDevConsts;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Generated Comments
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 11/7/2019
 */
@Schema(name = "ResultBase")
public class ResultBase {

    @Schema(description = "错误码，0表示请求成功，-1表示请求失败")
    private int code = 0;

    @Schema(description = "返回状态说明")
    private String msg = "操作成功";

    public ResultBase() {
    }

    public ResultBase(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static ResultBase success() {
        return new ResultBase();
    }

    public static ResultBase success(String msg) {
        return new ResultBase(SusDevConsts.RespStatus.SUCCESS, msg);
    }

    public static ResultBase failure(String msg) {
        return new ResultBase(SusDevConsts.RespStatus.FAIL, msg);
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
