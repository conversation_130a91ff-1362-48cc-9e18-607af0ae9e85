package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class AmbientEnergyBill {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill.id
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill.head_id
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    private String headId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill.type
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    private String type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill.bill_no
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    private String billNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill.from_time
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    private LocalDateTime fromTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill.to_time
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    private LocalDateTime toTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill.consumption
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    private BigDecimal consumption;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill.attachment_id
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    private String attachmentId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill.creation_time
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill.create_username
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill.create_user_id
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill.last_update_time
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill.last_update_username
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill.last_update_user_id
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill.last_update_version
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    private Integer lastUpdateVersion;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill.id
     *
     * @return the value of t_ambient_energy_bill.id
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill.id
     *
     * @param id the value for t_ambient_energy_bill.id
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill.head_id
     *
     * @return the value of t_ambient_energy_bill.head_id
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public String getHeadId() {
        return headId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill.head_id
     *
     * @param headId the value for t_ambient_energy_bill.head_id
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public void setHeadId(String headId) {
        this.headId = headId == null ? null : headId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill.type
     *
     * @return the value of t_ambient_energy_bill.type
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill.type
     *
     * @param type the value for t_ambient_energy_bill.type
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill.bill_no
     *
     * @return the value of t_ambient_energy_bill.bill_no
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public String getBillNo() {
        return billNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill.bill_no
     *
     * @param billNo the value for t_ambient_energy_bill.bill_no
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public void setBillNo(String billNo) {
        this.billNo = billNo == null ? null : billNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill.from_time
     *
     * @return the value of t_ambient_energy_bill.from_time
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public LocalDateTime getFromTime() {
        return fromTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill.from_time
     *
     * @param fromTime the value for t_ambient_energy_bill.from_time
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public void setFromTime(LocalDateTime fromTime) {
        this.fromTime = fromTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill.to_time
     *
     * @return the value of t_ambient_energy_bill.to_time
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public LocalDateTime getToTime() {
        return toTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill.to_time
     *
     * @param toTime the value for t_ambient_energy_bill.to_time
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public void setToTime(LocalDateTime toTime) {
        this.toTime = toTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill.consumption
     *
     * @return the value of t_ambient_energy_bill.consumption
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public BigDecimal getConsumption() {
        return consumption;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill.consumption
     *
     * @param consumption the value for t_ambient_energy_bill.consumption
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public void setConsumption(BigDecimal consumption) {
        this.consumption = consumption;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill.attachment_id
     *
     * @return the value of t_ambient_energy_bill.attachment_id
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public String getAttachmentId() {
        return attachmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill.attachment_id
     *
     * @param attachmentId the value for t_ambient_energy_bill.attachment_id
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId == null ? null : attachmentId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill.creation_time
     *
     * @return the value of t_ambient_energy_bill.creation_time
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill.creation_time
     *
     * @param creationTime the value for t_ambient_energy_bill.creation_time
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill.create_username
     *
     * @return the value of t_ambient_energy_bill.create_username
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill.create_username
     *
     * @param createUsername the value for t_ambient_energy_bill.create_username
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill.create_user_id
     *
     * @return the value of t_ambient_energy_bill.create_user_id
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill.create_user_id
     *
     * @param createUserId the value for t_ambient_energy_bill.create_user_id
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill.last_update_time
     *
     * @return the value of t_ambient_energy_bill.last_update_time
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill.last_update_time
     *
     * @param lastUpdateTime the value for t_ambient_energy_bill.last_update_time
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill.last_update_username
     *
     * @return the value of t_ambient_energy_bill.last_update_username
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill.last_update_username
     *
     * @param lastUpdateUsername the value for t_ambient_energy_bill.last_update_username
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill.last_update_user_id
     *
     * @return the value of t_ambient_energy_bill.last_update_user_id
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_ambient_energy_bill.last_update_user_id
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill.last_update_version
     *
     * @return the value of t_ambient_energy_bill.last_update_version
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill.last_update_version
     *
     * @param lastUpdateVersion the value for t_ambient_energy_bill.last_update_version
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }
}