package com.csci.susdev.model;

import java.time.LocalDateTime;

public class FactorScope {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_scope.id
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_scope.protocol_detail_id
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    private String protocolDetailId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_scope.form_detail_id
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    private String formDetailId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_scope.factor_type
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    private String factorType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_scope.creation_time
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_scope.create_username
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_scope.create_user_id
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_scope.last_update_time
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_scope.last_update_username
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_scope.last_update_user_id
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_scope.last_update_version
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_scope.is_deleted
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    private Boolean isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_scope.id
     *
     * @return the value of t_factor_scope.id
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_scope.id
     *
     * @param id the value for t_factor_scope.id
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_scope.protocol_detail_id
     *
     * @return the value of t_factor_scope.protocol_detail_id
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public String getProtocolDetailId() {
        return protocolDetailId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_scope.protocol_detail_id
     *
     * @param protocolDetailId the value for t_factor_scope.protocol_detail_id
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public void setProtocolDetailId(String protocolDetailId) {
        this.protocolDetailId = protocolDetailId == null ? null : protocolDetailId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_scope.form_detail_id
     *
     * @return the value of t_factor_scope.form_detail_id
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public String getFormDetailId() {
        return formDetailId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_scope.form_detail_id
     *
     * @param formDetailId the value for t_factor_scope.form_detail_id
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public void setFormDetailId(String formDetailId) {
        this.formDetailId = formDetailId == null ? null : formDetailId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_scope.factor_type
     *
     * @return the value of t_factor_scope.factor_type
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public String getFactorType() {
        return factorType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_scope.factor_type
     *
     * @param factorType the value for t_factor_scope.factor_type
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public void setFactorType(String factorType) {
        this.factorType = factorType == null ? null : factorType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_scope.creation_time
     *
     * @return the value of t_factor_scope.creation_time
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_scope.creation_time
     *
     * @param creationTime the value for t_factor_scope.creation_time
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_scope.create_username
     *
     * @return the value of t_factor_scope.create_username
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_scope.create_username
     *
     * @param createUsername the value for t_factor_scope.create_username
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_scope.create_user_id
     *
     * @return the value of t_factor_scope.create_user_id
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_scope.create_user_id
     *
     * @param createUserId the value for t_factor_scope.create_user_id
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_scope.last_update_time
     *
     * @return the value of t_factor_scope.last_update_time
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_scope.last_update_time
     *
     * @param lastUpdateTime the value for t_factor_scope.last_update_time
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_scope.last_update_username
     *
     * @return the value of t_factor_scope.last_update_username
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_scope.last_update_username
     *
     * @param lastUpdateUsername the value for t_factor_scope.last_update_username
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_scope.last_update_user_id
     *
     * @return the value of t_factor_scope.last_update_user_id
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_scope.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_factor_scope.last_update_user_id
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_scope.last_update_version
     *
     * @return the value of t_factor_scope.last_update_version
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_scope.last_update_version
     *
     * @param lastUpdateVersion the value for t_factor_scope.last_update_version
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_scope.is_deleted
     *
     * @return the value of t_factor_scope.is_deleted
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_scope.is_deleted
     *
     * @param isDeleted the value for t_factor_scope.is_deleted
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
}