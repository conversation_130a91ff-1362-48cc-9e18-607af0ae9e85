package com.csci.susdev.model;

import java.time.LocalDateTime;

public class SocialPerfTwoActivity {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.id
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.head_id
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String headId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.organization_id
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String organizationId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.type
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String type;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.name
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String name;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.date
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private LocalDateTime date;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.staff_count
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String staffCount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.hour_count
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String hourCount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.service_target
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String serviceTarget;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.beneficiary_organization
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String beneficiaryOrganization;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.no_of_beneficiaries
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String noOfBeneficiaries;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.total_donation
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String totalDonation;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.donation_type
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String donationType;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.donation_qty
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String donationQty;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.creation_time
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private LocalDateTime creationTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.create_username
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String createUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.create_user_id
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String createUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.last_update_time
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private LocalDateTime lastUpdateTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.last_update_username
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String lastUpdateUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.last_update_user_id
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private String lastUpdateUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_social_perf_two_activity.last_update_version
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	private Integer lastUpdateVersion;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.id
	 * @return  the value of t_social_perf_two_activity.id
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.id
	 * @param id  the value for t_social_perf_two_activity.id
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.head_id
	 * @return  the value of t_social_perf_two_activity.head_id
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getHeadId() {
		return headId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.head_id
	 * @param headId  the value for t_social_perf_two_activity.head_id
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setHeadId(String headId) {
		this.headId = headId == null ? null : headId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.organization_id
	 * @return  the value of t_social_perf_two_activity.organization_id
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getOrganizationId() {
		return organizationId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.organization_id
	 * @param organizationId  the value for t_social_perf_two_activity.organization_id
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setOrganizationId(String organizationId) {
		this.organizationId = organizationId == null ? null : organizationId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.type
	 * @return  the value of t_social_perf_two_activity.type
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getType() {
		return type;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.type
	 * @param type  the value for t_social_perf_two_activity.type
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setType(String type) {
		this.type = type == null ? null : type.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.name
	 * @return  the value of t_social_perf_two_activity.name
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getName() {
		return name;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.name
	 * @param name  the value for t_social_perf_two_activity.name
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setName(String name) {
		this.name = name == null ? null : name.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.date
	 * @return  the value of t_social_perf_two_activity.date
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public LocalDateTime getDate() {
		return date;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.date
	 * @param date  the value for t_social_perf_two_activity.date
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setDate(LocalDateTime date) {
		this.date = date;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.staff_count
	 * @return  the value of t_social_perf_two_activity.staff_count
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getStaffCount() {
		return staffCount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.staff_count
	 * @param staffCount  the value for t_social_perf_two_activity.staff_count
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setStaffCount(String staffCount) {
		this.staffCount = staffCount == null ? null : staffCount.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.hour_count
	 * @return  the value of t_social_perf_two_activity.hour_count
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getHourCount() {
		return hourCount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.hour_count
	 * @param hourCount  the value for t_social_perf_two_activity.hour_count
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setHourCount(String hourCount) {
		this.hourCount = hourCount == null ? null : hourCount.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.service_target
	 * @return  the value of t_social_perf_two_activity.service_target
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getServiceTarget() {
		return serviceTarget;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.service_target
	 * @param serviceTarget  the value for t_social_perf_two_activity.service_target
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setServiceTarget(String serviceTarget) {
		this.serviceTarget = serviceTarget == null ? null : serviceTarget.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.beneficiary_organization
	 * @return  the value of t_social_perf_two_activity.beneficiary_organization
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getBeneficiaryOrganization() {
		return beneficiaryOrganization;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.beneficiary_organization
	 * @param beneficiaryOrganization  the value for t_social_perf_two_activity.beneficiary_organization
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setBeneficiaryOrganization(String beneficiaryOrganization) {
		this.beneficiaryOrganization = beneficiaryOrganization == null ? null : beneficiaryOrganization.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.no_of_beneficiaries
	 * @return  the value of t_social_perf_two_activity.no_of_beneficiaries
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getNoOfBeneficiaries() {
		return noOfBeneficiaries;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.no_of_beneficiaries
	 * @param noOfBeneficiaries  the value for t_social_perf_two_activity.no_of_beneficiaries
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setNoOfBeneficiaries(String noOfBeneficiaries) {
		this.noOfBeneficiaries = noOfBeneficiaries == null ? null : noOfBeneficiaries.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.total_donation
	 * @return  the value of t_social_perf_two_activity.total_donation
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getTotalDonation() {
		return totalDonation;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.total_donation
	 * @param totalDonation  the value for t_social_perf_two_activity.total_donation
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setTotalDonation(String totalDonation) {
		this.totalDonation = totalDonation == null ? null : totalDonation.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.donation_type
	 * @return  the value of t_social_perf_two_activity.donation_type
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getDonationType() {
		return donationType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.donation_type
	 * @param donationType  the value for t_social_perf_two_activity.donation_type
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setDonationType(String donationType) {
		this.donationType = donationType == null ? null : donationType.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.donation_qty
	 * @return  the value of t_social_perf_two_activity.donation_qty
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getDonationQty() {
		return donationQty;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.donation_qty
	 * @param donationQty  the value for t_social_perf_two_activity.donation_qty
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setDonationQty(String donationQty) {
		this.donationQty = donationQty == null ? null : donationQty.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.creation_time
	 * @return  the value of t_social_perf_two_activity.creation_time
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public LocalDateTime getCreationTime() {
		return creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.creation_time
	 * @param creationTime  the value for t_social_perf_two_activity.creation_time
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setCreationTime(LocalDateTime creationTime) {
		this.creationTime = creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.create_username
	 * @return  the value of t_social_perf_two_activity.create_username
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getCreateUsername() {
		return createUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.create_username
	 * @param createUsername  the value for t_social_perf_two_activity.create_username
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setCreateUsername(String createUsername) {
		this.createUsername = createUsername == null ? null : createUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.create_user_id
	 * @return  the value of t_social_perf_two_activity.create_user_id
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getCreateUserId() {
		return createUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.create_user_id
	 * @param createUserId  the value for t_social_perf_two_activity.create_user_id
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId == null ? null : createUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.last_update_time
	 * @return  the value of t_social_perf_two_activity.last_update_time
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public LocalDateTime getLastUpdateTime() {
		return lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.last_update_time
	 * @param lastUpdateTime  the value for t_social_perf_two_activity.last_update_time
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.last_update_username
	 * @return  the value of t_social_perf_two_activity.last_update_username
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getLastUpdateUsername() {
		return lastUpdateUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.last_update_username
	 * @param lastUpdateUsername  the value for t_social_perf_two_activity.last_update_username
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setLastUpdateUsername(String lastUpdateUsername) {
		this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.last_update_user_id
	 * @return  the value of t_social_perf_two_activity.last_update_user_id
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getLastUpdateUserId() {
		return lastUpdateUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.last_update_user_id
	 * @param lastUpdateUserId  the value for t_social_perf_two_activity.last_update_user_id
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setLastUpdateUserId(String lastUpdateUserId) {
		this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_social_perf_two_activity.last_update_version
	 * @return  the value of t_social_perf_two_activity.last_update_version
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public Integer getLastUpdateVersion() {
		return lastUpdateVersion;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_social_perf_two_activity.last_update_version
	 * @param lastUpdateVersion  the value for t_social_perf_two_activity.last_update_version
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setLastUpdateVersion(Integer lastUpdateVersion) {
		this.lastUpdateVersion = lastUpdateVersion;
	}
}