package com.csci.susdev.model;

import java.time.LocalDateTime;

public class FfCmMobileDetail {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.id
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.head_id
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String headId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.col_1
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String col1;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.col_2
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String col2;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.col_3
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String col3;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.col_4
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String col4;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.col_5
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String col5;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.col_6
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String col6;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.col_7
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String col7;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.col_8
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String col8;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.col_9
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String col9;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.col_10
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String col10;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.col_11
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String col11;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.col_12
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String col12;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.col_13
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String col13;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.col_14
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String col14;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.col_15
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String col15;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.col_16
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private String col16;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.seq
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private Integer seq;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ff_cm_mobile_detail.creation_time
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	private LocalDateTime creationTime;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.id
	 * @return  the value of t_ff_cm_mobile_detail.id
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.id
	 * @param id  the value for t_ff_cm_mobile_detail.id
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.head_id
	 * @return  the value of t_ff_cm_mobile_detail.head_id
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getHeadId() {
		return headId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.head_id
	 * @param headId  the value for t_ff_cm_mobile_detail.head_id
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setHeadId(String headId) {
		this.headId = headId == null ? null : headId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.col_1
	 * @return  the value of t_ff_cm_mobile_detail.col_1
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getCol1() {
		return col1;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.col_1
	 * @param col1  the value for t_ff_cm_mobile_detail.col_1
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCol1(String col1) {
		this.col1 = col1 == null ? null : col1.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.col_2
	 * @return  the value of t_ff_cm_mobile_detail.col_2
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getCol2() {
		return col2;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.col_2
	 * @param col2  the value for t_ff_cm_mobile_detail.col_2
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCol2(String col2) {
		this.col2 = col2 == null ? null : col2.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.col_3
	 * @return  the value of t_ff_cm_mobile_detail.col_3
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getCol3() {
		return col3;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.col_3
	 * @param col3  the value for t_ff_cm_mobile_detail.col_3
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCol3(String col3) {
		this.col3 = col3 == null ? null : col3.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.col_4
	 * @return  the value of t_ff_cm_mobile_detail.col_4
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getCol4() {
		return col4;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.col_4
	 * @param col4  the value for t_ff_cm_mobile_detail.col_4
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCol4(String col4) {
		this.col4 = col4 == null ? null : col4.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.col_5
	 * @return  the value of t_ff_cm_mobile_detail.col_5
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getCol5() {
		return col5;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.col_5
	 * @param col5  the value for t_ff_cm_mobile_detail.col_5
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCol5(String col5) {
		this.col5 = col5 == null ? null : col5.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.col_6
	 * @return  the value of t_ff_cm_mobile_detail.col_6
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getCol6() {
		return col6;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.col_6
	 * @param col6  the value for t_ff_cm_mobile_detail.col_6
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCol6(String col6) {
		this.col6 = col6 == null ? null : col6.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.col_7
	 * @return  the value of t_ff_cm_mobile_detail.col_7
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getCol7() {
		return col7;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.col_7
	 * @param col7  the value for t_ff_cm_mobile_detail.col_7
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCol7(String col7) {
		this.col7 = col7 == null ? null : col7.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.col_8
	 * @return  the value of t_ff_cm_mobile_detail.col_8
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getCol8() {
		return col8;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.col_8
	 * @param col8  the value for t_ff_cm_mobile_detail.col_8
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCol8(String col8) {
		this.col8 = col8 == null ? null : col8.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.col_9
	 * @return  the value of t_ff_cm_mobile_detail.col_9
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getCol9() {
		return col9;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.col_9
	 * @param col9  the value for t_ff_cm_mobile_detail.col_9
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCol9(String col9) {
		this.col9 = col9 == null ? null : col9.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.col_10
	 * @return  the value of t_ff_cm_mobile_detail.col_10
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getCol10() {
		return col10;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.col_10
	 * @param col10  the value for t_ff_cm_mobile_detail.col_10
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCol10(String col10) {
		this.col10 = col10 == null ? null : col10.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.col_11
	 * @return  the value of t_ff_cm_mobile_detail.col_11
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getCol11() {
		return col11;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.col_11
	 * @param col11  the value for t_ff_cm_mobile_detail.col_11
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCol11(String col11) {
		this.col11 = col11 == null ? null : col11.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.col_12
	 * @return  the value of t_ff_cm_mobile_detail.col_12
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getCol12() {
		return col12;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.col_12
	 * @param col12  the value for t_ff_cm_mobile_detail.col_12
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCol12(String col12) {
		this.col12 = col12 == null ? null : col12.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.col_13
	 * @return  the value of t_ff_cm_mobile_detail.col_13
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getCol13() {
		return col13;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.col_13
	 * @param col13  the value for t_ff_cm_mobile_detail.col_13
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCol13(String col13) {
		this.col13 = col13 == null ? null : col13.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.col_14
	 * @return  the value of t_ff_cm_mobile_detail.col_14
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getCol14() {
		return col14;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.col_14
	 * @param col14  the value for t_ff_cm_mobile_detail.col_14
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCol14(String col14) {
		this.col14 = col14 == null ? null : col14.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.col_15
	 * @return  the value of t_ff_cm_mobile_detail.col_15
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getCol15() {
		return col15;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.col_15
	 * @param col15  the value for t_ff_cm_mobile_detail.col_15
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCol15(String col15) {
		this.col15 = col15 == null ? null : col15.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.col_16
	 * @return  the value of t_ff_cm_mobile_detail.col_16
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public String getCol16() {
		return col16;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.col_16
	 * @param col16  the value for t_ff_cm_mobile_detail.col_16
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCol16(String col16) {
		this.col16 = col16 == null ? null : col16.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.seq
	 * @return  the value of t_ff_cm_mobile_detail.seq
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public Integer getSeq() {
		return seq;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.seq
	 * @param seq  the value for t_ff_cm_mobile_detail.seq
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setSeq(Integer seq) {
		this.seq = seq;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ff_cm_mobile_detail.creation_time
	 * @return  the value of t_ff_cm_mobile_detail.creation_time
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public LocalDateTime getCreationTime() {
		return creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ff_cm_mobile_detail.creation_time
	 * @param creationTime  the value for t_ff_cm_mobile_detail.creation_time
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	public void setCreationTime(LocalDateTime creationTime) {
		this.creationTime = creationTime;
	}
}