package com.csci.susdev.model;

import java.time.LocalDateTime;

public class Permission {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_permission.id
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_permission.code
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	private String code;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_permission.name
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	private String name;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_permission.description
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	private String description;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_permission.creation_time
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	private LocalDateTime creationTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_permission.create_username
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	private String createUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_permission.create_user_id
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	private String createUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_permission.last_update_time
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	private LocalDateTime lastUpdateTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_permission.last_update_username
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	private String lastUpdateUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_permission.last_update_user_id
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	private String lastUpdateUserId;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_permission.id
	 * @return  the value of t_permission.id
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_permission.id
	 * @param id  the value for t_permission.id
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_permission.code
	 * @return  the value of t_permission.code
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public String getCode() {
		return code;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_permission.code
	 * @param code  the value for t_permission.code
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public void setCode(String code) {
		this.code = code == null ? null : code.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_permission.name
	 * @return  the value of t_permission.name
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public String getName() {
		return name;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_permission.name
	 * @param name  the value for t_permission.name
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public void setName(String name) {
		this.name = name == null ? null : name.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_permission.description
	 * @return  the value of t_permission.description
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_permission.description
	 * @param description  the value for t_permission.description
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public void setDescription(String description) {
		this.description = description == null ? null : description.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_permission.creation_time
	 * @return  the value of t_permission.creation_time
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public LocalDateTime getCreationTime() {
		return creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_permission.creation_time
	 * @param creationTime  the value for t_permission.creation_time
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public void setCreationTime(LocalDateTime creationTime) {
		this.creationTime = creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_permission.create_username
	 * @return  the value of t_permission.create_username
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public String getCreateUsername() {
		return createUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_permission.create_username
	 * @param createUsername  the value for t_permission.create_username
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public void setCreateUsername(String createUsername) {
		this.createUsername = createUsername == null ? null : createUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_permission.create_user_id
	 * @return  the value of t_permission.create_user_id
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public String getCreateUserId() {
		return createUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_permission.create_user_id
	 * @param createUserId  the value for t_permission.create_user_id
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId == null ? null : createUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_permission.last_update_time
	 * @return  the value of t_permission.last_update_time
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public LocalDateTime getLastUpdateTime() {
		return lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_permission.last_update_time
	 * @param lastUpdateTime  the value for t_permission.last_update_time
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_permission.last_update_username
	 * @return  the value of t_permission.last_update_username
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public String getLastUpdateUsername() {
		return lastUpdateUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_permission.last_update_username
	 * @param lastUpdateUsername  the value for t_permission.last_update_username
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public void setLastUpdateUsername(String lastUpdateUsername) {
		this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_permission.last_update_user_id
	 * @return  the value of t_permission.last_update_user_id
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public String getLastUpdateUserId() {
		return lastUpdateUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_permission.last_update_user_id
	 * @param lastUpdateUserId  the value for t_permission.last_update_user_id
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	public void setLastUpdateUserId(String lastUpdateUserId) {
		this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
	}
}