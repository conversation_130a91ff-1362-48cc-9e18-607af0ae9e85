package com.csci.susdev.model;

import java.time.LocalDateTime;

public class AmbientDetailExt {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.id
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.head_id
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String headId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.category
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String category;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.category_digest
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String categoryDigest;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.type
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.type2
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String type2;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.unit
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String unit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.unit_code
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String unitCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.month_value_1
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String monthValue1;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.month_value_2
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String monthValue2;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.month_value_3
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String monthValue3;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.season_value_1
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String seasonValue1;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.month_value_4
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String monthValue4;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.month_value_5
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String monthValue5;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.month_value_6
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String monthValue6;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.season_value_2
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String seasonValue2;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.month_value_7
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String monthValue7;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.month_value_8
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String monthValue8;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.month_value_9
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String monthValue9;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.season_value_3
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String seasonValue3;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.month_value_10
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String monthValue10;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.month_value_11
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String monthValue11;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.month_value_12
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String monthValue12;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.season_value_4
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String seasonValue4;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.year_total_value
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String yearTotalValue;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.remark
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private String remark;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.seq
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private Integer seq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_detail_ext.creation_time
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    private LocalDateTime creationTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.id
     *
     * @return the value of t_ambient_detail_ext.id
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.id
     *
     * @param id the value for t_ambient_detail_ext.id
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.head_id
     *
     * @return the value of t_ambient_detail_ext.head_id
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getHeadId() {
        return headId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.head_id
     *
     * @param headId the value for t_ambient_detail_ext.head_id
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setHeadId(String headId) {
        this.headId = headId == null ? null : headId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.category
     *
     * @return the value of t_ambient_detail_ext.category
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getCategory() {
        return category;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.category
     *
     * @param category the value for t_ambient_detail_ext.category
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setCategory(String category) {
        this.category = category == null ? null : category.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.category_digest
     *
     * @return the value of t_ambient_detail_ext.category_digest
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getCategoryDigest() {
        return categoryDigest;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.category_digest
     *
     * @param categoryDigest the value for t_ambient_detail_ext.category_digest
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setCategoryDigest(String categoryDigest) {
        this.categoryDigest = categoryDigest == null ? null : categoryDigest.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.type
     *
     * @return the value of t_ambient_detail_ext.type
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.type
     *
     * @param type the value for t_ambient_detail_ext.type
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.type2
     *
     * @return the value of t_ambient_detail_ext.type2
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getType2() {
        return type2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.type2
     *
     * @param type2 the value for t_ambient_detail_ext.type2
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setType2(String type2) {
        this.type2 = type2 == null ? null : type2.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.unit
     *
     * @return the value of t_ambient_detail_ext.unit
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getUnit() {
        return unit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.unit
     *
     * @param unit the value for t_ambient_detail_ext.unit
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.unit_code
     *
     * @return the value of t_ambient_detail_ext.unit_code
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getUnitCode() {
        return unitCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.unit_code
     *
     * @param unitCode the value for t_ambient_detail_ext.unit_code
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode == null ? null : unitCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.month_value_1
     *
     * @return the value of t_ambient_detail_ext.month_value_1
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getMonthValue1() {
        return monthValue1;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.month_value_1
     *
     * @param monthValue1 the value for t_ambient_detail_ext.month_value_1
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setMonthValue1(String monthValue1) {
        this.monthValue1 = monthValue1 == null ? null : monthValue1.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.month_value_2
     *
     * @return the value of t_ambient_detail_ext.month_value_2
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getMonthValue2() {
        return monthValue2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.month_value_2
     *
     * @param monthValue2 the value for t_ambient_detail_ext.month_value_2
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setMonthValue2(String monthValue2) {
        this.monthValue2 = monthValue2 == null ? null : monthValue2.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.month_value_3
     *
     * @return the value of t_ambient_detail_ext.month_value_3
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getMonthValue3() {
        return monthValue3;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.month_value_3
     *
     * @param monthValue3 the value for t_ambient_detail_ext.month_value_3
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setMonthValue3(String monthValue3) {
        this.monthValue3 = monthValue3 == null ? null : monthValue3.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.season_value_1
     *
     * @return the value of t_ambient_detail_ext.season_value_1
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getSeasonValue1() {
        return seasonValue1;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.season_value_1
     *
     * @param seasonValue1 the value for t_ambient_detail_ext.season_value_1
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setSeasonValue1(String seasonValue1) {
        this.seasonValue1 = seasonValue1 == null ? null : seasonValue1.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.month_value_4
     *
     * @return the value of t_ambient_detail_ext.month_value_4
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getMonthValue4() {
        return monthValue4;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.month_value_4
     *
     * @param monthValue4 the value for t_ambient_detail_ext.month_value_4
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setMonthValue4(String monthValue4) {
        this.monthValue4 = monthValue4 == null ? null : monthValue4.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.month_value_5
     *
     * @return the value of t_ambient_detail_ext.month_value_5
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getMonthValue5() {
        return monthValue5;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.month_value_5
     *
     * @param monthValue5 the value for t_ambient_detail_ext.month_value_5
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setMonthValue5(String monthValue5) {
        this.monthValue5 = monthValue5 == null ? null : monthValue5.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.month_value_6
     *
     * @return the value of t_ambient_detail_ext.month_value_6
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getMonthValue6() {
        return monthValue6;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.month_value_6
     *
     * @param monthValue6 the value for t_ambient_detail_ext.month_value_6
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setMonthValue6(String monthValue6) {
        this.monthValue6 = monthValue6 == null ? null : monthValue6.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.season_value_2
     *
     * @return the value of t_ambient_detail_ext.season_value_2
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getSeasonValue2() {
        return seasonValue2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.season_value_2
     *
     * @param seasonValue2 the value for t_ambient_detail_ext.season_value_2
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setSeasonValue2(String seasonValue2) {
        this.seasonValue2 = seasonValue2 == null ? null : seasonValue2.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.month_value_7
     *
     * @return the value of t_ambient_detail_ext.month_value_7
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getMonthValue7() {
        return monthValue7;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.month_value_7
     *
     * @param monthValue7 the value for t_ambient_detail_ext.month_value_7
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setMonthValue7(String monthValue7) {
        this.monthValue7 = monthValue7 == null ? null : monthValue7.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.month_value_8
     *
     * @return the value of t_ambient_detail_ext.month_value_8
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getMonthValue8() {
        return monthValue8;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.month_value_8
     *
     * @param monthValue8 the value for t_ambient_detail_ext.month_value_8
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setMonthValue8(String monthValue8) {
        this.monthValue8 = monthValue8 == null ? null : monthValue8.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.month_value_9
     *
     * @return the value of t_ambient_detail_ext.month_value_9
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getMonthValue9() {
        return monthValue9;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.month_value_9
     *
     * @param monthValue9 the value for t_ambient_detail_ext.month_value_9
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setMonthValue9(String monthValue9) {
        this.monthValue9 = monthValue9 == null ? null : monthValue9.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.season_value_3
     *
     * @return the value of t_ambient_detail_ext.season_value_3
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getSeasonValue3() {
        return seasonValue3;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.season_value_3
     *
     * @param seasonValue3 the value for t_ambient_detail_ext.season_value_3
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setSeasonValue3(String seasonValue3) {
        this.seasonValue3 = seasonValue3 == null ? null : seasonValue3.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.month_value_10
     *
     * @return the value of t_ambient_detail_ext.month_value_10
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getMonthValue10() {
        return monthValue10;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.month_value_10
     *
     * @param monthValue10 the value for t_ambient_detail_ext.month_value_10
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setMonthValue10(String monthValue10) {
        this.monthValue10 = monthValue10 == null ? null : monthValue10.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.month_value_11
     *
     * @return the value of t_ambient_detail_ext.month_value_11
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getMonthValue11() {
        return monthValue11;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.month_value_11
     *
     * @param monthValue11 the value for t_ambient_detail_ext.month_value_11
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setMonthValue11(String monthValue11) {
        this.monthValue11 = monthValue11 == null ? null : monthValue11.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.month_value_12
     *
     * @return the value of t_ambient_detail_ext.month_value_12
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getMonthValue12() {
        return monthValue12;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.month_value_12
     *
     * @param monthValue12 the value for t_ambient_detail_ext.month_value_12
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setMonthValue12(String monthValue12) {
        this.monthValue12 = monthValue12 == null ? null : monthValue12.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.season_value_4
     *
     * @return the value of t_ambient_detail_ext.season_value_4
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getSeasonValue4() {
        return seasonValue4;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.season_value_4
     *
     * @param seasonValue4 the value for t_ambient_detail_ext.season_value_4
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setSeasonValue4(String seasonValue4) {
        this.seasonValue4 = seasonValue4 == null ? null : seasonValue4.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.year_total_value
     *
     * @return the value of t_ambient_detail_ext.year_total_value
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getYearTotalValue() {
        return yearTotalValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.year_total_value
     *
     * @param yearTotalValue the value for t_ambient_detail_ext.year_total_value
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setYearTotalValue(String yearTotalValue) {
        this.yearTotalValue = yearTotalValue == null ? null : yearTotalValue.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.remark
     *
     * @return the value of t_ambient_detail_ext.remark
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.remark
     *
     * @param remark the value for t_ambient_detail_ext.remark
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.seq
     *
     * @return the value of t_ambient_detail_ext.seq
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public Integer getSeq() {
        return seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.seq
     *
     * @param seq the value for t_ambient_detail_ext.seq
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_detail_ext.creation_time
     *
     * @return the value of t_ambient_detail_ext.creation_time
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_detail_ext.creation_time
     *
     * @param creationTime the value for t_ambient_detail_ext.creation_time
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }
}