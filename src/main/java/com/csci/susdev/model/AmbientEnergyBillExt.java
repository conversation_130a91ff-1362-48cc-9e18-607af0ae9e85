package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class AmbientEnergyBillExt {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill_ext.id
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill_ext.head_id
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    private String headId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill_ext.type
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    private String type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill_ext.bill_no
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    private String billNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill_ext.from_time
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    private LocalDateTime fromTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill_ext.to_time
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    private LocalDateTime toTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill_ext.consumption
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    private BigDecimal consumption;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill_ext.attachment_id
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    private String attachmentId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill_ext.creation_time
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill_ext.create_username
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill_ext.create_user_id
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill_ext.last_update_time
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill_ext.last_update_username
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill_ext.last_update_user_id
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ambient_energy_bill_ext.last_update_version
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    private Integer lastUpdateVersion;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill_ext.id
     *
     * @return the value of t_ambient_energy_bill_ext.id
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill_ext.id
     *
     * @param id the value for t_ambient_energy_bill_ext.id
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill_ext.head_id
     *
     * @return the value of t_ambient_energy_bill_ext.head_id
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public String getHeadId() {
        return headId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill_ext.head_id
     *
     * @param headId the value for t_ambient_energy_bill_ext.head_id
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public void setHeadId(String headId) {
        this.headId = headId == null ? null : headId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill_ext.type
     *
     * @return the value of t_ambient_energy_bill_ext.type
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill_ext.type
     *
     * @param type the value for t_ambient_energy_bill_ext.type
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill_ext.bill_no
     *
     * @return the value of t_ambient_energy_bill_ext.bill_no
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public String getBillNo() {
        return billNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill_ext.bill_no
     *
     * @param billNo the value for t_ambient_energy_bill_ext.bill_no
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public void setBillNo(String billNo) {
        this.billNo = billNo == null ? null : billNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill_ext.from_time
     *
     * @return the value of t_ambient_energy_bill_ext.from_time
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public LocalDateTime getFromTime() {
        return fromTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill_ext.from_time
     *
     * @param fromTime the value for t_ambient_energy_bill_ext.from_time
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public void setFromTime(LocalDateTime fromTime) {
        this.fromTime = fromTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill_ext.to_time
     *
     * @return the value of t_ambient_energy_bill_ext.to_time
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public LocalDateTime getToTime() {
        return toTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill_ext.to_time
     *
     * @param toTime the value for t_ambient_energy_bill_ext.to_time
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public void setToTime(LocalDateTime toTime) {
        this.toTime = toTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill_ext.consumption
     *
     * @return the value of t_ambient_energy_bill_ext.consumption
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public BigDecimal getConsumption() {
        return consumption;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill_ext.consumption
     *
     * @param consumption the value for t_ambient_energy_bill_ext.consumption
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public void setConsumption(BigDecimal consumption) {
        this.consumption = consumption;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill_ext.attachment_id
     *
     * @return the value of t_ambient_energy_bill_ext.attachment_id
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public String getAttachmentId() {
        return attachmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill_ext.attachment_id
     *
     * @param attachmentId the value for t_ambient_energy_bill_ext.attachment_id
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId == null ? null : attachmentId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill_ext.creation_time
     *
     * @return the value of t_ambient_energy_bill_ext.creation_time
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill_ext.creation_time
     *
     * @param creationTime the value for t_ambient_energy_bill_ext.creation_time
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill_ext.create_username
     *
     * @return the value of t_ambient_energy_bill_ext.create_username
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill_ext.create_username
     *
     * @param createUsername the value for t_ambient_energy_bill_ext.create_username
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill_ext.create_user_id
     *
     * @return the value of t_ambient_energy_bill_ext.create_user_id
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill_ext.create_user_id
     *
     * @param createUserId the value for t_ambient_energy_bill_ext.create_user_id
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill_ext.last_update_time
     *
     * @return the value of t_ambient_energy_bill_ext.last_update_time
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill_ext.last_update_time
     *
     * @param lastUpdateTime the value for t_ambient_energy_bill_ext.last_update_time
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill_ext.last_update_username
     *
     * @return the value of t_ambient_energy_bill_ext.last_update_username
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill_ext.last_update_username
     *
     * @param lastUpdateUsername the value for t_ambient_energy_bill_ext.last_update_username
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill_ext.last_update_user_id
     *
     * @return the value of t_ambient_energy_bill_ext.last_update_user_id
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill_ext.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_ambient_energy_bill_ext.last_update_user_id
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ambient_energy_bill_ext.last_update_version
     *
     * @return the value of t_ambient_energy_bill_ext.last_update_version
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ambient_energy_bill_ext.last_update_version
     *
     * @param lastUpdateVersion the value for t_ambient_energy_bill_ext.last_update_version
     *
     * @mbg.generated Fri Aug 16 12:03:59 HKT 2024
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }
}