package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FcCarbonFactorFlc {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.id
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.chinese_name
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private String chineseName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.upstream_emission
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private BigDecimal upstreamEmission;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.downstream_emission
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private BigDecimal downstreamEmission;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.emission_unit
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private String emissionUnit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.emission_stage
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private String emissionStage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.uncertainty
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private String uncertainty;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.caption
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private String caption;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.data_time
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private String dataTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.description
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.carbon_factor
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private BigDecimal carbonFactor;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.carbon_factor_unit
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private String carbonFactorUnit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.datasource
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private String datasource;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.creation_time
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.create_username
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.create_user_id
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.last_update_time
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.last_update_username
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.last_update_user_id
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.last_update_version
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.is_deleted
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private Boolean isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_carbon_factor_flc.record_year
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    private Integer recordYear;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.id
     *
     * @return the value of t_fc_carbon_factor_flc.id
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.id
     *
     * @param id the value for t_fc_carbon_factor_flc.id
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.chinese_name
     *
     * @return the value of t_fc_carbon_factor_flc.chinese_name
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public String getChineseName() {
        return chineseName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.chinese_name
     *
     * @param chineseName the value for t_fc_carbon_factor_flc.chinese_name
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setChineseName(String chineseName) {
        this.chineseName = chineseName == null ? null : chineseName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.upstream_emission
     *
     * @return the value of t_fc_carbon_factor_flc.upstream_emission
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public BigDecimal getUpstreamEmission() {
        return upstreamEmission;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.upstream_emission
     *
     * @param upstreamEmission the value for t_fc_carbon_factor_flc.upstream_emission
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setUpstreamEmission(BigDecimal upstreamEmission) {
        this.upstreamEmission = upstreamEmission;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.downstream_emission
     *
     * @return the value of t_fc_carbon_factor_flc.downstream_emission
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public BigDecimal getDownstreamEmission() {
        return downstreamEmission;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.downstream_emission
     *
     * @param downstreamEmission the value for t_fc_carbon_factor_flc.downstream_emission
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setDownstreamEmission(BigDecimal downstreamEmission) {
        this.downstreamEmission = downstreamEmission;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.emission_unit
     *
     * @return the value of t_fc_carbon_factor_flc.emission_unit
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public String getEmissionUnit() {
        return emissionUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.emission_unit
     *
     * @param emissionUnit the value for t_fc_carbon_factor_flc.emission_unit
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setEmissionUnit(String emissionUnit) {
        this.emissionUnit = emissionUnit == null ? null : emissionUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.emission_stage
     *
     * @return the value of t_fc_carbon_factor_flc.emission_stage
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public String getEmissionStage() {
        return emissionStage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.emission_stage
     *
     * @param emissionStage the value for t_fc_carbon_factor_flc.emission_stage
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setEmissionStage(String emissionStage) {
        this.emissionStage = emissionStage == null ? null : emissionStage.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.uncertainty
     *
     * @return the value of t_fc_carbon_factor_flc.uncertainty
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public String getUncertainty() {
        return uncertainty;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.uncertainty
     *
     * @param uncertainty the value for t_fc_carbon_factor_flc.uncertainty
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setUncertainty(String uncertainty) {
        this.uncertainty = uncertainty == null ? null : uncertainty.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.caption
     *
     * @return the value of t_fc_carbon_factor_flc.caption
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public String getCaption() {
        return caption;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.caption
     *
     * @param caption the value for t_fc_carbon_factor_flc.caption
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setCaption(String caption) {
        this.caption = caption == null ? null : caption.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.data_time
     *
     * @return the value of t_fc_carbon_factor_flc.data_time
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public String getDataTime() {
        return dataTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.data_time
     *
     * @param dataTime the value for t_fc_carbon_factor_flc.data_time
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setDataTime(String dataTime) {
        this.dataTime = dataTime == null ? null : dataTime.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.description
     *
     * @return the value of t_fc_carbon_factor_flc.description
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.description
     *
     * @param description the value for t_fc_carbon_factor_flc.description
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.carbon_factor
     *
     * @return the value of t_fc_carbon_factor_flc.carbon_factor
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public BigDecimal getCarbonFactor() {
        return carbonFactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.carbon_factor
     *
     * @param carbonFactor the value for t_fc_carbon_factor_flc.carbon_factor
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setCarbonFactor(BigDecimal carbonFactor) {
        this.carbonFactor = carbonFactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.carbon_factor_unit
     *
     * @return the value of t_fc_carbon_factor_flc.carbon_factor_unit
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public String getCarbonFactorUnit() {
        return carbonFactorUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.carbon_factor_unit
     *
     * @param carbonFactorUnit the value for t_fc_carbon_factor_flc.carbon_factor_unit
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setCarbonFactorUnit(String carbonFactorUnit) {
        this.carbonFactorUnit = carbonFactorUnit == null ? null : carbonFactorUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.datasource
     *
     * @return the value of t_fc_carbon_factor_flc.datasource
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public String getDatasource() {
        return datasource;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.datasource
     *
     * @param datasource the value for t_fc_carbon_factor_flc.datasource
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setDatasource(String datasource) {
        this.datasource = datasource == null ? null : datasource.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.creation_time
     *
     * @return the value of t_fc_carbon_factor_flc.creation_time
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.creation_time
     *
     * @param creationTime the value for t_fc_carbon_factor_flc.creation_time
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.create_username
     *
     * @return the value of t_fc_carbon_factor_flc.create_username
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.create_username
     *
     * @param createUsername the value for t_fc_carbon_factor_flc.create_username
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.create_user_id
     *
     * @return the value of t_fc_carbon_factor_flc.create_user_id
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.create_user_id
     *
     * @param createUserId the value for t_fc_carbon_factor_flc.create_user_id
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.last_update_time
     *
     * @return the value of t_fc_carbon_factor_flc.last_update_time
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.last_update_time
     *
     * @param lastUpdateTime the value for t_fc_carbon_factor_flc.last_update_time
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.last_update_username
     *
     * @return the value of t_fc_carbon_factor_flc.last_update_username
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.last_update_username
     *
     * @param lastUpdateUsername the value for t_fc_carbon_factor_flc.last_update_username
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.last_update_user_id
     *
     * @return the value of t_fc_carbon_factor_flc.last_update_user_id
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_fc_carbon_factor_flc.last_update_user_id
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.last_update_version
     *
     * @return the value of t_fc_carbon_factor_flc.last_update_version
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.last_update_version
     *
     * @param lastUpdateVersion the value for t_fc_carbon_factor_flc.last_update_version
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.is_deleted
     *
     * @return the value of t_fc_carbon_factor_flc.is_deleted
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.is_deleted
     *
     * @param isDeleted the value for t_fc_carbon_factor_flc.is_deleted
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_carbon_factor_flc.record_year
     *
     * @return the value of t_fc_carbon_factor_flc.record_year
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public Integer getRecordYear() {
        return recordYear;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_carbon_factor_flc.record_year
     *
     * @param recordYear the value for t_fc_carbon_factor_flc.record_year
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setRecordYear(Integer recordYear) {
        this.recordYear = recordYear;
    }
}