package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class FcCarbonFactorHkExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    public FcCarbonFactorHkExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andChineseNameIsNull() {
            addCriterion("chinese_name is null");
            return (Criteria) this;
        }

        public Criteria andChineseNameIsNotNull() {
            addCriterion("chinese_name is not null");
            return (Criteria) this;
        }

        public Criteria andChineseNameEqualTo(String value) {
            addCriterion("chinese_name =", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameNotEqualTo(String value) {
            addCriterion("chinese_name <>", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameGreaterThan(String value) {
            addCriterion("chinese_name >", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameGreaterThanOrEqualTo(String value) {
            addCriterion("chinese_name >=", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameLessThan(String value) {
            addCriterion("chinese_name <", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameLessThanOrEqualTo(String value) {
            addCriterion("chinese_name <=", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameLike(String value) {
            addCriterion("chinese_name like", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameNotLike(String value) {
            addCriterion("chinese_name not like", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameIn(List<String> values) {
            addCriterion("chinese_name in", values, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameNotIn(List<String> values) {
            addCriterion("chinese_name not in", values, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameBetween(String value1, String value2) {
            addCriterion("chinese_name between", value1, value2, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameNotBetween(String value1, String value2) {
            addCriterion("chinese_name not between", value1, value2, "chineseName");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeIsNull() {
            addCriterion("material_attribute is null");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeIsNotNull() {
            addCriterion("material_attribute is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeEqualTo(String value) {
            addCriterion("material_attribute =", value, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeNotEqualTo(String value) {
            addCriterion("material_attribute <>", value, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeGreaterThan(String value) {
            addCriterion("material_attribute >", value, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeGreaterThanOrEqualTo(String value) {
            addCriterion("material_attribute >=", value, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeLessThan(String value) {
            addCriterion("material_attribute <", value, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeLessThanOrEqualTo(String value) {
            addCriterion("material_attribute <=", value, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeLike(String value) {
            addCriterion("material_attribute like", value, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeNotLike(String value) {
            addCriterion("material_attribute not like", value, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeIn(List<String> values) {
            addCriterion("material_attribute in", values, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeNotIn(List<String> values) {
            addCriterion("material_attribute not in", values, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeBetween(String value1, String value2) {
            addCriterion("material_attribute between", value1, value2, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeNotBetween(String value1, String value2) {
            addCriterion("material_attribute not between", value1, value2, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andSupplierIsNull() {
            addCriterion("supplier is null");
            return (Criteria) this;
        }

        public Criteria andSupplierIsNotNull() {
            addCriterion("supplier is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierEqualTo(String value) {
            addCriterion("supplier =", value, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierNotEqualTo(String value) {
            addCriterion("supplier <>", value, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierGreaterThan(String value) {
            addCriterion("supplier >", value, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierGreaterThanOrEqualTo(String value) {
            addCriterion("supplier >=", value, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierLessThan(String value) {
            addCriterion("supplier <", value, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierLessThanOrEqualTo(String value) {
            addCriterion("supplier <=", value, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierLike(String value) {
            addCriterion("supplier like", value, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierNotLike(String value) {
            addCriterion("supplier not like", value, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierIn(List<String> values) {
            addCriterion("supplier in", values, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierNotIn(List<String> values) {
            addCriterion("supplier not in", values, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierBetween(String value1, String value2) {
            addCriterion("supplier between", value1, value2, "supplier");
            return (Criteria) this;
        }

        public Criteria andSupplierNotBetween(String value1, String value2) {
            addCriterion("supplier not between", value1, value2, "supplier");
            return (Criteria) this;
        }

        public Criteria andProductionProcessIsNull() {
            addCriterion("production_process is null");
            return (Criteria) this;
        }

        public Criteria andProductionProcessIsNotNull() {
            addCriterion("production_process is not null");
            return (Criteria) this;
        }

        public Criteria andProductionProcessEqualTo(String value) {
            addCriterion("production_process =", value, "productionProcess");
            return (Criteria) this;
        }

        public Criteria andProductionProcessNotEqualTo(String value) {
            addCriterion("production_process <>", value, "productionProcess");
            return (Criteria) this;
        }

        public Criteria andProductionProcessGreaterThan(String value) {
            addCriterion("production_process >", value, "productionProcess");
            return (Criteria) this;
        }

        public Criteria andProductionProcessGreaterThanOrEqualTo(String value) {
            addCriterion("production_process >=", value, "productionProcess");
            return (Criteria) this;
        }

        public Criteria andProductionProcessLessThan(String value) {
            addCriterion("production_process <", value, "productionProcess");
            return (Criteria) this;
        }

        public Criteria andProductionProcessLessThanOrEqualTo(String value) {
            addCriterion("production_process <=", value, "productionProcess");
            return (Criteria) this;
        }

        public Criteria andProductionProcessLike(String value) {
            addCriterion("production_process like", value, "productionProcess");
            return (Criteria) this;
        }

        public Criteria andProductionProcessNotLike(String value) {
            addCriterion("production_process not like", value, "productionProcess");
            return (Criteria) this;
        }

        public Criteria andProductionProcessIn(List<String> values) {
            addCriterion("production_process in", values, "productionProcess");
            return (Criteria) this;
        }

        public Criteria andProductionProcessNotIn(List<String> values) {
            addCriterion("production_process not in", values, "productionProcess");
            return (Criteria) this;
        }

        public Criteria andProductionProcessBetween(String value1, String value2) {
            addCriterion("production_process between", value1, value2, "productionProcess");
            return (Criteria) this;
        }

        public Criteria andProductionProcessNotBetween(String value1, String value2) {
            addCriterion("production_process not between", value1, value2, "productionProcess");
            return (Criteria) this;
        }

        public Criteria andMaterialBelongsToIsNull() {
            addCriterion("material_belongs_to is null");
            return (Criteria) this;
        }

        public Criteria andMaterialBelongsToIsNotNull() {
            addCriterion("material_belongs_to is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialBelongsToEqualTo(String value) {
            addCriterion("material_belongs_to =", value, "materialBelongsTo");
            return (Criteria) this;
        }

        public Criteria andMaterialBelongsToNotEqualTo(String value) {
            addCriterion("material_belongs_to <>", value, "materialBelongsTo");
            return (Criteria) this;
        }

        public Criteria andMaterialBelongsToGreaterThan(String value) {
            addCriterion("material_belongs_to >", value, "materialBelongsTo");
            return (Criteria) this;
        }

        public Criteria andMaterialBelongsToGreaterThanOrEqualTo(String value) {
            addCriterion("material_belongs_to >=", value, "materialBelongsTo");
            return (Criteria) this;
        }

        public Criteria andMaterialBelongsToLessThan(String value) {
            addCriterion("material_belongs_to <", value, "materialBelongsTo");
            return (Criteria) this;
        }

        public Criteria andMaterialBelongsToLessThanOrEqualTo(String value) {
            addCriterion("material_belongs_to <=", value, "materialBelongsTo");
            return (Criteria) this;
        }

        public Criteria andMaterialBelongsToLike(String value) {
            addCriterion("material_belongs_to like", value, "materialBelongsTo");
            return (Criteria) this;
        }

        public Criteria andMaterialBelongsToNotLike(String value) {
            addCriterion("material_belongs_to not like", value, "materialBelongsTo");
            return (Criteria) this;
        }

        public Criteria andMaterialBelongsToIn(List<String> values) {
            addCriterion("material_belongs_to in", values, "materialBelongsTo");
            return (Criteria) this;
        }

        public Criteria andMaterialBelongsToNotIn(List<String> values) {
            addCriterion("material_belongs_to not in", values, "materialBelongsTo");
            return (Criteria) this;
        }

        public Criteria andMaterialBelongsToBetween(String value1, String value2) {
            addCriterion("material_belongs_to between", value1, value2, "materialBelongsTo");
            return (Criteria) this;
        }

        public Criteria andMaterialBelongsToNotBetween(String value1, String value2) {
            addCriterion("material_belongs_to not between", value1, value2, "materialBelongsTo");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorIsNull() {
            addCriterion("carbon_factor is null");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorIsNotNull() {
            addCriterion("carbon_factor is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorEqualTo(BigDecimal value) {
            addCriterion("carbon_factor =", value, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorNotEqualTo(BigDecimal value) {
            addCriterion("carbon_factor <>", value, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorGreaterThan(BigDecimal value) {
            addCriterion("carbon_factor >", value, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("carbon_factor >=", value, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorLessThan(BigDecimal value) {
            addCriterion("carbon_factor <", value, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorLessThanOrEqualTo(BigDecimal value) {
            addCriterion("carbon_factor <=", value, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorIn(List<BigDecimal> values) {
            addCriterion("carbon_factor in", values, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorNotIn(List<BigDecimal> values) {
            addCriterion("carbon_factor not in", values, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carbon_factor between", value1, value2, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carbon_factor not between", value1, value2, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitIsNull() {
            addCriterion("carbon_factor_unit is null");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitIsNotNull() {
            addCriterion("carbon_factor_unit is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitEqualTo(String value) {
            addCriterion("carbon_factor_unit =", value, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitNotEqualTo(String value) {
            addCriterion("carbon_factor_unit <>", value, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitGreaterThan(String value) {
            addCriterion("carbon_factor_unit >", value, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitGreaterThanOrEqualTo(String value) {
            addCriterion("carbon_factor_unit >=", value, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitLessThan(String value) {
            addCriterion("carbon_factor_unit <", value, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitLessThanOrEqualTo(String value) {
            addCriterion("carbon_factor_unit <=", value, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitLike(String value) {
            addCriterion("carbon_factor_unit like", value, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitNotLike(String value) {
            addCriterion("carbon_factor_unit not like", value, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitIn(List<String> values) {
            addCriterion("carbon_factor_unit in", values, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitNotIn(List<String> values) {
            addCriterion("carbon_factor_unit not in", values, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitBetween(String value1, String value2) {
            addCriterion("carbon_factor_unit between", value1, value2, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitNotBetween(String value1, String value2) {
            addCriterion("carbon_factor_unit not between", value1, value2, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andSourceDescriptionIsNull() {
            addCriterion("source_description is null");
            return (Criteria) this;
        }

        public Criteria andSourceDescriptionIsNotNull() {
            addCriterion("source_description is not null");
            return (Criteria) this;
        }

        public Criteria andSourceDescriptionEqualTo(String value) {
            addCriterion("source_description =", value, "sourceDescription");
            return (Criteria) this;
        }

        public Criteria andSourceDescriptionNotEqualTo(String value) {
            addCriterion("source_description <>", value, "sourceDescription");
            return (Criteria) this;
        }

        public Criteria andSourceDescriptionGreaterThan(String value) {
            addCriterion("source_description >", value, "sourceDescription");
            return (Criteria) this;
        }

        public Criteria andSourceDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("source_description >=", value, "sourceDescription");
            return (Criteria) this;
        }

        public Criteria andSourceDescriptionLessThan(String value) {
            addCriterion("source_description <", value, "sourceDescription");
            return (Criteria) this;
        }

        public Criteria andSourceDescriptionLessThanOrEqualTo(String value) {
            addCriterion("source_description <=", value, "sourceDescription");
            return (Criteria) this;
        }

        public Criteria andSourceDescriptionLike(String value) {
            addCriterion("source_description like", value, "sourceDescription");
            return (Criteria) this;
        }

        public Criteria andSourceDescriptionNotLike(String value) {
            addCriterion("source_description not like", value, "sourceDescription");
            return (Criteria) this;
        }

        public Criteria andSourceDescriptionIn(List<String> values) {
            addCriterion("source_description in", values, "sourceDescription");
            return (Criteria) this;
        }

        public Criteria andSourceDescriptionNotIn(List<String> values) {
            addCriterion("source_description not in", values, "sourceDescription");
            return (Criteria) this;
        }

        public Criteria andSourceDescriptionBetween(String value1, String value2) {
            addCriterion("source_description between", value1, value2, "sourceDescription");
            return (Criteria) this;
        }

        public Criteria andSourceDescriptionNotBetween(String value1, String value2) {
            addCriterion("source_description not between", value1, value2, "sourceDescription");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNull() {
            addCriterion("creation_time is null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNotNull() {
            addCriterion("creation_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeEqualTo(LocalDateTime value) {
            addCriterion("creation_time =", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
            addCriterion("creation_time <>", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
            addCriterion("creation_time >", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time >=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThan(LocalDateTime value) {
            addCriterion("creation_time <", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time <=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIn(List<LocalDateTime> values) {
            addCriterion("creation_time in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
            addCriterion("creation_time not in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time not between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNull() {
            addCriterion("create_username is null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNotNull() {
            addCriterion("create_username is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameEqualTo(String value) {
            addCriterion("create_username =", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotEqualTo(String value) {
            addCriterion("create_username <>", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThan(String value) {
            addCriterion("create_username >", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("create_username >=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThan(String value) {
            addCriterion("create_username <", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
            addCriterion("create_username <=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLike(String value) {
            addCriterion("create_username like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotLike(String value) {
            addCriterion("create_username not like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIn(List<String> values) {
            addCriterion("create_username in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotIn(List<String> values) {
            addCriterion("create_username not in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameBetween(String value1, String value2) {
            addCriterion("create_username between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotBetween(String value1, String value2) {
            addCriterion("create_username not between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNull() {
            addCriterion("last_update_time is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNotNull() {
            addCriterion("last_update_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("last_update_time =", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <>", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("last_update_time >", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time >=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("last_update_time <", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("last_update_time in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("last_update_time not in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNull() {
            addCriterion("last_update_username is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNotNull() {
            addCriterion("last_update_username is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameEqualTo(String value) {
            addCriterion("last_update_username =", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotEqualTo(String value) {
            addCriterion("last_update_username <>", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThan(String value) {
            addCriterion("last_update_username >", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_username >=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThan(String value) {
            addCriterion("last_update_username <", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
            addCriterion("last_update_username <=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLike(String value) {
            addCriterion("last_update_username like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotLike(String value) {
            addCriterion("last_update_username not like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIn(List<String> values) {
            addCriterion("last_update_username in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotIn(List<String> values) {
            addCriterion("last_update_username not in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
            addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
            addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNull() {
            addCriterion("last_update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNotNull() {
            addCriterion("last_update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdEqualTo(String value) {
            addCriterion("last_update_user_id =", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotEqualTo(String value) {
            addCriterion("last_update_user_id <>", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThan(String value) {
            addCriterion("last_update_user_id >", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_user_id >=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThan(String value) {
            addCriterion("last_update_user_id <", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThanOrEqualTo(String value) {
            addCriterion("last_update_user_id <=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLike(String value) {
            addCriterion("last_update_user_id like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotLike(String value) {
            addCriterion("last_update_user_id not like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIn(List<String> values) {
            addCriterion("last_update_user_id in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotIn(List<String> values) {
            addCriterion("last_update_user_id not in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdBetween(String value1, String value2) {
            addCriterion("last_update_user_id between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotBetween(String value1, String value2) {
            addCriterion("last_update_user_id not between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNull() {
            addCriterion("last_update_version is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNotNull() {
            addCriterion("last_update_version is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionEqualTo(Integer value) {
            addCriterion("last_update_version =", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotEqualTo(Integer value) {
            addCriterion("last_update_version <>", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThan(Integer value) {
            addCriterion("last_update_version >", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("last_update_version >=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThan(Integer value) {
            addCriterion("last_update_version <", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThanOrEqualTo(Integer value) {
            addCriterion("last_update_version <=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIn(List<Integer> values) {
            addCriterion("last_update_version in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotIn(List<Integer> values) {
            addCriterion("last_update_version not in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version not between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andRecordYearIsNull() {
            addCriterion("record_year is null");
            return (Criteria) this;
        }

        public Criteria andRecordYearIsNotNull() {
            addCriterion("record_year is not null");
            return (Criteria) this;
        }

        public Criteria andRecordYearEqualTo(Integer value) {
            addCriterion("record_year =", value, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearNotEqualTo(Integer value) {
            addCriterion("record_year <>", value, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearGreaterThan(Integer value) {
            addCriterion("record_year >", value, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearGreaterThanOrEqualTo(Integer value) {
            addCriterion("record_year >=", value, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearLessThan(Integer value) {
            addCriterion("record_year <", value, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearLessThanOrEqualTo(Integer value) {
            addCriterion("record_year <=", value, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearIn(List<Integer> values) {
            addCriterion("record_year in", values, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearNotIn(List<Integer> values) {
            addCriterion("record_year not in", values, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearBetween(Integer value1, Integer value2) {
            addCriterion("record_year between", value1, value2, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearNotBetween(Integer value1, Integer value2) {
            addCriterion("record_year not between", value1, value2, "recordYear");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated do_not_delete_during_merge Wed Feb 05 09:32:45 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_fc_carbon_factor_hk
     *
     * @mbg.generated Wed Feb 05 09:32:45 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}