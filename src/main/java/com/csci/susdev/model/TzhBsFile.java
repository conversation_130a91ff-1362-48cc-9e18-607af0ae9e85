package com.csci.susdev.model;

import java.time.LocalDateTime;

public class TzhBsFile {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_File.Id
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_File.SiteName
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    private String sitename;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_File.ProtocolName
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    private String protocolname;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_File.RefId
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    private String refid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_File.Section
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    private String section;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_File.Category
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    private String category;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_File.Name
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_File.Type
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    private String type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_File.CreatedBy
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    private String createdby;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_File.CreatedTime
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    private LocalDateTime createdtime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_File.DeletedBy
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    private String deletedby;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_File.DeletedTime
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    private LocalDateTime deletedtime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_File.IsDeleted
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    private Boolean isdeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_File.Data
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    private byte[] data;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_File.Id
     *
     * @return the value of Tzh_Bs_File.Id
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_File.Id
     *
     * @param id the value for Tzh_Bs_File.Id
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_File.SiteName
     *
     * @return the value of Tzh_Bs_File.SiteName
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public String getSitename() {
        return sitename;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_File.SiteName
     *
     * @param sitename the value for Tzh_Bs_File.SiteName
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public void setSitename(String sitename) {
        this.sitename = sitename == null ? null : sitename.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_File.ProtocolName
     *
     * @return the value of Tzh_Bs_File.ProtocolName
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public String getProtocolname() {
        return protocolname;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_File.ProtocolName
     *
     * @param protocolname the value for Tzh_Bs_File.ProtocolName
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public void setProtocolname(String protocolname) {
        this.protocolname = protocolname == null ? null : protocolname.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_File.RefId
     *
     * @return the value of Tzh_Bs_File.RefId
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public String getRefid() {
        return refid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_File.RefId
     *
     * @param refid the value for Tzh_Bs_File.RefId
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public void setRefid(String refid) {
        this.refid = refid == null ? null : refid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_File.Section
     *
     * @return the value of Tzh_Bs_File.Section
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public String getSection() {
        return section;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_File.Section
     *
     * @param section the value for Tzh_Bs_File.Section
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public void setSection(String section) {
        this.section = section == null ? null : section.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_File.Category
     *
     * @return the value of Tzh_Bs_File.Category
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public String getCategory() {
        return category;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_File.Category
     *
     * @param category the value for Tzh_Bs_File.Category
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public void setCategory(String category) {
        this.category = category == null ? null : category.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_File.Name
     *
     * @return the value of Tzh_Bs_File.Name
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_File.Name
     *
     * @param name the value for Tzh_Bs_File.Name
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_File.Type
     *
     * @return the value of Tzh_Bs_File.Type
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_File.Type
     *
     * @param type the value for Tzh_Bs_File.Type
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_File.CreatedBy
     *
     * @return the value of Tzh_Bs_File.CreatedBy
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public String getCreatedby() {
        return createdby;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_File.CreatedBy
     *
     * @param createdby the value for Tzh_Bs_File.CreatedBy
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public void setCreatedby(String createdby) {
        this.createdby = createdby == null ? null : createdby.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_File.CreatedTime
     *
     * @return the value of Tzh_Bs_File.CreatedTime
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public LocalDateTime getCreatedtime() {
        return createdtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_File.CreatedTime
     *
     * @param createdtime the value for Tzh_Bs_File.CreatedTime
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public void setCreatedtime(LocalDateTime createdtime) {
        this.createdtime = createdtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_File.DeletedBy
     *
     * @return the value of Tzh_Bs_File.DeletedBy
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public String getDeletedby() {
        return deletedby;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_File.DeletedBy
     *
     * @param deletedby the value for Tzh_Bs_File.DeletedBy
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public void setDeletedby(String deletedby) {
        this.deletedby = deletedby == null ? null : deletedby.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_File.DeletedTime
     *
     * @return the value of Tzh_Bs_File.DeletedTime
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public LocalDateTime getDeletedtime() {
        return deletedtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_File.DeletedTime
     *
     * @param deletedtime the value for Tzh_Bs_File.DeletedTime
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public void setDeletedtime(LocalDateTime deletedtime) {
        this.deletedtime = deletedtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_File.IsDeleted
     *
     * @return the value of Tzh_Bs_File.IsDeleted
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public Boolean getIsdeleted() {
        return isdeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_File.IsDeleted
     *
     * @param isdeleted the value for Tzh_Bs_File.IsDeleted
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public void setIsdeleted(Boolean isdeleted) {
        this.isdeleted = isdeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_File.Data
     *
     * @return the value of Tzh_Bs_File.Data
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public byte[] getData() {
        return data;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_File.Data
     *
     * @param data the value for Tzh_Bs_File.Data
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    public void setData(byte[] data) {
        this.data = data;
    }
}