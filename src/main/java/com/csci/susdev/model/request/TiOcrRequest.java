package com.csci.susdev.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TiOcrRequest {

    /**
     * 接入服务时生成的唯一id，用于唯一标识接入业务(私有化部署之后，app_id填写任意的由数字组成的字符串即可)
     * 必填
     */
    @JsonProperty("app_id")
    private String appId;

    /**
     * 图像base64编码后的字符串，图像需是JPG、PNG、BMP其中之一的格式
     * 必填
     */
    private String image;

    /**
     * 用户自定义的唯一会话id
     * 选填
     */
    @JsonProperty("session_id")
    private String sessionId;

    /**
     * 要运行服务的模板名称,根据传入的模板来调用不同算法模型返回结果。
     * 1、当模板为rec时，调用切片文本识别；
     * 2、当模板为ocr时，调用检测识别；
     * 3、当模板为iocr时，调用智能结构化；
     * 4、当模板为det时,调用智能检测；
     * 5、当模板为det_ocr时，调用定制结构化；
     * 6、当模板为det_ocr_iocr时，调用定制结构化+智能结构化；
     * 必填
     */
    @JsonProperty("ocr_template")
    private String ocrTemplate;

    private Options options;

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Options {

        /**
         * 1、该值为空时，输出图片上所有的key-value字段；
         * 2、若要进行普通key查询（用原文存在的key来做查询），多个原文key用"|"进行拼接即可；
         * 3、若要进行转义key查询（用原文不存在的key进行查询），这时需要传入转义key到原文key的映射字典，单个转义key的拼接方式示例如下： "转义key1:原文key1;原文key2;原文key3"，多个转义key的结果再用"|"进行拼接；
         * 4、上述原文key和转义key可以混合传入，用"|"进行拼接即可；
         * 5、该值为"all_items=true"时，使用训练集保存的原文keyList+转义key字字典来进行过滤，这种情况下只会输出训练集中见到的key;
         * 6、需注意拼接item_names时，不要使用这几个特殊符号（"|",":",";"），以免引起内部解析错误；
         * 7、另外需注意item_names中不要出现重复key或者出现一个原文key对应多个转义key的情况，可能会出现多召回或误召回的情况。
         * 选填
         */
        @JsonProperty("item_names")
        private String itemNames;

        /**
         * 1、该选项表示是否进行印章识别。
         * 2、印章识别结果会当作一个value字段返回，字段名称为印章内容。
         * 选填
         */
        @JsonProperty("enable_seal_recognize")
        private Boolean enableSealRecognize;

    }

}
