package com.csci.susdev.model;

public class TzhBsUserRole {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_UserRole.Id
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_UserRole.UserName
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    private String username;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_UserRole.RoleName
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    private String rolename;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_UserRole.Id
     *
     * @return the value of Tzh_Bs_UserRole.Id
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_UserRole.Id
     *
     * @param id the value for Tzh_Bs_UserRole.Id
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_UserRole.UserName
     *
     * @return the value of Tzh_Bs_UserRole.UserName
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    public String getUsername() {
        return username;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_UserRole.UserName
     *
     * @param username the value for Tzh_Bs_UserRole.UserName
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    public void setUsername(String username) {
        this.username = username == null ? null : username.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_UserRole.RoleName
     *
     * @return the value of Tzh_Bs_UserRole.RoleName
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    public String getRolename() {
        return rolename;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_UserRole.RoleName
     *
     * @param rolename the value for Tzh_Bs_UserRole.RoleName
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    public void setRolename(String rolename) {
        this.rolename = rolename == null ? null : rolename.trim();
    }
}