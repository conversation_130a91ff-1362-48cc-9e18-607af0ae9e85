package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FcMaterialFactor {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_material_factor.id
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_material_factor.chinese_name
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    private String chineseName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_material_factor.performance_specifications
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    private String performanceSpecifications;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_material_factor.unit
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    private String unit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_material_factor.carbon_factor
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    private BigDecimal carbonFactor;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_material_factor.carbon_factor_unit
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    private String carbonFactorUnit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_material_factor.description
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_material_factor.source_description
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    private String sourceDescription;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_material_factor.creation_time
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_material_factor.create_username
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_material_factor.create_user_id
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_material_factor.last_update_time
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_material_factor.last_update_username
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_material_factor.last_update_user_id
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_material_factor.last_update_version
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_material_factor.is_deleted
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    private Boolean isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_material_factor.id
     *
     * @return the value of t_fc_material_factor.id
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_material_factor.id
     *
     * @param id the value for t_fc_material_factor.id
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_material_factor.chinese_name
     *
     * @return the value of t_fc_material_factor.chinese_name
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public String getChineseName() {
        return chineseName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_material_factor.chinese_name
     *
     * @param chineseName the value for t_fc_material_factor.chinese_name
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public void setChineseName(String chineseName) {
        this.chineseName = chineseName == null ? null : chineseName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_material_factor.performance_specifications
     *
     * @return the value of t_fc_material_factor.performance_specifications
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public String getPerformanceSpecifications() {
        return performanceSpecifications;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_material_factor.performance_specifications
     *
     * @param performanceSpecifications the value for t_fc_material_factor.performance_specifications
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public void setPerformanceSpecifications(String performanceSpecifications) {
        this.performanceSpecifications = performanceSpecifications == null ? null : performanceSpecifications.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_material_factor.unit
     *
     * @return the value of t_fc_material_factor.unit
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public String getUnit() {
        return unit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_material_factor.unit
     *
     * @param unit the value for t_fc_material_factor.unit
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_material_factor.carbon_factor
     *
     * @return the value of t_fc_material_factor.carbon_factor
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public BigDecimal getCarbonFactor() {
        return carbonFactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_material_factor.carbon_factor
     *
     * @param carbonFactor the value for t_fc_material_factor.carbon_factor
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public void setCarbonFactor(BigDecimal carbonFactor) {
        this.carbonFactor = carbonFactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_material_factor.carbon_factor_unit
     *
     * @return the value of t_fc_material_factor.carbon_factor_unit
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public String getCarbonFactorUnit() {
        return carbonFactorUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_material_factor.carbon_factor_unit
     *
     * @param carbonFactorUnit the value for t_fc_material_factor.carbon_factor_unit
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public void setCarbonFactorUnit(String carbonFactorUnit) {
        this.carbonFactorUnit = carbonFactorUnit == null ? null : carbonFactorUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_material_factor.description
     *
     * @return the value of t_fc_material_factor.description
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_material_factor.description
     *
     * @param description the value for t_fc_material_factor.description
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_material_factor.source_description
     *
     * @return the value of t_fc_material_factor.source_description
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public String getSourceDescription() {
        return sourceDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_material_factor.source_description
     *
     * @param sourceDescription the value for t_fc_material_factor.source_description
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public void setSourceDescription(String sourceDescription) {
        this.sourceDescription = sourceDescription == null ? null : sourceDescription.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_material_factor.creation_time
     *
     * @return the value of t_fc_material_factor.creation_time
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_material_factor.creation_time
     *
     * @param creationTime the value for t_fc_material_factor.creation_time
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_material_factor.create_username
     *
     * @return the value of t_fc_material_factor.create_username
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_material_factor.create_username
     *
     * @param createUsername the value for t_fc_material_factor.create_username
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_material_factor.create_user_id
     *
     * @return the value of t_fc_material_factor.create_user_id
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_material_factor.create_user_id
     *
     * @param createUserId the value for t_fc_material_factor.create_user_id
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_material_factor.last_update_time
     *
     * @return the value of t_fc_material_factor.last_update_time
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_material_factor.last_update_time
     *
     * @param lastUpdateTime the value for t_fc_material_factor.last_update_time
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_material_factor.last_update_username
     *
     * @return the value of t_fc_material_factor.last_update_username
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_material_factor.last_update_username
     *
     * @param lastUpdateUsername the value for t_fc_material_factor.last_update_username
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_material_factor.last_update_user_id
     *
     * @return the value of t_fc_material_factor.last_update_user_id
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_material_factor.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_fc_material_factor.last_update_user_id
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_material_factor.last_update_version
     *
     * @return the value of t_fc_material_factor.last_update_version
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_material_factor.last_update_version
     *
     * @param lastUpdateVersion the value for t_fc_material_factor.last_update_version
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_material_factor.is_deleted
     *
     * @return the value of t_fc_material_factor.is_deleted
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_material_factor.is_deleted
     *
     * @param isDeleted the value for t_fc_material_factor.is_deleted
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
}