package com.csci.susdev.model;

import java.time.LocalDateTime;

public class ProtocolConfiguration {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_configuration.id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_configuration.sub_category_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    private String subCategoryId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_configuration.form_detail_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    private String formDetailId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_configuration.fc_factor_type
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    private String fcFactorType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_configuration.fc_factor_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    private String fcFactorId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_configuration.organization_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    private String organizationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_configuration.is_active
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    private Boolean isActive;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_configuration.creation_time
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_configuration.create_username
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_configuration.create_user_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_configuration.last_update_time
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_configuration.last_update_username
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_configuration.last_update_user_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_configuration.last_update_version
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_configuration.is_deleted
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    private Boolean isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_protocol_configuration.form_type
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    private String formType;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_configuration.id
     *
     * @return the value of t_protocol_configuration.id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_configuration.id
     *
     * @param id the value for t_protocol_configuration.id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_configuration.sub_category_id
     *
     * @return the value of t_protocol_configuration.sub_category_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public String getSubCategoryId() {
        return subCategoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_configuration.sub_category_id
     *
     * @param subCategoryId the value for t_protocol_configuration.sub_category_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public void setSubCategoryId(String subCategoryId) {
        this.subCategoryId = subCategoryId == null ? null : subCategoryId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_configuration.form_detail_id
     *
     * @return the value of t_protocol_configuration.form_detail_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public String getFormDetailId() {
        return formDetailId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_configuration.form_detail_id
     *
     * @param formDetailId the value for t_protocol_configuration.form_detail_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public void setFormDetailId(String formDetailId) {
        this.formDetailId = formDetailId == null ? null : formDetailId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_configuration.fc_factor_type
     *
     * @return the value of t_protocol_configuration.fc_factor_type
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public String getFcFactorType() {
        return fcFactorType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_configuration.fc_factor_type
     *
     * @param fcFactorType the value for t_protocol_configuration.fc_factor_type
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public void setFcFactorType(String fcFactorType) {
        this.fcFactorType = fcFactorType == null ? null : fcFactorType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_configuration.fc_factor_id
     *
     * @return the value of t_protocol_configuration.fc_factor_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public String getFcFactorId() {
        return fcFactorId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_configuration.fc_factor_id
     *
     * @param fcFactorId the value for t_protocol_configuration.fc_factor_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public void setFcFactorId(String fcFactorId) {
        this.fcFactorId = fcFactorId == null ? null : fcFactorId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_configuration.organization_id
     *
     * @return the value of t_protocol_configuration.organization_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public String getOrganizationId() {
        return organizationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_configuration.organization_id
     *
     * @param organizationId the value for t_protocol_configuration.organization_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId == null ? null : organizationId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_configuration.is_active
     *
     * @return the value of t_protocol_configuration.is_active
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public Boolean getIsActive() {
        return isActive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_configuration.is_active
     *
     * @param isActive the value for t_protocol_configuration.is_active
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_configuration.creation_time
     *
     * @return the value of t_protocol_configuration.creation_time
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_configuration.creation_time
     *
     * @param creationTime the value for t_protocol_configuration.creation_time
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_configuration.create_username
     *
     * @return the value of t_protocol_configuration.create_username
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_configuration.create_username
     *
     * @param createUsername the value for t_protocol_configuration.create_username
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_configuration.create_user_id
     *
     * @return the value of t_protocol_configuration.create_user_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_configuration.create_user_id
     *
     * @param createUserId the value for t_protocol_configuration.create_user_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_configuration.last_update_time
     *
     * @return the value of t_protocol_configuration.last_update_time
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_configuration.last_update_time
     *
     * @param lastUpdateTime the value for t_protocol_configuration.last_update_time
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_configuration.last_update_username
     *
     * @return the value of t_protocol_configuration.last_update_username
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_configuration.last_update_username
     *
     * @param lastUpdateUsername the value for t_protocol_configuration.last_update_username
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_configuration.last_update_user_id
     *
     * @return the value of t_protocol_configuration.last_update_user_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_configuration.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_protocol_configuration.last_update_user_id
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_configuration.last_update_version
     *
     * @return the value of t_protocol_configuration.last_update_version
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_configuration.last_update_version
     *
     * @param lastUpdateVersion the value for t_protocol_configuration.last_update_version
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_configuration.is_deleted
     *
     * @return the value of t_protocol_configuration.is_deleted
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_configuration.is_deleted
     *
     * @param isDeleted the value for t_protocol_configuration.is_deleted
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_protocol_configuration.form_type
     *
     * @return the value of t_protocol_configuration.form_type
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public String getFormType() {
        return formType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_protocol_configuration.form_type
     *
     * @param formType the value for t_protocol_configuration.form_type
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    public void setFormType(String formType) {
        this.formType = formType == null ? null : formType.trim();
    }
}