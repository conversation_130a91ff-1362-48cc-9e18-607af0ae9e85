package com.csci.susdev.model;

import java.time.LocalDateTime;

public class EmissionFactor {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.id
     *
     * @mbg.generated
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.batch_id
     *
     * @mbg.generated
     */
    private String batchId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.category
     *
     * @mbg.generated
     */
    private String category;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.emission_source
     *
     * @mbg.generated
     */
    private String emissionSource;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.type
     *
     * @mbg.generated
     */
    private String type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.unit
     *
     * @mbg.generated
     */
    private String unit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.emission_factor
     *
     * @mbg.generated
     */
    private String emissionFactor;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.emission_unit
     *
     * @mbg.generated
     */
    private String emissionUnit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.energy_consumption_factor
     *
     * @mbg.generated
     */
    private String energyConsumptionFactor;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.energy_consumption_unit
     *
     * @mbg.generated
     */
    private String energyConsumptionUnit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.creation_time
     *
     * @mbg.generated
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.create_username
     *
     * @mbg.generated
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.create_user_id
     *
     * @mbg.generated
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.last_update_time
     *
     * @mbg.generated
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.last_update_username
     *
     * @mbg.generated
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.last_update_user_id
     *
     * @mbg.generated
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_emission_factor.last_update_version
     *
     * @mbg.generated
     */
    private Integer lastUpdateVersion;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.id
     *
     * @return the value of t_cf_emission_factor.id
     *
     * @mbg.generated
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.id
     *
     * @param id the value for t_cf_emission_factor.id
     *
     * @mbg.generated
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.batch_id
     *
     * @return the value of t_cf_emission_factor.batch_id
     *
     * @mbg.generated
     */
    public String getBatchId() {
        return batchId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.batch_id
     *
     * @param batchId the value for t_cf_emission_factor.batch_id
     *
     * @mbg.generated
     */
    public void setBatchId(String batchId) {
        this.batchId = batchId == null ? null : batchId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.category
     *
     * @return the value of t_cf_emission_factor.category
     *
     * @mbg.generated
     */
    public String getCategory() {
        return category;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.category
     *
     * @param category the value for t_cf_emission_factor.category
     *
     * @mbg.generated
     */
    public void setCategory(String category) {
        this.category = category == null ? null : category.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.emission_source
     *
     * @return the value of t_cf_emission_factor.emission_source
     *
     * @mbg.generated
     */
    public String getEmissionSource() {
        return emissionSource;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.emission_source
     *
     * @param emissionSource the value for t_cf_emission_factor.emission_source
     *
     * @mbg.generated
     */
    public void setEmissionSource(String emissionSource) {
        this.emissionSource = emissionSource == null ? null : emissionSource.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.type
     *
     * @return the value of t_cf_emission_factor.type
     *
     * @mbg.generated
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.type
     *
     * @param type the value for t_cf_emission_factor.type
     *
     * @mbg.generated
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.unit
     *
     * @return the value of t_cf_emission_factor.unit
     *
     * @mbg.generated
     */
    public String getUnit() {
        return unit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.unit
     *
     * @param unit the value for t_cf_emission_factor.unit
     *
     * @mbg.generated
     */
    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.emission_factor
     *
     * @return the value of t_cf_emission_factor.emission_factor
     *
     * @mbg.generated
     */
    public String getEmissionFactor() {
        return emissionFactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.emission_factor
     *
     * @param emissionFactor the value for t_cf_emission_factor.emission_factor
     *
     * @mbg.generated
     */
    public void setEmissionFactor(String emissionFactor) {
        this.emissionFactor = emissionFactor == null ? null : emissionFactor.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.emission_unit
     *
     * @return the value of t_cf_emission_factor.emission_unit
     *
     * @mbg.generated
     */
    public String getEmissionUnit() {
        return emissionUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.emission_unit
     *
     * @param emissionUnit the value for t_cf_emission_factor.emission_unit
     *
     * @mbg.generated
     */
    public void setEmissionUnit(String emissionUnit) {
        this.emissionUnit = emissionUnit == null ? null : emissionUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.energy_consumption_factor
     *
     * @return the value of t_cf_emission_factor.energy_consumption_factor
     *
     * @mbg.generated
     */
    public String getEnergyConsumptionFactor() {
        return energyConsumptionFactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.energy_consumption_factor
     *
     * @param energyConsumptionFactor the value for t_cf_emission_factor.energy_consumption_factor
     *
     * @mbg.generated
     */
    public void setEnergyConsumptionFactor(String energyConsumptionFactor) {
        this.energyConsumptionFactor = energyConsumptionFactor == null ? null : energyConsumptionFactor.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.energy_consumption_unit
     *
     * @return the value of t_cf_emission_factor.energy_consumption_unit
     *
     * @mbg.generated
     */
    public String getEnergyConsumptionUnit() {
        return energyConsumptionUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.energy_consumption_unit
     *
     * @param energyConsumptionUnit the value for t_cf_emission_factor.energy_consumption_unit
     *
     * @mbg.generated
     */
    public void setEnergyConsumptionUnit(String energyConsumptionUnit) {
        this.energyConsumptionUnit = energyConsumptionUnit == null ? null : energyConsumptionUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.creation_time
     *
     * @return the value of t_cf_emission_factor.creation_time
     *
     * @mbg.generated
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.creation_time
     *
     * @param creationTime the value for t_cf_emission_factor.creation_time
     *
     * @mbg.generated
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.create_username
     *
     * @return the value of t_cf_emission_factor.create_username
     *
     * @mbg.generated
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.create_username
     *
     * @param createUsername the value for t_cf_emission_factor.create_username
     *
     * @mbg.generated
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.create_user_id
     *
     * @return the value of t_cf_emission_factor.create_user_id
     *
     * @mbg.generated
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.create_user_id
     *
     * @param createUserId the value for t_cf_emission_factor.create_user_id
     *
     * @mbg.generated
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.last_update_time
     *
     * @return the value of t_cf_emission_factor.last_update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.last_update_time
     *
     * @param lastUpdateTime the value for t_cf_emission_factor.last_update_time
     *
     * @mbg.generated
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.last_update_username
     *
     * @return the value of t_cf_emission_factor.last_update_username
     *
     * @mbg.generated
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.last_update_username
     *
     * @param lastUpdateUsername the value for t_cf_emission_factor.last_update_username
     *
     * @mbg.generated
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.last_update_user_id
     *
     * @return the value of t_cf_emission_factor.last_update_user_id
     *
     * @mbg.generated
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_cf_emission_factor.last_update_user_id
     *
     * @mbg.generated
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_emission_factor.last_update_version
     *
     * @return the value of t_cf_emission_factor.last_update_version
     *
     * @mbg.generated
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_emission_factor.last_update_version
     *
     * @param lastUpdateVersion the value for t_cf_emission_factor.last_update_version
     *
     * @mbg.generated
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }
}