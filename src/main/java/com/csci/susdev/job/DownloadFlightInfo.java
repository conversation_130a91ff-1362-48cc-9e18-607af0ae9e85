package com.csci.susdev.job;

import com.csci.susdev.service.FlightInfoService;
import com.csci.susdev.service.RawEpdWasteTransportationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;

@ConditionalOnProperty(value = "app.scheduling.enable", havingValue = "true", matchIfMissing = true)
@EnableScheduling
@Configuration
public class DownloadFlightInfo {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(DownloadFlightInfo.class);

    @Resource
    private FlightInfoService flightInfoService;

    @Value("${rpa.robot.active}")
    private boolean isActive;

    public void syncWasteTransportationData() {
        if(isActive) {
            logger.info("飛行里數資料抓取工作開始", System.currentTimeMillis());
            try {
                flightInfoService.downloadDataNew();
            } catch (Exception e) {
                logger.error("飛行里數資料抓取工作 error", e);
            }
        }
    }
}
