package com.csci.susdev.configuration.parallel;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * 通用线程池管理
 * 短事务处理，超时时间设置为2分钟（120秒）
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 10/31/2019
 */
public class ParallelExecutor {

    /**
     * 通用线程池
     */
    private static final ThreadPool EXECUTOR = new ThreadPool(120, TimeUnit.SECONDS, "ParallelExecutor");

    /**
     * 提交一个异步任务
     *
     * @param callable
     * @return
     */
    public static <T> Future<T> submit(Callable<T> callable) {
        return EXECUTOR.submit(callable);
    }

    /**
     * 提交一个异步任务
     *
     * @param runnable
     * @return
     */
    public static Future<?> submit(Runnable runnable) {
        return EXECUTOR.submit(runnable);
    }

    /**
     * 添加多个并行任务并等待执行完毕
     *
     * @param callables
     */
    public static void submitAndWait(Callable<?>... callables) {
        EXECUTOR.submitAndWait(callables);
    }

    /**
     * 添加多个并行任务并等待执行完毕
     *
     * @param tasks
     */
    public static void submitAndWait(Runnable... tasks) {
        EXECUTOR.submitAndWait(tasks);
    }

}
