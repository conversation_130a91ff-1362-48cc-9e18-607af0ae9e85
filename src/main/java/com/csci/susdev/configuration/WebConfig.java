package com.csci.susdev.configuration;

import com.csci.susdev.interceptor.AuthenticateInterceptor;
import com.csci.susdev.interceptor.AuthorityInterceptor;
import com.csci.susdev.util.DateUtils;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.format.FormatterRegistry;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 在此处添加自定义的配置
 *
 * <AUTHOR>
 * @date 9/20/2019
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Resource
    private AuthorityInterceptor authorityInterceptor;

    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        AntPathMatcher pathMatcher = new AntPathMatcher();
        // 设置忽略url大小写
        pathMatcher.setCaseSensitive(false);
        configurer.setPathMatcher(pathMatcher);
    }
        @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.
                addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/springfox-swagger-ui/")
                .resourceChain(false);
        registry.addResourceHandler("/swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/doc.html").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
    }
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //todo 这里加上bi就可以管控大屏的接口 "/bi/**"
        registry.addInterceptor(new AuthenticateInterceptor())
                .addPathPatterns("/api/**", "/user/**","/bi/**")
                .excludePathPatterns(getAuthExcludePathPatterns());
        registry.addInterceptor(authorityInterceptor)
                .addPathPatterns("/api/**", "/user/**")
                .excludePathPatterns(getAuthExcludePathPatterns());
    }

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverter(new StringToLocalDateTime());
    }

    /**
     * Json序列化和反序列化转换器，用于转换Post请求体中的json以及将我们的对象序列化为返回响应的json
     */
    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE);
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        // 配置接收json参数时忽略属性大小写
        objectMapper.enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES);

        //LocalDateTime系列序列化和反序列化模块，继承自jsr310，我们在这里修改了日期格式
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_TIME_FORMAT)) {
            @Override
            public void serialize(LocalDateTime value, JsonGenerator g, SerializerProvider provider) throws IOException {
                super.serialize(value, g, provider);
            }
        });
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_FORMAT)));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_TIME_FORMAT)));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_TIME_FORMAT)) {
            @Override
            public LocalDateTime deserialize(JsonParser parser, DeserializationContext context) throws IOException {
                String date = parser.getText();
                if (StringUtils.isBlank(date)) {
                    return null;
                }
                LocalDateTime dateTime = DateUtils.toLocalDateTime(date);
                if (dateTime == null) {
                    dateTime = super.deserialize(parser, context);
                }
                return dateTime;
            }

        });
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_FORMAT)));
        javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_TIME_FORMAT)));

        //Date序列化和反序列化
        javaTimeModule.addSerializer(Date.class, new JsonSerializer<Date>() {
            @Override
            public void serialize(Date date, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
                SimpleDateFormat formatter = new SimpleDateFormat(DateUtils.DEFAULT_DATE_TIME_FORMAT);
                String formattedDate = formatter.format(date);
                jsonGenerator.writeString(formattedDate);
            }
        });
        javaTimeModule.addDeserializer(Date.class, new JsonDeserializer<Date>() {
            @Override
            public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
                SimpleDateFormat format = new SimpleDateFormat(DateUtils.DEFAULT_DATE_TIME_FORMAT);
                String date = jsonParser.getText();
                try {
                    return format.parse(date);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
        });

        objectMapper.registerModule(javaTimeModule);
        return objectMapper;
    }

    /**
     * 不需要进行授权认证的接口
     *
     * @return {@link List}
     */
    private List<String> getAuthExcludePathPatterns() {
        List<String> excludePathPatterns = new ArrayList<>();
        excludePathPatterns.add("/**/login");
        excludePathPatterns.add("/**/adminLogin");
        excludePathPatterns.add("/**/register");
        excludePathPatterns.add("/api/sample");
        excludePathPatterns.add("/api/test");
        excludePathPatterns.add("/test/**");
        excludePathPatterns.add("/**/attachment/**");
        excludePathPatterns.add("/");
        excludePathPatterns.add("**/health-check");
        excludePathPatterns.add("/download/**");
        excludePathPatterns.add("/**/login");
        excludePathPatterns.add("/api/tzh/external/**");
        excludePathPatterns.add("/api/external/v1/login");
        excludePathPatterns.add("/bi/auth/captcha");
        excludePathPatterns.add("/bi/tzh-common/proxy");
        excludePathPatterns.add("/bi/auth/list-current-org-permission");
        excludePathPatterns.add("/bi/tzh-bs-ai-carbon-emission-result/row/query-carbon-emission-result");

        // swagger related-----------
        excludePathPatterns.add("/v3/api-docs/**");
        excludePathPatterns.add("/swagger-ui/**");
        excludePathPatterns.add("/swagger-ui.html");
        excludePathPatterns.add("/doc.html");
        excludePathPatterns.add("/webjars/**");
        return excludePathPatterns;
    }

    /**
     * get方法中作为requestParam或者pathVariable的参数会使用这里的逻辑进行转换
     */
    private static class StringToLocalDateTime implements Converter<String, LocalDateTime> {
        @Override
        public LocalDateTime convert(String source) {
            try {
                return LocalDateTime.from(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_TIME_FORMAT).parse(source));
            } catch (Exception e) {
                return DateUtils.beginOfDate(source);
            }
        }
    }
}
