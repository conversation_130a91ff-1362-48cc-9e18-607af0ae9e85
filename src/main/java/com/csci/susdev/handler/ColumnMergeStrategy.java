package com.csci.susdev.handler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.List;

public class ColumnMergeStrategy implements CellWriteHandler {
    private final int[] mergeColumns; // 需要合并的列索引（例如1-4）

    public ColumnMergeStrategy(int[] mergeColumns) {
        this.mergeColumns = mergeColumns;
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                 List<WriteCellData<?>> cell<PERSON><PERSON><PERSON><PERSON>, <PERSON> cell, Head head, Integer relativeRowIndex, Bo<PERSON><PERSON> isHead) {
        int curRowIndex = cell.getRowIndex();
        int curColIndex = cell.getColumnIndex();

        // 只处理数据行（跳过表头）
        if (isHead != null && !isHead) {
            for (int col : mergeColumns) {
                if (curColIndex == col) {
                    mergeWithPreviousRow(writeSheetHolder, cell, curRowIndex, col);
                    break;
                }
            }
        }
    }

    private void mergeWithPreviousRow(WriteSheetHolder writeSheetHolder, Cell cell, int curRowIndex, int curColIndex) {
        Object curCellValue = cell.getCellType() == CellType.STRING ? cell.getStringCellValue() : "";
        Row preRow = cell.getSheet().getRow(curRowIndex - 1);
        if (preRow == null) return;

        Cell preCell = preRow.getCell(curColIndex);
        if (preCell == null) return;

        Object preCellValue = preCell.getCellType() == CellType.STRING ? preCell.getStringCellValue() : "";

        if (curCellValue.equals(preCellValue)) {
            CellRangeAddress region = new CellRangeAddress(curRowIndex - 1, curRowIndex, curColIndex, curColIndex);
            writeSheetHolder.getSheet().addMergedRegion(region);
        }
    }
}
