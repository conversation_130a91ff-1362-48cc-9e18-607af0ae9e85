package com.csci.susdev.handler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.csci.common.util.CustomGsonBuilder;
import com.csci.susdev.service.FfCmMobileService;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.util.StringUtil;
import com.csci.susdev.vo.ExcelCeIdentificationRunawayEmissionVO;
import com.csci.susdev.vo.FfCmMobileDetailVO;
import com.csci.susdev.vo.MergeDataVO;
import com.google.gson.Gson;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class CeIdentificationRunawayEmissionHandler extends AbstractMergeStrategy {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(CeIdentificationRunawayEmissionHandler.class);
    private static final List<String> thirdColMergeValueList = new ArrayList<>();

    static {
        thirdColMergeValueList.add("地盤/工地、工廠、辦公室及發電廠能源消耗");
    }

    private static final Gson gson = CustomGsonBuilder.createGson();
    private List<MergeDataVO> mergeDataList = new ArrayList<>();

    public CeIdentificationRunawayEmissionHandler(List<ExcelCeIdentificationRunawayEmissionVO> excelData) {

        // 合并第一列
        mergeDataList.addAll(calcFirstCol(excelData));
        // 合并第二列
        mergeDataList.addAll(calcSecondCol(excelData));
        // 合并第三列
        mergeDataList.addAll(calcThreeCol(excelData));
        // 合并第四列
        mergeDataList.addAll(calcFourCol(excelData));
        // 合并第五列
        mergeDataList.addAll(calcFiveCol(excelData));


        // 过滤掉同行的数据
        mergeDataList = mergeDataList.stream().filter(x -> !(Objects.equals(x.getFromRow(), x.getToRow()) && Objects.equals(x.getFromCol(), x.getToCol()))).collect(Collectors.toList());
    }

    public static List<MergeDataVO> calcFirstCol(List<ExcelCeIdentificationRunawayEmissionVO> emissionVOS) {
        List<MergeDataVO> lstMergeData = new ArrayList<>();
        MergeDataVO mergeDataVO = null;
        int currentRowIndex = 0;
        for (int i = 0; i < emissionVOS.size(); i++) {
            ExcelCeIdentificationRunawayEmissionVO emissionVO = emissionVOS.get(i);

            if (i == 0) {
                // 第一行
                currentRowIndex = 2;
                mergeDataVO = new MergeDataVO().setFromCol(0).setToCol(0).setFromRow(currentRowIndex).setToRow(currentRowIndex).setValue(emissionVO.getModule());
                lstMergeData.add(mergeDataVO);
                continue;
            }
            currentRowIndex++;
            if (StringUtils.isNotBlank(emissionVO.getModule()) && !StringUtils.equals(mergeDataVO.getValue(), emissionVO.getModule())) {
                // 不同类别
                mergeDataVO = new MergeDataVO().setFromCol(0).setToCol(0).setFromRow(currentRowIndex).setToRow(currentRowIndex).setValue(emissionVO.getModule());
                lstMergeData.add(mergeDataVO);
                continue;
            }

            mergeDataVO.setToRow(mergeDataVO.getToRow() + 1);
            // 第一列合并计算----------end
        }

        return lstMergeData.stream().filter(x -> !Objects.equals(x.getFromRow(), x.getToRow())).collect(Collectors.toList());
    }

    /**
     * 暂时不适用
     *
     * @param emissionVOS
     */
    public static List<MergeDataVO> calcSecondCol(List<ExcelCeIdentificationRunawayEmissionVO> emissionVOS) {
        List<MergeDataVO> lstMergeData = new ArrayList<>();
        MergeDataVO mergeDataVO = null;
        int currentRowIndex = 0;
        for (int i = 0; i < emissionVOS.size(); i++) {
            ExcelCeIdentificationRunawayEmissionVO emissionVO = emissionVOS.get(i);

            if (i == 0) {
                // 第一行
                currentRowIndex = 2;
                mergeDataVO = new MergeDataVO().setFromCol(1).setToCol(1).setFromRow(currentRowIndex).setToRow(currentRowIndex).setValue(emissionVO.getConfirmationItemOne());
                lstMergeData.add(mergeDataVO);
                continue;
            }
            currentRowIndex++;
            if (StringUtils.isNotBlank(emissionVO.getConfirmationItemOne()) && !StringUtils.equals(mergeDataVO.getValue(), emissionVO.getConfirmationItemOne())) {
                // 不同类别
                mergeDataVO = new MergeDataVO().setFromCol(1).setToCol(1).setFromRow(currentRowIndex).setToRow(currentRowIndex).setValue(emissionVO.getConfirmationItemOne());
                lstMergeData.add(mergeDataVO);
                continue;
            }

            mergeDataVO.setToRow(mergeDataVO.getToRow() + 1);
            // 第一列合并计算----------end
        }

        return lstMergeData.stream().filter(x -> !Objects.equals(x.getFromRow(), x.getToRow())).collect(Collectors.toList());
    }

    public static List<MergeDataVO> calcThreeCol(List<ExcelCeIdentificationRunawayEmissionVO> emissionVOS) {
        List<MergeDataVO> lstMergeData = new ArrayList<>();
        MergeDataVO mergeDataVO = null;
        int currentRowIndex = 0;
        for (int i = 0; i < emissionVOS.size(); i++) {
            ExcelCeIdentificationRunawayEmissionVO emissionVO = emissionVOS.get(i);

            if (i == 0) {
                // 第一行
                currentRowIndex = 2;
                mergeDataVO = new MergeDataVO().setFromCol(2).setToCol(2).setFromRow(currentRowIndex).setToRow(currentRowIndex).setValue(emissionVO.getConfirmationItemTwo());
                lstMergeData.add(mergeDataVO);
                continue;
            }
            currentRowIndex++;
            if (StringUtils.isNotBlank(emissionVO.getConfirmationItemTwo()) && !StringUtils.equals(mergeDataVO.getValue(), emissionVO.getConfirmationItemTwo())) {
                // 不同类别
                mergeDataVO = new MergeDataVO().setFromCol(2).setToCol(2).setFromRow(currentRowIndex).setToRow(currentRowIndex).setValue(emissionVO.getConfirmationItemTwo());
                lstMergeData.add(mergeDataVO);
                continue;
            }

            mergeDataVO.setToRow(mergeDataVO.getToRow() + 1);
            // 第一列合并计算----------end
        }

        return lstMergeData.stream().filter(x -> !Objects.equals(x.getFromRow(), x.getToRow())).collect(Collectors.toList());
    }

    public static List<MergeDataVO> calcFourCol(List<ExcelCeIdentificationRunawayEmissionVO> emissionVOS) {
        List<MergeDataVO> lstMergeData = new ArrayList<>();
        MergeDataVO mergeDataVO = null;
        int currentRowIndex = 0;
        for (int i = 0; i < emissionVOS.size(); i++) {
            ExcelCeIdentificationRunawayEmissionVO emissionVO = emissionVOS.get(i);

            if (i == 0) {
                // 第一行
                currentRowIndex = 2;
                mergeDataVO = new MergeDataVO().setFromCol(3).setToCol(3).setFromRow(currentRowIndex).setToRow(currentRowIndex).setValue(emissionVO.getConfirmationItemThree());
                lstMergeData.add(mergeDataVO);
                continue;
            }
            currentRowIndex++;
            if (StringUtils.isNotBlank(emissionVO.getConfirmationItemThree()) && !StringUtils.equals(mergeDataVO.getValue(), emissionVO.getConfirmationItemThree())) {
                // 不同类别
                mergeDataVO = new MergeDataVO().setFromCol(3).setToCol(3).setFromRow(currentRowIndex).setToRow(currentRowIndex).setValue(emissionVO.getConfirmationItemThree());
                lstMergeData.add(mergeDataVO);
                continue;
            }

            mergeDataVO.setToRow(mergeDataVO.getToRow() + 1);
            // 第一列合并计算----------end
        }

        return lstMergeData.stream().filter(x -> !Objects.equals(x.getFromRow(), x.getToRow())).collect(Collectors.toList());
    }

    public static List<MergeDataVO> calcFiveCol(List<ExcelCeIdentificationRunawayEmissionVO> emissionVOS) {
        List<MergeDataVO> lstMergeData = new ArrayList<>();
        MergeDataVO mergeDataVO = null;
        int currentRowIndex = 0;
        for (int i = 0; i < emissionVOS.size(); i++) {
            ExcelCeIdentificationRunawayEmissionVO emissionVO = emissionVOS.get(i);

            if (i == 0) {
                // 第一行
                currentRowIndex = 2;
                mergeDataVO = new MergeDataVO().setFromCol(4).setToCol(4).setFromRow(currentRowIndex).setToRow(currentRowIndex).setValue(emissionVO.getInstructions());
                lstMergeData.add(mergeDataVO);
                continue;
            }
            currentRowIndex++;
            if (StringUtils.isNotBlank(emissionVO.getInstructions()) && !StringUtils.equals(mergeDataVO.getValue(), emissionVO.getInstructions())) {
                // 不同类别
                mergeDataVO = new MergeDataVO().setFromCol(4).setToCol(4).setFromRow(currentRowIndex).setToRow(currentRowIndex).setValue(emissionVO.getInstructions());
                lstMergeData.add(mergeDataVO);
                continue;
            }

            mergeDataVO.setToRow(mergeDataVO.getToRow() + 1);
            // 第一列合并计算----------end
        }

        return lstMergeData.stream().filter(x -> !Objects.equals(x.getFromRow(), x.getToRow())).collect(Collectors.toList());
    }





    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        if (CollectionUtils.isNotEmpty(mergeDataList)) {
            for (MergeDataVO mergeDataVO : mergeDataList) {
                if (cell.getRowIndex() == mergeDataVO.getFromRow() && cell.getColumnIndex() == mergeDataVO.getFromCol()) {
                    sheet.addMergedRegion(new CellRangeAddress(mergeDataVO.getFromRow(), mergeDataVO.getToRow(), mergeDataVO.getFromCol(), mergeDataVO.getToCol()));
                    break;
                }
            }
        }
    }
}
