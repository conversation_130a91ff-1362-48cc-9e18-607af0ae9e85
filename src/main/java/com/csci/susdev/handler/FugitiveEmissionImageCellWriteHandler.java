package com.csci.susdev.handler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.csci.susdev.vo.ExcelCeIdentificationFugitiveEmissionVO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/1 19:06
 * @description TODO
 */
public class FugitiveEmissionImageCellWriteHandler implements CellWriteHandler {

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                 List<WriteCellData<?>> <PERSON><PERSON><PERSON><PERSON><PERSON>, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        if (isHead || cell.getRowIndex() == 0) return; // 跳过表头和空行

        // 获取当前行对应的数据对象
        int rowIndex = cell.getRowIndex();
        Object dataObj = writeSheetHolder.getSheet().getRow(rowIndex).getCell(0).getRow();

        if (dataObj instanceof ExcelCeIdentificationFugitiveEmissionVO) {
            ExcelCeIdentificationFugitiveEmissionVO vo = (ExcelCeIdentificationFugitiveEmissionVO) dataObj;
            byte[] imageBytes = (vo.getImgData() instanceof byte[]) ? (byte[]) vo.getImgData() : null;

            if (imageBytes != null && imageBytes.length > 0) {
                insertImage(writeSheetHolder, cell, imageBytes);
            }
        }
    }

    private void insertImage(WriteSheetHolder writeSheetHolder, Cell cell, byte[] imageBytes) {
        try {
            Sheet sheet = writeSheetHolder.getSheet();
            Drawing<?> drawing = sheet.createDrawingPatriarch();

            // 设置图片位置（覆盖当前单元格）
            ClientAnchor anchor = new XSSFClientAnchor(
                    0, 0, 0, 0,
                    cell.getColumnIndex(), cell.getRowIndex(),
                    cell.getColumnIndex() + 1, cell.getRowIndex() + 1
            );

            // 插入图片
            int pictureIdx = writeSheetHolder.getParentWriteWorkbookHolder().getWorkbook()
                    .addPicture(imageBytes, Workbook.PICTURE_TYPE_PNG);
            drawing.createPicture(anchor, pictureIdx);

            // 调整行高和列宽以适应图片
            cell.getRow().setHeightInPoints(100); // 行高
            sheet.setColumnWidth(cell.getColumnIndex(), 30 * 256); // 列宽
        } catch (Exception e) {
            throw new RuntimeException("插入图片失败: " + e.getMessage(), e);
        }
    }
}
