package com.csci.susdev.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * Generated Comments
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 10/17/2019
 */
@Target(ElementType.FIELD)
@Retention(RUNTIME)
public @interface Size {
    /**
     * @return size the element must be higher or equal to
     */
    int min() default 0;

    /**
     * @return size the element must be lower or equal to
     */
    int max() default Integer.MAX_VALUE;
}
