package com.csci.susdev.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Generated Comments
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 12/21/2019
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Authorize {

    String role() default "";

    String permission() default "";

}
