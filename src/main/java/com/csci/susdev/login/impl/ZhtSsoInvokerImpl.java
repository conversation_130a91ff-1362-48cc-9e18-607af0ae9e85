package com.csci.susdev.login.impl;

import com.csci.common.exception.ServiceException;
import com.csci.common.util.CustomGsonBuilder;
import com.csci.common.util.HttpSimulator;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.login.IZhtSsoInvoker;
import com.csci.susdev.login.vo.ExchangeTokenVO;
import com.csci.susdev.login.vo.GetAccessTokenVO;
import com.csci.susdev.login.vo.RequestTokenVO;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 实现中海通 sso 单点登录接口请求
 *
 * <AUTHOR>
 */
@Component
@LogMethod
public class ZhtSsoInvokerImpl implements IZhtSsoInvoker {

    private final Gson gson = CustomGsonBuilder.createGson();

    @Value("${feishu.sso.app.code}")
    private String appCode;

    @Value("${feishu.sso.app.secret}")
    private String appSecret;

    @Value("${feishu.sso.app.url}")
    private String ssoUrl;

    @Value("${feishu.sso.app.validate}")
    private String validateTokenURL;

    @Override
    public GetAccessTokenVO exchangeAccessToken(ExchangeTokenVO exchangeTokenVO) {
        if (StringUtils.isBlank(exchangeTokenVO.getCode())) {
            throw new ServiceException("交换 token 时 code 不能为空");
        }
        RequestTokenVO params = new RequestTokenVO();
        params.setCode(exchangeTokenVO.getCode());
        params.setState(exchangeTokenVO.getState());
        params.setAppCode(appCode);
        params.setAppSecret(appSecret);

        Map<String, String> headers = new HashMap<>(1);
        headers.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        String response = HttpSimulator.sendPostRequest(ssoUrl, gson.toJson(params), headers);
        return gson.fromJson(response, GetAccessTokenVO.class);
    }
}
