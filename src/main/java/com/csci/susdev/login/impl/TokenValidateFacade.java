package com.csci.susdev.login.impl;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.login.Oauth2Invoker;
import com.csci.susdev.model.UserSession;
import com.csci.susdev.service.UserSessionService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@LogMethod
public class TokenValidateFacade {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(TokenValidateFacade.class);

    @Autowired
    private UserSessionService userSessionService;

    @Autowired
    private Oauth2Invoker oauth2Invoker;

    public boolean isTokenValid(String token) {
        if (StringUtils.isBlank(token)) {
            return false;
        }
        try {
            UserSession userSession = userSessionService.getUserSessionByTokenOrAccessToken(token);
            if (Objects.isNull(userSession)) {
                return false;
            }

            oauth2Invoker.validateAccessToken(userSession.getAccessToken());
            return true;
        } catch (Exception e) {
            logger.error("验证 token 是否合法时出错", e);
            return false;
        }
    }
}
