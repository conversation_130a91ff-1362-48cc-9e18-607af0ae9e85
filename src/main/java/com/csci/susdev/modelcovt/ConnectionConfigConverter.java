package com.csci.susdev.modelcovt;

import com.csci.susdev.model.ConnectionConfig;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.ConnectionConfigVO;
import com.csci.susdev.vo.UserVO;
import org.springframework.beans.BeanUtils;

public class ConnectionConfigConverter {

    public static ConnectionConfigVO convertToVO(ConnectionConfig connectionConfig) {
        ConnectionConfigVO connectionConfigVO = new ConnectionConfigVO();
        BeanUtils.copyProperties(connectionConfig, connectionConfigVO);
        UserService userService = SpringContextUtil.getBean(UserService.class);
        UserVO userVO = userService.getUserById(connectionConfig.getUserId());
        connectionConfigVO.setUsername(userVO.getUsername());
        return connectionConfigVO;
    }

    public static ConnectionConfig convertToModel(ConnectionConfigVO connectionConfigVO) {
        ConnectionConfig connectionConfig = new ConnectionConfig();
        BeanUtils.copyProperties(connectionConfigVO, connectionConfig);
        return connectionConfig;
    }

    public static ConnectionConfig convertToModelWithBase(
            ConnectionConfigVO connectionConfigVO,
            ConnectionConfig connectionConfig) {
        BeanUtils.copyProperties(connectionConfigVO, connectionConfig);
        return connectionConfig;
    }

}
