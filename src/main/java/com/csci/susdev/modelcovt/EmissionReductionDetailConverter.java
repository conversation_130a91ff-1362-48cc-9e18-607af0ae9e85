package com.csci.susdev.modelcovt;

import com.csci.susdev.model.EmissionReductionDetail;
import com.csci.susdev.vo.EmissionReductionDetailVO;
import org.springframework.beans.BeanUtils;
import org.springframework.core.convert.converter.Converter;

public class EmissionReductionDetailConverter implements Converter<EmissionReductionDetail, EmissionReductionDetailVO> {
    @Override
    public EmissionReductionDetailVO convert(EmissionReductionDetail source) {
        EmissionReductionDetailVO vo = new EmissionReductionDetailVO();
        BeanUtils.copyProperties(source, vo);
        return vo;
    }

    public EmissionReductionDetail revert(EmissionReductionDetailVO vo) {
        EmissionReductionDetail source = new EmissionReductionDetail();
        BeanUtils.copyProperties(vo, source);
        return source;
    }
}
