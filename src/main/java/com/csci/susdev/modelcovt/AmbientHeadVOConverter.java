package com.csci.susdev.modelcovt;

import com.csci.susdev.model.AmbientHead;
import com.csci.susdev.vo.AmbientHeadVO;
import com.csci.susdev.vo.AmbientHeadWithMeterTransVO;
import org.springframework.beans.BeanUtils;

public class AmbientHeadVOConverter {

    public static AmbientHeadVO convert(AmbientHead ambientHead) {
        AmbientHeadVO ambientHeadVO = new AmbientHeadVO();
        BeanUtils.copyProperties(ambientHead, ambientHeadVO);
        return ambientHeadVO;
    }

    public static AmbientHead convert(AmbientHeadVO ambientHeadVO) {
        AmbientHead ambientHead = new AmbientHead();
        BeanUtils.copyProperties(ambientHeadVO, ambientHead);
        return ambientHead;
    }

    public static AmbientHead convert(AmbientHeadWithMeterTransVO ambientHeadVO) {
        AmbientHead ambientHead = new AmbientHead();
        BeanUtils.copyProperties(ambientHeadVO, ambientHead);
        return ambientHead;
    }
}
