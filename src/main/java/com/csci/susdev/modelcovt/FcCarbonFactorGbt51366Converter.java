package com.csci.susdev.modelcovt;

import com.csci.susdev.model.FcCarbonFactorGbt51366;
import com.csci.susdev.vo.FcCarbonFactorGbt51366VO;
import org.springframework.beans.BeanUtils;

public class FcCarbonFactorGbt51366Converter {

    public static FcCarbonFactorGbt51366VO convertToVO(FcCarbonFactorGbt51366 fcCarbonFactorGbt51366) {
        FcCarbonFactorGbt51366VO fcCarbonFactorGbt51366VO = new FcCarbonFactorGbt51366VO();
        BeanUtils.copyProperties(fcCarbonFactorGbt51366, fcCarbonFactorGbt51366VO);
        return fcCarbonFactorGbt51366VO;
    }

    public static FcCarbonFactorGbt51366 convertToModel(FcCarbonFactorGbt51366VO fcCarbonFactorGbt51366VO) {
        FcCarbonFactorGbt51366 fcCarbonFactorGbt51366 = new FcCarbonFactorGbt51366();
        BeanUtils.copyProperties(fcCarbonFactorGbt51366VO, fcCarbonFactorGbt51366);
        return fcCarbonFactorGbt51366;
    }

    public static FcCarbonFactorGbt51366 convertToModelWithBase(
            FcCarbonFactorGbt51366VO fcCarbonFactorGbt51366VO,
            FcCarbonFactorGbt51366 fcCarbonFactorGbt51366) {
        BeanUtils.copyProperties(fcCarbonFactorGbt51366VO, fcCarbonFactorGbt51366);
        return fcCarbonFactorGbt51366;
    }

}
