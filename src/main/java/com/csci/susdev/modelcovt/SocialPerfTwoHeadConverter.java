package com.csci.susdev.modelcovt;

import com.csci.susdev.model.SocialPerfTwoHead;
import com.csci.susdev.vo.SocialPerfTwoHeadVO;
import org.springframework.beans.BeanUtils;

public class SocialPerfTwoHeadConverter {

    public static SocialPerfTwoHeadVO convert(SocialPerfTwoHead source) {
        SocialPerfTwoHeadVO target = new SocialPerfTwoHeadVO();
        BeanUtils.copyProperties(source, target);
        return target;
    }

    public static SocialPerfTwoHead convert(SocialPerfTwoHeadVO source) {
        SocialPerfTwoHead target = new SocialPerfTwoHead();
        BeanUtils.copyProperties(source, target);
        return target;
    }

}
