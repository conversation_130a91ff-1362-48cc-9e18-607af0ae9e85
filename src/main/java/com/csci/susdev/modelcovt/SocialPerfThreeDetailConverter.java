package com.csci.susdev.modelcovt;

import com.csci.susdev.model.SocialPerfThreeDetail;
import com.csci.susdev.vo.SocialPerfThreeDetailVO;
import org.springframework.beans.BeanUtils;

public class SocialPerfThreeDetailConverter {

    public static SocialPerfThreeDetailVO convert(SocialPerfThreeDetail source) {
        SocialPerfThreeDetailVO target = new SocialPerfThreeDetailVO();
        BeanUtils.copyProperties(source, target);
        return target;
    }

    public static SocialPerfThreeDetail convert(SocialPerfThreeDetailVO source) {
        SocialPerfThreeDetail target = new SocialPerfThreeDetail();
        BeanUtils.copyProperties(source, target);
        return target;
    }
}
