package com.csci.susdev.modelcovt;

import com.csci.susdev.model.FcCarbonFactorEsgDataset;
import com.csci.susdev.vo.FcCarbonFactorEsgDatasetVO;
import org.springframework.beans.BeanUtils;

public class FcCarbonFactorEsgDatasetConverter {

    public static FcCarbonFactorEsgDatasetVO convertToVO(FcCarbonFactorEsgDataset fcCarbonFactorEsgDataset) {
        FcCarbonFactorEsgDatasetVO fcCarbonFactorEsgDatasetVO = new FcCarbonFactorEsgDatasetVO();
        BeanUtils.copyProperties(fcCarbonFactorEsgDataset, fcCarbonFactorEsgDatasetVO);
        return fcCarbonFactorEsgDatasetVO;
    }

    public static FcCarbonFactorEsgDataset convertToModel(FcCarbonFactorEsgDatasetVO fcCarbonFactorEsgDatasetVO) {
        FcCarbonFactorEsgDataset fcCarbonFactorEsgDataset = new FcCarbonFactorEsgDataset();
        BeanUtils.copyProperties(fcCarbonFactorEsgDatasetVO, fcCarbonFactorEsgDataset);
        return fcCarbonFactorEsgDataset;
    }

    public static FcCarbonFactorEsgDataset convertToModelWithBase(
            FcCarbonFactorEsgDatasetVO fcCarbonFactorEsgDatasetVO,
            FcCarbonFactorEsgDataset fcCarbonFactorEsgDataset) {
        BeanUtils.copyProperties(fcCarbonFactorEsgDatasetVO, fcCarbonFactorEsgDataset);
        return fcCarbonFactorEsgDataset;
    }

}
