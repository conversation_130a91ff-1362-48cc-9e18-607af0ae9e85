package com.csci.susdev.modelcovt;

import com.csci.susdev.model.User;
import com.csci.susdev.vo.UserSaveVO;
import org.springframework.beans.BeanUtils;

public class UserSaveVOConverter {

    public static UserSaveVO convert(User user) {
        UserSaveVO userSaveVO = new UserSaveVO();
        BeanUtils.copyProperties(user, userSaveVO);
        return userSaveVO;
    }

    public static User convert(UserSaveVO userSaveVO) {
        User user = new User();
        BeanUtils.copyProperties(userSaveVO, user);
        return user;
    }
}
