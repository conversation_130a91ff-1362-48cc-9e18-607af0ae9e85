package com.csci.susdev.modelcovt;

import com.csci.susdev.model.SubmissionMonthConfig;
import com.csci.susdev.vo.SubmissionMonthConfigVO;
import org.springframework.beans.BeanUtils;

public class SubmissionMonthConfigConverter {

    public static SubmissionMonthConfigVO convertToVO(SubmissionMonthConfig submissionMonthConfig) {
        SubmissionMonthConfigVO submissionMonthConfigVO = new SubmissionMonthConfigVO();
        BeanUtils.copyProperties(submissionMonthConfig, submissionMonthConfigVO);
        return submissionMonthConfigVO;
    }

    public static SubmissionMonthConfig convertToModel(SubmissionMonthConfigVO submissionMonthConfigVO) {
        SubmissionMonthConfig submissionMonthConfig = new SubmissionMonthConfig();
        BeanUtils.copyProperties(submissionMonthConfigVO, submissionMonthConfig);
        return submissionMonthConfig;
    }

    public static SubmissionMonthConfig convertToModelWithBase(
            SubmissionMonthConfigVO submissionMonthConfigVO,
            SubmissionMonthConfig submissionMonthConfig) {
        BeanUtils.copyProperties(submissionMonthConfigVO, submissionMonthConfig);
        return submissionMonthConfig;
    }

}
