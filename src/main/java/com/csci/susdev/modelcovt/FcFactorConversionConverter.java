package com.csci.susdev.modelcovt;

import com.csci.susdev.model.FcFactorConversion;
import com.csci.susdev.vo.FcFactorConversionVO;
import org.springframework.beans.BeanUtils;

public class FcFactorConversionConverter {

    public static FcFactorConversionVO convertToVO(FcFactorConversion fcFactorConversion) {
        FcFactorConversionVO fcFactorConversionVO = new FcFactorConversionVO();
        BeanUtils.copyProperties(fcFactorConversion, fcFactorConversionVO);
        return fcFactorConversionVO;
    }

    public static FcFactorConversion convertToModel(FcFactorConversionVO fcFactorConversionVO) {
        FcFactorConversion FcFactorConversion = new FcFactorConversion();
        BeanUtils.copyProperties(fcFactorConversionVO, FcFactorConversion);
        return FcFactorConversion;
    }

    public static FcFactorConversion convertToModelWithBase(
            FcFactorConversionVO fcFactorConversionVO,
            FcFactorConversion fcFactorConversion) {
        BeanUtils.copyProperties(fcFactorConversionVO, fcFactorConversion);
        return fcFactorConversion;
    }

}
