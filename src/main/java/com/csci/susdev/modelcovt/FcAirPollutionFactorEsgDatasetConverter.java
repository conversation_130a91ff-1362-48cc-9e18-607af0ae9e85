package com.csci.susdev.modelcovt;

import com.csci.susdev.model.FcAirPollutionFactorEsgDataset;
import com.csci.susdev.vo.FcAirPollutionFactorEsgDatasetVO;
import org.springframework.beans.BeanUtils;

public class FcAirPollutionFactorEsgDatasetConverter {

    public static FcAirPollutionFactorEsgDatasetVO convertToVO(FcAirPollutionFactorEsgDataset fcAirPollutionFactorEsgDataset) {
        FcAirPollutionFactorEsgDatasetVO fcAirPollutionFactorEsgDatasetVO = new FcAirPollutionFactorEsgDatasetVO();
        BeanUtils.copyProperties(fcAirPollutionFactorEsgDataset, fcAirPollutionFactorEsgDatasetVO);
        return fcAirPollutionFactorEsgDatasetVO;
    }

    public static FcAirPollutionFactorEsgDataset convertToModel(FcAirPollutionFactorEsgDatasetVO fcAirPollutionFactorEsgDatasetVO) {
        FcAirPollutionFactorEsgDataset fcAirPollutionFactorEsgDataset = new FcAirPollutionFactorEsgDataset();
        BeanUtils.copyProperties(fcAirPollutionFactorEsgDatasetVO, fcAirPollutionFactorEsgDataset);
        return fcAirPollutionFactorEsgDataset;
    }

    public static FcAirPollutionFactorEsgDataset convertToModelWithBase(
            FcAirPollutionFactorEsgDatasetVO fcAirPollutionFactorEsgDatasetVO,
            FcAirPollutionFactorEsgDataset fcAirPollutionFactorEsgDataset) {
        BeanUtils.copyProperties(fcAirPollutionFactorEsgDatasetVO, fcAirPollutionFactorEsgDataset);
        return fcAirPollutionFactorEsgDataset;
    }

}
