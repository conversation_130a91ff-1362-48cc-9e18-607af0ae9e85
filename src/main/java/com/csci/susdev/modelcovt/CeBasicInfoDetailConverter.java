package com.csci.susdev.modelcovt;

import com.csci.susdev.model.CeBasicInfoDetail;
import com.csci.susdev.vo.CeBasicInfoDetailVO;
import org.springframework.beans.BeanUtils;
import org.springframework.core.convert.converter.Converter;

public class CeBasicInfoDetailConverter implements Converter<CeBasicInfoDetail, CeBasicInfoDetailVO> {
    @Override
    public CeBasicInfoDetailVO convert(CeBasicInfoDetail source) {
        CeBasicInfoDetailVO vo = new CeBasicInfoDetailVO();
        BeanUtils.copyProperties(source, vo);
        return vo;
    }

    public CeBasicInfoDetail revert(CeBasicInfoDetailVO vo) {
        CeBasicInfoDetail source = new CeBasicInfoDetail();
        BeanUtils.copyProperties(vo, source);
        return source;
    }
}
