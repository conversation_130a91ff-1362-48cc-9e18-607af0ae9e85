package com.csci.susdev.modelcovt;

import com.csci.susdev.model.CeBasicInfoDetail;
import com.csci.susdev.vo.CeBasicInfoTableDataVO;
import org.springframework.beans.BeanUtils;

public class CeBasicInfoTableDataConverter {

    public static CeBasicInfoTableDataVO convert(CeBasicInfoDetail ceBasicInfoDetail) {
        CeBasicInfoTableDataVO ceBasicInfoTableDataVO = new CeBasicInfoTableDataVO();
        BeanUtils.copyProperties(ceBasicInfoDetail, ceBasicInfoTableDataVO);
        return ceBasicInfoTableDataVO;
    }

    public static CeBasicInfoDetail convert(CeBasicInfoTableDataVO ceBasicInfoTableDataVO) {
        CeBasicInfoDetail ceBasicInfoDetail = new CeBasicInfoDetail();
        BeanUtils.copyProperties(ceBasicInfoTableDataVO, ceBasicInfoDetail);
        return ceBasicInfoDetail;
    }

}
