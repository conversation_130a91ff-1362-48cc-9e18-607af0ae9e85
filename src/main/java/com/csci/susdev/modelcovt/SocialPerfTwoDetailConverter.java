package com.csci.susdev.modelcovt;

import com.csci.susdev.model.SocialPerfTwoDetail;
import com.csci.susdev.vo.SocialPerfTwoDetailVO;
import org.springframework.beans.BeanUtils;

public class SocialPerfTwoDetailConverter {

    public static SocialPerfTwoDetailVO convert(SocialPerfTwoDetail source) {
        SocialPerfTwoDetailVO target = new SocialPerfTwoDetailVO();
        BeanUtils.copyProperties(source, target);
        return target;
    }

    public static SocialPerfTwoDetail convert(SocialPerfTwoDetailVO source) {
        SocialPerfTwoDetail target = new SocialPerfTwoDetail();
        BeanUtils.copyProperties(source, target);
        return target;
    }
}
