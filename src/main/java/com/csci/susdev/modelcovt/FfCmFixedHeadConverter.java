package com.csci.susdev.modelcovt;

import com.csci.susdev.constant.ApproveStatus;
import com.csci.susdev.model.FfCmFixedHead;
import com.csci.susdev.vo.FfCmFixedHeadVO;
import org.springframework.beans.BeanUtils;

public class FfCmFixedHeadConverter {

    static String getApproveStatusName(Integer approveStatus) {
        ApproveStatus[] approveStatuses = ApproveStatus.values();
        for (ApproveStatus status : approveStatuses) {
            if (status.getCode().equals(approveStatus)) {
                return status.getName();
            }
        }
        return "未知状态";
    }

    public static FfCmFixedHeadVO convert(FfCmFixedHead source) {
        FfCmFixedHeadVO ffCmFixedHeadVO = new FfCmFixedHeadVO();
        BeanUtils.copyProperties(source, ffCmFixedHeadVO);
        return ffCmFixedHeadVO;
    }

    public static FfCmFixedHead convert(FfCmFixedHeadVO source) {
        FfCmFixedHead ffCmFixedHead = new FfCmFixedHead();
        BeanUtils.copyProperties(source, ffCmFixedHead);
        return ffCmFixedHead;
    }

}
