package com.csci.susdev.modelcovt;

import com.csci.susdev.model.BDict;
import com.csci.susdev.vo.BDictVO;
import org.springframework.beans.BeanUtils;

public class BDictConverter {

    public static BDictVO convertToVO(BDict bDict) {
        BDictVO bDictVO = new BDictVO();
        BeanUtils.copyProperties(bDict, bDictVO);
        return bDictVO;
    }

    public static BDict convertToModel(BDictVO bDictVO) {
        BDict bDict = new BDict();
        BeanUtils.copyProperties(bDictVO, bDict);
        return bDict;
    }

    public static BDict convertToModelWithBase(
            BDictVO bDictVO,
            BDict bDict) {
        BeanUtils.copyProperties(bDictVO, bDict);
        return bDict;
    }

}
