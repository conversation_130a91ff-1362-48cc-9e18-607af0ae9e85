package com.csci.susdev.modelcovt;

import com.csci.susdev.model.Area;
import com.csci.susdev.vo.AreaVO;
import org.springframework.beans.BeanUtils;
import org.springframework.core.convert.converter.Converter;

public class AreaConverter implements Converter<Area, AreaVO> {
    @Override
    public AreaVO convert(Area area) {
        AreaVO areaVO = new AreaVO();
        BeanUtils.copyProperties(area, areaVO);
        return areaVO;
    }

    public Area revert(AreaVO areaVO) {
        Area area = new Area();
        BeanUtils.copyProperties(areaVO, area);
        return area;
    }
}
