package com.csci.susdev.modelcovt;

import com.csci.susdev.model.FormDetail;
import com.csci.susdev.model.FormDetail;
import com.csci.susdev.vo.FormDetailVO;
import com.csci.susdev.vo.FormDetailVO;
import org.springframework.beans.BeanUtils;

public class FormDetailConverter {

    public static FormDetailVO convertToVO(FormDetail formDetail) {
        FormDetailVO formDetailVO = new FormDetailVO();
        BeanUtils.copyProperties(formDetail, formDetailVO);
        return formDetailVO;
    }

    public static FormDetail convertToModel(FormDetailVO formDetailVO) {
        FormDetail formDetail = new FormDetail();
        BeanUtils.copyProperties(formDetailVO, formDetail);
        return formDetail;
    }

    public static FormDetail convertToModelWithBase(
            FormDetailVO formDetailVO,
            FormDetail formDetail) {
        BeanUtils.copyProperties(formDetailVO, formDetail);
        return formDetail;
    }

}
