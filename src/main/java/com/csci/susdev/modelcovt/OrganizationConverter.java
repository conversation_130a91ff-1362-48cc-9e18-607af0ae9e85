package com.csci.susdev.modelcovt;

import com.csci.susdev.model.Area;
import com.csci.susdev.model.Organization;
import com.csci.susdev.service.AreaService;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.OrganizationVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.Optional;

public class OrganizationConverter {
    public static OrganizationVO convert(Organization source) {
        OrganizationVO organizationVO = new OrganizationVO();
        BeanUtils.copyProperties(source, organizationVO);
        AreaService areaService = SpringContextUtil.getBean(AreaService.class);
        if (StringUtils.isNotBlank(source.getAreaId())) {
            Area area = areaService.selectByPrimaryKey(source.getAreaId());
            organizationVO.setAreaName(Optional.ofNullable(area).map(Area::getName).orElse(""));
        }
        return organizationVO;
    }

    public static Organization convert(OrganizationVO source) {
        Organization organization = new Organization();
        BeanUtils.copyProperties(source, organization);
        return organization;
    }
}
