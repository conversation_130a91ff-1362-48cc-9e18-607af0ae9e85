package com.csci.susdev.modelcovt;

import com.csci.susdev.model.*;
import com.csci.susdev.service.*;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.FactorScopeVO;
import com.csci.susdev.vo.FactorSelectionVO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;

public class FactorSelectionConverter {

    public static FactorSelectionVO fillInDetail(FactorSelectionVO vo) {
        FactorSelectionVO factorSelectionVO = new FactorSelectionVO();
        BeanUtils.copyProperties(vo, factorSelectionVO);

        /*
        //配置因子範圍
        FactorScopeService factorScopeService = SpringContextUtil.getBean(FactorScopeService.class);
        FactorScope factorScope = factorScopeService.selectByPrimaryKey(vo.getFactorScopeId());
        FactorScopeVO factorScopeVO = FactorScopeConverter.convertToVO(factorScope);

        factorSelectionVO.setProtocolDetailId(factorScopeVO.getProtocolDetailId());

        factorSelectionVO.setSubCategoryId(factorScopeVO.getSubCategoryId());
        factorSelectionVO.setSubCategoryName(factorScopeVO.getSubCategoryName());
        factorSelectionVO.setSubCategoryNameSc(factorScopeVO.getSubCategoryNameSc());
        factorSelectionVO.setSubCategoryNameEn(factorScopeVO.getSubCategoryNameEn());

        factorSelectionVO.setCategoryId(factorScopeVO.getCategoryId());
        factorSelectionVO.setCategoryName(factorScopeVO.getCategoryName());
        factorSelectionVO.setCategoryNameSc(factorScopeVO.getCategoryNameSc());
        factorSelectionVO.setCategoryNameEn(factorScopeVO.getCategoryNameEn());

        factorSelectionVO.setProtocolId(factorScopeVO.getProtocolId());
        factorSelectionVO.setProtocolName(factorScopeVO.getProtocolName());
        factorSelectionVO.setProtocolNameSc(factorScopeVO.getProtocolNameSc());
        factorSelectionVO.setProtocolNameEn(factorScopeVO.getProtocolNameEn());

        factorSelectionVO.setCarbonEmissionLocationId(factorScopeVO.getCarbonEmissionLocationId());
        factorSelectionVO.setCarbonEmissionLocationName(factorScopeVO.getCarbonEmissionLocationName());
        factorSelectionVO.setCarbonEmissionLocationNameSc(factorScopeVO.getCarbonEmissionLocationNameSc());
        factorSelectionVO.setCarbonEmissionLocationNameEn(factorScopeVO.getCarbonEmissionLocationNameEn());

        factorSelectionVO.setFormDetailId(factorScopeVO.getFormDetailId());

        factorSelectionVO.setFormCode(factorScopeVO.getFormCode());
        factorSelectionVO.setYear(factorScopeVO.getYear());
        factorSelectionVO.setTypeA(factorScopeVO.getTypeA());
        factorSelectionVO.setTypeB(factorScopeVO.getTypeB());
        factorSelectionVO.setTypeC(factorScopeVO.getTypeC());
        factorSelectionVO.setTypeD(factorScopeVO.getTypeD());
        factorSelectionVO.setTypeE(factorScopeVO.getTypeE());

        //配置組織
        OrganizationService organizationService = SpringContextUtil.getBean(OrganizationService.class);
        Organization organization = organizationService.selectByPrimaryKey(vo.getOrganizationId());
        if(ObjectUtils.isNotEmpty(organization)) {
            factorSelectionVO.setOrganizationNo(organization.getNo());
            factorSelectionVO.setOrganizationName(organization.getCode());
            factorSelectionVO.setOrganizationName(organization.getName());
        }
         */


        //配置因子管理
        switch (factorSelectionVO.getFcCarbonFactorType() + factorSelectionVO.getFcCarbonFactorDatasource()) {
            case "碳排因子香港碳排數據":
                FcCarbonFactorHkService fcCarbonFactorHkService = SpringContextUtil.getBean(FcCarbonFactorHkService.class);
                FcCarbonFactorHk fcCarbonFactorHk = fcCarbonFactorHkService.selectByPrimaryKey(vo.getFcCarbonFactorId());
                factorSelectionVO.setFcCarbonFactorVO(FcCarbonFactorHkConverter.convertToVO(fcCarbonFactorHk));
                break;
            case "碳排因子GBT51366":
                FcCarbonFactorGbt51366Service fcCarbonFactorGbt51366Service = SpringContextUtil.getBean(FcCarbonFactorGbt51366Service.class);
                FcCarbonFactorGbt51366 fcCarbonFactorGbt51366 = fcCarbonFactorGbt51366Service.selectByPrimaryKey(vo.getFcCarbonFactorId());
                factorSelectionVO.setFcCarbonFactorVO(FcCarbonFactorGbt51366Converter.convertToVO(fcCarbonFactorGbt51366));
                break;
            case "碳排因子全生命周期數據集":
                FcCarbonFactorFlcService fcCarbonFactorFlcService = SpringContextUtil.getBean(FcCarbonFactorFlcService.class);
                FcCarbonFactorFlc fcCarbonFactorFlc = fcCarbonFactorFlcService.selectByPrimaryKey(vo.getFcCarbonFactorId());
                factorSelectionVO.setFcCarbonFactorVO(FcCarbonFactorFlcConverter.convertToVO(fcCarbonFactorFlc));
                break;
            case "碳排因子ESG數據集":
                FcCarbonFactorEsgDatasetService fcCarbonFactorEsgDatasetService = SpringContextUtil.getBean(FcCarbonFactorEsgDatasetService.class);
                FcCarbonFactorEsgDataset fcCarbonFactorEsgDataset = fcCarbonFactorEsgDatasetService.selectByPrimaryKey(vo.getFcCarbonFactorId());
                factorSelectionVO.setFcCarbonFactorVO(FcCarbonFactorEsgDatasetConverter.convertToVO(fcCarbonFactorEsgDataset));
                break;
            case "能源因子ESG數據集":
                FcEnergyFactorEsgDatasetService fcEnergyFactorEsgDatasetService = SpringContextUtil.getBean(FcEnergyFactorEsgDatasetService.class);
                FcEnergyFactorEsgDataset fcEnergyFactorEsgDataset = fcEnergyFactorEsgDatasetService.selectByPrimaryKey(vo.getFcCarbonFactorId());
                factorSelectionVO.setFcCarbonFactorVO(FcEnergyFactorEsgDatasetConverter.convertToVO(fcEnergyFactorEsgDataset));
                break;
            case "空氣污染因子ESG數據集":
                FcAirPollutionFactorEsgDatasetService fcAirPollutionFactorEsgDatasetService = SpringContextUtil.getBean(FcAirPollutionFactorEsgDatasetService.class);
                FcAirPollutionFactorEsgDataset fcAirPollutionFactorEsgDataset = fcAirPollutionFactorEsgDatasetService.selectByPrimaryKey(vo.getFcCarbonFactorId());
                factorSelectionVO.setFcCarbonFactorVO(FcAirPollutionFactorEsgDatasetConverter.convertToVO(fcAirPollutionFactorEsgDataset));
                break;

            default:
                break;
        }

        return factorSelectionVO;
    }

    public static FactorSelectionVO convertToVO(FactorSelection factorSelection) {
        FactorSelectionVO factorSelectionVO = new FactorSelectionVO();
        BeanUtils.copyProperties(factorSelection, factorSelectionVO);

        //配置因子範圍
        FactorScopeService factorScopeService = SpringContextUtil.getBean(FactorScopeService.class);
        FactorScope factorScope = factorScopeService.selectByPrimaryKey(factorSelection.getFactorScopeId());
        FactorScopeVO factorScopeVO = FactorScopeConverter.convertToVO(factorScope);

        factorSelectionVO.setProtocolDetailId(factorScopeVO.getProtocolDetailId());

        factorSelectionVO.setSubCategoryId(factorScopeVO.getSubCategoryId());
        factorSelectionVO.setSubCategoryName(factorScopeVO.getSubCategoryName());
        factorSelectionVO.setSubCategoryNameSc(factorScopeVO.getSubCategoryNameSc());
        factorSelectionVO.setSubCategoryNameEn(factorScopeVO.getSubCategoryNameEn());

        factorSelectionVO.setCategoryId(factorScopeVO.getCategoryId());
        factorSelectionVO.setCategoryName(factorScopeVO.getCategoryName());
        factorSelectionVO.setCategoryNameSc(factorScopeVO.getCategoryNameSc());
        factorSelectionVO.setCategoryNameEn(factorScopeVO.getCategoryNameEn());

        factorSelectionVO.setProtocolId(factorScopeVO.getProtocolId());
        factorSelectionVO.setProtocolName(factorScopeVO.getProtocolName());
        factorSelectionVO.setProtocolNameSc(factorScopeVO.getProtocolNameSc());
        factorSelectionVO.setProtocolNameEn(factorScopeVO.getProtocolNameEn());

        factorSelectionVO.setCarbonEmissionLocationId(factorScopeVO.getCarbonEmissionLocationId());
        factorSelectionVO.setCarbonEmissionLocationName(factorScopeVO.getCarbonEmissionLocationName());
        factorSelectionVO.setCarbonEmissionLocationNameSc(factorScopeVO.getCarbonEmissionLocationNameSc());
        factorSelectionVO.setCarbonEmissionLocationNameEn(factorScopeVO.getCarbonEmissionLocationNameEn());

        factorSelectionVO.setFormDetailId(factorScopeVO.getFormDetailId());

        factorSelectionVO.setFormCode(factorScopeVO.getFormCode());
        factorSelectionVO.setYear(factorScopeVO.getYear());
        factorSelectionVO.setTypeA(factorScopeVO.getTypeA());
        factorSelectionVO.setTypeB(factorScopeVO.getTypeB());
        factorSelectionVO.setTypeC(factorScopeVO.getTypeC());
        factorSelectionVO.setTypeD(factorScopeVO.getTypeD());
        factorSelectionVO.setTypeE(factorScopeVO.getTypeE());

        //配置組織
        OrganizationService organizationService = SpringContextUtil.getBean(OrganizationService.class);
        Organization organization = organizationService.selectByPrimaryKey(factorSelection.getOrganizationId());
        if(ObjectUtils.isNotEmpty(organization)) {
            factorSelectionVO.setOrganizationNo(organization.getNo());
            factorSelectionVO.setOrganizationName(organization.getCode());
            factorSelectionVO.setOrganizationName(organization.getName());
        }

        //配置因子管理
        switch (factorSelectionVO.getFcCarbonFactorType() + factorSelectionVO.getFcCarbonFactorDatasource()) {
            case "碳排因子香港碳排數據":
                FcCarbonFactorHkService fcCarbonFactorHkService = SpringContextUtil.getBean(FcCarbonFactorHkService.class);
                FcCarbonFactorHk fcCarbonFactorHk = fcCarbonFactorHkService.selectByPrimaryKey(factorSelection.getFcCarbonFactorId());
                factorSelectionVO.setFcCarbonFactorVO(FcCarbonFactorHkConverter.convertToVO(fcCarbonFactorHk));
                break;
            case "碳排因子GBT51366":
                FcCarbonFactorGbt51366Service fcCarbonFactorGbt51366Service = SpringContextUtil.getBean(FcCarbonFactorGbt51366Service.class);
                FcCarbonFactorGbt51366 fcCarbonFactorGbt51366 = fcCarbonFactorGbt51366Service.selectByPrimaryKey(factorSelection.getFcCarbonFactorId());
                factorSelectionVO.setFcCarbonFactorVO(FcCarbonFactorGbt51366Converter.convertToVO(fcCarbonFactorGbt51366));
                break;
            case "碳排因子全生命周期數據集":
                FcCarbonFactorFlcService fcCarbonFactorFlcService = SpringContextUtil.getBean(FcCarbonFactorFlcService.class);
                FcCarbonFactorFlc fcCarbonFactorFlc = fcCarbonFactorFlcService.selectByPrimaryKey(factorSelection.getFcCarbonFactorId());
                factorSelectionVO.setFcCarbonFactorVO(FcCarbonFactorFlcConverter.convertToVO(fcCarbonFactorFlc));
                break;
            case "碳排因子ESG數據集":
                FcCarbonFactorEsgDatasetService fcCarbonFactorEsgDatasetService = SpringContextUtil.getBean(FcCarbonFactorEsgDatasetService.class);
                FcCarbonFactorEsgDataset fcCarbonFactorEsgDataset = fcCarbonFactorEsgDatasetService.selectByPrimaryKey(factorSelection.getFcCarbonFactorId());
                factorSelectionVO.setFcCarbonFactorVO(FcCarbonFactorEsgDatasetConverter.convertToVO(fcCarbonFactorEsgDataset));
                break;
            case "能源因子ESG數據集":
                FcEnergyFactorEsgDatasetService fcEnergyFactorEsgDatasetService = SpringContextUtil.getBean(FcEnergyFactorEsgDatasetService.class);
                FcEnergyFactorEsgDataset fcEnergyFactorEsgDataset = fcEnergyFactorEsgDatasetService.selectByPrimaryKey(factorSelection.getFcCarbonFactorId());
                factorSelectionVO.setFcCarbonFactorVO(FcEnergyFactorEsgDatasetConverter.convertToVO(fcEnergyFactorEsgDataset));
                break;
            case "空氣污染因子ESG數據集":
                FcAirPollutionFactorEsgDatasetService fcAirPollutionFactorEsgDatasetService = SpringContextUtil.getBean(FcAirPollutionFactorEsgDatasetService.class);
                FcAirPollutionFactorEsgDataset fcAirPollutionFactorEsgDataset = fcAirPollutionFactorEsgDatasetService.selectByPrimaryKey(factorSelection.getFcCarbonFactorId());
                factorSelectionVO.setFcCarbonFactorVO(FcAirPollutionFactorEsgDatasetConverter.convertToVO(fcAirPollutionFactorEsgDataset));
                break;


            default:
                break;
        }

        return factorSelectionVO;
    }

    public static FactorSelection convertToModel(FactorSelectionVO factorSelectionVO) {
        FactorSelection factorSelection = new FactorSelection();
        BeanUtils.copyProperties(factorSelectionVO, factorSelection);
        return factorSelection;
    }

    public static FactorSelection convertToModelWithBase(
            FactorSelectionVO factorSelectionVO,
            FactorSelection factorSelection) {
        BeanUtils.copyProperties(factorSelectionVO, factorSelection);
        return factorSelection;
    }

}
