package com.csci.susdev.modelcovt;

import com.csci.susdev.model.User;
import com.csci.susdev.vo.UserVO;
import org.springframework.beans.BeanUtils;

public class UserVOConverter {

    public static UserVO convert(User user) {
        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(user, userVO);
        userVO.setPassword("");
        return userVO;
    }

    public static User convert(UserVO userVO) {
        User user = new User();
        BeanUtils.copyProperties(userVO, user);
        return user;
    }
}
