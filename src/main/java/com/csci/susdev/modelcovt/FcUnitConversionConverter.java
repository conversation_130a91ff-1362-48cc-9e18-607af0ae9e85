package com.csci.susdev.modelcovt;

import com.csci.susdev.model.FcUnitConversion;
import com.csci.susdev.vo.FcUnitConversionVO;
import org.springframework.beans.BeanUtils;

public class FcUnitConversionConverter {

    public static FcUnitConversionVO convertToVO(FcUnitConversion fcUnitConversion) {
        FcUnitConversionVO fcUnitConversionVO = new FcUnitConversionVO();
        BeanUtils.copyProperties(fcUnitConversion, fcUnitConversionVO);
        return fcUnitConversionVO;
    }

    public static FcUnitConversion convertToModel(FcUnitConversionVO fcUnitConversionVO) {
        FcUnitConversion FcUnitConversion = new FcUnitConversion();
        BeanUtils.copyProperties(fcUnitConversionVO, FcUnitConversion);
        return FcUnitConversion;
    }

    public static FcUnitConversion convertToModelWithBase(
            FcUnitConversionVO fcUnitConversionVO,
            FcUnitConversion fcUnitConversion) {
        BeanUtils.copyProperties(fcUnitConversionVO, fcUnitConversion);
        return fcUnitConversion;
    }

}
