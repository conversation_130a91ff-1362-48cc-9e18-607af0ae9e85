package com.csci.susdev.modelcovt;

import com.csci.susdev.model.BusinessTrip;
import com.csci.susdev.vo.BusinessTripExportData;
import org.springframework.beans.BeanUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class BusinessTripExportDataConverter {

    static Map<Integer, String> tripTypeMap = new HashMap<>();
    static Map<Integer, String> ticketTypeMap = new HashMap<>();

    static {
        tripTypeMap.put(1, "長途");
        tripTypeMap.put(2, "短途");
        tripTypeMap.put(3, "國內");

        ticketTypeMap.put(1, "往返");
        ticketTypeMap.put(2, "單程");
    }


    public static BusinessTripExportData convert(BusinessTrip businessTrip) {
        BusinessTripExportData businessTripExportData = new BusinessTripExportData();
        BeanUtils.copyProperties(businessTrip, businessTripExportData);
        businessTripExportData.setMonthValueText(businessTrip.getMonthValue() + "月");
        businessTripExportData.setTripTypeText(Optional.ofNullable(tripTypeMap.get(businessTrip.getTripType())).orElse("未知"));
        businessTripExportData.setTicketTypeText(Optional.ofNullable(ticketTypeMap.get(businessTrip.getTicketType())).orElse("未知"));
        return businessTripExportData;
    }
}
