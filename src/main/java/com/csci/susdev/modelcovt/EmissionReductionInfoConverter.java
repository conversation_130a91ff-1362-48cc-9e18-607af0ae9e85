package com.csci.susdev.modelcovt;

import com.csci.susdev.model.EmissionReductionInfo;
import com.csci.susdev.vo.EmissionReductionInfoVO;
import org.springframework.beans.BeanUtils;

public class EmissionReductionInfoConverter {

    public static EmissionReductionInfoVO convert(EmissionReductionInfo ceIdentificationSubcontractor) {
        EmissionReductionInfoVO ceIdentificationSubcontractorVO = new EmissionReductionInfoVO();
        BeanUtils.copyProperties(ceIdentificationSubcontractor, ceIdentificationSubcontractorVO);
        return ceIdentificationSubcontractorVO;
    }

    public static EmissionReductionInfo convert(EmissionReductionInfoVO ceIdentificationSubcontractorVO) {
        EmissionReductionInfo ceIdentificationSubcontractor = new EmissionReductionInfo();
        BeanUtils.copyProperties(ceIdentificationSubcontractorVO, ceIdentificationSubcontractor);
        return ceIdentificationSubcontractor;
    }

}
