package com.csci.susdev.modelcovt;

import com.csci.susdev.model.EmissionReductionDetail;
import com.csci.susdev.vo.EmissionReductionTableDataVO;
import org.springframework.beans.BeanUtils;

public class EmissionReductionTableDataConverter {

    public static EmissionReductionTableDataVO convert(EmissionReductionDetail emissionReductionDetail) {
        EmissionReductionTableDataVO emissionReductionTableDataVO = new EmissionReductionTableDataVO();
        BeanUtils.copyProperties(emissionReductionDetail, emissionReductionTableDataVO);
        return emissionReductionTableDataVO;
    }

    public static EmissionReductionDetail convert(EmissionReductionTableDataVO emissionReductionTableDataVO) {
        EmissionReductionDetail emissionReductionDetail = new EmissionReductionDetail();
        BeanUtils.copyProperties(emissionReductionTableDataVO, emissionReductionDetail);
        return emissionReductionDetail;
    }

}
