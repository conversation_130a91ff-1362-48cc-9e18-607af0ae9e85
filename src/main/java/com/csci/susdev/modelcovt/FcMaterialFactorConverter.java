package com.csci.susdev.modelcovt;

import com.csci.susdev.model.FcCarbonFactorHk;
import com.csci.susdev.model.FcMaterialFactor;
import com.csci.susdev.vo.FcCarbonFactorHkVO;
import com.csci.susdev.vo.FcMaterialFactorVO;
import org.springframework.beans.BeanUtils;

public class FcMaterialFactorConverter {

    public static FcMaterialFactorVO convertToVO(FcMaterialFactor fcMaterialFactor) {
        FcMaterialFactorVO fcMaterialFactorVO = new FcMaterialFactorVO();
        BeanUtils.copyProperties(fcMaterialFactor, fcMaterialFactorVO);
        return fcMaterialFactorVO;
    }

    public static FcMaterialFactor convertToModel(FcMaterialFactorVO fcMaterialFactorVO) {
        FcMaterialFactor FcMaterialFactor = new FcMaterialFactor();
        BeanUtils.copyProperties(fcMaterialFactorVO, FcMaterialFactor);
        return FcMaterialFactor;
    }

    public static FcMaterialFactor convertToModelWithBase(
            FcMaterialFactorVO fcMaterialFactorVO,
            FcMaterialFactor fcMaterialFactor) {
        BeanUtils.copyProperties(fcMaterialFactorVO, fcMaterialFactor);
        return fcMaterialFactor;
    }

}
