package com.csci.susdev.modelcovt;

import com.csci.susdev.model.FfCmMobileDetail;
import com.csci.susdev.vo.FfCmMobileDetailVO;
import org.springframework.beans.BeanUtils;
import org.springframework.core.convert.converter.Converter;

public class FfCmMobileDetailConverter implements Converter<FfCmMobileDetail, FfCmMobileDetailVO> {
    @Override
    public FfCmMobileDetailVO convert(FfCmMobileDetail source) {
        FfCmMobileDetailVO vo = new FfCmMobileDetailVO();
        BeanUtils.copyProperties(source, vo);
        return vo;
    }

    public FfCmMobileDetail revert(FfCmMobileDetailVO vo) {
        FfCmMobileDetail source = new FfCmMobileDetail();
        BeanUtils.copyProperties(vo, source);
        return source;
    }
}
