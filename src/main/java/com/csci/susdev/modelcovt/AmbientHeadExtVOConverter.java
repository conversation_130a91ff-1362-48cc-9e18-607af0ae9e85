package com.csci.susdev.modelcovt;

import com.csci.susdev.model.AmbientHeadExt;
import com.csci.susdev.vo.AmbientHeadExtVO;
import org.springframework.beans.BeanUtils;

public class AmbientHeadExtVOConverter {

    public static AmbientHeadExtVO convert(AmbientHeadExt ambientHeadExt) {
        AmbientHeadExtVO ambientHeadExtExtVO = new AmbientHeadExtVO();
        BeanUtils.copyProperties(ambientHeadExt, ambientHeadExtExtVO);
        return ambientHeadExtExtVO;
    }

    public static AmbientHeadExt convert(AmbientHeadExtVO ambientHeadExtVO) {
        AmbientHeadExt ambientHeadExt = new AmbientHeadExt();
        BeanUtils.copyProperties(ambientHeadExtVO, ambientHeadExt);
        return ambientHeadExt;
    }
    
}
