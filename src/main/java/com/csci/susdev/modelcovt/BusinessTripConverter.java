package com.csci.susdev.modelcovt;

import com.csci.susdev.model.BusinessTrip;
import com.csci.susdev.vo.BusinessTripVO;
import org.springframework.beans.BeanUtils;

public class BusinessTripConverter {

    public static BusinessTripVO convert(BusinessTrip businessTrip) {
        BusinessTripVO businessTripVO = new BusinessTripVO();
        BeanUtils.copyProperties(businessTrip, businessTripVO);
        return businessTripVO;
    }

    public static BusinessTrip convert(BusinessTripVO businessTripVO) {
        BusinessTrip businessTrip = new BusinessTrip();
        BeanUtils.copyProperties(businessTripVO, businessTrip);
        return businessTrip;
    }

}
