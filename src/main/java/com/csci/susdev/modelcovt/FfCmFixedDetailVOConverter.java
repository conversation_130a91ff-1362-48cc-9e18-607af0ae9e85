package com.csci.susdev.modelcovt;

import com.csci.susdev.model.FfCmFixedDetail;
import com.csci.susdev.vo.FfCmFixedDetailVO;
import org.springframework.beans.BeanUtils;

public class FfCmFixedDetailVOConverter {

    public static FfCmFixedDetailVO convert(FfCmFixedDetail ffCmFixedDetail) {
        FfCmFixedDetailVO ffCmFixedDetailVO = new FfCmFixedDetailVO();
        BeanUtils.copyProperties(ffCmFixedDetail, ffCmFixedDetailVO);
        return ffCmFixedDetailVO;
    }

    public static FfCmFixedDetail convert(FfCmFixedDetailVO ffCmFixedDetailVO) {
        FfCmFixedDetail ffCmFixedDetail = new FfCmFixedDetail();
        BeanUtils.copyProperties(ffCmFixedDetailVO, ffCmFixedDetail);
        return ffCmFixedDetail;
    }

}
