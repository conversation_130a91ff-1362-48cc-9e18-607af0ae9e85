package com.csci.susdev.modelcovt;

import com.csci.susdev.constant.ApproveStatus;
import com.csci.susdev.model.CeBasicInfoHead;
import com.csci.susdev.vo.CeBasicInfoHeadVO;
import org.springframework.beans.BeanUtils;

public class CeBasicInfoHeadConverter {

    static String getApproveStatusName(Integer approveStatus) {
        ApproveStatus[] approveStatuses = ApproveStatus.values();
        for (ApproveStatus status : approveStatuses) {
            if (status.getCode().equals(approveStatus)) {
                return status.getName();
            }
        }
        return "未知状态";
    }

    public static CeBasicInfoHeadVO convert(CeBasicInfoHead source) {
        CeBasicInfoHeadVO ceBasicInfoHeadVO = new CeBasicInfoHeadVO();
        BeanUtils.copyProperties(source, ceBasicInfoHeadVO);
        return ceBasicInfoHeadVO;
    }

    public static CeBasicInfoHead convert(CeBasicInfoHeadVO source) {
        CeBasicInfoHead ceBasicInfoHead = new CeBasicInfoHead();
        BeanUtils.copyProperties(source, ceBasicInfoHead);
        return ceBasicInfoHead;
    }

}
