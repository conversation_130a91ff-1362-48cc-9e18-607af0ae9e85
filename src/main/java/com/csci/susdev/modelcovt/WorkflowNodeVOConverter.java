package com.csci.susdev.modelcovt;

import com.csci.susdev.model.WorkflowNode;
import com.csci.susdev.vo.WorkflowNodeVO;
import org.springframework.beans.BeanUtils;

public class WorkflowNodeVOConverter {

    public static WorkflowNodeVO convert(WorkflowNode workflowNode) {
        WorkflowNodeVO workflowNodeVO = new WorkflowNodeVO();
        BeanUtils.copyProperties(workflowNode, workflowNodeVO);
        return workflowNodeVO;
    }

    public static WorkflowNode convert(WorkflowNodeVO workflowNodeVO) {
        WorkflowNode workflowNode = new WorkflowNode();
        BeanUtils.copyProperties(workflowNodeVO, workflowNode);
        return workflowNode;
    }
}
