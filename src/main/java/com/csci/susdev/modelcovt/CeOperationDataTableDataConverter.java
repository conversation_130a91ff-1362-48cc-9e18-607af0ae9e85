package com.csci.susdev.modelcovt;

import com.csci.susdev.model.CeOperationDataDetail;
import com.csci.susdev.vo.CeOperationDataTableDataVO;
import org.springframework.beans.BeanUtils;

public class CeOperationDataTableDataConverter {

    public static CeOperationDataTableDataVO convert(CeOperationDataDetail ceOperationDataDetail) {
        CeOperationDataTableDataVO ceOperationDataTableDataVO = new CeOperationDataTableDataVO();
        BeanUtils.copyProperties(ceOperationDataDetail, ceOperationDataTableDataVO);
        return ceOperationDataTableDataVO;
    }

    public static CeOperationDataDetail convert(CeOperationDataTableDataVO ceOperationDataTableDataVO) {
        CeOperationDataDetail ceOperationDataDetail = new CeOperationDataDetail();
        BeanUtils.copyProperties(ceOperationDataTableDataVO, ceOperationDataDetail);
        return ceOperationDataDetail;
    }

}
