package com.csci.susdev.modelcovt;

import com.csci.susdev.constant.ApproveStatus;
import com.csci.susdev.model.EmissionReductionHead;
import com.csci.susdev.vo.EmissionReductionHeadVO;
import org.springframework.beans.BeanUtils;

public class EmissionReductionHeadConverter {

    static String getApproveStatusName(Integer approveStatus) {
        ApproveStatus[] approveStatuses = ApproveStatus.values();
        for (ApproveStatus status : approveStatuses) {
            if (status.getCode().equals(approveStatus)) {
                return status.getName();
            }
        }
        return "未知状态";
    }

    public static EmissionReductionHeadVO convert(EmissionReductionHead source) {
        EmissionReductionHeadVO emissionReductionHeadVO = new EmissionReductionHeadVO();
        BeanUtils.copyProperties(source, emissionReductionHeadVO);
        return emissionReductionHeadVO;
    }

    public static EmissionReductionHead convert(EmissionReductionHeadVO source) {
        EmissionReductionHead emissionReductionHead = new EmissionReductionHead();
        BeanUtils.copyProperties(source, emissionReductionHead);
        return emissionReductionHead;
    }

}
