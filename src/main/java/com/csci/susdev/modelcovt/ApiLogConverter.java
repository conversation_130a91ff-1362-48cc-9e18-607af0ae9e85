package com.csci.susdev.modelcovt;

import com.csci.susdev.model.ApiLog;
import com.csci.susdev.vo.ApiLogVO;
import org.springframework.beans.BeanUtils;

public class ApiLogConverter {

    public static ApiLogVO convertToVO(ApiLog apiLog) {
        ApiLogVO apiLogVO = new ApiLogVO();
        BeanUtils.copyProperties(apiLog, apiLogVO);
        return apiLogVO;
    }

    public static ApiLog convertToModel(ApiLogVO apiLogVO) {
        ApiLog apiLog = new ApiLog();
        BeanUtils.copyProperties(apiLogVO, apiLog);
        return apiLog;
    }

    public static ApiLog convertToModelWithBase(
            ApiLogVO apiLogVO,
            ApiLog apiLog) {
        BeanUtils.copyProperties(apiLogVO, apiLog);
        return apiLog;
    }

}
