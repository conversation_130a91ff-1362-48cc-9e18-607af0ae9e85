package com.csci.susdev.modelcovt;

import com.csci.susdev.model.CarbonEmissionLocation;
import com.csci.susdev.vo.CarbonEmissionLocationVO;
import org.springframework.beans.BeanUtils;

public class CarbonEmissionLocationConverter {

    public static CarbonEmissionLocationVO convertToVO(CarbonEmissionLocation carbonEmissionLocation) {
        CarbonEmissionLocationVO carbonEmissionLocationVO = new CarbonEmissionLocationVO();
        BeanUtils.copyProperties(carbonEmissionLocation, carbonEmissionLocationVO);
        return carbonEmissionLocationVO;
    }

    public static CarbonEmissionLocation convertToModel(CarbonEmissionLocationVO carbonEmissionLocationVO) {
        CarbonEmissionLocation carbonEmissionLocation = new CarbonEmissionLocation();
        BeanUtils.copyProperties(carbonEmissionLocationVO, carbonEmissionLocation);
        return carbonEmissionLocation;
    }

    public static CarbonEmissionLocation convertToModelWithBase(
            CarbonEmissionLocationVO carbonEmissionLocationVO,
            CarbonEmissionLocation carbonEmissionLocation) {
        BeanUtils.copyProperties(carbonEmissionLocationVO, carbonEmissionLocation);
        return carbonEmissionLocation;
    }

}
