package com.csci.susdev.modelcovt;

import com.csci.susdev.model.FcCarbonFactorHk;
import com.csci.susdev.vo.FcCarbonFactorHkVO;
import org.springframework.beans.BeanUtils;

public class FcCarbonFactorHkConverter {

    public static FcCarbonFactorHkVO convertToVO(FcCarbonFactorHk fcCarbonFactorHk) {
        FcCarbonFactorHkVO fcCarbonFactorHkVO = new FcCarbonFactorHkVO();
        BeanUtils.copyProperties(fcCarbonFactorHk, fcCarbonFactorHkVO);
        return fcCarbonFactorHkVO;
    }

    public static FcCarbonFactorHk convertToModel(FcCarbonFactorHkVO fcCarbonFactorHkVO) {
        FcCarbonFactorHk fcCarbonFactorHk = new FcCarbonFactorHk();
        BeanUtils.copyProperties(fcCarbonFactorHkVO, fcCarbonFactorHk);
        return fcCarbonFactorHk;
    }

    public static FcCarbonFactorHk convertToModelWithBase(
            FcCarbonFactorHkVO fcCarbonFactorHkVO,
            FcCarbonFactorHk fcCarbonFactorHk) {
        BeanUtils.copyProperties(fcCarbonFactorHkVO, fcCarbonFactorHk);
        return fcCarbonFactorHk;
    }

}
