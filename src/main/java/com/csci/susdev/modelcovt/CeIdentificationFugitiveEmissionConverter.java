package com.csci.susdev.modelcovt;

import com.csci.susdev.model.CeIdentificationFugitiveEmission;
import com.csci.susdev.vo.CeIdentificationFugitiveEmissionVO;
import org.springframework.beans.BeanUtils;

public class CeIdentificationFugitiveEmissionConverter {

    public static CeIdentificationFugitiveEmissionVO convertToVO(CeIdentificationFugitiveEmission ceIdentificationFugitiveEmission) {
        CeIdentificationFugitiveEmissionVO ceIdentificationFugitiveEmissionVO = new CeIdentificationFugitiveEmissionVO();
        BeanUtils.copyProperties(ceIdentificationFugitiveEmission, ceIdentificationFugitiveEmissionVO);
        return ceIdentificationFugitiveEmissionVO;
    }

    public static CeIdentificationFugitiveEmission convertToModel(CeIdentificationFugitiveEmissionVO ceIdentificationFugitiveEmissionVO) {
        CeIdentificationFugitiveEmission ceIdentificationFugitiveEmission = new CeIdentificationFugitiveEmission();
        BeanUtils.copyProperties(ceIdentificationFugitiveEmissionVO, ceIdentificationFugitiveEmission);
        return ceIdentificationFugitiveEmission;
    }

    public static CeIdentificationFugitiveEmission convertToModelWithBase(
            CeIdentificationFugitiveEmissionVO ceIdentificationFugitiveEmissionVO,
            CeIdentificationFugitiveEmission ceIdentificationFugitiveEmission) {
        BeanUtils.copyProperties(ceIdentificationFugitiveEmissionVO, ceIdentificationFugitiveEmission);
        return ceIdentificationFugitiveEmission;
    }

}
