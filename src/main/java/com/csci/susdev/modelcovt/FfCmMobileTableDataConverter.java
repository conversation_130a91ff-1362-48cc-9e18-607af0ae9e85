package com.csci.susdev.modelcovt;

import com.csci.susdev.model.FfCmMobileDetail;
import com.csci.susdev.vo.FfCmMobileTableDataVO;
import org.springframework.beans.BeanUtils;

public class FfCmMobileTableDataConverter {

    public static FfCmMobileTableDataVO convert(FfCmMobileDetail ffCmMobileDetail) {
        FfCmMobileTableDataVO ffCmMobileTableDataVO = new FfCmMobileTableDataVO();
        BeanUtils.copyProperties(ffCmMobileDetail, ffCmMobileTableDataVO);
        return ffCmMobileTableDataVO;
    }

    public static FfCmMobileDetail convert(FfCmMobileTableDataVO ffCmMobileTableDataVO) {
        FfCmMobileDetail ffCmMobileDetail = new FfCmMobileDetail();
        BeanUtils.copyProperties(ffCmMobileTableDataVO, ffCmMobileDetail);
        return ffCmMobileDetail;
    }

}
