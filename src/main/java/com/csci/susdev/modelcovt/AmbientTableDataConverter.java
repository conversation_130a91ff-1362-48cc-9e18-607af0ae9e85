package com.csci.susdev.modelcovt;

import com.csci.susdev.model.AmbientDetail;
import com.csci.susdev.vo.AmbientTableDataExtVO;
import com.csci.susdev.vo.AmbientTableDataVO;
import org.springframework.beans.BeanUtils;

public class AmbientTableDataConverter {

    public static AmbientDetail convert(AmbientTableDataExtVO ambientTableDataExtVO) {
        AmbientDetail ambientDetail = new AmbientDetail();
        BeanUtils.copyProperties(ambientTableDataExtVO, ambientDetail);
        return ambientDetail;
    }

    public static AmbientTableDataVO convert(AmbientDetail ambientDetail) {
        AmbientTableDataVO ambientTableDataVO = new AmbientTableDataVO();
        BeanUtils.copyProperties(ambientDetail, ambientTableDataVO);
        return ambientTableDataVO;
    }

    public static AmbientDetail convert(AmbientTableDataVO ambientTableDataVO) {
        AmbientDetail ambientDetail = new AmbientDetail();
        BeanUtils.copyProperties(ambientTableDataVO, ambientDetail);
        return ambientDetail;
    }

}
