package com.csci.susdev.modelcovt;

import com.csci.susdev.constant.ApproveStatus;
import com.csci.susdev.model.CeIdentificationHead;
import com.csci.susdev.vo.CeIdentificationHeadVO;
import org.springframework.beans.BeanUtils;

public class CeIdentificationHeadConverter {

    static String getApproveStatusName(Integer approveStatus) {
        ApproveStatus[] approveStatuses = ApproveStatus.values();
        for (ApproveStatus status : approveStatuses) {
            if (status.getCode().equals(approveStatus)) {
                return status.getName();
            }
        }
        return "未知状态";
    }

    public static CeIdentificationHeadVO convert(CeIdentificationHead source) {
        CeIdentificationHeadVO ceIdentificationHeadVO = new CeIdentificationHeadVO();
        BeanUtils.copyProperties(source, ceIdentificationHeadVO);
        return ceIdentificationHeadVO;
    }

    public static CeIdentificationHead convert(CeIdentificationHeadVO source) {
        CeIdentificationHead ceIdentificationHead = new CeIdentificationHead();
        BeanUtils.copyProperties(source, ceIdentificationHead);
        return ceIdentificationHead;
    }

}
