package com.csci.susdev.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.csci.common.util.CustomGsonBuilder;
import com.csci.susdev.mapper.SocialPerfTwoDetailMapper;
import com.csci.susdev.model.SocialPerfTwoDetail;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.SocialPerfTwoDetailVO;
import com.csci.susdev.vo.SocialPerfTwoImportDataVO;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.util.Objects;

public class SocialPerfTwoListener implements ReadListener<SocialPerfTwoImportDataVO> {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(SocialPerfTwoListener.class);

    private final Gson gson = CustomGsonBuilder.createGson();
    private final String headId;

    private SocialPerfTwoDetail sample;

    private int seq = 1;

    public SocialPerfTwoListener(String headId) {
        this.headId = headId;
    }

    @Override
    public void invoke(SocialPerfTwoImportDataVO data, AnalysisContext context) {
        logger.info("解析到一条数据:{}", gson.toJson(data));

        if (Objects.equals(data.getHeader(), Boolean.TRUE)) {
            // 遇到内部表头，存入样例数据
            sample = new SocialPerfTwoDetail();
            BeanUtils.copyProperties(data, sample);
            sample.setHeadId(headId);
        }

        SocialPerfTwoDetail record = new SocialPerfTwoDetailVO();
        BeanUtils.copyProperties(data, record);
        record.setHeadId(headId);
        record.setCategory(sample.getCategory());
        if (StringUtils.isNotBlank(sample.getReportItem())) {
            record.setReportItem(sample.getReportItem());
        }
        record.setSeq(seq++);

        SocialPerfTwoDetailMapper socialPerfTwoDetailMapper = SpringContextUtil.getBean(SocialPerfTwoDetailMapper.class);
        socialPerfTwoDetailMapper.insertSelective(record);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
    }

}
