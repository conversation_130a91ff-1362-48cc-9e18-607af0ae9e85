package com.csci.susdev.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.csci.common.util.CustomGsonBuilder;
import com.csci.susdev.model.SocialPerformanceDetail;
import com.csci.susdev.modelcovt.SocialPerfImportConverter;
import com.csci.susdev.service.SocialPerformanceService;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.SocialPerformanceImportDataVO;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SocialPerfImportListener implements ReadListener<SocialPerformanceImportDataVO> {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(SocialPerfImportListener.class);

    private static final Gson gson = CustomGsonBuilder.createGson();

    private String headId;

    private int seq = 0;

    public SocialPerfImportListener(String headId) {
        this.headId = headId;
    }

    @Override
    public void invoke(SocialPerformanceImportDataVO data, AnalysisContext context) {
        SocialPerformanceService socialPerformanceService = SpringContextUtil.getBean(SocialPerformanceService.class);
        try {
            SocialPerformanceDetail detail = new SocialPerfImportConverter().revert(data);
            detail.setHeadId(headId);
            detail.setSeq(seq++);
            socialPerformanceService.insertDetailSelective(detail);
        } catch (Exception e) {
            logger.error("插入数据失败: {}", gson.toJson(data), e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }
}
