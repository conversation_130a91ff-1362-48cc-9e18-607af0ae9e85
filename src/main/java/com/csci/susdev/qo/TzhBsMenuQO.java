package com.csci.susdev.qo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@SuperBuilder
@NoArgsConstructor
public class TzhBsMenuQO implements Serializable {

    /**
     * 路径url
     */
    @NotBlank(message = "路径url不能为空")
    private String path;
    private String pageName;
    private String name;
    /**
     * 前端组件
     */
    @NotBlank(message = "前端组件不能为空")
    private String component;
    /**
     * 逻辑删除 1->删除
     */
    private Boolean isDeleted;
    /**
     * 英文名
     */
    @NotBlank(message = "英文名不能为空")
    private String titleEN;
    /**
     * 简体中文名
     */
    @NotBlank(message = "简体中文名不能为空")
    private String titleSC;

    @NotBlank(message = "繁体中文名不能为空")
    @Schema(description = "繁体中文名")
    private String title;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 标签类别 0->通用模板 1->自定义模板
     */
    @Schema(title ="标签类别",type = "integer",example = "1",description = "标签类别 0->通用模板 1->自定义模板",minimum = "0")
    @NotNull(message = "标签类别不能为空")
    private Integer type;

    /**
     * ESG:1,碳中和:2
     */
    @Schema(description ="模块",type = "integer",example = "1")
    @NotNull(message = "模块不能为空")
    private Integer module;

}
