package com.csci.susdev.qo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "同步传参对象")
public class SyncAmbientEnergyBillQO {

    @Schema(description = "当前月份环境绩效的ID")
	private String headId;
    @Schema(description = "组织架构ID")
    private String organizationId;
    @Schema(description = "要同步的年份")
    private Integer year;
    @Schema(description = "要同步的月份")
    private Integer month;
}
