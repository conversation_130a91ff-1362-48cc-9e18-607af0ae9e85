package com.csci.susdev.qo;

import com.csci.common.model.PageableVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ProtocolManagementQO extends PageableVO {

    @Schema(description = "协议ID")
    private String protocolId;
    @Schema(description = "大类ID")
    private String categoryId;
    @Schema(description = "小类ID")
    private String subCategoryId;


    private String orderBy;

    private Map<String, List> filter;
}
