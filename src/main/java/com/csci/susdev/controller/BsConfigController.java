package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.BsConfigQO;
import com.csci.susdev.service.BsConfigService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.BsConfigVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/bsConfig", produces = "application/json")
@Tag(name = "大屏配置 接口", description = "用于接口调试")
@LogMethod
public class BsConfigController {

    @Resource
    private BsConfigService bsConfigService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody BsConfigVO bsConfigVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(bsConfigService.saveBsConfig(bsConfigVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<BsConfigVO> listBsConfig(@RequestBody BsConfigQO bsConfigQO) {
        return bsConfigService.listBsConfig(bsConfigQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteBsConfig(@PathVariable String id) {
        userService.checkIsReadOnly();

        bsConfigService.deleteBsConfig(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<BsConfigVO> getBsConfig(@PathVariable String id) {
        return new ResultBean<>(bsConfigService.getBsConfig(id));
    }
}
