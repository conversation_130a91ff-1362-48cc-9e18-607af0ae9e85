package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.*;
import com.csci.susdev.qo.*;
import com.csci.susdev.service.*;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.io.IOException;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = "/api/permissionMenu", produces = "application/json")
@Tag(name = "權限菜單關聯 接口展示", description = "用于接口调试")
@LogMethod
public class PermissionMenuController {

    @Autowired
    private PermissionMenuService permissionMenuService;

    @Resource
    private UserService userService;
    
    /**
     * 查找 權限菜單關聯 数据列表
     *
     * @param permissionname
     * @return
     */
    @GetMapping("/list")
    @Operation(description = "分页查询 權限菜單關聯 统计数据列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public List<PermissionMenu> list(String permissionname) {
        return permissionMenuService.listPermissionMenuByCode(permissionname);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 權限菜單關聯 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody PermissionMenu permissionMenu) {
        userService.checkIsReadOnly();

        return new ResultBean<>(permissionMenuService.savePermissionMenu(permissionMenu));
    }
    
    @PostMapping("/savelist")
    @Operation(description = "保存 權限菜單關聯 記錄列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<String>> savelist(@RequestBody List<PermissionMenu> permissionMenuLst) {
        userService.checkIsReadOnly();

        return new ResultBean<>(permissionMenuService.savePermissionMenuList(permissionMenuLst));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(description = "刪除 權限菜單關聯 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Integer> delete(@PathVariable String id) {
        userService.checkIsReadOnly();

        return new ResultBean<>(Integer.valueOf(permissionMenuService.deletePermissionMenu(id)));
    }
}
