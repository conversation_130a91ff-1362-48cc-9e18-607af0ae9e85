package com.csci.susdev.controller;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.facade.WorkflowFacade;
import com.csci.susdev.model.*;
import com.csci.susdev.qo.SocialPerfTwoQO;
import com.csci.susdev.service.*;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.vo.ApproveParamVO;
import com.csci.susdev.vo.SocialPerfTwoHeadVO;
import com.csci.susdev.vo.SocialPerfTwoTableDataVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@RestController
@RequestMapping("/api/social-perf-two")
@Tag(name = "社会绩效2", description = "用于接口调试")
@LogMethod
public class SocialPerfTwoController {

    private static String formId;
    @Resource
    private SocialPerfTwoHeadService socialPerfTwoHeadService;

    @Resource
    private UserService userService;
    @Resource
    private SocialPerfTwoDetailService socialPerfTwoDetailService;
    @Resource
    private WorkflowFacade workflowFacade;
    @Resource
    private WorkflowService workflowService;
    @Resource
    private WorkflowControlService workflowControlService;
    @Resource
    private FormService formService;

    @PostMapping("/head")
    @Operation(description = "获取社会绩效2")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<SocialPerfTwoHeadVO> getSocialPerfTwoHead(@RequestBody SocialPerfTwoQO socialPerfTwoQO) {
        // 默认使用1月份数据；由于现在确定社会绩效按年度填写， 因此默认使用一月份；保留月份设计，以便后续扩展
        return new ResultBean<>(socialPerfTwoHeadService.getOrInitSocialPerfTwo(
                socialPerfTwoQO.getOrganizationId(), socialPerfTwoQO.getYear(), socialPerfTwoQO.getMonth()));
    }

    @GetMapping("/list-detail")
    @Operation(description = "获取社会绩效2明细")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<SocialPerfTwoTableDataVO> listSocialPerfTwoDetail(String headId) {
        return new ResultList<>(socialPerfTwoDetailService.listSocialPerfTwoTableData(headId));
    }

    @PostMapping("/update")
    @Operation(description = "更新社会绩效2")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase updateSocialPerfTwo(@RequestBody SocialPerfTwoHeadVO socialPerfTwoHeadVO) {
        userService.checkIsReadOnly();

        socialPerfTwoHeadService.updateSocialPerfTwo(socialPerfTwoHeadVO);
        return ResultBase.success();
    }

    @PostMapping("/approve")
    @Operation(description = "审批社会绩效")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase approve(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "社会绩效id不能为空");

        SocialPerfTwoHead socialPerfTwoHead = socialPerfTwoHeadService.selectByPrimaryKey(approveParamVO.getId());

        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(approveParamVO.getId());
        if (Objects.isNull(workflowControl)) {
            workflowFacade.startWorkflow(socialPerfTwoHead.getOrganizationId(), getFormId(), approveParamVO.getId());
        } else {
            workflowFacade.approve(approveParamVO.getId(), approveParamVO.getRemark());
        }
        workflowControlService.sendReviewEmail(approveParamVO.getId());
        return new ResultBase();
    }

    @PostMapping("/reject")
    @Operation(description = "驳回社会绩效")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase reject(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "社会绩效id不能为空");
        SocialPerfTwoHead head = socialPerfTwoHeadService.selectByPrimaryKey(approveParamVO.getId());

        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(head.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");

        workflowFacade.reject(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    @PostMapping("/terminate")
    @Operation(description = "终止社会绩效")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase terminate(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "社会绩效id不能为空");
        SocialPerfTwoHead head = socialPerfTwoHeadService.selectByPrimaryKey(approveParamVO.getId());

        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(head.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");

        workflowFacade.terminate(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    private String getFormId() {
        if (StringUtils.isBlank(formId)) {
            Form form = formService.findFormByCode("sociology-index-two");
            formId = Optional.ofNullable(form).map(Form::getId).orElseThrow(() -> new ServiceException("未找到表单"));
        }
        return formId;
    }
}
