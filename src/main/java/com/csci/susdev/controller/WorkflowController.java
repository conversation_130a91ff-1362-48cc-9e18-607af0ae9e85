package com.csci.susdev.controller;

import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.facade.WorkflowFacade;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.WorkflowControlQO;
import com.csci.susdev.qo.WorkflowQO;
import com.csci.susdev.service.UserService;
import com.csci.susdev.service.WorkflowControlService;
import com.csci.susdev.service.WorkflowService;
import com.csci.susdev.vo.ApproveParamVO;
import com.csci.susdev.vo.WorkFlowControlUrgentEditableVO;
import com.csci.susdev.vo.WorkflowControlVO;
import com.csci.susdev.vo.WorkflowVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/api/workflow", produces = "application/json")
@Tag(name = "工作流 接口展示", description = "用于接口调试")
public class WorkflowController {

    @Resource
    private WorkflowService workflowService;

    @Resource
    private UserService userService;

    @Resource
    private WorkflowFacade workflowFacade;

    @Resource
    private WorkflowControlService workflowControlService;

    @GetMapping("/list")
    @Operation(description = "分页查询工作流列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<WorkflowVO> listWorkflow(WorkflowQO workflowQO) {
        return workflowService.listWorkflow(workflowQO);
    }

    @GetMapping("/get/{id}")
    @Operation(description = "根据id获取工作流")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<WorkflowVO> getWorkflow(@PathVariable String id) {
        return new ResultBean<>(workflowService.findWorkflowById(id));
    }

    @PostMapping("/save")
    @Operation(description = "保存工作流")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> saveWorkflow(@RequestBody WorkflowVO workflowVO) {
        userService.checkIsReadOnly();
        return new ResultBean<>(workflowFacade.saveWorkflow(workflowVO));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(description = "删除工作流")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Integer> deleteWorkflow(@PathVariable String id) {
        userService.checkIsReadOnly();
        return new ResultBean<>(workflowService.inactiveWorkflow(id));
    }

    /*@PostMapping("/start")
    @Operation(description = "发起审批流程")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase startWorkflow(@RequestBody ApproveVO approveVO) {
        workflowFacade.startWorkflow(approveVO.getOrganizationId(), approveVO.getFormId(), approveVO.getBusinessId());
        return ResultBase.success();
    }

    @PostMapping("/approve")
    @Operation(description = "审批流程")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase approve(@RequestBody ApproveVO approveVO) {
        workflowFacade.approve(approveVO);
        return ResultBase.success();
    }

    @PostMapping("/reject")
    @Operation(description = "驳回流程")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase reject(@RequestBody ApproveVO approveVO) {
        workflowFacade.reject(approveVO);
        return ResultBase.success();
    }

    @PostMapping("/terminate")
    @Operation(description = "终止流程")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase terminate(@RequestBody ApproveVO approveVO) {
        workflowFacade.terminate(approveVO);
        return ResultBase.success();
    }*/

    @GetMapping("/control/notification/list")
    @Operation(description = "查询工作流控制列表(供提示消息用)")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<WorkflowControlVO>> listWorkflowControlForNotification() {
        return new ResultBean<>(workflowControlService.listWorkflowControlVOByCurrentUser());
    }

    @GetMapping("/control/list")
    @Operation(description = "分页查询工作流控制列表(供流程記錄用)")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<WorkflowControlVO> listWorkflowControl(WorkflowControlQO workflowControlQO) {
        return workflowControlService.listWorkflowControlByPage(workflowControlQO);
    }

    @GetMapping("/control/list/exact")
    @Operation(description = "分页查询工作流控制列表(供流程記錄用)")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<WorkflowControlVO> listWorkflowControlExact(WorkflowControlQO workflowControlQO) {
        return workflowControlService.listWorkflowControlExactByPage(workflowControlQO);
    }

    @GetMapping("/control/get")
    @Operation(description = "根据业务id获取工作流控制记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<WorkflowControlVO> getWorkflowControl(String businessId) {
        return new ResultBean<>(workflowControlService.getWorkflowControlByBusinessId(businessId));
    }

    @PostMapping("/control/setUrgentEditable")
    @Operation(description = "設置允許緊急修改狀態")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Integer> saveWorkflowControl(@RequestBody WorkFlowControlUrgentEditableVO vo) {
        userService.checkIsReadOnly();
        return new ResultBean<>(workflowControlService.setUrgentEditable(vo));
    }

    @PostMapping("/control/recall")
    @Operation(description = "撤回工作流控制记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> recallWorkflowControl(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();
        workflowFacade.recall(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBean<>();
    }

    @GetMapping("/control/stateSummary")
    @Operation(description = "流程狀態匯總")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Map<String, Long>> listWorkflowControlStateSummary(WorkflowControlQO workflowControlQO) {
        return new ResultBean<>(workflowControlService.listWorkflowControlStateSummary(workflowControlQO));
    }

    @GetMapping("/control/getLatestDate")
    @Operation(description = "获取审核设置最新配置年月")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> getLatestDate() {
        userService.checkIsReadOnly();
        return new ResultBean<>(workflowService.getLatestDate());
    }
}
