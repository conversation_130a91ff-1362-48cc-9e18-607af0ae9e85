package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.*;
import com.csci.susdev.qo.*;
import com.csci.susdev.service.*;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.vo.FcAirPollutionFactorEsgDatasetVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/form", produces = "application/json")
@Tag(name = "表格 接口展示", description = "用于接口调试")
@LogMethod
public class FormController {

    @Autowired
    private FormService formService;

    @Resource
    private UserService userService;

    private class ResultPageOfForm extends ResultPage<Form>{
		public ResultPageOfForm(List<?> page) {
			super(page);
		}
	}

    /**
     * 查找 表格 数据列表
     *
     * @param qo
     * @return
     */
    @GetMapping("/list")
    @Operation(description = "分页查询 表格 统计数据列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfForm.class)))
    public ResultPage<Form> listForm(FormQO qo) {
        return formService.listForm(qo);
    }


    @PostMapping("/list")
    @Operation(description = "分页查询 表格 统计数据列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<Form> listFormPost(@RequestBody FormQO qo) {
        return formService.listForm(qo);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 表格 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody Form form) {
        userService.checkIsReadOnly();

        return new ResultBean<>(formService.saveForm(form));
    }
    
    @PostMapping("/savelist")
    @Operation(description = "保存 表格 記錄列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<String>> savelist(@RequestBody List<Form> formLst) {
        userService.checkIsReadOnly();

        return new ResultBean<>(formService.saveFormList(formLst));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(description = "刪除 表格 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Integer> delete(@PathVariable String id) {
        userService.checkIsReadOnly();

        return new ResultBean<>(formService.deleteForm(id));
    }
}
