package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.ProtocolQO;
import com.csci.susdev.service.ProtocolService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.ProtocolVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/protocol", produces = "application/json")
@Tag(name = "協議 接口", description = "用于接口调试")
@LogMethod
public class ProtocolController {

    @Resource
    private ProtocolService protocolService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody ProtocolVO protocolVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(protocolService.saveProtocol(protocolVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<ProtocolVO> listProtocol(@RequestBody ProtocolQO protocolQO) {
        return protocolService.listProtocol(protocolQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteProtocol(@PathVariable String id) {
        userService.checkIsReadOnly();

        protocolService.deleteProtocol(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<ProtocolVO> getProtocol(@PathVariable String id) {
        return new ResultBean<>(protocolService.getProtocol(id));
    }
}
