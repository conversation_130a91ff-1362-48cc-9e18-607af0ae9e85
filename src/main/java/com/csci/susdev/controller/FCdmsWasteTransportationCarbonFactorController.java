package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.service.FCdmsWasteTransportationCarbonFactorService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.model.FCdmsWasteTransportationCarbonFactor;
import com.csci.tzh.qo.FCdmsWasteTransportationCarbonFactorPageableQO;
import com.csci.tzh.vo.FCdmsWasteTransportationCarbonFactorVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/tzh/f-cdms-waste-transportation-carbon-factor", produces = "application/json")
@Tag(name = "碳排放因子 接口展示", description = "用于接口调试")
@LogMethod
public class FCdmsWasteTransportationCarbonFactorController {

    @Autowired
    private FCdmsWasteTransportationCarbonFactorService fCdmsWasteTransportationCarbonFactorService;

    @Resource
    private UserService userService;

    private class ResultPageOfModel extends ResultPage<FCdmsWasteTransportationCarbonFactorVO>{
		public ResultPageOfModel(List<?> page) {
			super(page);
		}
	}
    
    @GetMapping("/list")
    @Operation(description = "查詢 廢物運輸 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfModel.class)))
    public ResultPage<FCdmsWasteTransportationCarbonFactorVO> list(FCdmsWasteTransportationCarbonFactorPageableQO dCdmsMaterialCarbonFactorQO) {
        return fCdmsWasteTransportationCarbonFactorService.list(dCdmsMaterialCarbonFactorQO);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 廢物運輸 所有記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<FCdmsWasteTransportationCarbonFactor> save(@RequestBody FCdmsWasteTransportationCarbonFactor fCdmsWasteTransportationCarbonFactor) {
        userService.checkIsReadOnly();

        return new ResultBean<>(fCdmsWasteTransportationCarbonFactorService.save(fCdmsWasteTransportationCarbonFactor));
    }

    @PostMapping("/save/all")
    @Operation(description = "保存 廢物運輸 所有記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<FCdmsWasteTransportationCarbonFactor>> saveAll(@RequestBody List<FCdmsWasteTransportationCarbonFactor> listFCdmsWasteTransportationCarbonFactor) {
        userService.checkIsReadOnly();


        return new ResultBean<>(fCdmsWasteTransportationCarbonFactorService.saveAll(listFCdmsWasteTransportationCarbonFactor));
    }
}
