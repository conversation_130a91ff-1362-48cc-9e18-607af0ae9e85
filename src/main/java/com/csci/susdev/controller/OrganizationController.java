package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.Organization;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultList;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.KeywordQO;
import com.csci.susdev.qo.OrganizationMoveQO;
import com.csci.susdev.qo.OrganizationPageableQO;
import com.csci.susdev.qo.OrganizationSearchQO;
import com.csci.susdev.service.OrganizationService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.vo.CompanyDeptVO;
import com.csci.susdev.vo.CompanyPlatformVO;
import com.csci.susdev.vo.CompanyVO;
import com.csci.susdev.vo.OrganizationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/organization", produces = "application/json")
@Tag(name = "組織 接口展示", description = "用于接口调试")
@LogMethod
public class OrganizationController {

    /** 日志记录对象 */
    private static final Logger logger = LoggerFactory.getLogger(OrganizationController.class);

    @Autowired
    private OrganizationService organizationService;

    @Resource
    private UserService userService;

    @GetMapping("/list")
    @Operation(description = "分页查询 組織 统计数据列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<OrganizationVO> list(OrganizationPageableQO organizationQO) {
        return organizationService.listOrganization(organizationQO);
    }

    @GetMapping("/list-all")
    @Operation(description = "根据关键字分页查询组织机构数据列表，忽略权限")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<CompanyDeptVO> listAll(KeywordQO keywordQO) {
        return organizationService.listCompanyDeptByPage(keywordQO);
    }

    @PostMapping("/move-org")
    @Operation(description = "移動 組織")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean moveOrg(@RequestBody OrganizationMoveQO organizationMoveQO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(organizationService.moveOrg(organizationMoveQO));
    }

    @PostMapping("/save")
    @Operation(description = "保存 組織 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody OrganizationVO organizationVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(organizationService.saveOrganization(organizationVO));
    }

    @PostMapping("/savelist")
    @Operation(description = "保存 組織 記錄列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<String>> savelist(@RequestBody List<OrganizationVO> organizationLst) {
        userService.checkIsReadOnly();

        return new ResultBean<>(organizationService.saveOrganizationList(organizationLst));
    }

    @DeleteMapping("/deletetree/{no}")
    @Operation(description = "刪除 組織 記錄樹(所有子類及子類的子類)")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<String>> deletelistAndChilds(@PathVariable String no) {
        userService.checkIsReadOnly();

        return new ResultBean<>(organizationService.deleteOrganizationTree(no));
    }

    @GetMapping("/list-my-orgs")
    @Operation(description = "查詢當前用戶所屬組織")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<CompanyDeptVO> listMyOrganization(OrganizationSearchQO qo) {
        return organizationService.listCurrentUsersOrganization(qo);
    }

    @GetMapping("/is-leaf/{organizationId}")
    @Operation(description = "判断組織是否是叶子节点")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Boolean> isOrganizationLeaf(@PathVariable String organizationId) {
        return new ResultBean<>(organizationService.isOrganizationLeaf(organizationId));
    }

    @GetMapping("/list-my-dept")
    @Operation(description = "查詢當前用戶所屬部門")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<OrganizationVO> listMyDept() {
        return new ResultList<>(organizationService.listDeptByUserId(ContextUtils.getCurrentUser().getId()));
    }

    @GetMapping("/list-by-parent")
    @Operation(description = "根据父节点查詢組織")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<OrganizationVO> listByParentId(String parentId, Integer curPage, Integer pageSize) {
        return organizationService.listOrgByParentId(parentId, curPage, pageSize);
    }

    @GetMapping("/{id}")
    @Operation(description = "根据組織編號查詢組織")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<OrganizationVO> getOrganization(@PathVariable String id) {
        return new ResultBean<>(organizationService.getOrganizationById(id));
    }

    @GetMapping("/list-top-companies")
    @Operation(description = "查詢所有顶级公司")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<CompanyVO> listTopCompanies() {
        userService.checkIsReadOnly();

        return new ResultList<>(organizationService.listTopCompanies());
    }

    @GetMapping("/list-fuzzy-search")
    @Operation(description = "模糊搜索分页查询组织机构数据列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<CompanyPlatformVO> listFuzzySearch(KeywordQO keywordQO) {
        return organizationService.listFuzzySearch(keywordQO);
    }

}
