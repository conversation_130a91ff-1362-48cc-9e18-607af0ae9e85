package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.FcEnergyFactorQO;
import com.csci.susdev.service.FcEnergyFactorService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.FcEnergyFactorVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/fcEnergyFactor", produces = "application/json")
@Tag(name = "因子管理-能源因子 接口", description = "用于接口调试")
@LogMethod
public class FcEnergyFactorController {

    @Resource
    private FcEnergyFactorService fcEnergyFactorService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody FcEnergyFactorVO fcEnergyFactorVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(fcEnergyFactorService.saveFcEnergyFactor(fcEnergyFactorVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<FcEnergyFactorVO> listFcEnergyFactor(@RequestBody FcEnergyFactorQO fcEnergyFactorQO) {
        return fcEnergyFactorService.listFcEnergyFactor(fcEnergyFactorQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteFcEnergyFactor(@PathVariable String id) {
        userService.checkIsReadOnly();

        fcEnergyFactorService.deleteFcEnergyFactor(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<FcEnergyFactorVO> getFcEnergyFactor(@PathVariable String id) {
        return new ResultBean<>(fcEnergyFactorService.getFcEnergyFactor(id));
    }
}
