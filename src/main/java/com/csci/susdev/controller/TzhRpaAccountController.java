package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.service.TzhRpaAccountService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.model.TzhRpaAccount;
import com.csci.tzh.qo.TzhRpaAccountPageableQO;
import com.csci.tzh.vo.TzhRpaAccountVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/tzh/tzh-rpa-account", produces = "application/json")
@Tag(name = "RPA帳號 接口展示", description = "用于接口调试")
@LogMethod
public class TzhRpaAccountController {

    @Autowired
    private TzhRpaAccountService tzhRpaAccountService;

    @Resource
    private UserService userService;

    private class ResultPageOfTzhRpaAccount extends ResultPage<TzhRpaAccount>{
		public ResultPageOfTzhRpaAccount(List<?> page) {
			super(page);
		}
	}
    
    @GetMapping("/list")
    @Operation(description = "查詢 RPA帳號 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfTzhRpaAccount.class)))
    public ResultPage<TzhRpaAccountVO> list(TzhRpaAccountPageableQO qo) {
        return tzhRpaAccountService.list(qo);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 RPA帳號 所有記錄, 如沒有記錄則保存")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<TzhRpaAccount> save(@RequestBody TzhRpaAccount tzhRpaAccount) {
        userService.checkIsReadOnly();

        return new ResultBean<>(tzhRpaAccountService.save(tzhRpaAccount));
    }
}
