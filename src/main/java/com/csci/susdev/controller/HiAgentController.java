package com.csci.susdev.controller;

import com.csci.common.model.ResultBean;
import com.csci.susdev.annotation.NoApiLogging;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.facade.HiAgentFacade;
import com.csci.susdev.model.AiAgentConversation;
import com.csci.susdev.service.AiAgentConversationService;
import com.csci.susdev.vo.AiAgentConversationVO;
import com.csci.susdev.vo.AiAgentOpenQuestionVO;
import com.csci.susdev.vo.HiAgentParamVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/api/hi-agent")
@Tag(name = "HiAgentController", description = "HiAgent接口")
public class HiAgentController {

    @Resource
    private HiAgentFacade hiAgentFacade;
    @Resource
    private AiAgentConversationService aiAgentConversationService;

    @PostMapping("/getAppConfig")
    @Operation(summary = "获取应用配置", description = "获取应用配置")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> getAppConfig(@RequestBody HiAgentParamVO vo) {
        return new ResultBean<>(hiAgentFacade.getAppConfig(vo));
    }

    @PostMapping("/createConversation")
    @Operation(summary = "创建会话", description = "创建会话")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> createConversation(@RequestBody HiAgentParamVO vo) {
        String conversation = hiAgentFacade.createConversation(vo);
        aiAgentConversationService.saveConversation(conversation);
        return new ResultBean<>(conversation);
    }

    @PostMapping("/chatQuery")
    @Operation(summary = "聊天查询", description = "聊天查询")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> chatQuery(@RequestBody HiAgentParamVO vo) {
        return new ResultBean<>(hiAgentFacade.chatQuery(vo));
    }

    @NoApiLogging
    @PostMapping(value = "/chatQueryStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "聊天查询(流式响应)", description = "聊天查询，返回流式响应")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public SseEmitter chatQueryStream(@RequestBody HiAgentParamVO vo) {
        return hiAgentFacade.chatQueryStream(vo);
    }

    @PostMapping("/queryAgain")
    @Operation(summary = "重新生成聊天查询", description = "重新生成聊天查询")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> queryAgain(@RequestBody HiAgentParamVO vo) {
        return new ResultBean<>(hiAgentFacade.queryAgain(vo));
    }

    @NoApiLogging
    @PostMapping(value = "/queryAgainStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "重新生成聊天查询(流式响应)", description = "重新生成聊天查询，返回流式响应")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public SseEmitter queryAgainStream(@RequestBody HiAgentParamVO vo) {
        return hiAgentFacade.queryAgainStream(vo);
    }

    @PostMapping("/getSuggestedQuestions")
    @Operation(summary = "获取建议问题", description = "获取建议问题")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> getSuggestedQuestions(@RequestBody HiAgentParamVO vo) {
        return new ResultBean<>(hiAgentFacade.getSuggestedQuestions(vo));
    }

    @PostMapping("/getMessageInfo")
    @Operation(summary = "获取消息内容", description = "获取消息内容")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> getMessageInfo(@RequestBody HiAgentParamVO vo) {
        return new ResultBean<>(hiAgentFacade.getMessageInfo(vo));
    }

    @PostMapping("/getConversationMessages")
    @Operation(summary = "获取会话消息", description = "获取会话消息")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> getConversationMessages(@RequestBody HiAgentParamVO vo) {
        return new ResultBean<>(hiAgentFacade.getConversationMessages(vo));
    }


    @PostMapping("/getConversationList")
    @Operation(summary = "获取会话列表", description = "获取会话列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<AiAgentConversation>> getConversationList(@RequestBody HiAgentParamVO vo) {
        return new ResultBean<>(aiAgentConversationService.getConversationList(vo));
    }

    @PostMapping("/updateConversationName")
    @Operation(summary = "更新会话名称", description = "更新会话名称")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> updateConversationName(@RequestBody AiAgentConversationVO vo) {
        return new ResultBean<>(aiAgentConversationService.updateConversationName(vo));
    }

    @PostMapping("/batchRemoveConversation")
    @Operation(summary = "批量删除", description = "批量删除")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> batchRemoveConversation(@RequestBody AiAgentConversationVO vo) {
        return new ResultBean<>(aiAgentConversationService.batchRemoveConversation(vo));
    }


    @GetMapping("/queryOpenQuestion")
    @Operation(summary = "获取开场问题", description = "获取开场问题")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<AiAgentOpenQuestionVO>> queryOpenQuestion() {
        return new ResultBean<>(aiAgentConversationService.queryOpenQuestion());
    }

    @PostMapping("/stopMessage")
    @Operation(summary = "停止响应", description = "停止响应")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> stopMessage(@RequestBody HiAgentParamVO vo) {
        return new ResultBean<>(hiAgentFacade.stopMessage(vo));
    }


}
