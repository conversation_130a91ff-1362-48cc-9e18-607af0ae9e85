package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.model.*;
import com.csci.tzh.qo.*;
import com.csci.susdev.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.io.IOException;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/api/tzh/sysorganization", produces = "application/json")
@Tag(name = "組織信息 接口展示", description = "用于接口调试")
@LogMethod
public class SysOrganizationController {

    @Autowired
    private SysOrganizationService sysOrganizationService;

    @Resource
    private UserService userService;

    private class ResultPageOfSysOrganization extends ResultPage<SysOrganization>{
		public ResultPageOfSysOrganization(List<?> page) {
			super(page);
		}
	}
    
    @GetMapping("/list")
    @Operation(description = "查詢 組織信息 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfSysOrganization.class)))
    public ResultPage<SysOrganization> listSysOrganization(SysOrganizationPageableQO sysOrganizationQO) {
        return sysOrganizationService.listSysOrganization(sysOrganizationQO);
    }

    @GetMapping("/name/list")
    @Operation(description = "查詢 組織信息-名稱 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfSysOrganization.class)))
    public ResultPage<String> nameListSysOrganization(SysOrganizationPageableQO sysOrganizationQO) {
        return sysOrganizationService.nameListSysOrganization(sysOrganizationQO);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 組織信息 所有記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<SysOrganization> save(@RequestBody SysOrganization sysOrganization) {
        userService.checkIsReadOnly();

        return new ResultBean<>(sysOrganizationService.saveSysOrganization(sysOrganization));
    }

    @PostMapping("/create")
    @Operation(description = "新增 組織信息 所有記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<SysOrganization> create(@RequestBody SysOrganization sysOrganization) {
        userService.checkIsReadOnly();

        return new ResultBean<>(sysOrganizationService.createSysOrganization(sysOrganization));
    }
}
