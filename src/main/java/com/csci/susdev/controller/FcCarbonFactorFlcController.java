package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.FcCarbonFactorFlcQO;
import com.csci.susdev.service.FcCarbonFactorFlcService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.FcCarbonFactorFlcVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/fcCarbonFactorFlc", produces = "application/json")
@Tag(name = "碳排因子+全生命周期数据集 接口", description = "用于接口调试")
@LogMethod
public class FcCarbonFactorFlcController {

    @Resource
    private FcCarbonFactorFlcService fcCarbonFactorFlcService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody FcCarbonFactorFlcVO fcCarbonFactorFlcVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(fcCarbonFactorFlcService.saveFcCarbonFactorFlc(fcCarbonFactorFlcVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<FcCarbonFactorFlcVO> listFcCarbonFactorFlc(@RequestBody FcCarbonFactorFlcQO fcCarbonFactorFlcQO) {
        return fcCarbonFactorFlcService.listFcCarbonFactorFlc(fcCarbonFactorFlcQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteFcCarbonFactorFlc(@PathVariable String id) {
        userService.checkIsReadOnly();

        fcCarbonFactorFlcService.deleteFcCarbonFactorFlc(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<FcCarbonFactorFlcVO> getFcCarbonFactorFlc(@PathVariable String id) {
        return new ResultBean<>(fcCarbonFactorFlcService.getFcCarbonFactorFlc(id));
    }
}
