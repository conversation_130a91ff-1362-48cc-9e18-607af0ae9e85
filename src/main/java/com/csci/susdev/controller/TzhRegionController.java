package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.service.TzhRegionService;
import com.csci.tzh.model.TzhRegion;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/api/tzh/tzh-region", produces = "application/json")
@Tag(name = "地區 接口展示", description = "用于接口调试")
@LogMethod
public class TzhRegionController {

    @Autowired
    private TzhRegionService service;

    @GetMapping("/list")
    @Operation(description = "查詢 地區 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public ResultBean<List<TzhRegion>> list() {
        return new ResultBean<>(service.list());
    }

}
