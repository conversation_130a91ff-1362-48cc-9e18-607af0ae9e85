package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.service.TzhEquipmentUsageService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.model.TzhEquipmentUsage;
import com.csci.tzh.qo.SiteNameProtocolPageableQO;
import com.csci.tzh.vo.TzhEquipmentUsageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/tzh/equipment-usage", produces = "application/json")
@Tag(name = "機械設備 接口展示", description = "用于接口调试")
@LogMethod
public class TzhEquipmentUsageController {

    @Autowired
    private TzhEquipmentUsageService tzhEquipmentUsageService;

    @Resource
    private UserService userService;

    private class ResultPageOfTzhEquipmentUsage extends ResultPage<TzhEquipmentUsageVO>{
		public ResultPageOfTzhEquipmentUsage(List<?> page) {
			super(page);
		}
	}
    
    @GetMapping("/list")
    @Operation(description = "查詢 機械設備 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfTzhEquipmentUsage.class)))
    public ResultPage<TzhEquipmentUsageVO> list(SiteNameProtocolPageableQO qo) {
        return tzhEquipmentUsageService.list(qo);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 機械設備 所有記錄, 如沒有記錄則保存")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<TzhEquipmentUsage> save(@RequestBody TzhEquipmentUsage tzhEquipmentUsage) {
        userService.checkIsReadOnly();

        return new ResultBean<>(tzhEquipmentUsageService.save(tzhEquipmentUsage));
    }

    @PostMapping("/save/all")
    @Operation(description = "保存 機械設備 所有記錄, 如沒有記錄則保存")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<TzhEquipmentUsage>> saveAll(@RequestBody List<TzhEquipmentUsage> listTzhEquipmentUsage) {
        userService.checkIsReadOnly();

        return new ResultBean<>(tzhEquipmentUsageService.saveAll(listTzhEquipmentUsage));
    }
}
