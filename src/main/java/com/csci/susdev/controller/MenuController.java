package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.*;
import com.csci.susdev.qo.*;
import com.csci.susdev.service.*;
import com.csci.susdev.vo.MenuExtVO;
import com.csci.susdev.vo.MenuVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/api/menu", produces = "application/json")
@Tag(name = "菜單 接口展示", description = "用于接口调试")
@LogMethod
public class MenuController {

    @Autowired
    private MenuService menuService;

    private class ResultPageOfMenu extends ResultPage<Menu>{
		public ResultPageOfMenu(List<?> page) {
			super(page);
		}
	}

    @GetMapping("/tree/list")
    @Operation(description = "查询 菜單 统计数据列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    ResultBean<List<MenuVO>> treeList() {
        return new ResultBean<>(menuService.treeListMenu());
    }

    @GetMapping("/list")
    @Operation(description = "查询 菜單 列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    ResultPage<Menu> list(MenuPageableQO qo) {
        return menuService.listMenu(qo);
    }

    @GetMapping("/listExt")
    @Operation(description = "查询 菜單 列表 包含父菜单路径")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    ResultPage<MenuExtVO> listExt(MenuPageableQO qo) {
        return menuService.listMenuExt(qo);
    }

    @PostMapping("/save")
    @Operation(description = "保存 菜單 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<MenuVO> save(@RequestBody MenuVO vo) {
        return new ResultBean<>(menuService.saveMenuWithPermission(vo));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(description = "刪除 菜單 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Integer> delete(@PathVariable String id) {
        return new ResultBean<>(Integer.valueOf(menuService.deleteMenu(id)));
    }
}
