package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.service.FMaterialDetailService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.model.TzhMaterialInvoiceTransport;
import com.csci.tzh.qo.FMaterialDetailPageableQO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

@RestController
@RequestMapping(value = "/api/tzh/fmaterialdetail", produces = "application/json")
@Tag(name = "物資數量展示 接口展示", description = "用于接口调试")
@LogMethod
public class FMaterialDetailController {


    @Resource
    private UserService userService;

    @Autowired
    private FMaterialDetailService fMaterialDetailService;

    @GetMapping("/list")
    @Operation(description = "查詢 物料數量 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<Map<String, Object>> list(FMaterialDetailPageableQO qo) {
        return fMaterialDetailService.list(qo);
    }

    @PostMapping("/save")
    @Operation(description = "保存 物料數量 運輸數值 所有記錄, 如沒有記錄(sitename, materialcode, billno) 則保存")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<TzhMaterialInvoiceTransport> save(@RequestBody TzhMaterialInvoiceTransport tzhMaterialInvoiceTransport) {
        userService.checkIsReadOnly();

        return new ResultBean<>(fMaterialDetailService.save(tzhMaterialInvoiceTransport));
    }

    @GetMapping("/downloadinvoice")
    @Operation(description = "查詢 物料數量 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> downloadinvoice(@RequestParam("billNo") String billNo) throws Exception {
        return new ResultBean<>(fMaterialDetailService.downloadCdmsInvoicePdf(billNo));
    }
}
