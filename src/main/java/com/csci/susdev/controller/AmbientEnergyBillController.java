package com.csci.susdev.controller;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.modelcovt.AmbientEnergyBillConverter;
import com.csci.susdev.qo.AmbientEnergyBillQO;
import com.csci.susdev.qo.SyncAmbientEnergyBillQO;
import com.csci.susdev.service.AmbientEnergyBillService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.vo.BusinessTripVO;
import com.csci.susdev.vo.IdVO;
import com.csci.susdev.vo.AmbientEnergyBillVO;
import com.csci.susdev.vo.UserBasicVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/ambient-energy-bill")
@Tag(name = "環境積效能源收據", description = "環境積效能源收據")
@LogMethod
public class AmbientEnergyBillController {

    @Resource
    private AmbientEnergyBillService ambientEnergyBillService;

    @Resource
    private UserService userService;

    @GetMapping("/cal-monthly-consumption")
    @Operation(description = "計算每月耗能量")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<Map<String,String>>> listAmbientEnergyBill(String headId) {
        return new ResultBean<>(ambientEnergyBillService.calMonthlyConsumption(headId));
    }

    @GetMapping("/list")
    @Operation(description = "分页查询数据")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<AmbientEnergyBillVO> listAmbientEnergyBill(AmbientEnergyBillQO ambientEnergyBillQO) {
        return ambientEnergyBillService.listAmbientEnergyBill(ambientEnergyBillQO);
    }

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody AmbientEnergyBillVO ambientEnergyBillVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(ambientEnergyBillService.saveAmbientEnergyBill(ambientEnergyBillVO));
    }

    @PostMapping("/save/all")
    @Operation(description = "批量保存水电费记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<String>> saveAll(@RequestBody List<AmbientEnergyBillVO> ambientEnergyBillVOList) {
        userService.checkIsReadOnly();
        return new ResultBean<>(ambientEnergyBillService.saveAmbientEnergyBillList(ambientEnergyBillVOList));
    }

    @PostMapping("/duplicate")
    @Operation(description = "复制记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<AmbientEnergyBillVO> duplicate(@RequestBody IdVO idVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(AmbientEnergyBillConverter.convert(ambientEnergyBillService.duplicateAmbientEnergyBill(idVO.getId())));
    }

    @GetMapping("/{id}")
    @Operation(description = "根据id查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<AmbientEnergyBillVO> getAmbientEnergyBillById(@PathVariable String id) {
        return new ResultBean<>(ambientEnergyBillService.getAmbientEnergyBillById(id));
    }

    @DeleteMapping("/{id}")
    @Operation(description = "根据id删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteAmbientEnergyBillById(@PathVariable String id) {
        userService.checkIsReadOnly();

        ambientEnergyBillService.deleteAmbientEnergyBillById(id);
        return new ResultBase();
    }

    @PostMapping("/synchronization-data")
    @Operation(description = "同步数据")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase synchronizationData(@RequestBody SyncAmbientEnergyBillQO syncAmbientEnergyBillQO) {
        userService.checkIsReadOnly();

        ambientEnergyBillService.synchronizationData(syncAmbientEnergyBillQO);
        return new ResultBase();
    }

}
