package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.*;
import com.csci.susdev.qo.*;
import com.csci.susdev.service.*;
import com.csci.susdev.util.context.ContextUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/operation", produces = "application/json")
@Tag(name = "操作 接口展示", description = "用于接口调试")
@LogMethod
public class OperationController {

    @Autowired
    private OperationService operationService;

    @Resource
    private UserService userService;

    private class ResultPageOfOperation extends ResultPage<Operation>{
		public ResultPageOfOperation(List<?> page) {
			super(page);
		}
	}

    /**
     * 查找 操作 数据列表
     *
     * @param operationQO
     * @return
     */
    @GetMapping("/list")
    @Operation(description = "分页查询 操作 统计数据列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfOperation.class)))
    public ResultPage<com.csci.susdev.model.Operation> list(OperationPageableQO operationQO) {
        return operationService.listOperation(operationQO);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 操作 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody com.csci.susdev.model.Operation operation) {
        userService.checkIsReadOnly();

        return new ResultBean<>(operationService.saveOperation(operation));
    }
    
    @PostMapping("/savelist")
    @Operation(description = "保存 操作 記錄列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<String>> savelist(@RequestBody List<com.csci.susdev.model.Operation> operationLst) {
        userService.checkIsReadOnly();

        return new ResultBean<>(operationService.saveOperationList(operationLst));
    }
    
    @DeleteMapping("/delete/{id}")
    @Operation(description = "刪除 操作 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Integer> delete(@PathVariable String id) {
        userService.checkIsReadOnly();

        return new ResultBean<>(Integer.valueOf(operationService.deleteOperation(id)));
    }
}
