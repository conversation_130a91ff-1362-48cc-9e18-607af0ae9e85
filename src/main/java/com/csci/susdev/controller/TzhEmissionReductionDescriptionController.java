package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.service.TzhEmissionReductionDescriptionService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.model.TzhEmissionReductionDescription;
import com.csci.tzh.qo.TzhEmissionReductionDescriptionQO;
import com.csci.tzh.vo.TzhEmissionReductionDescriptionVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/tzh/tzh-emission-reduction-description", produces = "application/json")
@Tag(name = "低碳設計 接口展示", description = "用于接口调试")
@LogMethod
public class TzhEmissionReductionDescriptionController {

    @Autowired
    private TzhEmissionReductionDescriptionService service;

    @Resource
    private UserService userService;

    @GetMapping("/list")
    @Operation(description = "查詢 低碳設計 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public ResultBean<List<TzhEmissionReductionDescriptionVO>> list(TzhEmissionReductionDescriptionQO qo) {
        return new ResultBean<>(service.list(qo));
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 低碳設計 所有記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<TzhEmissionReductionDescription> save(@RequestBody TzhEmissionReductionDescription model) {
        userService.checkIsReadOnly();

        return new ResultBean<>(service.save(model));
    }
}
