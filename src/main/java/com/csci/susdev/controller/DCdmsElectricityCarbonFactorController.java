package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.service.DCdmsElectricityCarbonFactorService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.model.DCdmsElectricityCarbonFactor;
import com.csci.tzh.qo.DCdmsElectricityCarbonFactorPageableQO;
import com.csci.tzh.qo.SiteNameProtocolPageableQO;
import com.csci.tzh.vo.DCdmsElectricityCarbonFactorVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/tzh/d-cdms-electricity-carbon-factor", produces = "application/json")
@Tag(name = "工業用電 接口展示", description = "用于接口调试")
@LogMethod
public class DCdmsElectricityCarbonFactorController {

    @Resource
    private UserService userService;

    @Autowired
    private DCdmsElectricityCarbonFactorService DCdmsElectricityCarbonFactorService;

    private class ResultPageOfDCdmsElectricityCarbonFactor extends ResultPage<DCdmsElectricityCarbonFactor>{
		public ResultPageOfDCdmsElectricityCarbonFactor(List<?> page) {
			super(page);
		}
	}
    
    @GetMapping("/list")
    @Operation(description = "查詢 工業用電 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfDCdmsElectricityCarbonFactor.class)))
    public ResultPage<DCdmsElectricityCarbonFactorVO> list(DCdmsElectricityCarbonFactorPageableQO qo) {
        return DCdmsElectricityCarbonFactorService.list(qo);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 工業用電 所有記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<DCdmsElectricityCarbonFactor> save(@RequestBody DCdmsElectricityCarbonFactor DCdmsElectricityCarbonFactor) {
        userService.checkIsReadOnly();

        return new ResultBean<>(DCdmsElectricityCarbonFactorService.save(DCdmsElectricityCarbonFactor));
    }
}
