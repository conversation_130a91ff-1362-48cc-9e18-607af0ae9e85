package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultList;
import com.csci.susdev.service.CeIdentificationFugitiveEmissionService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.CeIdentificationFugitiveEmissionVO;
import com.csci.susdev.vo.CeIdentificationFugitiveHeadVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/ceIdentificationFugitiveEmission", produces = "application/json")
@Tag(name = "碳排识别-逸散排放填写", description = "用于接口调试")
@LogMethod
public class CeIdentificationFugitiveEmissionController {

    @Resource
    private CeIdentificationFugitiveEmissionService ceIdentificationFugitiveEmissionService;

    @Resource
    private UserService userService;

    @PostMapping("/save/all")
    @Operation(description = "保存所有记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase save(@RequestBody CeIdentificationFugitiveHeadVO ceIdentificationFugitiveHeadVO) {
        userService.checkIsReadOnly();

        ceIdentificationFugitiveEmissionService.saveCeIdentificationFugitiveEmissionList(ceIdentificationFugitiveHeadVO);
        return new ResultBase();
    }

    @GetMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<CeIdentificationFugitiveEmissionVO> listCeIdentificationFugitiveEmission(String headId) {
        return new ResultList<>(ceIdentificationFugitiveEmissionService.listCeIdentificationFugitiveEmission(headId));
    }

}
