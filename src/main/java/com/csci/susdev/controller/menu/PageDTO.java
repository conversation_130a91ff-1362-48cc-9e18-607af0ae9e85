package com.csci.susdev.controller.menu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * @description:
 * @author: barry
 * @create: 2024-05-15 17:20
 */
@Data
public class PageDTO<T>{
    @Min(value = 1,message = "页码不能小于1")
    @Schema(description = "页码",example = "1",type = "integer")
    private Integer pageNum=1;

    @Min(value = 1,message = "每页显示数量不能小于1")
    @Schema(description = "每页显示数量",example = "10",type = "integer")
    private Integer pageSize=10;
    @Schema(description = "查询条件")
    private T queryForm;
}
