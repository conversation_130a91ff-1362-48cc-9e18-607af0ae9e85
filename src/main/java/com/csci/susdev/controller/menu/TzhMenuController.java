package com.csci.susdev.controller.menu;

import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.model.TzhBsMenu;
import com.csci.susdev.qo.TzhBsMenuQO;
import com.csci.susdev.service.ITzhBsMenuService;
import com.csci.susdev.util.ConvertBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description:
 * @author: barry
 * @create: 2024-05-15 08:42
 */
@RestController
@Tag(name = "大屏菜单管理", description = "大屏菜单管理")
public class TzhMenuController {
    @Resource
    private ITzhBsMenuService tzhBsMenuService;

    @PostMapping("/api/bi/menu/getAll")
    @ApiResponse(content = @Content(schema = @Schema(implementation = ResultPage.class)))
    @Operation(description = "查询所有菜单")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string", example = "uuid"), required = true)
    public ResultPage<TzhBsMenu> searchAll(@Validated @RequestBody(required = false) PageDTO<TzhBsMenuQO> pageDTO) {
        return tzhBsMenuService.getAll(pageDTO);
    }

    @PostMapping("/api/bi/menu/save")
    @Operation(description = "保存菜单")
    public ResultBean save(@Validated @RequestBody TzhBsMenuQO tzhBsMenu){
        TzhBsMenu convert = ConvertBeanUtils.convert(tzhBsMenu, TzhBsMenu.class);
        return tzhBsMenuService.save(convert);
    }

    @PostMapping("/api/bi/menu/edit")
    @Operation(description = "编辑菜单")
    public ResultBean edit(@RequestBody TzhBsMenu tzhBsMenu) {
        return tzhBsMenuService.edit(tzhBsMenu);
    }

    @PostMapping("/api/bi/menu/delete")
    @Operation(description = "删除菜单")
    public ResultBean delete(@RequestBody TzhBsMenu tzhBsMenu) throws Exception {
        return tzhBsMenuService.delete(tzhBsMenu);
    }
}
