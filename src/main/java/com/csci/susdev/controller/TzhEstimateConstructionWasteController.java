package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.service.TzhEstimateConstructionWasteService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.model.TzhEstimateConstructionWaste;
import com.csci.tzh.qo.*;
import com.csci.tzh.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/tzh/estimate-construction-waste", produces = "application/json")
@Tag(name = "垃圾預測 接口展示", description = "用于接口调试")
@LogMethod
public class TzhEstimateConstructionWasteController {

    @Autowired
    private TzhEstimateConstructionWasteService tzhEstimateConstructionWasteService;

    @Resource
    private UserService userService;

    private class ResultPageOfTzhEstimateConstructionWaste extends ResultPage<TzhEstimateConstructionWasteVO>{
		public ResultPageOfTzhEstimateConstructionWaste(List<?> page) {
			super(page);
		}
	}
    
    @GetMapping("/list")
    @Operation(description = "查詢 垃圾預測 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfTzhEstimateConstructionWaste.class)))
    public ResultPage<TzhEstimateConstructionWasteVO> list(SiteNameProtocolPageableQO qo) throws Exception {
        return tzhEstimateConstructionWasteService.list(qo);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 垃圾預測 所有記錄, 如沒有記錄則保存")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<TzhEstimateConstructionWaste> save(@RequestBody TzhEstimateConstructionWaste tzhEstimateConstructionWaste) throws Exception {
        userService.checkIsReadOnly();
        return new ResultBean<>(tzhEstimateConstructionWasteService.save(tzhEstimateConstructionWaste));
    }

    @PostMapping("/save/all")
    @Operation(description = "保存 垃圾預測 所有記錄, 如沒有記錄則保存")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<TzhEstimateConstructionWaste>> saveAll(@RequestBody List<TzhEstimateConstructionWaste> listTzhEstimateConstructionWaste) {
        userService.checkIsReadOnly();
        return new ResultBean<>(tzhEstimateConstructionWasteService.saveAll(listTzhEstimateConstructionWaste));
    }

    @GetMapping("disposal-method/list")
    @Operation(description = "查詢 垃圾預測 - 處理方式 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<TzhEstimateConstructionWasteDisposalMethodVO>> list() {
        return new ResultBean<>(tzhEstimateConstructionWasteService.listDisposalMethod());
    }

    @GetMapping("sub-category/list")
    @Operation(description = "查詢 垃圾預測 - 子分類及所屬分類 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<TzhEstimateConstructionWasteCategoryVO>> list(ProtocolQO qo) {
        return new ResultBean<>(tzhEstimateConstructionWasteService.listSubCategory(qo));
    }
}
