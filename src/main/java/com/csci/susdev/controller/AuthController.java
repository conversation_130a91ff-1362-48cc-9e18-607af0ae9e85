package com.csci.susdev.controller;

import com.csci.cohl.service.AuthService;
import com.csci.susdev.login.LoginFacade;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.vo.LoginVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 用户认证接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/auth", produces = "application/json")
@Tag(name = "用户认证接口", description = "用户认证接口")
public class AuthController {

    @Autowired
    private LoginFacade loginFacade;

    @Autowired
    private AuthService authService;

    @PostMapping("/logout")
    @Operation(description = "用户登录")
    ResultBean logout(HttpServletRequest request, @RequestBody LoginVO loginVO) {
        return loginFacade.logout(loginVO.getUsername());
    }
    @PostMapping("/v1/login")
    @Operation(description = "用户登录")
    ResultBean<Map<String, Object>> login(HttpServletRequest request, @RequestBody LoginVO loginVO) throws Exception {
        return loginFacade.login(loginVO);
    }

    @PostMapping("/captcha")
    @Operation(description = "獲取驗證碼")
    public ResultBean<Map> captcha() throws Exception {
        return loginFacade.captcha();
    }

    @PostMapping("/captcha/all")
    @Operation(description = "獲取ESG雲平台和碳中和大屏的驗證碼")
    public ResultBean<Map> captchaAll() throws Exception {
        return loginFacade.captchaAll();
    }
}
