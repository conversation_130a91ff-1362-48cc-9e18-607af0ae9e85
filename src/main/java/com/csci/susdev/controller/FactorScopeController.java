package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.FactorScopeQO;
import com.csci.susdev.service.FactorScopeService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.FactorScopeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/factorScope", produces = "application/json")
@Tag(name = "因子範圍 接口", description = "用于接口调试")
@LogMethod
public class FactorScopeController {

    @Resource
    private FactorScopeService factorScopeService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody FactorScopeVO factorScopeVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(factorScopeService.saveFactorScope(factorScopeVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<FactorScopeVO> listFactorScope(@RequestBody FactorScopeQO factorScopeQO) {
        return factorScopeService.listFactorScope(factorScopeQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteFactorScope(@PathVariable String id) {
        userService.checkIsReadOnly();

        factorScopeService.deleteFactorScope(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<FactorScopeVO> getFactorScope(@PathVariable String id) {
        return new ResultBean<>(factorScopeService.getFactorScope(id));
    }
}
