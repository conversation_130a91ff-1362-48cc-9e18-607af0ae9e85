package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.ProtocolConfigurationQO;
import com.csci.susdev.service.ProtocolConfigurationService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.ProtocolConfigurationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/protocolConfiguration", produces = "application/json")
@Tag(name = "协议配置 接口", description = "用于接口调试")
@LogMethod
public class ProtocolConfigurationController {

    @Resource
    private ProtocolConfigurationService protocolConfigurationService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody ProtocolConfigurationVO protocolConfigurationVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(protocolConfigurationService.saveProtocolConfiguration(protocolConfigurationVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<ProtocolConfigurationVO> listProtocolConfiguration(@RequestBody ProtocolConfigurationQO ProtocolConfigurationQO) {
        return protocolConfigurationService.listProtocolConfiguration(ProtocolConfigurationQO);
    }


    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteProtocolConfiguration(@PathVariable String id) {
        userService.checkIsReadOnly();

        protocolConfigurationService.deleteProtocolConfiguration(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<ProtocolConfigurationVO> getProtocolConfiguration(@PathVariable String id) {
        return new ResultBean<>(protocolConfigurationService.getProtocolConfiguration(id));
    }

}
