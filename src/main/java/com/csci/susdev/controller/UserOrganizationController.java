package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.UserOrganization;
import com.csci.susdev.service.UserOrganizationService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/userOrganization", produces = "application/json")
@Tag(name = "用戶組織關聯 接口展示", description = "用于接口调试")
@LogMethod
public class UserOrganizationController {

    @Autowired
    private UserOrganizationService userOrganizationService;

    @Resource
    private UserService userService;
    
    /**
     * 查找 用戶組織關聯 数据列表
     *
     * @param username
     * @return
     */
    @GetMapping("/list")
    @Operation(description = "分页查询 用戶組織關聯 统计数据列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public List<UserOrganization> list(String username) {
        return userOrganizationService.listUserOrganizationByUsername(username);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 用戶組織關聯 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody UserOrganization userOrganization) {
        userService.checkIsReadOnly();

        return new ResultBean<>(userOrganizationService.saveUserOrganization(userOrganization));
    }
    
    @PostMapping("/savelist")
    @Operation(description = "保存 用戶組織關聯 記錄列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<String>> savelist(@RequestBody List<UserOrganization> userOrganizationLst) {
        userService.checkIsReadOnly();

        return new ResultBean<>(userOrganizationService.saveUserOrganizationList(userOrganizationLst));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(description = "刪除 用戶組織關聯 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Integer> delete(@PathVariable String id) {
        userService.checkIsReadOnly();

        return new ResultBean<>(Integer.valueOf(userOrganizationService.deleteUserOrganization(id)));
    }

}
