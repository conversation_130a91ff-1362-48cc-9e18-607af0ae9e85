package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.service.FCdmsGasCarbonFactorService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.model.FCdmsGasCarbonFactor;
import com.csci.tzh.qo.FCdmsGasCarbonFactorPageableQO;
import com.csci.tzh.vo.FCdmsGasCarbonFactorVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/tzh/f-cdms-gas-carbon-factor", produces = "application/json")
@Tag(name = "地盤瓦斯 接口展示", description = "用于接口调试")
@LogMethod
public class FCdmsGasCarbonFactorController {

    @Autowired
    private FCdmsGasCarbonFactorService FCdmsGasCarbonFactorService;

    @Resource
    private UserService userService;

    private class ResultPageOfFCdmsGasCarbonFactor extends ResultPage<FCdmsGasCarbonFactor>{
		public ResultPageOfFCdmsGasCarbonFactor(List<?> page) {
			super(page);
		}
	}
    
    @GetMapping("/list")
    @Operation(description = "查詢 地盤瓦斯 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfFCdmsGasCarbonFactor.class)))
    public ResultPage<FCdmsGasCarbonFactorVO> list(FCdmsGasCarbonFactorPageableQO qo) {
        return FCdmsGasCarbonFactorService.list(qo);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 地盤瓦斯 所有記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<FCdmsGasCarbonFactor> save(@RequestBody FCdmsGasCarbonFactor FCdmsGasCarbonFactor) {
        userService.checkIsReadOnly();

        return new ResultBean<>(FCdmsGasCarbonFactorService.save(FCdmsGasCarbonFactor));
    }
}
