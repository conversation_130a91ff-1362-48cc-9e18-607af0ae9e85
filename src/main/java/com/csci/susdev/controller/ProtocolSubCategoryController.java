package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.ProtocolSubCategoryQO;
import com.csci.susdev.service.ProtocolSubCategoryService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.ProtocolSubCategoryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/protocolSubCategory", produces = "application/json")
@Tag(name = "協議小類 接口", description = "用于接口调试")
@LogMethod
public class ProtocolSubCategoryController {

    @Resource
    private ProtocolSubCategoryService protocolSubCategoryService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody ProtocolSubCategoryVO protocolSubCategoryVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(protocolSubCategoryService.saveProtocolSubCategory(protocolSubCategoryVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<ProtocolSubCategoryVO> listProtocolSubCategory(@RequestBody ProtocolSubCategoryQO protocolSubCategoryQO) {
        return protocolSubCategoryService.listProtocolSubCategory(protocolSubCategoryQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteProtocolSubCategory(@PathVariable String id) {
        userService.checkIsReadOnly();

        protocolSubCategoryService.deleteProtocolSubCategory(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<ProtocolSubCategoryVO> getProtocolSubCategory(@PathVariable String id) {
        return new ResultBean<>(protocolSubCategoryService.getProtocolSubCategory(id));
    }
}
