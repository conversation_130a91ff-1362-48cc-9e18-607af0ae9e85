package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.*;
import com.csci.susdev.service.*;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.model.*;
import com.csci.tzh.qo.*;
import com.csci.tzh.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/tzh/tzhmonthlymaterialcarbon", produces = "application/json")
@Tag(name = "物料數量 接口展示", description = "用于接口调试")
@LogMethod
public class TzhMonthlyMaterialCarbonController {

    @Autowired
    private TzhMonthlyMaterialCarbonService tzhMonthlyMaterialCarbonService;

    @Resource
    private UserService userService;

    private class ResultPageOfTzhMonthlyMaterialCarbon extends ResultPage<TzhMonthlyMaterialCarbonVO>{
		public ResultPageOfTzhMonthlyMaterialCarbon(List<?> page) {
			super(page);
		}
	}
    
    @GetMapping("/list")
    @Operation(description = "查詢 物料數量 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfTzhMonthlyMaterialCarbon.class)))
    public ResultPage<TzhMonthlyMaterialCarbonVO> list(SiteProtocolDateRangePageableQO qo) {
        return tzhMonthlyMaterialCarbonService.list(qo);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 物料數量 所有記錄, 如沒有記錄則保存")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<TzhMonthlyMaterialCarbon> save(@RequestBody TzhMonthlyMaterialCarbon tzhMonthlyMaterialCarbon) {
        userService.checkIsReadOnly();

        return new ResultBean<>(tzhMonthlyMaterialCarbonService.save(tzhMonthlyMaterialCarbon));
    }

    @PostMapping("/save/all")
    @Operation(description = "保存 物料數量 所有記錄, 如沒有記錄則保存")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<TzhMonthlyMaterialCarbon>> saveAll(@RequestBody List<TzhMonthlyMaterialCarbon> listTzhMonthlyMaterialCarbon) {
        userService.checkIsReadOnly();

        return new ResultBean<>(tzhMonthlyMaterialCarbonService.saveAll(listTzhMonthlyMaterialCarbon));
    }
}
