package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.FlightInfo;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.FlightInfoQO;
import com.csci.susdev.qo.RefreshCurrentYearFlightInfoQO;
import com.csci.susdev.service.FResultService;
import com.csci.susdev.service.FlightInfoService;
import com.csci.tzh.qo.FResultPageableQO;
import com.csci.tzh.vo.FResultTotalVO;
import com.csci.tzh.vo.FResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@RestController
@RequestMapping(value = "/api/flight-info", produces = "application/json")
@Tag(name = "飛行里數接口", description = "用于接口调试")
@LogMethod
public class FlightInfoController {

    @Autowired
    private FlightInfoService flightInfoService;

    @GetMapping("/get")
    @Operation(description = "查詢 飛行里數")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<FlightInfo> get(FlightInfoQO qo) {
        FlightInfo flightInfo = flightInfoService.get(qo);
        if(flightInfo == null) {
            return new ResultBean<>();
        }
        return new ResultBean<>(flightInfo);
    }

    /**
     * 刷新当年的航班信息，如距离、碳排放信息
     * @param qo
     * @return
     */
    @PostMapping("/refreshCurrentYearFlightInfo")
    @Operation(description = "刷新当年的航班信息，如距离、碳排放信息")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase refreshCurrentYearFlightInfo(RefreshCurrentYearFlightInfoQO qo) {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.execute(() -> {
            flightInfoService.downloadDataNew();
        });
        return new ResultBase();
    }
}
