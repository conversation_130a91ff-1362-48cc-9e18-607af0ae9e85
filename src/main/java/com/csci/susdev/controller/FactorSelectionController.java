package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.FactorSelectionQO;
import com.csci.susdev.service.FactorSelectionService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.FactorSelectionVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/factorSelection", produces = "application/json")
@Tag(name = "因子選擇 接口", description = "用于接口调试")
@LogMethod
public class FactorSelectionController {

    @Resource
    private FactorSelectionService factorSelectionService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody FactorSelectionVO factorSelectionVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(factorSelectionService.saveFactorSelection(factorSelectionVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<FactorSelectionVO> listFactorSelection(@RequestBody FactorSelectionQO factorSelectionQO) {
        return factorSelectionService.listFactorSelection(factorSelectionQO);
    }

    @PostMapping("/export")
    @Operation(description = "导出查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResponseEntity<byte[]> exportFactorSelection(@RequestBody FactorSelectionQO factorSelectionQO) {
        factorSelectionQO.setCurPage(1);
        factorSelectionQO.setPageSize(10000);
        HttpHeaders headers = new HttpHeaders();
        byte[] bytes  = factorSelectionService.exportListFactorSelection(factorSelectionQO,headers);
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return ResponseEntity.ok()
                .headers(headers)
                .body(bytes);

    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteFactorSelection(@PathVariable String id) {
        userService.checkIsReadOnly();

        factorSelectionService.deleteFactorSelection(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<FactorSelectionVO> getFactorSelection(@PathVariable String id) {
        return new ResultBean<>(factorSelectionService.getFactorSelection(id));
    }

    @PostMapping(value = "/import-excel", produces = "application/json", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(description = "导入因子选择数据")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase importExcel(@RequestPart("file") MultipartFile file) {
        userService.checkIsReadOnly();
        factorSelectionService.importExcel(file);
        return new ResultBase();

    }
}
