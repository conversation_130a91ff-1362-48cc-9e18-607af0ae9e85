package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.FcUnitConversionQO;
import com.csci.susdev.service.FcUnitConversionService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.FcUnitConversionVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/fcUnitConversion", produces = "application/json")
@Tag(name = "产品管理-单位换算 接口", description = "用于接口调试")
@LogMethod
public class FcUnitConversionController {

    @Resource
    private FcUnitConversionService fcUnitConversionService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody FcUnitConversionVO fcUnitConversionVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(fcUnitConversionService.saveFcUnitConversion(fcUnitConversionVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<FcUnitConversionVO> listFcUnitConversion(@RequestBody FcUnitConversionQO fcUnitConversionQO) {
        return fcUnitConversionService.listFcUnitConversion(fcUnitConversionQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteFcUnitConversion(@PathVariable String id) {
        userService.checkIsReadOnly();

        fcUnitConversionService.deleteFcUnitConversion(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<FcUnitConversionVO> getFcUnitConversion(@PathVariable String id) {
        return new ResultBean<>(fcUnitConversionService.getFcUnitConversion(id));
    }

    @PostMapping("/save/all")
    @Operation(description = "批量保存单位换算记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<String>> saveFcUnitConversionList(@RequestBody List<FcUnitConversionVO> fcUnitConversionVOList) {
        userService.checkIsReadOnly();

        return new ResultBean<>(fcUnitConversionService.saveFcUnitConversionList(fcUnitConversionVOList));
    }

    @PostMapping("/export")
    @Operation(description = "导出查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResponseEntity<byte[]> exportFcUnitConversion(@RequestBody FcUnitConversionQO fcUnitConversionQO) {
        fcUnitConversionQO.setCurPage(1);
        fcUnitConversionQO.setPageSize(10000);
        HttpHeaders headers = new HttpHeaders();
        byte[] bytes  = fcUnitConversionService.exportFcUnitConversion(fcUnitConversionQO,headers);
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return ResponseEntity.ok()
                .headers(headers)
                .body(bytes);

    }
}
