package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultList;
import com.csci.susdev.service.AreaService;
import com.csci.susdev.service.BatchService;
import com.csci.susdev.vo.AreaVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/cf/", produces = "application/json")
@Tag(name = "EmissionFactorController", description = "排放因子")
@LogMethod
public class EmissionFactorController {

    @Autowired
    private BatchService batchService;

    @Autowired
    private AreaService areaService;

    @GetMapping("/area/list")
    @Operation(description = "获取区域列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<AreaVO> listArea() {
        return new ResultList<>(areaService.listArea());
    }

    @GetMapping("/batch")
    @Operation(description = "获取批次 id")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> getBatch(String areaId, Integer year, Integer month) {
        return new ResultBean<>(batchService.getBatchIdBy(areaId, year, month));
    }

}
