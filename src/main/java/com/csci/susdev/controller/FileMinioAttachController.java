package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.annotation.NoApiLogging;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.MinioAttachment;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.qo.MinioAttachmentListQO;
import com.csci.susdev.qo.MinioAttachmentQO;
import com.csci.susdev.service.MinioAttachmentService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.MinioAttachmentVO;
import com.csci.tzh.qo.IdQO;
import com.csci.tzh.qo.MinioFileNameQO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping(value = "/api/file-minio-attach", produces = "application/json")
@Tag(name = "Minio附件 接口展示", description = "用于接口调试")
@LogMethod
public class FileMinioAttachController {

    @Autowired
    private MinioAttachmentService minioAttachmentService;


    @javax.annotation.Resource
    private UserService userService;

    @PostMapping("/list")
    @Operation(description = "获取所有数据")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<MinioAttachmentVO>> list(@RequestBody MinioAttachmentListQO qo) {
        return new ResultBean<>(minioAttachmentService.list(qo));
    }

    @NoApiLogging
    @PostMapping("/upload")
    @Operation(description = "上傳")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> update(@ModelAttribute MinioAttachmentQO qo, @RequestParam("file") MultipartFile multipartFile) throws Exception {
        userService.checkIsReadOnly();

        return new ResultBean<>(minioAttachmentService.upload(qo, multipartFile));
    }

    @PostMapping("/delete")
    @Operation(description = "刪除數據")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Integer> delete(@RequestBody IdQO qo) {
        userService.checkIsReadOnly();

        return new ResultBean<>(minioAttachmentService.delete(qo.getId()));
    }

    @NoApiLogging
    @PostMapping("/download")
    @Operation(description = "下載")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResponseEntity<Resource> download(@RequestBody IdQO qo) {
        MinioAttachment x = minioAttachmentService.get(qo.getId());
        byte[] data = minioAttachmentService.getObject(x.getMinioFileName());
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + x.getName() + "\"")
                .contentType(MediaType.parseMediaType(x.getType()))
                .body(new ByteArrayResource(data));
    }

    @NoApiLogging
    @PostMapping("/preview")
    @Operation(description = "预览文件")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> preview(@RequestBody MinioFileNameQO qo) {
        return new ResultBean(minioAttachmentService.getFileUrl(qo.getMinioFileName()));
    }

    @NoApiLogging
    @PostMapping("/batchDownload")
    @Operation(description = "批量下载文件")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResponseEntity<byte[]> batchDownloadFiles(@RequestBody List<String> minioFileNames) {
        try {
            byte[] zipBytes = minioAttachmentService.batchDownloadFiles(minioFileNames);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDisposition(ContentDisposition.attachment().filename("batch_download.zip").build());
            return new ResponseEntity<>(zipBytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }




}
