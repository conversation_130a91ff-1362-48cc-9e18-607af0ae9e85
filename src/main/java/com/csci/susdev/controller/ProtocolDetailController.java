package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.ProtocolDetailQO;
import com.csci.susdev.service.ProtocolDetailService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.ProtocolDetailVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/protocolDetail", produces = "application/json")
@Tag(name = "協議明細 接口", description = "用于接口调试")
@LogMethod
public class ProtocolDetailController {

    @Resource
    private ProtocolDetailService protocolDetailService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody ProtocolDetailVO protocolDetailVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(protocolDetailService.saveProtocolDetail(protocolDetailVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<ProtocolDetailVO> listProtocolDetail(@RequestBody ProtocolDetailQO protocolDetailQO) {
        return protocolDetailService.listProtocolDetail(protocolDetailQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteProtocolDetail(@PathVariable String id) {
        userService.checkIsReadOnly();

        protocolDetailService.deleteProtocolDetail(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<ProtocolDetailVO> getProtocolDetail(@PathVariable String id) {
        return new ResultBean<>(protocolDetailService.getProtocolDetail(id));
    }
}
