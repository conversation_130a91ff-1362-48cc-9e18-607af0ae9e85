package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.FcCarbonFactorEsgDatasetQO;
import com.csci.susdev.service.FcCarbonFactorEsgDatasetService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.FcCarbonFactorEsgDatasetVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/fcCarbonFactorEsgDataset", produces = "application/json")
@Tag(name = "碳排因子+esg数据集 接口", description = "用于接口调试")
@LogMethod
public class FcCarbonFactorEsgDatasetController {

    @Resource
    private FcCarbonFactorEsgDatasetService fcCarbonFactorEsgDatasetService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody FcCarbonFactorEsgDatasetVO fcCarbonFactorEsgDatasetVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(fcCarbonFactorEsgDatasetService.saveFcCarbonFactorEsgDataset(fcCarbonFactorEsgDatasetVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<FcCarbonFactorEsgDatasetVO> listFcCarbonFactorEsgDataset(@RequestBody FcCarbonFactorEsgDatasetQO fcCarbonFactorEsgDatasetQO) {
        return fcCarbonFactorEsgDatasetService.listFcCarbonFactorEsgDataset(fcCarbonFactorEsgDatasetQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteFcCarbonFactorEsgDataset(@PathVariable String id) {
        userService.checkIsReadOnly();

        fcCarbonFactorEsgDatasetService.deleteFcCarbonFactorEsgDataset(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<FcCarbonFactorEsgDatasetVO> getFcCarbonFactorEsgDataset(@PathVariable String id) {
        return new ResultBean<>(fcCarbonFactorEsgDatasetService.getFcCarbonFactorEsgDataset(id));
    }
}
