package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.FlightInfo;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.Train;
import com.csci.susdev.qo.AirportQO;
import com.csci.susdev.qo.FlightInfoQO;
import com.csci.susdev.qo.KeywordQO;
import com.csci.susdev.qo.TrainQO;
import com.csci.susdev.service.AirportService;
import com.csci.susdev.service.FlightInfoService;
import com.csci.susdev.service.TrainService;
import com.csci.susdev.vo.AirportVO;
import com.csci.susdev.vo.TrainVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping(value = "/api/train", produces = "application/json")
@Tag(name = "火車接口", description = "用于接口调试")
@LogMethod
public class TrainController {

    @Autowired
    private TrainService trainService;

    @GetMapping("/list")
    @Operation(description = "查詢 火車列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<TrainVO>> list(KeywordQO qo) {
        return new ResultBean<>(trainService.list(qo));
    }

    @GetMapping("/list-all-name")
    @Operation(description = "查詢 所有站名")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<String>> listAllName() {
        return new ResultBean<>(trainService.listAllName());
    }

    @GetMapping("/carbon-emission/cal")
    @Operation(description = "查詢 火車里數")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Train> get(TrainQO qo) {
        Train train = trainService.calCarbonEmission(qo);
        if(train == null) {
            return new ResultBean<>();
        }
        return new ResultBean<>(train);
    }
}
