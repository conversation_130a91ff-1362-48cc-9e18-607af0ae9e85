package com.csci.susdev.mapper;

import com.csci.susdev.model.AirPollutants;
import com.csci.susdev.model.AirPollutantsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AirPollutantsMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    long countByExample(AirPollutantsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    int deleteByExample(AirPollutantsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    int insert(AirPollutants record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    int insertSelective(AirPollutants record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    List<AirPollutants> selectByExample(AirPollutantsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    AirPollutants selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") AirPollutants record, @Param("example") AirPollutantsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") AirPollutants record, @Param("example") AirPollutantsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(AirPollutants record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_air_pollutants
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(AirPollutants record);
}