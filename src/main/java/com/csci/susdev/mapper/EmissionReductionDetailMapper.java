package com.csci.susdev.mapper;

import com.csci.susdev.model.EmissionReductionDetail;
import com.csci.susdev.model.EmissionReductionDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface EmissionReductionDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_detail
     *
     * @mbg.generated Thu Oct 12 14:25:30 HKT 2023
     */
    long countByExample(EmissionReductionDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_detail
     *
     * @mbg.generated Thu Oct 12 14:25:30 HKT 2023
     */
    int deleteByExample(EmissionReductionDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_detail
     *
     * @mbg.generated Thu Oct 12 14:25:30 HKT 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_detail
     *
     * @mbg.generated Thu Oct 12 14:25:30 HKT 2023
     */
    int insert(EmissionReductionDetail row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_detail
     *
     * @mbg.generated Thu Oct 12 14:25:30 HKT 2023
     */
    int insertSelective(EmissionReductionDetail row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_detail
     *
     * @mbg.generated Thu Oct 12 14:25:30 HKT 2023
     */
    List<EmissionReductionDetail> selectByExample(EmissionReductionDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_detail
     *
     * @mbg.generated Thu Oct 12 14:25:30 HKT 2023
     */
    EmissionReductionDetail selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_detail
     *
     * @mbg.generated Thu Oct 12 14:25:30 HKT 2023
     */
    int updateByExampleSelective(@Param("row") EmissionReductionDetail row, @Param("example") EmissionReductionDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_detail
     *
     * @mbg.generated Thu Oct 12 14:25:30 HKT 2023
     */
    int updateByExample(@Param("row") EmissionReductionDetail row, @Param("example") EmissionReductionDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_detail
     *
     * @mbg.generated Thu Oct 12 14:25:30 HKT 2023
     */
    int updateByPrimaryKeySelective(EmissionReductionDetail row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_detail
     *
     * @mbg.generated Thu Oct 12 14:25:30 HKT 2023
     */
    int updateByPrimaryKey(EmissionReductionDetail row);
}