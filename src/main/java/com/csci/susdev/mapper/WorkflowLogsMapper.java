package com.csci.susdev.mapper;

import com.csci.susdev.model.WorkflowLogs;
import com.csci.susdev.model.WorkflowLogsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WorkflowLogsMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_logs
     *
     * @mbg.generated
     */
    long countByExample(WorkflowLogsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_logs
     *
     * @mbg.generated
     */
    int deleteByExample(WorkflowLogsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_logs
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_logs
     *
     * @mbg.generated
     */
    int insert(WorkflowLogs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_logs
     *
     * @mbg.generated
     */
    int insertSelective(WorkflowLogs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_logs
     *
     * @mbg.generated
     */
    List<WorkflowLogs> selectByExample(WorkflowLogsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_logs
     *
     * @mbg.generated
     */
    WorkflowLogs selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_logs
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") WorkflowLogs record, @Param("example") WorkflowLogsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_logs
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") WorkflowLogs record, @Param("example") WorkflowLogsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_logs
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(WorkflowLogs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_logs
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(WorkflowLogs record);
}