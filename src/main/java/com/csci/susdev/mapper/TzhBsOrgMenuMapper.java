package com.csci.susdev.mapper;

import com.csci.susdev.model.TzhBsOrgMenu;
import com.csci.susdev.qo.TzhBsOrgMenuQO;
import com.csci.susdev.vo.TzhBsOrgMenuVO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface TzhBsOrgMenuMapper {
    int deleteByPrimaryKey(Object id);

    int insert(TzhBsOrgMenu record);

    int insertSelective(TzhBsOrgMenu record);

    TzhBsOrgMenu selectByPrimaryKey(Object id);

    int updateByPrimaryKeySelective(TzhBsOrgMenu record);

    int updateByPrimaryKey(TzhBsOrgMenu record);

    int logicallyDelete(String id);

    List<TzhBsOrgMenuVO> queryAll(TzhBsOrgMenuQO queryForm);
}
