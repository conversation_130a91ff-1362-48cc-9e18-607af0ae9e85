<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.EmpCommutingHeadMapper">
  <resultMap id="BaseResultMap" type="com.csci.cohl.model.EmpCommutingHead">
    <!--@mbg.generated-->
    <!--@Table t_emp_commuting_head-->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="organization_id" jdbcType="VARCHAR" property="organizationId" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="month" jdbcType="INTEGER" property="month" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="VARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="VARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, organization_id, [year], [month], is_active, creation_time, create_username, 
    create_user_id, last_update_time, last_update_username, last_update_user_id, last_update_version
  </sql>
</mapper>