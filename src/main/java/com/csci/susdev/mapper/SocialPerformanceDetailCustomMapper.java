package com.csci.susdev.mapper;

import com.csci.susdev.model.SocialPerformanceDetail;
import com.csci.susdev.model.Workflow;
import com.csci.susdev.qo.WorkflowControlQO;
import com.csci.susdev.vo.UnSubmitWorkflowExportData;
import com.csci.susdev.vo.WorkflowControlVO;
import com.csci.susdev.vo.WorkflowExportData;
import com.csci.susdev.vo.WorkflowNodeVO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface SocialPerformanceDetailCustomMapper {

    @Insert("<script>" +
            "insert into t_social_performance_detail (id, head_id, category," +
            "                  report_item, classification1, classification2," +
            "                  classification3, classification4, classification5," +
            "                  classification6, classification7, remark," +
            "                  is_header, seq, creation_time) values" +
            "<foreach collection='recordList' item='item' separator=','>" +
            "(#{item.id,jdbcType=VARCHAR}, #{item.headId,jdbcType=VARCHAR}, #{item.category,jdbcType=VARCHAR}," +
            "                  #{item.reportItem,jdbcType=VARCHAR}, #{item.classification1,jdbcType=VARCHAR}, #{item.classification2,jdbcType=VARCHAR}," +
            "                  #{item.classification3,jdbcType=VARCHAR}, #{item.classification4,jdbcType=VARCHAR}, #{item.classification5,jdbcType=VARCHAR}," +
            "                  #{item.classification6,jdbcType=VARCHAR}, #{item.classification7,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}," +
            "                  #{item.header,jdbcType=BIT}, #{item.seq,jdbcType=INTEGER}, #{item.creationTime,jdbcType=TIMESTAMP})" +
            "</foreach>" +
            "</script>"
            )
    void batchInsert(@Param("recordList") List<SocialPerformanceDetail> recordList);
}
