package com.csci.susdev.mapper;

import com.csci.susdev.qo.BDictQO;
import com.csci.susdev.vo.BDictVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface BDictCustomMapper {

    @Select("""
            <script>
                select bd.id, bd.code , bd.name, bd.value, bd.dict_type as dictType,
                bd.remarks, bd.seq, bd.last_update_version as lastUpdateVersion
                from t_b_dict bd
                where bd.is_deleted = 0 
                
                <if test="bDictQO.code != null and bDictQO.code != ''">
                and bd.code = #{bDictQO.code}
                </if>
                <if test="bDictQO.name != null and bDictQO.name != ''">
                and bd.name = #{bDictQO.name}
                </if>
                <if test="bDictQO.value != null and bDictQO.value != ''">
                and bd.value = #{bDictQO.value}
                </if>
                <if test="bDictQO.dictType != null and bDictQO.dictType != ''">
                and bd.dict_type = #{bDictQO.dictType}
                </if>
                order by bd.code desc, bd.seq asc
            </script>
                """)
    public List<BDictVO> lisBDict(@Param("bDictQO") BDictQO bDictQO);


}
