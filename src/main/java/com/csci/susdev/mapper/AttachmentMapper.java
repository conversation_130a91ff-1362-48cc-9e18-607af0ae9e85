package com.csci.susdev.mapper;

import com.csci.susdev.model.Attachment;
import com.csci.susdev.model.AttachmentExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AttachmentMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_attachment
	 * @mbg.generated  Wed Jul 26 15:03:47 HKT 2023
	 */
	long countByExample(AttachmentExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_attachment
	 * @mbg.generated  Wed Jul 26 15:03:47 HKT 2023
	 */
	int deleteByExample(AttachmentExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_attachment
	 * @mbg.generated  Wed Jul 26 15:03:47 HKT 2023
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_attachment
	 * @mbg.generated  Wed Jul 26 15:03:47 HKT 2023
	 */
	int insert(Attachment row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_attachment
	 * @mbg.generated  Wed Jul 26 15:03:47 HKT 2023
	 */
	int insertSelective(Attachment row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_attachment
	 * @mbg.generated  Wed Jul 26 15:03:47 HKT 2023
	 */
	List<Attachment> selectByExampleWithBLOBs(AttachmentExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_attachment
	 * @mbg.generated  Wed Jul 26 15:03:47 HKT 2023
	 */
	List<Attachment> selectByExample(AttachmentExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_attachment
	 * @mbg.generated  Wed Jul 26 15:03:47 HKT 2023
	 */
	Attachment selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_attachment
	 * @mbg.generated  Wed Jul 26 15:03:47 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") Attachment row, @Param("example") AttachmentExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_attachment
	 * @mbg.generated  Wed Jul 26 15:03:47 HKT 2023
	 */
	int updateByExampleWithBLOBs(@Param("row") Attachment row, @Param("example") AttachmentExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_attachment
	 * @mbg.generated  Wed Jul 26 15:03:47 HKT 2023
	 */
	int updateByExample(@Param("row") Attachment row, @Param("example") AttachmentExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_attachment
	 * @mbg.generated  Wed Jul 26 15:03:47 HKT 2023
	 */
	int updateByPrimaryKeySelective(Attachment row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_attachment
	 * @mbg.generated  Wed Jul 26 15:03:47 HKT 2023
	 */
	int updateByPrimaryKeyWithBLOBs(Attachment row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_attachment
	 * @mbg.generated  Wed Jul 26 15:03:47 HKT 2023
	 */
	int updateByPrimaryKey(Attachment row);
}