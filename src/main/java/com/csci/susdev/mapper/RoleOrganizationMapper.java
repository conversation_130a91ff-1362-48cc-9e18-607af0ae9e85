package com.csci.susdev.mapper;

import com.csci.susdev.model.RoleOrganization;
import com.csci.susdev.model.RoleOrganizationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RoleOrganizationMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_role_organization
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    long countByExample(RoleOrganizationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_role_organization
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    int deleteByExample(RoleOrganizationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_role_organization
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_role_organization
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    int insert(RoleOrganization record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_role_organization
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    int insertSelective(RoleOrganization record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_role_organization
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    List<RoleOrganization> selectByExample(RoleOrganizationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_role_organization
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    RoleOrganization selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_role_organization
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    int updateByExampleSelective(@Param("record") RoleOrganization record, @Param("example") RoleOrganizationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_role_organization
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    int updateByExample(@Param("record") RoleOrganization record, @Param("example") RoleOrganizationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_role_organization
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    int updateByPrimaryKeySelective(RoleOrganization record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_role_organization
     *
     * @mbg.generated Thu May 08 14:18:18 CST 2025
     */
    int updateByPrimaryKey(RoleOrganization record);
}