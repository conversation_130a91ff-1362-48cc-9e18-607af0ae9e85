package com.csci.susdev.mapper;

import com.csci.susdev.model.FlightInfo;
import com.csci.susdev.model.Train;
import com.csci.susdev.vo.TrainVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TrainCustomMapper {
	
    @Select("""
            SELECT t.id, t.code, t.start_name AS startName, t.dest_name AS destName, t.carbon_emission AS carbonEmission
            FROM t_train t
            WHERE t.start_name like CONCAT('%', #{keyword}, '%')
            OR t.dest_name like CONCAT('%', #{keyword}, '%')
            OR t.code like CONCAT('%', #{keyword}, '%')
            ORDER BY t.code
            """)
    public List<TrainVO> list(@Param("keyword") String keyword);

    @Select("""
            SELECT DISTINCT name FROM (
                SELECT t1.start_name as name
                FROM t_train t1
            	UNION
            	SELECt t2.dest_name as name
                FROM t_train t2
            ) t
            ORDER BY t.name
            """)
    public List<String> listAllName();

    @Select("""
            SELECT TOP 1 t.id, t.code AS code, t.start_name AS startName, t.dest_name AS destName,
            t.carbon_emission AS carbonEmission, t.creation_time AS creationTime, t.create_username AS createUsername,
            t.create_user_id AS createUserId, t.last_update_time AS lastUpdateTime, t.last_update_username AS lastUpdateUsername,
            t.last_update_user_id AS lastUpdateUserId FROM t_train t
            WHERE t.start_name = #{startPlace}
            AND t.dest_name = #{destination}
            """)
    public Train calCarbonEmission(@Param("startPlace") String startPlace,
                                   @Param("destination") String destination);
}