package com.csci.susdev.mapper;

import com.csci.susdev.model.TzhBsUserRole;
import com.csci.susdev.model.TzhBsUserRoleExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhBsUserRoleMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserRole
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    long countByExample(TzhBsUserRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserRole
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    int deleteByExample(TzhBsUserRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserRole
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserRole
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    int insert(TzhBsUserRole record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserRole
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    int insertSelective(TzhBsUserRole record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserRole
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    List<TzhBsUserRole> selectByExample(TzhBsUserRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserRole
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    TzhBsUserRole selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserRole
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    int updateByExampleSelective(@Param("record") TzhBsUserRole record, @Param("example") TzhBsUserRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserRole
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    int updateByExample(@Param("record") TzhBsUserRole record, @Param("example") TzhBsUserRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserRole
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    int updateByPrimaryKeySelective(TzhBsUserRole record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserRole
     *
     * @mbg.generated Tue Jun 04 12:09:18 HKT 2024
     */
    int updateByPrimaryKey(TzhBsUserRole record);
}