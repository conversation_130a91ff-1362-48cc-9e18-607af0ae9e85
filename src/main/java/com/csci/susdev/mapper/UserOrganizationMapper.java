package com.csci.susdev.mapper;

import com.csci.susdev.model.UserOrganization;
import com.csci.susdev.model.UserOrganizationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface UserOrganizationMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_organization
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	long countByExample(UserOrganizationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_organization
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	int deleteByExample(UserOrganizationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_organization
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_organization
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	int insert(UserOrganization row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_organization
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	int insertSelective(UserOrganization row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_organization
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	List<UserOrganization> selectByExample(UserOrganizationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_organization
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	UserOrganization selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_organization
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	int updateByExampleSelective(@Param("row") UserOrganization row, @Param("example") UserOrganizationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_organization
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	int updateByExample(@Param("row") UserOrganization row, @Param("example") UserOrganizationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_organization
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	int updateByPrimaryKeySelective(UserOrganization row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_organization
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	int updateByPrimaryKey(UserOrganization row);
}