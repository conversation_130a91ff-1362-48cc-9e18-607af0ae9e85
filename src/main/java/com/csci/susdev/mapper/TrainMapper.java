package com.csci.susdev.mapper;

import com.csci.susdev.model.Train;
import com.csci.susdev.model.TrainExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TrainMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_train
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    long countByExample(TrainExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_train
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    int deleteByExample(TrainExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_train
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_train
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    int insert(Train row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_train
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    int insertSelective(Train row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_train
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    List<Train> selectByExample(TrainExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_train
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    Train selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_train
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    int updateByExampleSelective(@Param("row") Train row, @Param("example") TrainExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_train
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    int updateByExample(@Param("row") Train row, @Param("example") TrainExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_train
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    int updateByPrimaryKeySelective(Train row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_train
     *
     * @mbg.generated Tue Aug 22 10:17:51 HKT 2023
     */
    int updateByPrimaryKey(Train row);
}