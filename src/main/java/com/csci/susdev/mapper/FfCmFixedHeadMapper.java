package com.csci.susdev.mapper;

import com.csci.susdev.model.FfCmFixedHead;
import com.csci.susdev.model.FfCmFixedHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FfCmFixedHeadMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_head
     *
     * @mbg.generated Thu Apr 18 16:47:00 HKT 2024
     */
    long countByExample(FfCmFixedHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_head
     *
     * @mbg.generated Thu Apr 18 16:47:00 HKT 2024
     */
    int deleteByExample(FfCmFixedHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_head
     *
     * @mbg.generated Thu Apr 18 16:47:00 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_head
     *
     * @mbg.generated Thu Apr 18 16:47:00 HKT 2024
     */
    int insert(FfCmFixedHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_head
     *
     * @mbg.generated Thu Apr 18 16:47:00 HKT 2024
     */
    int insertSelective(FfCmFixedHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_head
     *
     * @mbg.generated Thu Apr 18 16:47:00 HKT 2024
     */
    List<FfCmFixedHead> selectByExample(FfCmFixedHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_head
     *
     * @mbg.generated Thu Apr 18 16:47:00 HKT 2024
     */
    FfCmFixedHead selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_head
     *
     * @mbg.generated Thu Apr 18 16:47:00 HKT 2024
     */
    int updateByExampleSelective(@Param("record") FfCmFixedHead record, @Param("example") FfCmFixedHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_head
     *
     * @mbg.generated Thu Apr 18 16:47:00 HKT 2024
     */
    int updateByExample(@Param("record") FfCmFixedHead record, @Param("example") FfCmFixedHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_head
     *
     * @mbg.generated Thu Apr 18 16:47:00 HKT 2024
     */
    int updateByPrimaryKeySelective(FfCmFixedHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_head
     *
     * @mbg.generated Thu Apr 18 16:47:00 HKT 2024
     */
    int updateByPrimaryKey(FfCmFixedHead record);
}