package com.csci.susdev.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 车辆用油
 * <AUTHOR>
 * @date 20250208
 */
public interface VehicleFuelUsageCustomMapper {
	/**
	 *  根据ID批量删除
	 * @param ids
	 * @return
	 */
	int batchDeleteByIdList(@Param("ids") List<String> ids);

	/**
	 * 更新新的ID
	 * @param oldId
	 * @param newId
	 * @return
	 */
	int updateNewIdByOldId(String oldId, String newId);
}