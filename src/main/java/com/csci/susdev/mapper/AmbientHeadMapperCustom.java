package com.csci.susdev.mapper;

import com.csci.susdev.vo.AmbientHeadOrganizationVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AmbientHeadMapperCustom {

    List<String> selectAllSubmittedHeadId(List<String> organizationIds, Integer year, Integer month);

    List<String> selectAllSubmittedHeadIdByYearMonth(Integer year, Integer month);

    /**
     * 查询环境绩效，返回项目名称、时间年月
     * <AUTHOR>
     * @date 2025/2/26 11:31
     * @param id
     * @return com.csci.susdev.vo.AmbientHeadOrganizationVO
     */
    @Select("""
            select
               top 1
               h.id,
               h.organization_id,
               h.year,
               h.month,
               CONCAT(h.[year] , FORMAT(h.[month], '00')) as recordYearMonth,
               o.name as organization_name
             from t_ambient_head h
             left join t_organization o on o.is_deleted = 0 and o.id = h.organization_id
             where h.is_active = 1
             and h.id = #{id}
            """)
    AmbientHeadOrganizationVO getNameById(@Param("id") String id);
}
