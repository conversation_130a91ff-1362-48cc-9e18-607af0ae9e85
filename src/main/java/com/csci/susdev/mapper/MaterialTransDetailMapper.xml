<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.MaterialTransDetailMapper">
  <resultMap id="BaseResultMap" type="com.csci.cohl.model.MaterialTransDetail">
    <!--@mbg.generated-->
    <!--@Table t_material_trans_detail-->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="head_id" jdbcType="VARCHAR" property="headId" />
    <result column="col_1" jdbcType="VARCHAR" property="col1" />
    <result column="col_2" jdbcType="VARCHAR" property="col2" />
    <result column="col_3" jdbcType="VARCHAR" property="col3" />
    <result column="col_4" jdbcType="VARCHAR" property="col4" />
    <result column="col_5" jdbcType="VARCHAR" property="col5" />
    <result column="col_6" jdbcType="VARCHAR" property="col6" />
    <result column="col_7" jdbcType="VARCHAR" property="col7" />
    <result column="col_8" jdbcType="VARCHAR" property="col8" />
    <result column="col_9" jdbcType="VARCHAR" property="col9" />
    <result column="col_10" jdbcType="VARCHAR" property="col10" />
    <result column="col_11" jdbcType="VARCHAR" property="col11" />
    <result column="col_12" jdbcType="VARCHAR" property="col12" />
    <result column="col_13" jdbcType="VARCHAR" property="col13" />
    <result column="col_14" jdbcType="VARCHAR" property="col14" />
    <result column="col_15" jdbcType="VARCHAR" property="col15" />
    <result column="col_16" jdbcType="VARCHAR" property="col16" />
    <result column="col_17" jdbcType="VARCHAR" property="col17" />
    <result column="col_18" jdbcType="VARCHAR" property="col18" />
    <result column="col_19" jdbcType="VARCHAR" property="col19" />
    <result column="col_20" jdbcType="VARCHAR" property="col20" />
    <result column="col_21" jdbcType="VARCHAR" property="col21" />
    <result column="col_22" jdbcType="VARCHAR" property="col22" />
    <result column="col_23" jdbcType="VARCHAR" property="col23" />
    <result column="col_24" jdbcType="VARCHAR" property="col24" />
    <result column="col_25" jdbcType="VARCHAR" property="col25" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, head_id, col_1, col_2, col_3, col_4, col_5, col_6, col_7, col_8, col_9, col_10, 
    col_11, col_12, col_13, col_14, col_15, col_16, col_17, col_18, col_19, col_20, col_21, 
    col_22, col_23, col_24, col_25, seq, creation_time
  </sql>
</mapper>