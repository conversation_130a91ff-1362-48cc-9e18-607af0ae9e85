package com.csci.susdev.mapper;

import com.csci.susdev.model.TzhCarbonPredictionDetail1;
import com.csci.susdev.model.TzhCarbonPredictionDetail1Example;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhCarbonPredictionDetail1Mapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_1
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    long countByExample(TzhCarbonPredictionDetail1Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_1
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    int deleteByExample(TzhCarbonPredictionDetail1Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_1
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_1
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    int insert(TzhCarbonPredictionDetail1 row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_1
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    int insertSelective(TzhCarbonPredictionDetail1 row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_1
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    List<TzhCarbonPredictionDetail1> selectByExample(TzhCarbonPredictionDetail1Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_1
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    TzhCarbonPredictionDetail1 selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_1
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    int updateByExampleSelective(@Param("row") TzhCarbonPredictionDetail1 row, @Param("example") TzhCarbonPredictionDetail1Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_1
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    int updateByExample(@Param("row") TzhCarbonPredictionDetail1 row, @Param("example") TzhCarbonPredictionDetail1Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_1
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    int updateByPrimaryKeySelective(TzhCarbonPredictionDetail1 row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_1
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    int updateByPrimaryKey(TzhCarbonPredictionDetail1 row);
}