package com.csci.susdev.mapper;

import com.csci.susdev.model.FormDetail;
import com.csci.susdev.model.FormDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FormDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form_detail
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    long countByExample(FormDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form_detail
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    int deleteByExample(FormDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form_detail
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form_detail
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    int insert(FormDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form_detail
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    int insertSelective(FormDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form_detail
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    List<FormDetail> selectByExample(FormDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form_detail
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    FormDetail selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form_detail
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    int updateByExampleSelective(@Param("record") FormDetail record, @Param("example") FormDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form_detail
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    int updateByExample(@Param("record") FormDetail record, @Param("example") FormDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form_detail
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    int updateByPrimaryKeySelective(FormDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_form_detail
     *
     * @mbg.generated Thu Apr 11 20:49:50 HKT 2024
     */
    int updateByPrimaryKey(FormDetail record);
}