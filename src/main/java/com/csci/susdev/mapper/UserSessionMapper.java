package com.csci.susdev.mapper;

import com.csci.susdev.model.UserSession;
import com.csci.susdev.model.UserSessionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface UserSessionMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_session
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	long countByExample(UserSessionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_session
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	int deleteByExample(UserSessionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_session
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_session
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	int insert(UserSession row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_session
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	int insertSelective(UserSession row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_session
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	List<UserSession> selectByExample(UserSessionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_session
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	UserSession selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_session
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	int updateByExampleSelective(@Param("row") UserSession row, @Param("example") UserSessionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_session
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	int updateByExample(@Param("row") UserSession row, @Param("example") UserSessionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_session
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	int updateByPrimaryKeySelective(UserSession row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_user_session
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	int updateByPrimaryKey(UserSession row);
}