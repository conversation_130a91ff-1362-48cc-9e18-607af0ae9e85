package com.csci.susdev.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface AmbientEnergyBillCustomMapper {

    @Select("""
SELECT t.recordYearMonth, t.type, SUM(t.avgConsumption) AS avgConsumption FROM (
SELECT (YEAR(a.date)*100 + MONTH(a.date)) AS recordYearMonth, b.type, SUM(consumption/(DATEDIFF(day, from_time, to_time) + 1)) AS avgConsumption
FROM (select date from t_date_o where date <= getdate()) a
LEFT JOIN t_ambient_energy_bill b ON a.date >= b.from_time AND a.date <= b.to_time
WHERE b.type IS NOT NULL AND b.head_id = #{headId}
GROUP BY (YEAR(a.date)*100 + MONTH(a.date)), b.type
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '01' AS recordYearMonth, 'electricity' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '01' AS recordYearMonth, 'water' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '02' AS recordYearMonth, 'electricity' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '02' AS recordYearMonth, 'water' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '03' AS recordYearMonth, 'electricity' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '03' AS recordYearMonth, 'water' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '04' AS recordYearMonth, 'electricity' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '04' AS recordYearMonth, 'water' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '05' AS recordYearMonth, 'electricity' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '05' AS recordYearMonth, 'water' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '06' AS recordYearMonth, 'electricity' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '06' AS recordYearMonth, 'water' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '07' AS recordYearMonth, 'electricity' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '07' AS recordYearMonth, 'water' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '08' AS recordYearMonth, 'electricity' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '08' AS recordYearMonth, 'water' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '09' AS recordYearMonth, 'electricity' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '09' AS recordYearMonth, 'water' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '10' AS recordYearMonth, 'electricity' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '10' AS recordYearMonth, 'water' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '11' AS recordYearMonth, 'electricity' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '11' AS recordYearMonth, 'water' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '12' AS recordYearMonth, 'electricity' AS type, 0 AS avgConsumption
UNION
SELECT (SELECT YEAR FROM t_ambient_head WHERE id = #{headId})*100 + '12' AS recordYearMonth, 'water' AS type, 0 AS avgConsumption
) t
GROUP BY t.recordYearMonth, t.type
            """)
    public List<Map<String, String>> calMonthlyConsumption(@Param("headId") String headId);

}
