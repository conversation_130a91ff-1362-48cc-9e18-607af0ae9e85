package com.csci.susdev.mapper;

import com.csci.susdev.model.UserHistory;
import com.csci.susdev.model.UserHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface UserHistoryMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_user_history
     *
     * @mbg.generated
     */
    long countByExample(UserHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_user_history
     *
     * @mbg.generated
     */
    int deleteByExample(UserHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_user_history
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_user_history
     *
     * @mbg.generated
     */
    int insert(UserHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_user_history
     *
     * @mbg.generated
     */
    int insertSelective(UserHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_user_history
     *
     * @mbg.generated
     */
    List<UserHistory> selectByExampleWithBLOBs(UserHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_user_history
     *
     * @mbg.generated
     */
    List<UserHistory> selectByExample(UserHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_user_history
     *
     * @mbg.generated
     */
    UserHistory selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_user_history
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") UserHistory record, @Param("example") UserHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_user_history
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") UserHistory record, @Param("example") UserHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_user_history
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") UserHistory record, @Param("example") UserHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_user_history
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(UserHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_user_history
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(UserHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_user_history
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(UserHistory record);
}