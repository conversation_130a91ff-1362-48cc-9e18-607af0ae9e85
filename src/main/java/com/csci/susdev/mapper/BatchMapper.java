package com.csci.susdev.mapper;

import com.csci.susdev.model.Batch;
import com.csci.susdev.model.BatchExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BatchMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_batch
     *
     * @mbg.generated
     */
    long countByExample(BatchExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_batch
     *
     * @mbg.generated
     */
    int deleteByExample(BatchExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_batch
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_batch
     *
     * @mbg.generated
     */
    int insert(Batch record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_batch
     *
     * @mbg.generated
     */
    int insertSelective(Batch record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_batch
     *
     * @mbg.generated
     */
    List<Batch> selectByExample(BatchExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_batch
     *
     * @mbg.generated
     */
    Batch selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_batch
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") Batch record, @Param("example") BatchExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_batch
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") Batch record, @Param("example") BatchExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_batch
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(Batch record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cf_batch
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(Batch record);
}