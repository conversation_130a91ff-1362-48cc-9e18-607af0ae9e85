package com.csci.susdev.mapper;

import com.csci.susdev.model.ConnectionConfig;
import com.csci.susdev.model.FactorSelection;
import com.csci.susdev.vo.ConnectionConfigVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ConnectionConfigCustomMapper {

    @Select("""
            <script>
                select * from (
                   select cc.id, cc.user_id as userId, u.username, cc.app_id as appId,
                   cc.app_key as appKey, cc.description, cc.is_active as isActive, 
                   cc.last_update_version as lastUpdateVersion
                   from t_connection_config cc
                   left join t_user u on cc.user_id = u.id
                   where cc.is_deleted = 0
                    
                    <if test="keyword != null and keyword != ''">
                    and (cc.app_id like '%' + #{keyword} + '%' or u.username like '%' + #{keyword} + '%')
                    </if>
                ) t
                
                <if test="orderBy != null and orderBy != ''">
                order by ${orderBy}
                </if>
            </script>
                """)
    public List<ConnectionConfigVO> list(
            @Param("keyword") String keyword,
            @Param("orderBy") String orderBy);
}
