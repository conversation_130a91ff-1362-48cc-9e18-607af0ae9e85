package com.csci.susdev.mapper;

import com.csci.susdev.vo.ProcedureVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @description:
 * @author: barry
 * @create: 2024-11-20 09:40
 */
@Mapper
public interface ProcedureMapper {
    @Select("""
EXEC [dbo].[cal_carbon_房屋建筑工程] N'${SiteName}'
""")
    List<ProcedureVO> callHouseProcedure(String SiteName);

    @Select("""
EXEC [dbo].[cal_carbon_专业工程] N'${SiteName}'
""")
    List<ProcedureVO> callMajorProcedure(String SiteName);
    @Select("""
EXEC [dbo].[cal_carbon_制造生产] N'${SiteName}'
""")
    List<ProcedureVO> callManufactureProcedure(String SiteName);

    @Select("""
EXEC [dbo].[cal_carbon_基础设施运营] N'${SiteName}'
""")
    List<ProcedureVO> callBasisProcedure(String SiteName);

    @Select("""
EXEC [dbo].[cal_carbon_办公场所] N'${SiteName}'
""")
    List<ProcedureVO> callWorkProcedure(String SiteName);
}
