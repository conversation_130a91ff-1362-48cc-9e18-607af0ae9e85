package com.csci.susdev.mapper;

import com.csci.susdev.model.CeBasicInfoDetail;
import com.csci.susdev.model.CeBasicInfoDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CeBasicInfoDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_detail
     *
     * @mbg.generated Thu Sep 14 16:40:36 HKT 2023
     */
    long countByExample(CeBasicInfoDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_detail
     *
     * @mbg.generated Thu Sep 14 16:40:36 HKT 2023
     */
    int deleteByExample(CeBasicInfoDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_detail
     *
     * @mbg.generated Thu Sep 14 16:40:36 HKT 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_detail
     *
     * @mbg.generated Thu Sep 14 16:40:36 HKT 2023
     */
    int insert(CeBasicInfoDetail row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_detail
     *
     * @mbg.generated Thu Sep 14 16:40:36 HKT 2023
     */
    int insertSelective(CeBasicInfoDetail row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_detail
     *
     * @mbg.generated Thu Sep 14 16:40:36 HKT 2023
     */
    List<CeBasicInfoDetail> selectByExample(CeBasicInfoDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_detail
     *
     * @mbg.generated Thu Sep 14 16:40:36 HKT 2023
     */
    CeBasicInfoDetail selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_detail
     *
     * @mbg.generated Thu Sep 14 16:40:36 HKT 2023
     */
    int updateByExampleSelective(@Param("row") CeBasicInfoDetail row, @Param("example") CeBasicInfoDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_detail
     *
     * @mbg.generated Thu Sep 14 16:40:36 HKT 2023
     */
    int updateByExample(@Param("row") CeBasicInfoDetail row, @Param("example") CeBasicInfoDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_detail
     *
     * @mbg.generated Thu Sep 14 16:40:36 HKT 2023
     */
    int updateByPrimaryKeySelective(CeBasicInfoDetail row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_detail
     *
     * @mbg.generated Thu Sep 14 16:40:36 HKT 2023
     */
    int updateByPrimaryKey(CeBasicInfoDetail row);
}