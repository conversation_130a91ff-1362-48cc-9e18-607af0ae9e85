package com.csci.susdev.mapper;

import com.csci.susdev.model.SocialPerfTwoActivity;
import com.csci.susdev.model.SocialPerfTwoActivityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SocialPerfTwoActivityMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	long countByExample(SocialPerfTwoActivityExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	int deleteByExample(SocialPerfTwoActivityExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	int insert(SocialPerfTwoActivity row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	int insertSelective(SocialPerfTwoActivity row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	List<SocialPerfTwoActivity> selectByExample(SocialPerfTwoActivityExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	SocialPerfTwoActivity selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") SocialPerfTwoActivity row,
			@Param("example") SocialPerfTwoActivityExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	int updateByExample(@Param("row") SocialPerfTwoActivity row,
			@Param("example") SocialPerfTwoActivityExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	int updateByPrimaryKeySelective(SocialPerfTwoActivity row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	int updateByPrimaryKey(SocialPerfTwoActivity row);
}