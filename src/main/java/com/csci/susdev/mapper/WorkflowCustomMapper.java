package com.csci.susdev.mapper;

import com.csci.susdev.model.User;
import com.csci.susdev.model.Workflow;
import com.csci.susdev.qo.WorkflowControlQO;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.susdev.vo.UnSubmitWorkflowExportData;
import com.csci.susdev.vo.WorkflowControlVO;
import com.csci.susdev.vo.WorkflowExportData;
import com.csci.susdev.vo.WorkflowNodeVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface WorkflowCustomMapper {

    List<WorkflowNodeVO> selectWorkflowNodeByWorkflowId(@Param("workflowId") String workflowId);

    List<UnSubmitWorkflowExportData> selectUnSubmitWorkflowExportData(WorkflowControlQO qo);

    List<WorkflowControlVO> selectWorkflowControlExact(WorkflowControlQO qo);

    List<WorkflowControlVO> selectWorkflowControl(WorkflowControlQO qo);
    @Select("""
            select c.id as id, c.workflow_id as workflowId, c.business_id as businessId, o.id as organizationId, o.name
            organizationName, 
            f.id as formId, f.name as formName, c.current_node_id as currentNodeId, n.name as currentNodeName, c.state,
            ah.year, ah.month, u.id as createUserId, u.name as createUserRealName,
            (select name as parentOrgName from t_organization where id = o.parent_id) as parentOrgName,
            cu.name AS currentNodeUserName, cu.email AS currentNodeUserEmail
            from t_workflow_control c left join t_workflow w on w.id = c.workflow_id
            inner join t_organization o on o.id = w.organization_id and o.is_deleted = 0 
            inner join t_form f on w.form_id = f.id
            inner join t_workflow_node n on n.id = c.current_node_id
            inner join t_workflow_node_user nu on n.id = nu.node_id
            inner JOIN (
            SELECT id, YEAR, MONTH, is_active  FROM t_ambient_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_social_perf_two_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_social_perf_three_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_social_performance_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_ff_cm_fixed_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_ff_cm_mobile_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_ce_identification_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_emission_reduction_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_ce_basic_info_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_emp_commuting_head
            ) ah on ah.id = c.business_id
            left join t_user u on c.create_user_id = u.id
            left join t_user cu on nu.user_id = cu.id
            where c.is_active = 1
            and c.state = 1
            and ah.is_active = 1
            and nu.user_id = #{userId}
            """)
    public List<WorkflowControlVO> selectWorkflowControlByUserId(@Param("userId") String userId);

    @Select("""
            select c.id as id, o.name
            organizationName, f.name as formName, c.state,
            w.year, w.is_active as isActive, ru.name as representativeName, ru.email as representativeEmail, ru.mobile as representativeMobile,
            (select name as parentOrgName from t_organization where id = o.parent_id) as parentOrgName,
            n.name as nodeName, cu.name AS nodeUserName, cu.email AS nodeUserEmail, cu.mobile
            from t_workflow_control c
            left join t_workflow w on w.id = c.workflow_id
            inner join t_organization o on o.id = w.organization_id and o.is_deleted = 0
            inner join t_form f on w.form_id = f.id
            inner join t_workflow_node n on n.workflow_id = w.id
            inner join t_workflow_node_user nu on n.id = nu.node_id
            left join t_user u on c.create_user_id = u.id
            left join t_user cu on nu.user_id = cu.id
            left join t_user ru on w.representative_id = ru.id
            where c.is_active = 1
            --and c.state = 1
            --and w.is_active = 1
            and w.year = #{year}
            order by f.name, o.name, n.name
            """)
    public List<Map<String, Object>> selectAllWorkflowControl(@Param("year") String year);

    @Select("""
            select c.id as id, c.workflow_id as workflowId, c.business_id as businessId, o.id as organizationId, o.name
            organizationName,
            f.id as formId, f.name as formName, c.current_node_id as currentNodeId, n.name as currentNodeName, c.state,
            ah.year, ah.month, u.id as createUserId, u.name as createUserRealName,
            (select name as parentOrgName from t_organization where id = o.parent_id) as parentOrgName,
            cu.name AS currentNodeUserName, cu.email AS currentNodeUserEmail
            from t_workflow_control c left join t_workflow w on w.id = c.workflow_id
            inner join t_organization o on o.id = w.organization_id
            inner join t_form f on w.form_id = f.id
            inner join t_workflow_node n on n.id = c.current_node_id
            inner join t_workflow_node_user nu on n.id = nu.node_id
            inner JOIN (
            SELECT id, YEAR, MONTH, is_active  FROM t_ambient_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_social_perf_two_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_social_perf_three_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_social_performance_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_ff_cm_fixed_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_ff_cm_mobile_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_ce_identification_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_emission_reduction_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_ce_basic_info_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_emp_commuting_head
            ) ah on ah.id = c.business_id
            left join t_user u on c.create_user_id = u.id
            left join t_user cu on nu.user_id = cu.id
            where c.is_active = 1
            and c.state = 1
            and ah.is_active = 1
            and c.business_id = #{businessId}
            """)
    public List<WorkflowControlVO> selectWorkflowControlByBusinessId(@Param("businessId") String businessId);

    @Select("""
            select TOP 1 t.year from (
            SELECT id, YEAR, MONTH, is_active  FROM t_ambient_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_social_perf_two_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_social_perf_three_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_social_performance_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_ff_cm_fixed_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_ff_cm_mobile_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_ce_identification_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_emission_reduction_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_ce_basic_info_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_emp_commuting_head
            ) t
            where t.id = #{businessId}
            """)
    public Integer selectFormYearByBusinessId(@Param("businessId") String businessId);

    @Select("""
            select TOP 1 t.month from (
            SELECT id, YEAR, MONTH, is_active  FROM t_ambient_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_social_perf_two_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_social_perf_three_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_social_performance_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_ff_cm_fixed_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_ff_cm_mobile_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_ce_identification_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_emission_reduction_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_ce_basic_info_head
            UNION
            SELECT id, YEAR, MONTH, is_active  FROM t_emp_commuting_head
            ) t
            where t.id = #{businessId}
            """)
    public Integer selectFormMonthByBusinessId(@Param("businessId") String businessId);

    @Select("""
            select w.id, o.name as organizationName, w.year, w.month, f.name as formName, isnull(ru.name, '') as representativeRealName, (
            select top 1 isnull(_u.name,'') from t_workflow_node _wn
            left join t_workflow_node_user _wnu on _wnu.node_id = _wn.id
            left join t_user _u on _wnu.user_id = _u.id
            where _wn.workflow_id = w.id and _wn.name = N'審核人一'
            ) as workflowUserRealName1, (
            select top 1 isnull(_u.name,'') from t_workflow_node _wn
            left join t_workflow_node_user _wnu on _wnu.node_id = _wn.id
            left join t_user _u on _wnu.user_id = _u.id
            where _wn.workflow_id = w.id and _wn.name = N'審核人二'
            ) as workflowUserRealName2, (
            select top 1 isnull(_u.name,'') from t_workflow_node _wn
            left join t_workflow_node_user _wnu on _wnu.node_id = _wn.id
            left join t_user _u on _wnu.user_id = _u.id
            where _wn.workflow_id = w.id and _wn.name = N'審核人三'
            ) as workflowUserRealName3, (
            select top 1 isnull(_u.name,'') from t_workflow_node _wn
            left join t_workflow_node_user _wnu on _wnu.node_id = _wn.id
            left join t_user _u on _wnu.user_id = _u.id
            where _wn.workflow_id = w.id and _wn.name = N'審核人四'
            ) as workflowUserRealName4
            from t_workflow w
            left join t_organization o on w.organization_id = o.id and o.is_deleted = 0
            left join t_form f on w.form_id = f.id
            left join t_user ru on w.representative_id = ru.id
            where w.is_active = 1
            and (#{organizationId} is null or o.no like (select TOP 1 concat(_o.no, '%') from t_organization _o where _o.id = #{organizationId}))
            and w.year = #{year}
            and w.month = #{month}
            and exists(select 1 from t_user_organization _uo where _uo.user_id = #{userId} and _uo.organization_id = o.id)
            """)
    public List<WorkflowExportData> selectWorkflowForExport(
            @Param("organizationId") String organizationId, @Param("year") Integer year,
            @Param("month") Integer month, @Param("userId") String userId);
    @Select("""
			select w.id, w.organization_id as organizationId, w.form_id as formId, w.year, w.month, w.name, w.code, w.is_active as isActive, 
			w.representative_id as representativeId, w.creation_time as creationTime, w.create_username as createUsername, w.create_user_id as createUserId, 
			w.last_update_username as lastUpdateUsername, w.last_update_user_id as lastUpdateUserId, w.last_update_version as lastUpdateVersion from t_workflow w 
			left join t_organization o on o.is_deleted = 0 and o.id = w.organization_id
            where w.is_active = 1
            and (#{organizationId} is null or o.no like (select TOP 1 concat(_o.no, '%') from t_organization _o where _o.id = #{organizationId}))
            and w.year = #{year}
            and w.month = #{month}
			and exists(select 1 from t_user_organization _uo where _uo.user_id = #{userId} and _uo.organization_id = o.id)
            """)
    public List<Workflow> selectWorkflow(
            @Param("organizationId") String organizationId, @Param("year") Integer year,
            @Param("month") Integer month, @Param("userId") String userId);

    /**
     * 获取工作流最新配置年月
     * <AUTHOR>
     * @date 2025/2/25 11:49
     * @return java.lang.String
     */
    @Select("""
            select
               top 1
               CONCAT(w.[year] , '-' , FORMAT(w.[month], '00'))
             from t_workflow w
             where w.is_active = 1
             order by w.[year] desc, w.[month] desc;
            """)
    String getLatestDate();

    @Select("""
            SELECT
              usr.id,
              usr.username,
              usr.name
            FROM
              t_workflow_node_user nodeUser
              INNER JOIN t_user usr ON usr.id= nodeUser.user_id
            WHERE
              nodeUser.node_id= #{nodeId}
            """)
    User getWorkFlowNodeUserByNodeId(String nodeId);
}
