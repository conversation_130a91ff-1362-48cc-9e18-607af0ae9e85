package com.csci.susdev.mapper;

import com.csci.susdev.model.EmissionReductionInfo;
import com.csci.susdev.model.EmissionReductionInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface EmissionReductionInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_info
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    long countByExample(EmissionReductionInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_info
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    int deleteByExample(EmissionReductionInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_info
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_info
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    int insert(EmissionReductionInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_info
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    int insertSelective(EmissionReductionInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_info
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    List<EmissionReductionInfo> selectByExample(EmissionReductionInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_info
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    EmissionReductionInfo selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_info
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    int updateByExampleSelective(@Param("record") EmissionReductionInfo record, @Param("example") EmissionReductionInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_info
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    int updateByExample(@Param("record") EmissionReductionInfo record, @Param("example") EmissionReductionInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_info
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    int updateByPrimaryKeySelective(EmissionReductionInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_info
     *
     * @mbg.generated Thu Jun 20 18:41:32 HKT 2024
     */
    int updateByPrimaryKey(EmissionReductionInfo record);
}