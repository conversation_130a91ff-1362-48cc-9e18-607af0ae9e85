package com.csci.susdev.mapper;

import com.csci.susdev.model.FfCmFixedDetail;
import com.csci.susdev.model.FfCmFixedDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FfCmFixedDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_detail
     *
     * @mbg.generated Tue Apr 02 16:05:29 HKT 2024
     */
    long countByExample(FfCmFixedDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_detail
     *
     * @mbg.generated Tue Apr 02 16:05:29 HKT 2024
     */
    int deleteByExample(FfCmFixedDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_detail
     *
     * @mbg.generated Tue Apr 02 16:05:29 HKT 2024
     */
    int insert(FfCmFixedDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_detail
     *
     * @mbg.generated Tue Apr 02 16:05:29 HKT 2024
     */
    int insertSelective(FfCmFixedDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_detail
     *
     * @mbg.generated Tue Apr 02 16:05:29 HKT 2024
     */
    List<FfCmFixedDetail> selectByExample(FfCmFixedDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_detail
     *
     * @mbg.generated Tue Apr 02 16:05:29 HKT 2024
     */
    int updateByExampleSelective(@Param("record") FfCmFixedDetail record, @Param("example") FfCmFixedDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_fixed_detail
     *
     * @mbg.generated Tue Apr 02 16:05:29 HKT 2024
     */
    int updateByExample(@Param("record") FfCmFixedDetail record, @Param("example") FfCmFixedDetailExample example);
}