package com.csci.susdev.mapper;

import com.csci.susdev.model.AmbientDetail;
import com.csci.susdev.model.AmbientDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AmbientDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    long countByExample(AmbientDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    int deleteByExample(AmbientDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    int insert(AmbientDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    int insertSelective(AmbientDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    List<AmbientDetail> selectByExample(AmbientDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    AmbientDetail selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") AmbientDetail record, @Param("example") AmbientDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") AmbientDetail record, @Param("example") AmbientDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(AmbientDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(AmbientDetail record);

}
