package com.csci.susdev.mapper;

import com.csci.susdev.model.CeIdentificationDetail;
import com.csci.susdev.model.CeIdentificationDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CeIdentificationDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_detail
     *
     * @mbg.generated Mon Sep 11 00:08:56 HKT 2023
     */
    long countByExample(CeIdentificationDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_detail
     *
     * @mbg.generated Mon Sep 11 00:08:56 HKT 2023
     */
    int deleteByExample(CeIdentificationDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_detail
     *
     * @mbg.generated Mon Sep 11 00:08:56 HKT 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_detail
     *
     * @mbg.generated Mon Sep 11 00:08:56 HKT 2023
     */
    int insert(CeIdentificationDetail row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_detail
     *
     * @mbg.generated Mon Sep 11 00:08:56 HKT 2023
     */
    int insertSelective(CeIdentificationDetail row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_detail
     *
     * @mbg.generated Mon Sep 11 00:08:56 HKT 2023
     */
    List<CeIdentificationDetail> selectByExample(CeIdentificationDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_detail
     *
     * @mbg.generated Mon Sep 11 00:08:56 HKT 2023
     */
    CeIdentificationDetail selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_detail
     *
     * @mbg.generated Mon Sep 11 00:08:56 HKT 2023
     */
    int updateByExampleSelective(@Param("row") CeIdentificationDetail row, @Param("example") CeIdentificationDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_detail
     *
     * @mbg.generated Mon Sep 11 00:08:56 HKT 2023
     */
    int updateByExample(@Param("row") CeIdentificationDetail row, @Param("example") CeIdentificationDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_detail
     *
     * @mbg.generated Mon Sep 11 00:08:56 HKT 2023
     */
    int updateByPrimaryKeySelective(CeIdentificationDetail row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_detail
     *
     * @mbg.generated Mon Sep 11 00:08:56 HKT 2023
     */
    int updateByPrimaryKey(CeIdentificationDetail row);
}