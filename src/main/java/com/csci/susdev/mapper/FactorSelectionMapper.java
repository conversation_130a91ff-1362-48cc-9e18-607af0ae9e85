package com.csci.susdev.mapper;

import com.csci.susdev.model.FactorSelection;
import com.csci.susdev.model.FactorSelectionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FactorSelectionMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_selection
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    long countByExample(FactorSelectionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_selection
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    int deleteByExample(FactorSelectionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_selection
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_selection
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    int insert(FactorSelection record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_selection
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    int insertSelective(FactorSelection record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_selection
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    List<FactorSelection> selectByExample(FactorSelectionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_selection
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    FactorSelection selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_selection
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    int updateByExampleSelective(@Param("record") FactorSelection record, @Param("example") FactorSelectionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_selection
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    int updateByExample(@Param("record") FactorSelection record, @Param("example") FactorSelectionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_selection
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    int updateByPrimaryKeySelective(FactorSelection record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_selection
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    int updateByPrimaryKey(FactorSelection record);
}