package com.csci.susdev.mapper;

import com.csci.susdev.model.AmbientEnergyBill;
import com.csci.susdev.model.AmbientEnergyBillExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AmbientEnergyBillMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_energy_bill
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    long countByExample(AmbientEnergyBillExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_energy_bill
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    int deleteByExample(AmbientEnergyBillExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_energy_bill
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_energy_bill
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    int insert(AmbientEnergyBill row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_energy_bill
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    int insertSelective(AmbientEnergyBill row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_energy_bill
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    List<AmbientEnergyBill> selectByExample(AmbientEnergyBillExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_energy_bill
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    AmbientEnergyBill selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_energy_bill
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    int updateByExampleSelective(@Param("row") AmbientEnergyBill row, @Param("example") AmbientEnergyBillExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_energy_bill
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    int updateByExample(@Param("row") AmbientEnergyBill row, @Param("example") AmbientEnergyBillExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_energy_bill
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    int updateByPrimaryKeySelective(AmbientEnergyBill row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_energy_bill
     *
     * @mbg.generated Mon Oct 09 15:36:35 HKT 2023
     */
    int updateByPrimaryKey(AmbientEnergyBill row);
}