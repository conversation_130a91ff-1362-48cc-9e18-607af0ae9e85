package com.csci.susdev.mapper;

import com.csci.susdev.model.Organization;
import com.csci.susdev.model.OrganizationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface OrganizationMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_organization
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	long countByExample(OrganizationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_organization
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	int deleteByExample(OrganizationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_organization
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_organization
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	int insert(Organization row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_organization
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	int insertSelective(Organization row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_organization
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	List<Organization> selectByExample(OrganizationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_organization
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	Organization selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_organization
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") Organization row, @Param("example") OrganizationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_organization
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	int updateByExample(@Param("row") Organization row, @Param("example") OrganizationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_organization
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	int updateByPrimaryKeySelective(Organization row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_organization
	 * @mbg.generated  Tue Sep 12 09:58:28 HKT 2023
	 */
	int updateByPrimaryKey(Organization row);
}