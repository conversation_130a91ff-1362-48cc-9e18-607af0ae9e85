package com.csci.susdev.mapper;

import com.csci.susdev.model.AccountingManagement;
import com.csci.susdev.model.AccountingManagementExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AccountingManagementMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    long countByExample(AccountingManagementExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    int deleteByExample(AccountingManagementExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    int insert(AccountingManagement record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    int insertSelective(AccountingManagement record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    List<AccountingManagement> selectByExample(AccountingManagementExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    AccountingManagement selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    int updateByExampleSelective(@Param("record") AccountingManagement record, @Param("example") AccountingManagementExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    int updateByExample(@Param("record") AccountingManagement record, @Param("example") AccountingManagementExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    int updateByPrimaryKeySelective(AccountingManagement record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    int updateByPrimaryKey(AccountingManagement record);
}