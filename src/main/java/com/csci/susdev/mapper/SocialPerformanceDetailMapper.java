package com.csci.susdev.mapper;

import com.csci.susdev.model.SocialPerformanceDetail;
import com.csci.susdev.model.SocialPerformanceDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SocialPerformanceDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_detail
     *
     * @mbg.generated
     */
    long countByExample(SocialPerformanceDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_detail
     *
     * @mbg.generated
     */
    int deleteByExample(SocialPerformanceDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_detail
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_detail
     *
     * @mbg.generated
     */
    int insert(SocialPerformanceDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_detail
     *
     * @mbg.generated
     */
    int insertSelective(SocialPerformanceDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_detail
     *
     * @mbg.generated
     */
    List<SocialPerformanceDetail> selectByExample(SocialPerformanceDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_detail
     *
     * @mbg.generated
     */
    SocialPerformanceDetail selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_detail
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") SocialPerformanceDetail record, @Param("example") SocialPerformanceDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_detail
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") SocialPerformanceDetail record, @Param("example") SocialPerformanceDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(SocialPerformanceDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_performance_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(SocialPerformanceDetail record);
}