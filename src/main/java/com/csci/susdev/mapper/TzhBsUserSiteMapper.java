package com.csci.susdev.mapper;

import com.csci.susdev.model.TzhBsUserSite;
import com.csci.susdev.model.TzhBsUserSiteExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhBsUserSiteMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserSite
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    long countByExample(TzhBsUserSiteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserSite
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    int deleteByExample(TzhBsUserSiteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserSite
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserSite
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    int insert(TzhBsUserSite record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserSite
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    int insertSelective(TzhBsUserSite record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserSite
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    List<TzhBsUserSite> selectByExample(TzhBsUserSiteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserSite
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    TzhBsUserSite selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserSite
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    int updateByExampleSelective(@Param("record") TzhBsUserSite record, @Param("example") TzhBsUserSiteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserSite
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    int updateByExample(@Param("record") TzhBsUserSite record, @Param("example") TzhBsUserSiteExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserSite
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    int updateByPrimaryKeySelective(TzhBsUserSite record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_UserSite
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    int updateByPrimaryKey(TzhBsUserSite record);
}