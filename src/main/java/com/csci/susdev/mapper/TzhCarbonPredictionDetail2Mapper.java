package com.csci.susdev.mapper;

import com.csci.susdev.model.TzhCarbonPredictionDetail2;
import com.csci.susdev.model.TzhCarbonPredictionDetail2Example;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhCarbonPredictionDetail2Mapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_2
     *
     * @mbg.generated Tue Jul 26 12:35:15 HKT 2022
     */
    long countByExample(TzhCarbonPredictionDetail2Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_2
     *
     * @mbg.generated Tue Jul 26 12:35:15 HKT 2022
     */
    int deleteByExample(TzhCarbonPredictionDetail2Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_2
     *
     * @mbg.generated Tue Jul 26 12:35:15 HKT 2022
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_2
     *
     * @mbg.generated Tue Jul 26 12:35:15 HKT 2022
     */
    int insert(TzhCarbonPredictionDetail2 row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_2
     *
     * @mbg.generated Tue Jul 26 12:35:15 HKT 2022
     */
    int insertSelective(TzhCarbonPredictionDetail2 row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_2
     *
     * @mbg.generated Tue Jul 26 12:35:15 HKT 2022
     */
    List<TzhCarbonPredictionDetail2> selectByExample(TzhCarbonPredictionDetail2Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_2
     *
     * @mbg.generated Tue Jul 26 12:35:15 HKT 2022
     */
    TzhCarbonPredictionDetail2 selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_2
     *
     * @mbg.generated Tue Jul 26 12:35:15 HKT 2022
     */
    int updateByExampleSelective(@Param("row") TzhCarbonPredictionDetail2 row, @Param("example") TzhCarbonPredictionDetail2Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_2
     *
     * @mbg.generated Tue Jul 26 12:35:15 HKT 2022
     */
    int updateByExample(@Param("row") TzhCarbonPredictionDetail2 row, @Param("example") TzhCarbonPredictionDetail2Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_2
     *
     * @mbg.generated Tue Jul 26 12:35:15 HKT 2022
     */
    int updateByPrimaryKeySelective(TzhCarbonPredictionDetail2 row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_detail_2
     *
     * @mbg.generated Tue Jul 26 12:35:15 HKT 2022
     */
    int updateByPrimaryKey(TzhCarbonPredictionDetail2 row);
}