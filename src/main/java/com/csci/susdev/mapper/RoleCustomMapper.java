package com.csci.susdev.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface RoleCustomMapper {

    @Select("""
            select tr.id, tr.code, tr.name from t_role tr
            inner join t_user_role tur on tur.role_id = tr.id
            inner join t_user tu on tu.id = tur.user_id
            where tu.username = #{username}
            """)
    public List<Map<String, Object>> getRoleListByUsername(@Param("username") String username);
}
