package com.csci.susdev.mapper;

import com.csci.susdev.model.ProtocolDetail;
import com.csci.susdev.model.ProtocolDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProtocolDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_detail
     *
     * @mbg.generated Wed Apr 10 11:07:02 HKT 2024
     */
    long countByExample(ProtocolDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_detail
     *
     * @mbg.generated Wed Apr 10 11:07:02 HKT 2024
     */
    int deleteByExample(ProtocolDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_detail
     *
     * @mbg.generated Wed Apr 10 11:07:02 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_detail
     *
     * @mbg.generated Wed Apr 10 11:07:02 HKT 2024
     */
    int insert(ProtocolDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_detail
     *
     * @mbg.generated Wed Apr 10 11:07:02 HKT 2024
     */
    int insertSelective(ProtocolDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_detail
     *
     * @mbg.generated Wed Apr 10 11:07:02 HKT 2024
     */
    List<ProtocolDetail> selectByExample(ProtocolDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_detail
     *
     * @mbg.generated Wed Apr 10 11:07:02 HKT 2024
     */
    ProtocolDetail selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_detail
     *
     * @mbg.generated Wed Apr 10 11:07:02 HKT 2024
     */
    int updateByExampleSelective(@Param("record") ProtocolDetail record, @Param("example") ProtocolDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_detail
     *
     * @mbg.generated Wed Apr 10 11:07:02 HKT 2024
     */
    int updateByExample(@Param("record") ProtocolDetail record, @Param("example") ProtocolDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_detail
     *
     * @mbg.generated Wed Apr 10 11:07:02 HKT 2024
     */
    int updateByPrimaryKeySelective(ProtocolDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_detail
     *
     * @mbg.generated Wed Apr 10 11:07:02 HKT 2024
     */
    int updateByPrimaryKey(ProtocolDetail record);
}