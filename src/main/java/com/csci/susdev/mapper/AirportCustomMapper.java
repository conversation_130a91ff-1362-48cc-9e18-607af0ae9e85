package com.csci.susdev.mapper;

import com.csci.susdev.model.Airport;
import com.csci.susdev.vo.AirportVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AirportCustomMapper {
	
    @Select("""
            SELECT t.id, t.code, t.name, t.name_sc AS nameSc, t.name_en AS nameEn
            FROM t_airport t
            WHERE (t.code IN (
                SELECT f.destination FROM t_flight_info f 
                WHERE f.start_place = #{startPlace}
                AND f.record_year = (SELECT MAX(record_year) FROM t_flight_info)
            ) OR #{startPlace} = '')
            AND (
                t.name like CONCAT('%', #{keyword}, '%') 
                OR t.name_sc like CONCAT('%', #{keyword}, '%') 
                OR t.name_en like CONCAT('%', #{keyword}, '%') 
                OR t.code like CONCAT('%', #{keyword}, '%') 
                OR t.id = #{keyword}
            )
            ORDER BY t.code
            """)
    public List<AirportVO> list(@Param("startPlace") String startPlace, @Param("keyword") String keyword);

    /**
     * 获取所有的机场编码
     * @return
     */
    @Select("""
            select code from t_airport
            """)
    List<String> getAllAirPortCode();

    @Select("""
            SELECT top 1 t.id, t.code, t.name, t.name_sc AS nameSc, t.name_en AS nameEn
            FROM t_airport t
            WHERE t.code = #{code}
            """)
    AirportVO getByCode(@Param("code") String code);
}