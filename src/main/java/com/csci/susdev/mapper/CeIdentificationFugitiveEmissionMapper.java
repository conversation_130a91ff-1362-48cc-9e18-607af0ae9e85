package com.csci.susdev.mapper;

import com.csci.susdev.model.CeIdentificationFugitiveEmission;
import com.csci.susdev.model.CeIdentificationFugitiveEmissionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CeIdentificationFugitiveEmissionMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    long countByExample(CeIdentificationFugitiveEmissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    int deleteByExample(CeIdentificationFugitiveEmissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    int insert(CeIdentificationFugitiveEmission record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    int insertSelective(CeIdentificationFugitiveEmission record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    List<CeIdentificationFugitiveEmission> selectByExample(CeIdentificationFugitiveEmissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    CeIdentificationFugitiveEmission selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    int updateByExampleSelective(@Param("record") CeIdentificationFugitiveEmission record, @Param("example") CeIdentificationFugitiveEmissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    int updateByExample(@Param("record") CeIdentificationFugitiveEmission record, @Param("example") CeIdentificationFugitiveEmissionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    int updateByPrimaryKeySelective(CeIdentificationFugitiveEmission record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_fugitive_emission
     *
     * @mbg.generated Wed Apr 02 16:23:01 CST 2025
     */
    int updateByPrimaryKey(CeIdentificationFugitiveEmission record);
}