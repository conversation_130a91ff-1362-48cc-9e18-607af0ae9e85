package com.csci.susdev.mapper;

import com.csci.susdev.model.Batch;
import org.apache.ibatis.annotations.Param;

public interface BatchCustomMapper {

    /**
     * 相同的区域下，同一时间只能有一个批次被激活
     *
     * @param record
     * @return
     */
    int insertBatchIgnoreExist(Batch record);

    /**
     * 根据区域信息、生效日期查询出生效的批次信息
     *
     * @param areaId 区域id
     * @param year   年份
     * @param month  月份
     * @return
     */
    Batch selectBatchBy(@Param("areaId") String areaId, @Param("year") Integer year, @Param("month") Integer month);

}
