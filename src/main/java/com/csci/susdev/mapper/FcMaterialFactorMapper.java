package com.csci.susdev.mapper;

import com.csci.susdev.model.FcMaterialFactor;
import com.csci.susdev.model.FcMaterialFactorExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FcMaterialFactorMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_material_factor
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    long countByExample(FcMaterialFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_material_factor
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    int deleteByExample(FcMaterialFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_material_factor
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_material_factor
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    int insert(FcMaterialFactor record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_material_factor
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    int insertSelective(FcMaterialFactor record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_material_factor
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    List<FcMaterialFactor> selectByExample(FcMaterialFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_material_factor
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    FcMaterialFactor selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_material_factor
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    int updateByExampleSelective(@Param("record") FcMaterialFactor record, @Param("example") FcMaterialFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_material_factor
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    int updateByExample(@Param("record") FcMaterialFactor record, @Param("example") FcMaterialFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_material_factor
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    int updateByPrimaryKeySelective(FcMaterialFactor record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_material_factor
     *
     * @mbg.generated Mon Mar 17 16:21:30 CST 2025
     */
    int updateByPrimaryKey(FcMaterialFactor record);
}