package com.csci.susdev.mapper;

import com.csci.susdev.model.TzhBsRole;
import com.csci.susdev.model.TzhBsRoleExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhBsRoleMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_Role
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    long countByExample(TzhBsRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_Role
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    int deleteByExample(TzhBsRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_Role
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_Role
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    int insert(TzhBsRole record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_Role
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    int insertSelective(TzhBsRole record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_Role
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    List<TzhBsRole> selectByExample(TzhBsRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_Role
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    TzhBsRole selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_Role
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    int updateByExampleSelective(@Param("record") TzhBsRole record, @Param("example") TzhBsRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_Role
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    int updateByExample(@Param("record") TzhBsRole record, @Param("example") TzhBsRoleExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_Role
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    int updateByPrimaryKeySelective(TzhBsRole record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_Role
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    int updateByPrimaryKey(TzhBsRole record);
}