package com.csci.susdev.mapper;

import com.csci.susdev.model.FfCmMobileHead;
import com.csci.susdev.model.FfCmMobileHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FfCmMobileHeadMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_mobile_head
     *
     * @mbg.generated Thu Apr 18 16:47:35 HKT 2024
     */
    long countByExample(FfCmMobileHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_mobile_head
     *
     * @mbg.generated Thu Apr 18 16:47:35 HKT 2024
     */
    int deleteByExample(FfCmMobileHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_mobile_head
     *
     * @mbg.generated Thu Apr 18 16:47:35 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_mobile_head
     *
     * @mbg.generated Thu Apr 18 16:47:35 HKT 2024
     */
    int insert(FfCmMobileHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_mobile_head
     *
     * @mbg.generated Thu Apr 18 16:47:35 HKT 2024
     */
    int insertSelective(FfCmMobileHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_mobile_head
     *
     * @mbg.generated Thu Apr 18 16:47:35 HKT 2024
     */
    List<FfCmMobileHead> selectByExample(FfCmMobileHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_mobile_head
     *
     * @mbg.generated Thu Apr 18 16:47:35 HKT 2024
     */
    FfCmMobileHead selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_mobile_head
     *
     * @mbg.generated Thu Apr 18 16:47:35 HKT 2024
     */
    int updateByExampleSelective(@Param("record") FfCmMobileHead record, @Param("example") FfCmMobileHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_mobile_head
     *
     * @mbg.generated Thu Apr 18 16:47:35 HKT 2024
     */
    int updateByExample(@Param("record") FfCmMobileHead record, @Param("example") FfCmMobileHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_mobile_head
     *
     * @mbg.generated Thu Apr 18 16:47:35 HKT 2024
     */
    int updateByPrimaryKeySelective(FfCmMobileHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ff_cm_mobile_head
     *
     * @mbg.generated Thu Apr 18 16:47:35 HKT 2024
     */
    int updateByPrimaryKey(FfCmMobileHead record);
}