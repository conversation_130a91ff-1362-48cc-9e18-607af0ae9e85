package com.csci.susdev.mapper;

import com.csci.susdev.model.FfCmMobileDetail;
import com.csci.susdev.model.FfCmMobileDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FfCmMobileDetailMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ff_cm_mobile_detail
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	long countByExample(FfCmMobileDetailExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ff_cm_mobile_detail
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	int deleteByExample(FfCmMobileDetailExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ff_cm_mobile_detail
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ff_cm_mobile_detail
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	int insert(FfCmMobileDetail row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ff_cm_mobile_detail
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	int insertSelective(FfCmMobileDetail row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ff_cm_mobile_detail
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	List<FfCmMobileDetail> selectByExample(FfCmMobileDetailExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ff_cm_mobile_detail
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	FfCmMobileDetail selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ff_cm_mobile_detail
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") FfCmMobileDetail row, @Param("example") FfCmMobileDetailExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ff_cm_mobile_detail
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	int updateByExample(@Param("row") FfCmMobileDetail row, @Param("example") FfCmMobileDetailExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ff_cm_mobile_detail
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	int updateByPrimaryKeySelective(FfCmMobileDetail row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ff_cm_mobile_detail
	 * @mbg.generated  Fri Sep 29 12:25:19 HKT 2023
	 */
	int updateByPrimaryKey(FfCmMobileDetail row);
}