package com.csci.susdev.mapper;

import com.csci.susdev.model.CeIdentificationHead;
import com.csci.susdev.model.CeIdentificationHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CeIdentificationHeadMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_head
     *
     * @mbg.generated Thu Apr 18 16:01:50 HKT 2024
     */
    long countByExample(CeIdentificationHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_head
     *
     * @mbg.generated Thu Apr 18 16:01:50 HKT 2024
     */
    int deleteByExample(CeIdentificationHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_head
     *
     * @mbg.generated Thu Apr 18 16:01:50 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_head
     *
     * @mbg.generated Thu Apr 18 16:01:50 HKT 2024
     */
    int insert(CeIdentificationHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_head
     *
     * @mbg.generated Thu Apr 18 16:01:50 HKT 2024
     */
    int insertSelective(CeIdentificationHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_head
     *
     * @mbg.generated Thu Apr 18 16:01:50 HKT 2024
     */
    List<CeIdentificationHead> selectByExample(CeIdentificationHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_head
     *
     * @mbg.generated Thu Apr 18 16:01:50 HKT 2024
     */
    CeIdentificationHead selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_head
     *
     * @mbg.generated Thu Apr 18 16:01:50 HKT 2024
     */
    int updateByExampleSelective(@Param("record") CeIdentificationHead record, @Param("example") CeIdentificationHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_head
     *
     * @mbg.generated Thu Apr 18 16:01:50 HKT 2024
     */
    int updateByExample(@Param("record") CeIdentificationHead record, @Param("example") CeIdentificationHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_head
     *
     * @mbg.generated Thu Apr 18 16:01:50 HKT 2024
     */
    int updateByPrimaryKeySelective(CeIdentificationHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_identification_head
     *
     * @mbg.generated Thu Apr 18 16:01:50 HKT 2024
     */
    int updateByPrimaryKey(CeIdentificationHead record);
}