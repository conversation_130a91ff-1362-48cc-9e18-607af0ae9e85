package com.csci.susdev.mapper;

import com.csci.susdev.model.AmbientHeadExt;
import com.csci.susdev.model.AmbientHeadExtExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AmbientHeadExtMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_head_ext
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    long countByExample(AmbientHeadExtExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_head_ext
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    int deleteByExample(AmbientHeadExtExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_head_ext
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_head_ext
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    int insert(AmbientHeadExt record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_head_ext
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    int insertSelective(AmbientHeadExt record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_head_ext
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    List<AmbientHeadExt> selectByExample(AmbientHeadExtExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_head_ext
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    AmbientHeadExt selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_head_ext
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    int updateByExampleSelective(@Param("record") AmbientHeadExt record, @Param("example") AmbientHeadExtExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_head_ext
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    int updateByExample(@Param("record") AmbientHeadExt record, @Param("example") AmbientHeadExtExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_head_ext
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    int updateByPrimaryKeySelective(AmbientHeadExt record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_head_ext
     *
     * @mbg.generated Tue Jul 09 18:28:18 HKT 2024
     */
    int updateByPrimaryKey(AmbientHeadExt record);
}