package com.csci.susdev.mapper;

import com.csci.susdev.model.FlightInfo;
import com.csci.susdev.model.FlightInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FlightInfoMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	long countByExample(FlightInfoExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	int deleteByExample(FlightInfoExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	int insert(FlightInfo row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	int insertSelective(FlightInfo row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	List<FlightInfo> selectByExample(FlightInfoExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	FlightInfo selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") FlightInfo row, @Param("example") FlightInfoExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	int updateByExample(@Param("row") FlightInfo row, @Param("example") FlightInfoExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	int updateByPrimaryKeySelective(FlightInfo row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	int updateByPrimaryKey(FlightInfo row);
}