package com.csci.susdev.mapper;

import com.csci.susdev.model.FcEnergyFactorEsgDataset;
import com.csci.susdev.model.FcEnergyFactorEsgDatasetExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FcEnergyFactorEsgDatasetMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    long countByExample(FcEnergyFactorEsgDatasetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    int deleteByExample(FcEnergyFactorEsgDatasetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    int insert(FcEnergyFactorEsgDataset record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    int insertSelective(FcEnergyFactorEsgDataset record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    List<FcEnergyFactorEsgDataset> selectByExample(FcEnergyFactorEsgDatasetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    FcEnergyFactorEsgDataset selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    int updateByExampleSelective(@Param("record") FcEnergyFactorEsgDataset record, @Param("example") FcEnergyFactorEsgDatasetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    int updateByExample(@Param("record") FcEnergyFactorEsgDataset record, @Param("example") FcEnergyFactorEsgDatasetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    int updateByPrimaryKeySelective(FcEnergyFactorEsgDataset record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_energy_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:48 CST 2025
     */
    int updateByPrimaryKey(FcEnergyFactorEsgDataset record);
}