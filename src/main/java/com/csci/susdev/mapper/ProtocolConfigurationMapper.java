package com.csci.susdev.mapper;

import com.csci.susdev.model.ProtocolConfiguration;
import com.csci.susdev.model.ProtocolConfigurationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProtocolConfigurationMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_configuration
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    long countByExample(ProtocolConfigurationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_configuration
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    int deleteByExample(ProtocolConfigurationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_configuration
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_configuration
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    int insert(ProtocolConfiguration record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_configuration
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    int insertSelective(ProtocolConfiguration record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_configuration
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    List<ProtocolConfiguration> selectByExample(ProtocolConfigurationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_configuration
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    ProtocolConfiguration selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_configuration
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    int updateByExampleSelective(@Param("record") ProtocolConfiguration record, @Param("example") ProtocolConfigurationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_configuration
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    int updateByExample(@Param("record") ProtocolConfiguration record, @Param("example") ProtocolConfigurationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_configuration
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    int updateByPrimaryKeySelective(ProtocolConfiguration record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_configuration
     *
     * @mbg.generated Thu Mar 20 16:09:27 CST 2025
     */
    int updateByPrimaryKey(ProtocolConfiguration record);
}