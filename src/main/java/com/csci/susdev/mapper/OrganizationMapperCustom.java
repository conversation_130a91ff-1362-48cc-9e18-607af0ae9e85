package com.csci.susdev.mapper;

import com.csci.susdev.model.Organization;
import com.csci.susdev.vo.CompanyDeptVO;
import com.csci.susdev.vo.CompanyPlatformVO;
import com.csci.susdev.vo.OrganizationTreeVO;
import com.csci.susdev.vo.SocialPerfTwoActivityVO;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface OrganizationMapperCustom {

    List<Organization> selectUserDepartments(@Param("userId") String userId);

    List<CompanyDeptVO> selectCompanyDept(@Param("keyword") String keyword, @Param("keywordSc") String keywordSc,
                                          @Param("userId") String userId, @Param("showLeafOnly") Boolean showLeafOnly,
                                          @Param("pattern") String pattern, @Param("patternSc") String patternSc,
                                          @Param("showCompanyOnly") Boolean showCompanyOnly);

    List<Organization> selectLeafChildrenByNo(@Param("no") String no);

    @Select("""
            select o.id from t_organization o 
            where exists (
            select 1 from t_organization _o
            inner join t_user_organization _uo on _o.id = _uo.organization_id
            inner join t_user _u on _u.id = _uo.user_id
            where _o.is_deleted = 0 and _u.id = #{userId}
            and o.no like concat(_o.no,'%')
            )
            """)
    public List<String> selectOrgIdsByUserId(@Param("userId") String userId);

    @Select("""
            select o.id from t_organization o 
            where exists (
            select 1 from t_organization _o
            inner join Tzh_Bs_UserSite _uo on _o.id = _uo.SiteId
            inner join t_user _u on _u.username = _uo.UserName
            where _o.is_deleted = 0 and _u.id = #{userId}
            and o.no like concat(_o.no,'%')
            )
            """)
    public List<String> selectBsOrgIdsByUserId(@Param("userId") String userId);

    @Select("""
        select u.id from t_user u
        where exists (
        select 1 from t_organization _o
        inner join t_user_organization _uo on _o.id = _uo.organization_id
        inner join t_user _u on _u.id = _uo.user_id
        where _o.is_deleted = 0 and _u.id = u.id
        and _o.id = #{organizationId}
        )
    """)
    List<String> selectUserIdsByOrgId(@Param("organizationId") String organizationId);

    @Select("""
        select u.username from t_user u
        where exists (
        select 1 from t_organization _o
        inner join Tzh_Bs_UserSite _uo on _o.id = _uo.SiteId
        inner join t_user _u on _u.username = _uo.UserName
        where _o.is_deleted = 0 and _u.id = u.id
        and _o.id = #{organizationId}
        )
    """)
    List<String> selectUserNamesByBsOrgId(@Param("organizationId") String organizationId);


    List<CompanyPlatformVO> listFuzzySearch(@Param("keyword") String keyword, @Param("keywordSc") String keywordSc);

    @Select("""
        WITH org_tree AS (
            SELECT
                *
            FROM t_organization WHERE id = #{organizationId} and is_deleted = 0
            UNION ALL
            SELECT o.* FROM t_organization o
            INNER JOIN org_tree ot ON o.parent_id = ot.id
            WHERE o.is_deleted = 0
        )
        SELECT
            id, parent_id, no, name, code
        FROM org_tree
        order by no
    """)
    List<OrganizationTreeVO> selectOrganizationTree(@Param("organizationId") String organizationId);

}
