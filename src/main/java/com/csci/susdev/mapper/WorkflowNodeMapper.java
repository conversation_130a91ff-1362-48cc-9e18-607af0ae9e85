package com.csci.susdev.mapper;

import com.csci.susdev.model.WorkflowNode;
import com.csci.susdev.model.WorkflowNodeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WorkflowNodeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    long countByExample(WorkflowNodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    int deleteByExample(WorkflowNodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    int insert(WorkflowNode record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    int insertSelective(WorkflowNode record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    List<WorkflowNode> selectByExample(WorkflowNodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    WorkflowNode selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") WorkflowNode record, @Param("example") WorkflowNodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") WorkflowNode record, @Param("example") WorkflowNodeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(WorkflowNode record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(WorkflowNode record);
}