package com.csci.susdev.mapper;

import com.csci.susdev.model.EmissionReductionHead;
import com.csci.susdev.model.EmissionReductionHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface EmissionReductionHeadMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_head
     *
     * @mbg.generated Thu Apr 18 16:45:23 HKT 2024
     */
    long countByExample(EmissionReductionHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_head
     *
     * @mbg.generated Thu Apr 18 16:45:23 HKT 2024
     */
    int deleteByExample(EmissionReductionHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_head
     *
     * @mbg.generated Thu Apr 18 16:45:23 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_head
     *
     * @mbg.generated Thu Apr 18 16:45:23 HKT 2024
     */
    int insert(EmissionReductionHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_head
     *
     * @mbg.generated Thu Apr 18 16:45:23 HKT 2024
     */
    int insertSelective(EmissionReductionHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_head
     *
     * @mbg.generated Thu Apr 18 16:45:23 HKT 2024
     */
    List<EmissionReductionHead> selectByExample(EmissionReductionHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_head
     *
     * @mbg.generated Thu Apr 18 16:45:23 HKT 2024
     */
    EmissionReductionHead selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_head
     *
     * @mbg.generated Thu Apr 18 16:45:23 HKT 2024
     */
    int updateByExampleSelective(@Param("record") EmissionReductionHead record, @Param("example") EmissionReductionHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_head
     *
     * @mbg.generated Thu Apr 18 16:45:23 HKT 2024
     */
    int updateByExample(@Param("record") EmissionReductionHead record, @Param("example") EmissionReductionHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_head
     *
     * @mbg.generated Thu Apr 18 16:45:23 HKT 2024
     */
    int updateByPrimaryKeySelective(EmissionReductionHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_emission_reduction_head
     *
     * @mbg.generated Thu Apr 18 16:45:23 HKT 2024
     */
    int updateByPrimaryKey(EmissionReductionHead record);
}