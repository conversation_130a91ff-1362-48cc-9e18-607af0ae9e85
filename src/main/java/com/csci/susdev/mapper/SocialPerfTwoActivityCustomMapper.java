package com.csci.susdev.mapper;

import com.csci.susdev.model.SocialPerfTwoActivity;
import com.csci.susdev.vo.SocialPerfTwoActivityVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface SocialPerfTwoActivityCustomMapper {

    @Select("""
            select spta.id, spta.head_id as headId, spta.organization_id as organizationId,
            spta.type, spta.name, spta.date, spta.staff_count as staffCount, spta.hour_count as hourCount,
            spta.service_target as serviceTarget, spta.beneficiary_organization as beneficiaryOrganization,
            spta.no_of_beneficiaries as noOfBeneficiaries, spta.total_donation as totalDonation, spta.donation_type as donationType,
            spta.donation_qty as donationQty
            from t_social_perf_two_activity spta
            inner join t_organization o on o.id = spta.organization_id and o.is_deleted = 0
            where o.no like (
            select TOP 1 concat(_o.no, '%') from t_social_perf_two_head _spth
            inner join t_organization _o on _o.id = _spth.organization_id and _o.is_deleted = 0
            where _spth.id = #{headId}
            )
            """)
    public List<SocialPerfTwoActivity> getIntegratedData(@Param("headId") String headId);


    @Select("""
            select spta.id, spta.head_id as headId, spta.organization_id as organizationId, o.name as organizationName,
            spta.type, spta.name, spta.date, spta.staff_count as staffCount, spta.hour_count as hourCount,
            spta.service_target as serviceTarget, spta.beneficiary_organization as beneficiaryOrganization,
            spta.no_of_beneficiaries as noOfBeneficiaries, spta.total_donation as totalDonation, spta.donation_type as donationType,
            spta.donation_qty as donationQty, spta.last_update_version as lastUpdateVersion
            from t_social_perf_two_activity spta
            inner join t_organization o on o.id = spta.organization_id and o.is_deleted = 0
            where spta.head_id = #{headId}
            """)
    public List<SocialPerfTwoActivityVO> listVO(@Param("headId") String headId);
}
