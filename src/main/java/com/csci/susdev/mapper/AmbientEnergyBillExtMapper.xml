<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.AmbientEnergyBillExtMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.AmbientEnergyBillExt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 16 12:03:59 HKT 2024.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="head_id" jdbcType="CHAR" property="headId" />
    <result column="type" jdbcType="NVARCHAR" property="type" />
    <result column="bill_no" jdbcType="NVARCHAR" property="billNo" />
    <result column="from_time" jdbcType="TIMESTAMP" property="fromTime" />
    <result column="to_time" jdbcType="TIMESTAMP" property="toTime" />
    <result column="consumption" jdbcType="DECIMAL" property="consumption" />
    <result column="attachment_id" jdbcType="CHAR" property="attachmentId" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="CHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="CHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 16 12:03:59 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 16 12:03:59 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 16 12:03:59 HKT 2024.
    -->
    id, head_id, type, bill_no, from_time, to_time, consumption, attachment_id, creation_time, 
    create_username, create_user_id, last_update_time, last_update_username, last_update_user_id, 
    last_update_version
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.AmbientEnergyBillExtExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 16 12:03:59 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_ambient_energy_bill_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 16 12:03:59 HKT 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_ambient_energy_bill_ext
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 16 12:03:59 HKT 2024.
    -->
    delete from t_ambient_energy_bill_ext
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.AmbientEnergyBillExtExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 16 12:03:59 HKT 2024.
    -->
    delete from t_ambient_energy_bill_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.AmbientEnergyBillExt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 16 12:03:59 HKT 2024.
    -->
    insert into t_ambient_energy_bill_ext (id, head_id, type, 
      bill_no, from_time, to_time, 
      consumption, attachment_id, creation_time, 
      create_username, create_user_id, last_update_time, 
      last_update_username, last_update_user_id, last_update_version
      )
    values (#{id,jdbcType=CHAR}, #{headId,jdbcType=CHAR}, #{type,jdbcType=NVARCHAR}, 
      #{billNo,jdbcType=NVARCHAR}, #{fromTime,jdbcType=TIMESTAMP}, #{toTime,jdbcType=TIMESTAMP}, 
      #{consumption,jdbcType=DECIMAL}, #{attachmentId,jdbcType=CHAR}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createUsername,jdbcType=NVARCHAR}, #{createUserId,jdbcType=CHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{lastUpdateUsername,jdbcType=NVARCHAR}, #{lastUpdateUserId,jdbcType=CHAR}, #{lastUpdateVersion,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.AmbientEnergyBillExt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 16 12:03:59 HKT 2024.
    -->
    insert into t_ambient_energy_bill_ext
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="headId != null">
        head_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="fromTime != null">
        from_time,
      </if>
      <if test="toTime != null">
        to_time,
      </if>
      <if test="consumption != null">
        consumption,
      </if>
      <if test="attachmentId != null">
        attachment_id,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="headId != null">
        #{headId,jdbcType=CHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=NVARCHAR},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=NVARCHAR},
      </if>
      <if test="fromTime != null">
        #{fromTime,jdbcType=TIMESTAMP},
      </if>
      <if test="toTime != null">
        #{toTime,jdbcType=TIMESTAMP},
      </if>
      <if test="consumption != null">
        #{consumption,jdbcType=DECIMAL},
      </if>
      <if test="attachmentId != null">
        #{attachmentId,jdbcType=CHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=CHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=CHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.AmbientEnergyBillExtExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 16 12:03:59 HKT 2024.
    -->
    select count(*) from t_ambient_energy_bill_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 16 12:03:59 HKT 2024.
    -->
    update t_ambient_energy_bill_ext
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.headId != null">
        head_id = #{record.headId,jdbcType=CHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=NVARCHAR},
      </if>
      <if test="record.billNo != null">
        bill_no = #{record.billNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.fromTime != null">
        from_time = #{record.fromTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.toTime != null">
        to_time = #{record.toTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.consumption != null">
        consumption = #{record.consumption,jdbcType=DECIMAL},
      </if>
      <if test="record.attachmentId != null">
        attachment_id = #{record.attachmentId,jdbcType=CHAR},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=CHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=CHAR},
      </if>
      <if test="record.lastUpdateVersion != null">
        last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 16 12:03:59 HKT 2024.
    -->
    update t_ambient_energy_bill_ext
    set id = #{record.id,jdbcType=CHAR},
      head_id = #{record.headId,jdbcType=CHAR},
      type = #{record.type,jdbcType=NVARCHAR},
      bill_no = #{record.billNo,jdbcType=NVARCHAR},
      from_time = #{record.fromTime,jdbcType=TIMESTAMP},
      to_time = #{record.toTime,jdbcType=TIMESTAMP},
      consumption = #{record.consumption,jdbcType=DECIMAL},
      attachment_id = #{record.attachmentId,jdbcType=CHAR},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=NVARCHAR},
      create_user_id = #{record.createUserId,jdbcType=CHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=CHAR},
      last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.AmbientEnergyBillExt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 16 12:03:59 HKT 2024.
    -->
    update t_ambient_energy_bill_ext
    <set>
      <if test="headId != null">
        head_id = #{headId,jdbcType=CHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=NVARCHAR},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=NVARCHAR},
      </if>
      <if test="fromTime != null">
        from_time = #{fromTime,jdbcType=TIMESTAMP},
      </if>
      <if test="toTime != null">
        to_time = #{toTime,jdbcType=TIMESTAMP},
      </if>
      <if test="consumption != null">
        consumption = #{consumption,jdbcType=DECIMAL},
      </if>
      <if test="attachmentId != null">
        attachment_id = #{attachmentId,jdbcType=CHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=CHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=CHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.AmbientEnergyBillExt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 16 12:03:59 HKT 2024.
    -->
    update t_ambient_energy_bill_ext
    set head_id = #{headId,jdbcType=CHAR},
      type = #{type,jdbcType=NVARCHAR},
      bill_no = #{billNo,jdbcType=NVARCHAR},
      from_time = #{fromTime,jdbcType=TIMESTAMP},
      to_time = #{toTime,jdbcType=TIMESTAMP},
      consumption = #{consumption,jdbcType=DECIMAL},
      attachment_id = #{attachmentId,jdbcType=CHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=NVARCHAR},
      create_user_id = #{createUserId,jdbcType=CHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=CHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>