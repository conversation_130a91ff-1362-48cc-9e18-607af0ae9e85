package com.csci.susdev.mapper;

import com.csci.susdev.model.EmailActivity;
import com.csci.susdev.model.EmailActivityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface EmailActivityMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	long countByExample(EmailActivityExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	int deleteByExample(EmailActivityExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	int insert(EmailActivity row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	int insertSelective(EmailActivity row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	List<EmailActivity> selectByExample(EmailActivityExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	EmailActivity selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") EmailActivity row, @Param("example") EmailActivityExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	int updateByExample(@Param("row") EmailActivity row, @Param("example") EmailActivityExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	int updateByPrimaryKeySelective(EmailActivity row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_email_activity
	 * @mbg.generated  Tue Oct 10 13:32:11 HKT 2023
	 */
	int updateByPrimaryKey(EmailActivity row);
}