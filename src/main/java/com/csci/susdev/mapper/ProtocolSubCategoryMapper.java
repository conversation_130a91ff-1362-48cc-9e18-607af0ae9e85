package com.csci.susdev.mapper;

import com.csci.susdev.model.ProtocolSubCategory;
import com.csci.susdev.model.ProtocolSubCategoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProtocolSubCategoryMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_sub_category
     *
     * @mbg.generated Tue Apr 09 18:07:14 HKT 2024
     */
    long countByExample(ProtocolSubCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_sub_category
     *
     * @mbg.generated Tue Apr 09 18:07:14 HKT 2024
     */
    int deleteByExample(ProtocolSubCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_sub_category
     *
     * @mbg.generated Tue Apr 09 18:07:14 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_sub_category
     *
     * @mbg.generated Tue Apr 09 18:07:14 HKT 2024
     */
    int insert(ProtocolSubCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_sub_category
     *
     * @mbg.generated Tue Apr 09 18:07:14 HKT 2024
     */
    int insertSelective(ProtocolSubCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_sub_category
     *
     * @mbg.generated Tue Apr 09 18:07:14 HKT 2024
     */
    List<ProtocolSubCategory> selectByExample(ProtocolSubCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_sub_category
     *
     * @mbg.generated Tue Apr 09 18:07:14 HKT 2024
     */
    ProtocolSubCategory selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_sub_category
     *
     * @mbg.generated Tue Apr 09 18:07:14 HKT 2024
     */
    int updateByExampleSelective(@Param("record") ProtocolSubCategory record, @Param("example") ProtocolSubCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_sub_category
     *
     * @mbg.generated Tue Apr 09 18:07:14 HKT 2024
     */
    int updateByExample(@Param("record") ProtocolSubCategory record, @Param("example") ProtocolSubCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_sub_category
     *
     * @mbg.generated Tue Apr 09 18:07:14 HKT 2024
     */
    int updateByPrimaryKeySelective(ProtocolSubCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_protocol_sub_category
     *
     * @mbg.generated Tue Apr 09 18:07:14 HKT 2024
     */
    int updateByPrimaryKey(ProtocolSubCategory record);
}