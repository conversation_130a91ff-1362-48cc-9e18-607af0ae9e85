package com.csci.susdev.mapper;

import com.csci.susdev.model.TzhBsFile;
import com.csci.susdev.model.TzhBsFileExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhBsFileMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_File
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    long countByExample(TzhBsFileExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_File
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    int deleteByExample(TzhBsFileExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_File
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    int insert(TzhBsFile record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_File
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    int insertSelective(TzhBsFile record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_File
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    List<TzhBsFile> selectByExampleWithBLOBs(TzhBsFileExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_File
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    List<TzhBsFile> selectByExample(TzhBsFileExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_File
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    int updateByExampleSelective(@Param("record") TzhBsFile record, @Param("example") TzhBsFileExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_File
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    int updateByExampleWithBLOBs(@Param("record") TzhBsFile record, @Param("example") TzhBsFileExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Bs_File
     *
     * @mbg.generated Fri Jun 07 11:29:56 HKT 2024
     */
    int updateByExample(@Param("record") TzhBsFile record, @Param("example") TzhBsFileExample example);
}