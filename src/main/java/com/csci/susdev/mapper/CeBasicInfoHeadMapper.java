package com.csci.susdev.mapper;

import com.csci.susdev.model.CeBasicInfoHead;
import com.csci.susdev.model.CeBasicInfoHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CeBasicInfoHeadMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_head
     *
     * @mbg.generated Thu Apr 18 16:35:08 HKT 2024
     */
    long countByExample(CeBasicInfoHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_head
     *
     * @mbg.generated Thu Apr 18 16:35:08 HKT 2024
     */
    int deleteByExample(CeBasicInfoHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_head
     *
     * @mbg.generated Thu Apr 18 16:35:08 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_head
     *
     * @mbg.generated Thu Apr 18 16:35:08 HKT 2024
     */
    int insert(CeBasicInfoHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_head
     *
     * @mbg.generated Thu Apr 18 16:35:08 HKT 2024
     */
    int insertSelective(CeBasicInfoHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_head
     *
     * @mbg.generated Thu Apr 18 16:35:08 HKT 2024
     */
    List<CeBasicInfoHead> selectByExample(CeBasicInfoHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_head
     *
     * @mbg.generated Thu Apr 18 16:35:08 HKT 2024
     */
    CeBasicInfoHead selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_head
     *
     * @mbg.generated Thu Apr 18 16:35:08 HKT 2024
     */
    int updateByExampleSelective(@Param("record") CeBasicInfoHead record, @Param("example") CeBasicInfoHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_head
     *
     * @mbg.generated Thu Apr 18 16:35:08 HKT 2024
     */
    int updateByExample(@Param("record") CeBasicInfoHead record, @Param("example") CeBasicInfoHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_head
     *
     * @mbg.generated Thu Apr 18 16:35:08 HKT 2024
     */
    int updateByPrimaryKeySelective(CeBasicInfoHead record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ce_basic_info_head
     *
     * @mbg.generated Thu Apr 18 16:35:08 HKT 2024
     */
    int updateByPrimaryKey(CeBasicInfoHead record);
}