package com.csci.susdev.mapper;

import com.csci.susdev.model.FfCmFixedDetail;
import com.csci.susdev.model.Organization;
import com.csci.susdev.vo.FfCmFixedTableDataVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface FfCmFixedCustomMapper {

    @Select("""
select (select top 1 id from t_ff_cm_fixed_head _fcfh where _fcfh.is_active = 1 and _fcfh.organization_id = #{organizationId} and _fcfh.year = #{year} and _fcfh.month = #{month}) as headId,
fcfd.col_1 as col1, fcfd.col_2 as col2, fcfd.col_3 as col3, fcfd.col_4 as col4, fcfd.col_5 as col5, SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_6), 0)) as col6, 
SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_7), 0)) as col7, SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_8), 0)) as col8, 
SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_9), 0)) as col9, SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_10), 0)) as col10, 
SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_11), 0)) as col11, SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_12), 0)) as col12,
SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_13), 0)) as col13, SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_14), 0)) as col14, 
SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_15), 0)) as col15, SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_16), 0)) as col16, 
SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_17), 0)) as col17, SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_18), 0)) as col18, 
SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_19), 0)) as col19, SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_20), 0)) as col20, 
SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_21), 0)) as col21, SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_22), 0)) as col22, 
SUM(ISNULL(TRY_CONVERT(decimal(15,2), fcfd.col_23), 0)) as col23, N'' as col24,
N'附件' as col25, fcfd.seq, getdate() as creationTime
from t_ff_cm_fixed_head fcfh
inner join t_organization torg on fcfh.organization_id = torg.id and torg.is_deleted = 0
inner join t_ff_cm_fixed_detail fcfd on fcfh.id = fcfd.head_id 
where fcfh.is_active = 1 
and torg.no like (select top 1 CAST(_o.no as nvarchar) + '%' from t_organization _o where _o.id = #{organizationId} and _o.is_deleted = 0) 
and torg.no <> (select top 1 CAST(_o.no as nvarchar) from t_organization _o where _o.id = #{organizationId} and _o.is_deleted = 0)  and year = #{year} and month = #{month} 
group by fcfd.seq, fcfd.col_1, fcfd.col_2, fcfd.col_3, fcfd.col_4, fcfd.col_5
order by fcfd.seq
            """)
    List<FfCmFixedDetail> summaryOrgData(@Param("organizationId") String organizationId,
                                         @Param("year") Integer year,
                                         @Param("month") Integer month);
}
