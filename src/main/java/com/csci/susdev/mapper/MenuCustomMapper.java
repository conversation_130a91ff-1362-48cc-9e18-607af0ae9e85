package com.csci.susdev.mapper;

import com.csci.susdev.model.Menu;
import com.csci.susdev.model.MenuExample;
import com.csci.susdev.qo.MenuPageableQO;
import com.csci.susdev.vo.MenuExtVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 菜单自定义mapper
 * <AUTHOR>
 * @since 20250306
 */
public interface MenuCustomMapper {
    /**
     * 查询 菜單 列表 包含父菜单路径
     * @param description
     * @return
     */
    List<MenuExtVO> listMenuExt(String description, Boolean onlyLeafMenu, List<String> menuIds);

    /**
     * 根据路由路径查询菜单
     * @param routePath
     * @return
     */
    List<Menu> getMenuByRoutePath(String routePath);

}