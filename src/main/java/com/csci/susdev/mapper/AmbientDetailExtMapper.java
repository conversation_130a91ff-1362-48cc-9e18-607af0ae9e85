package com.csci.susdev.mapper;

import com.csci.susdev.model.AmbientDetailExt;
import com.csci.susdev.model.AmbientDetailExtExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AmbientDetailExtMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail_ext
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    long countByExample(AmbientDetailExtExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail_ext
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    int deleteByExample(AmbientDetailExtExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail_ext
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail_ext
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    int insert(AmbientDetailExt record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail_ext
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    int insertSelective(AmbientDetailExt record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail_ext
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    List<AmbientDetailExt> selectByExample(AmbientDetailExtExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail_ext
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    AmbientDetailExt selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail_ext
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    int updateByExampleSelective(@Param("record") AmbientDetailExt record, @Param("example") AmbientDetailExtExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail_ext
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    int updateByExample(@Param("record") AmbientDetailExt record, @Param("example") AmbientDetailExtExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail_ext
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    int updateByPrimaryKeySelective(AmbientDetailExt record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ambient_detail_ext
     *
     * @mbg.generated Tue Jul 09 18:27:50 HKT 2024
     */
    int updateByPrimaryKey(AmbientDetailExt record);
}