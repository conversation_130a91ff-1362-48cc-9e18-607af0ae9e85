package com.csci.susdev.mapper;

import com.csci.susdev.model.FcFactorConversion;
import com.csci.susdev.model.FcFactorConversionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FcFactorConversionMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_factor_conversion
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    long countByExample(FcFactorConversionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_factor_conversion
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    int deleteByExample(FcFactorConversionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_factor_conversion
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_factor_conversion
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    int insert(FcFactorConversion record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_factor_conversion
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    int insertSelective(FcFactorConversion record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_factor_conversion
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    List<FcFactorConversion> selectByExample(FcFactorConversionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_factor_conversion
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    FcFactorConversion selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_factor_conversion
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    int updateByExampleSelective(@Param("record") FcFactorConversion record, @Param("example") FcFactorConversionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_factor_conversion
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    int updateByExample(@Param("record") FcFactorConversion record, @Param("example") FcFactorConversionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_factor_conversion
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    int updateByPrimaryKeySelective(FcFactorConversion record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_factor_conversion
     *
     * @mbg.generated Thu Mar 20 09:45:04 CST 2025
     */
    int updateByPrimaryKey(FcFactorConversion record);
}