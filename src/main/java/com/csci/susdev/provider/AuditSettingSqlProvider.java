package com.csci.susdev.provider;

import java.util.Map;
import java.util.logging.Logger;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

import com.csci.tzh.qo.DProjectPageableQO;

public class AuditSettingSqlProvider {

    public String listAuditSettingDetailSql(Map<String, Object> map){

    	String userId = (String) map.get("userId");
    	
    	String select = "SELECT f.code AS formCode, f.name AS formName, o.id AS organizationId, o.name AS organizationName, (\r\n"
    			+ "	SELECT _u.username FROM t_audit_setting _s\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _s.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _s.user_id\r\n"
    			+ "	WHERE _s.form_code = s.form_code AND _s.organization_id = s.organization_id\r\n"
    			+ "	AND _s.seq = 1\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS username1, (\r\n"
    			+ "	SELECT _u.name FROM t_audit_setting _s\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _s.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _s.user_id\r\n"
    			+ "	WHERE _s.form_code = s.form_code AND _s.organization_id = s.organization_id\r\n"
    			+ "	AND _s.seq = 1\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS userRealName1, (\r\n"
    			+ "	SELECT _u.id FROM t_audit_setting _s\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _s.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _s.user_id\r\n"
    			+ "	WHERE _s.form_code = s.form_code AND _s.organization_id = s.organization_id\r\n"
    			+ "	AND _s.seq = 1\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS userId1, (\r\n"
    			+ "	SELECT _u.username FROM t_audit_setting _s\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _s.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _s.user_id\r\n"
    			+ "	WHERE _s.form_code = s.form_code AND _s.organization_id = s.organization_id\r\n"
    			+ "	AND _s.seq = 2\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS username2, (\r\n"
    			+ "	SELECT _u.name FROM t_audit_setting _s\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _s.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _s.user_id\r\n"
    			+ "	WHERE _s.form_code = s.form_code AND _s.organization_id = s.organization_id\r\n"
    			+ "	AND _s.seq = 2\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS userRealName2, (\r\n"
    			+ "	SELECT _u.id FROM t_audit_setting _s\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _s.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _s.user_id\r\n"
    			+ "	WHERE _s.form_code = s.form_code AND _s.organization_id = s.organization_id\r\n"
    			+ "	AND _s.seq = 2\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS userId2, (\r\n"
    			+ "	SELECT _u.username FROM t_audit_setting _s\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _s.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _s.user_id\r\n"
    			+ "	WHERE _s.form_code = s.form_code AND _s.organization_id = s.organization_id\r\n"
    			+ "	AND _s.seq = 3\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS username3, (\r\n"
    			+ "	SELECT _u.name FROM t_audit_setting _s\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _s.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _s.user_id\r\n"
    			+ "	WHERE _s.form_code = s.form_code AND _s.organization_id = s.organization_id\r\n"
    			+ "	AND _s.seq = 3\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS userRealName3, (\r\n"
    			+ "	SELECT _u.id FROM t_audit_setting _s\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _s.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _s.user_id\r\n"
    			+ "	WHERE _s.form_code = s.form_code AND _s.organization_id = s.organization_id\r\n"
    			+ "	AND _s.seq = 3\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS userId3, (\r\n"
    			+ "	SELECT _u.username FROM t_audit_setting _s\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _s.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _s.user_id\r\n"
    			+ "	WHERE _s.form_code = s.form_code AND _s.organization_id = s.organization_id\r\n"
    			+ "	AND _s.seq = 4\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS username4, (\r\n"
    			+ "	SELECT _u.name FROM t_audit_setting _s\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _s.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _s.user_id\r\n"
    			+ "	WHERE _s.form_code = s.form_code AND _s.organization_id = s.organization_id\r\n"
    			+ "	AND _s.seq = 4\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS userRealName4, (\r\n"
    			+ "	SELECT _u.id FROM t_audit_setting _s\r\n"
    			+ "	LEFT JOIN t_organization _o ON _o.id = _s.organization_id\r\n"
    			+ "	LEFT JOIN t_user _u ON _u.id = _s.user_id\r\n"
    			+ "	WHERE _s.form_code = s.form_code AND _s.organization_id = s.organization_id\r\n"
    			+ "	AND _s.seq = 4\r\n"
    			+ "	LIMIT 1\r\n"
    			+ ") AS userId4 FROM t_audit_setting s\r\n"
    			+ "LEFT JOIN t_form f ON f.code = s.form_code\r\n"
    			+ "LEFT JOIN t_organization o ON o.id = s.organization_id\r\n";
    	String where = "WHERE s.seq = 1 AND o.id IN (SELECT _uo.organization_id FROM t_user_organization _uo WHERE _uo.user_id = '" + userId + "' OR '' = '" + userId + "') \r\n";
    	String orderBy = "ORDER BY formCode, organizationName";
        return select + where + orderBy;
    }
}