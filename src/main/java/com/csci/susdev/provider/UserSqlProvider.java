package com.csci.susdev.provider;

import java.util.Map;
import java.util.logging.Logger;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

import com.csci.tzh.qo.DProjectPageableQO;

public class UserSqlProvider {

	/* SQL Query Example 
	 * SELECT DISTINCT m.* FROM t_user u
		JOIN t_user_role ur ON ur.user_id = u.id
		JOIN t_role r ON r.id = ur.role_id
		JOIN t_role_permission rp ON rp.role_id = r.id
		JOIN t_permission p ON p.id = rp.permission_id
		JOIN t_permission_menu pm ON pm.permission_id = p.id
		JOIN t_menu m ON m.id = pm.menu_id
		WHERE u.username = 'massa_wu'
		ORDER BY seq
	 */
    public String listMenuByUsernameSql(Map<String, Object> map){

    	String username = (String) map.get("username");
    	
    	String select = """
       			SELECT DISTINCT m.* FROM t_user u
    			JOIN t_user_role ur ON ur.user_id = u.id
    			JOIN t_role r ON r.id = ur.role_id
    			JOIN t_role_permission rp ON rp.role_id = r.id
    			JOIN t_permission p ON p.id = rp.permission_id
    			JOIN t_permission_menu pm ON pm.permission_id = p.id
    			JOIN t_menu m ON m.id = pm.menu_id AND m.is_active = 1
    			""";
    	String where = "WHERE u.username = '" + username + "'\r\n";
        return select + where;
    }
    
    /* SQL Query Example
     * SELECT DISTINCT o.* FROM t_user u
		JOIN t_user_role ur ON ur.user_id = u.id
		JOIN t_role r ON r.id = ur.role_id
		JOIN t_role_permission rp ON rp.role_id = r.id
		JOIN t_permission p ON p.id = rp.permission_id
		JOIN t_permission_operation po ON po.permission_id = p.id
		JOIN t_operation o ON o.id = po.operation_id
		WHERE u.username = 'massa_wu'
     */
    public String listOperationByUsernameSql(Map<String, Object> map){

    	String username = (String) map.get("username");
    	
    	String select = "SELECT DISTINCT o.* FROM t_user u\r\n"
    			+ "JOIN t_user_role ur ON ur.user_id = u.id\r\n"
    			+ "JOIN t_role r ON r.id = ur.role_id\r\n"
    			+ "JOIN t_role_permission rp ON rp.role_id = r.id\r\n"
    			+ "JOIN t_permission p ON p.id = rp.permission_id\r\n"
    			+ "JOIN t_permission_operation po ON po.permission_id = p.id\r\n"
    			+ "JOIN t_operation o ON o.id = po.operation_idpermission_id = p.id\r\n"
    			+ "JOIN t_menu m ON m.id = pm.menu_id\r\n";
    	String where = "WHERE u.username = '" + username + "'\r\n";
        return select + where;
    }
}