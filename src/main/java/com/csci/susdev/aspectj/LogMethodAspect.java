package com.csci.susdev.aspectj;

import com.csci.common.util.CustomGsonBuilder;
import com.csci.susdev.log.ILogStorage;
import com.csci.susdev.log.LogStorageBuilder;
import com.csci.susdev.service.ApiLogService;
import com.google.gson.Gson;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Generated Comments
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 11/15/2019
 */
@Component
@Aspect
@Order(2)
public class LogMethodAspect {

    /**
     * 日志记录对象
     */

    @Resource
    private ApiLogService apiLogService;

    private static final Logger logger = LoggerFactory.getLogger(LogMethodAspect.class);

    private final Gson gson = CustomGsonBuilder.createGson();

    @Around("execution(* *(..)) && (@within(com.csci.susdev.annotation.LogMethod) || @annotation(com.csci.susdev.annotation.LogMethod))")
    public Object logMethod(ProceedingJoinPoint joinPoint) throws Throwable {

        ILogStorage logStorage = LogStorageBuilder.create();

        long start = System.currentTimeMillis();

        Object[] args = joinPoint.getArgs();

        String className = Optional.of(joinPoint).map(JoinPoint::getSignature).map(Signature::getDeclaringTypeName).orElse("unknownClass");
        String methodName = Optional.of(joinPoint).map(JoinPoint::getSignature).map(Signature::getName).orElse("unknownMethod");
        logStorage.setBasic(className, methodName, LocalDateTime.now());

        String params = convertArgs(args);
        logger.trace("{}.\n - {} -----> params: {}", className, methodName, params);
        logStorage.setParameter(params);

        Object result;
        try {
            result = joinPoint.proceed(args);
            String jsonResult = result instanceof String ? (String) result : parseToJson(result);
            logger.trace("{}.\n - {} -----> result: {}", className, methodName, jsonResult);
            logStorage.setResult(jsonResult);
            return result;
        } catch (Throwable throwable) {
            logger.error("{} -----> exception caught", methodName, throwable);
            logStorage.setException(convertThrowable(throwable));
            throw throwable;
        } finally {
            long duration = System.currentTimeMillis() - start;
            //
            logStorage.setDuration(duration);
            logStorage.store();
        }

    }

    /**
     * 将异常信息转换为字符串,最多5000字符
     *
     * @param throwable
     * @return
     */
    String convertThrowable(Throwable throwable) {
        StringBuilder sbResult = new StringBuilder();
        sbResult.append(throwable.getLocalizedMessage());
        sbResult.append(";\n");
        sbResult.append(Arrays.stream(throwable.getStackTrace()).map(StackTraceElement::toString).collect(Collectors.joining("\n\t")));
        if (sbResult.length() > 5000) {
            sbResult.setLength(5000);
        }
        return sbResult.toString();
    }

    /**
     * 忽略掉 request 和 response 参数
     *
     * @param args
     * @return
     */
    String convertArgs(Object[] args) {
        try {
            StringBuilder sbResult = new StringBuilder();
            int count = 0;
            for (Object arg : args) {
                if (arg instanceof ServletRequest || arg instanceof ServletResponse) {
                    continue;
                }
                if (count > 0) {
                    // 从第二个参数开始，用分号分隔
                    sbResult.append(";");
                }
                sbResult.append(gson.toJson(arg));
                count++;
            }
            if (sbResult.length() == 0) {
                sbResult.append("null");
            }
            return sbResult.toString();
        } catch (Exception e) {
            logger.error("LogMethodAspect#convertArgs, e", e);
            return Arrays.toString(args);
        }
    }

    String parseToJson(Object obj) {
        try {
            return gson.toJson(obj);
        } catch (Exception e) {
            logger.error("LogMethodAspect#parseToJson, e", e);
            return String.valueOf(obj);
        }
    }

}
