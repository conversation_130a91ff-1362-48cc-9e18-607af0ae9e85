package com.csci.susdev.aspectj;

import com.csci.cohl.exception.ResultBody;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.service.ApiLogService;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import com.fasterxml.jackson.databind.*;
import org.apache.commons.lang3.ObjectUtils;
import org.aspectj.lang.*;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.*;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.*;
import org.springframework.web.context.request.*;
import org.springframework.web.util.*;

import javax.annotation.Resource;
import javax.servlet.http.*;
import java.io.*;

@Component
@Aspect
@Order(1)
public class LogApiAspect {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private ObjectMapper mapper;

    @Resource
    private ApiLogService apiLogService;

    @Around("""
    execution(* com.csci.susdev.controller.*.*(..)) 
    || execution(* com.csci.cohl.controller.*.*(..)) 
    && !@annotation(com.csci.susdev.annotation.NoApiLogging) 
    && !@target(com.csci.susdev.annotation.NoApiLogging)
    """)
    public Object logging(ProceedingJoinPoint joinPoint) throws Throwable {
        String code = String.valueOf(SusDevConsts.RespStatus.FAIL);

        long startTime = System.currentTimeMillis();

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        Object responseBody = null;
        try {
            responseBody = joinPoint.proceed();
        } catch (Throwable throwable) {
            responseBody = throwable;
            throw throwable;
        } finally {
            if (responseBody instanceof com.csci.susdev.model.ResultBase) {
                com.csci.susdev.model.ResultBase resultBase = (com.csci.susdev.model.ResultBase) responseBody;
                code = String.valueOf(resultBase.getCode());
            }
            else if (responseBody instanceof com.csci.common.model.ResultBase) {
                com.csci.common.model.ResultBase resultBase = (com.csci.common.model.ResultBase) responseBody;
                code = String.valueOf(resultBase.getCode());
            }else if (responseBody instanceof ResultBody<?>) {
                ResultBody<?> resultBase = (ResultBody<?>) responseBody;
                code = String.valueOf(resultBase.getCode());
            }
            long endTime = System.currentTimeMillis();
            try {
                apiLogService.log(request.getMethod(), code,
                        mapper.writerWithDefaultPrettyPrinter().writeValueAsString(request.getParameterMap()),
                        getPayload(request),
                        this.mapper.writerWithDefaultPrettyPrinter().writeValueAsString(responseBody),
                        startTime,
                        endTime);
            } catch(Exception e) {
                logger.error("LogApiAspect#logging, e", e);
            }
        }

        return responseBody;
    }

    private String getPayload(HttpServletRequest request) {
        ContentCachingRequestWrapper wrapper = WebUtils.getNativeRequest(request, ContentCachingRequestWrapper.class);
        if (wrapper != null) {
            byte[] buf = wrapper.getContentAsByteArray();
            if (buf.length > 0) {
                try {
//                    int length = Math.min(buf.length, 1024);// 最多只印出1024長度
                    return new String(buf, wrapper.getCharacterEncoding());
                } catch (UnsupportedEncodingException ex) {
                    return "[unknown]";
                }
            }
        }
        return "";
    }
}
