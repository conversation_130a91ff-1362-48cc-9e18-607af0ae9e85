package com.csci.susdev.aspectj;

import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.service.ApiLogService;
import com.csci.susdev.service.ConnectionConfigService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.WebUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.Optional;

@Component
@Aspect
@Order(3)
public class ExternalAuthAspect {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    HttpServletRequest request;

    @Autowired
    private ObjectMapper mapper;

    @Resource
    private ConnectionConfigService connectionConfigService;

    @Around("execution(* *(..)) && (@within(com.csci.susdev.annotation.ExternalAuth) || @annotation(com.csci.susdev.annotation.ExternalAuth))")
    public Object auth(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = Optional.of(joinPoint).map(JoinPoint::getSignature).map(Signature::getName).orElse("unknownMethod");

        String appId = request.getHeader(SusDevConsts.External_App_Id);
        String appKey = request.getHeader(SusDevConsts.External_App_Key);

        try {
            connectionConfigService.checkValid(appId, appKey);
            Object result = joinPoint.proceed();
            return result;
        } catch (Throwable throwable) {
            logger.error("{} -----> exception caught", methodName, throwable);
            throw throwable;
        }
    }
}
