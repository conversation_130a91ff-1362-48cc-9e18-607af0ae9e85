package com.csci.susdev.util;

import com.csci.susdev.constant.NamingConventionEnum;
import com.csci.susdev.modelcovt.AmbientEnergyBillConverter;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Mybatis相关帮助类
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 10/14/2019
 */
public abstract class MybatisHelper {

    /**
     * 两边添加%
     *
     * @param value
     * @return
     */
    public static String like(String value) {
        return "%" + StringUtils.trimToEmpty(value) + "%";
    }

    /**
     * 左边添加%
     *
     * @param value
     * @return
     */
    public static String leftLike(String value) {
        return "%" + StringUtils.trimToEmpty(value);
    }

    /**
     * 右边添加%
     *
     * @param value
     * @return
     */
    public static String rightLike(String value) {
        return StringUtils.trimToEmpty(value) + "%";
    }

    /**
     * 檢查SqlInjection, 有注入行為返回為true
     *
     */
    public static boolean checkSqlInjectionForOrderBy(String orderBy, List<String> fieldNames, NamingConventionEnum dbNamingConvention) {

        //轉換成數據庫的命名規則
        for(int i=0; i < fieldNames.size(); i++) {
            switch (dbNamingConvention) {
                case SNAKE:
                    fieldNames.set(i, NamingConventionUtils.cameCaseToSnakeCase(fieldNames.get(i)));
                    break;
                case CAMO:
                    fieldNames.set(i, NamingConventionUtils.snakeCaseToCamelCase(fieldNames.get(i)));
                    break;
                default:
                    break;
            }
        }

        //合法字段排除
        String[] orderBySplitArr = orderBy.split(",");
        for (int i=0; i<orderBySplitArr.length; i++) {
            orderBySplitArr[i] = orderBySplitArr[i].toLowerCase();
            orderBySplitArr[i] = orderBySplitArr[i].trim();
            orderBySplitArr[i] = orderBySplitArr[i].replace(" desc", "");
            orderBySplitArr[i] = orderBySplitArr[i].replace(" asc", "");
            for(String fieldName : fieldNames) {
                if(StringUtils.equals(orderBySplitArr[i],fieldName.toLowerCase())) {
                    orderBySplitArr[i] = "";
                }
            }
            if(orderBySplitArr[i].length() > 0) {
                return true;
            }
        }

        return false;
    }
}
