package com.csci.susdev.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Iterator;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

public class ExcelUtils {
	public static Workbook getWorkbookFromUrl(String url) throws Exception {
		String type = url.substring(url.lastIndexOf(".") + 1);
		Workbook wb;
		// 根據不同版本決定不同讀取方法的先後嘗試順序
		if("xls".equalsIgnoreCase(type)) {
			try {
				InputStream input = new URL(url).openStream();
				wb = new HSSFWorkbook(input);
			} catch (Exception ex) {
				InputStream input = new URL(url).openStream();
				wb = new XSSFWorkbook(input);
			}
		} else {
			try {
				InputStream input = new URL(url).openStream();
				wb = new XSSFWorkbook(input);
			} catch (Exception ex) {
				InputStream input = new URL(url).openStream();
				wb = new HSSFWorkbook(input);
			}
		}
		return wb;
	}

	public static Workbook getWorkbookFromLocal(String path) throws Exception {
		String type = path.substring(path.lastIndexOf(".") + 1);
		Workbook wb;
		// 根據不同版本決定不同讀取方法的先後嘗試順序
		if("xls".equalsIgnoreCase(type)) {
			try {
				FileInputStream input=new FileInputStream(new File(path));
				wb = new HSSFWorkbook(input);
			} catch (Exception ex) {
				FileInputStream input=new FileInputStream(new File(path));
				wb = new XSSFWorkbook(input);
			}
		} else {
			try {
				FileInputStream input=new FileInputStream(new File(path));
				wb = new XSSFWorkbook(input);
			} catch (Exception ex) {
				FileInputStream input=new FileInputStream(new File(path));
				wb = new HSSFWorkbook(input);
			}
		}
		return wb;
	}

	public static String getStringValue(Cell cell) {
		String cellValue = "";

		if(cell == null) {
			return null;
		}

		if(cell.getCellType()==CellType.STRING)  {
			cellValue = cell.getRichStringCellValue().getString();
		} else if(cell.getCellType()==CellType.NUMERIC) {
			if (DateUtil.isCellDateFormatted(cell)) {
				cellValue = new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss").format(cell.getDateCellValue());
			} else {
				cellValue = String.valueOf(cell.getNumericCellValue());
			}
		} else if(cell.getCellType()==CellType.BOOLEAN) {
			cellValue = String.valueOf(cell.getBooleanCellValue());
		} else if(cell.getCellType()==CellType.FORMULA) {
			cellValue = cell.getCellFormula();
		}
		return cellValue;
	}

	public static void autoSizeColumns(Workbook workbook, int widthMultiplier) {
	    int numberOfSheets = workbook.getNumberOfSheets();
	    for (int i = 0; i < numberOfSheets; i++) {
	        Sheet sheet = workbook.getSheetAt(i);
	        if (sheet.getPhysicalNumberOfRows() > 0) {
	            Row row = sheet.getRow(sheet.getFirstRowNum());
	            Iterator<Cell> cellIterator = row.cellIterator();
	            while (cellIterator.hasNext()) {
	                Cell cell = cellIterator.next();
	                int columnIndex = cell.getColumnIndex();
	                sheet.autoSizeColumn(columnIndex);
	                sheet.setColumnWidth(columnIndex,sheet.getColumnWidth(columnIndex)*widthMultiplier/10);
	            }
	        }
	    }
	}

	public static Workbook getWorkbookFromUrl(InputStream is, String filename) throws IOException {
        if (StringUtils.endsWith(filename, "xls")) {
            return new HSSFWorkbook(is);
        } else {
            return new XSSFWorkbook(is);
        }
    }

	public static void setWrapText(Cell cell) {
	    CellStyle cellStyle = cell.getCellStyle();
	    if(cellStyle == null) {
	        cellStyle = cell.getSheet().getWorkbook().createCellStyle();
	    }
	    cellStyle.setWrapText(true);
	    cell.setCellStyle(cellStyle);
	}
	
	public static void setCellBackgroundColor(Cell cell, short ic) {
	    CellStyle cellStyle = cell.getCellStyle();
	    if(cellStyle == null) {
	        cellStyle = cell.getSheet().getWorkbook().createCellStyle();
	    }
	    cellStyle.setFillForegroundColor(ic);
	    cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
	    cell.setCellStyle(cellStyle);
	}
	
	public static Cell getCellByRef(Sheet sheet, String ref) {
		CellReference cr = new CellReference(ref);
		Row row = sheet.getRow(cr.getRow());
		return row.getCell(cr.getCol());
	}
	
	public static String getCellValueByRef(Sheet sheet, String ref) {
		CellReference cr = new CellReference(ref);
		Row row = sheet.getRow(cr.getRow());
		Cell cell = row.getCell(cr.getCol());
		String cellValue = "";
		
    	if(cell.getCellType()==CellType.STRING)  {
        	cellValue = cell.getRichStringCellValue().getString();
    	} else if(cell.getCellType()==CellType.NUMERIC) {
            if (DateUtil.isCellDateFormatted(cell)) {
            	cellValue = new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss").format(cell.getDateCellValue());
            } else {
            	cellValue = String.valueOf(cell.getNumericCellValue());
            }
    	} else if(cell.getCellType()==CellType.BOOLEAN) {
        	cellValue = String.valueOf(cell.getBooleanCellValue());
    	} else if(cell.getCellType()==CellType.FORMULA) {
    		cellValue = cell.getCellFormula();
    	}
        return cellValue;
	}
	
	public static void setCellValueByRef(Sheet sheet, String ref, String value) {
		CellReference cr = new CellReference(ref);
		Row row = sheet.getRow(cr.getRow());
		if(row == null){
			row = sheet.createRow(cr.getRow());
		}
		Cell cell = row.getCell(cr.getCol());
		if(cell == null){
		    cell = row.createCell(cr.getCol());
		}
		cell.setCellValue(value);
	}
}
