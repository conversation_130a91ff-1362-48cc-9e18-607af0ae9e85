package com.csci.susdev.util.workflowinit;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.csci.common.exception.ServiceException;
import com.csci.common.util.CustomGsonBuilder;
import com.csci.susdev.facade.WorkflowFacade;
import com.csci.susdev.model.*;
import com.csci.susdev.qo.FormQO;
import com.csci.susdev.qo.KeywordQO;
import com.csci.susdev.service.*;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.util.userinit.UserData;
import com.csci.susdev.util.userinit.UserInitializer;
import com.csci.susdev.vo.FlowInitDataVO;
import com.csci.susdev.vo.WorkflowNodeVO;
import com.csci.susdev.vo.WorkflowVO;
import com.google.common.base.Splitter;
import com.google.gson.Gson;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.csci.susdev.service.ServiceHelper.checkExist;

public class WorkflowDataListener implements ReadListener<FlowInitDataVO> {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(WorkflowDataListener.class);

    private final Gson gson = CustomGsonBuilder.createGson();

    List<FlowInitDataVO> failedDataList = new ArrayList<>();

    @Override
    public void invoke(FlowInitDataVO data, AnalysisContext context) {
        logger.info("解析到一条数据:{}", gson.toJson(data));

        try {
            handleData(data);
        } catch (Exception e) {
            data.setErrorMsg(e.getMessage());
            failedDataList.add(data);
            logger.error("流程数据初始化错误，数据：{}", gson.toJson(data));
            logger.error("流程初始化出错, {}", e.getMessage(), e);
        }

    }

    private void handleData(FlowInitDataVO data) throws Exception {
        validateData(data);

        WorkflowService workflowService = SpringContextUtil.getBean(WorkflowService.class);
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        WorkflowFacade workflowFacade = SpringContextUtil.getBean(WorkflowFacade.class);

        // 找到组织机构记录
        Organization organization;
        try {
            organization = findOrganization(data);
        } catch (Exception e) {
            logger.error("初始化流程出错, 未找到组织机构：{}", e.getMessage(), e);
            throw new ServiceException("未找到组织机构");
        }

        FormService formService = SpringContextUtil.getBean(FormService.class);
        FormQO qo = new FormQO();
        // 仅配置环境绩效, 如果需要配置所有表单，将这里的条件去掉
        qo.setKeyword("ambient");
        List<Form> lstForm = formService.listForm(qo).getList();
        for (Form form : lstForm) {
            Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(organization.getId(), form.getId(), 2022, 12);
            if (workflow == null) {
                WorkflowVO workflowVO = new WorkflowVO();
                workflowVO.setOrganizationId(organization.getId());
                workflowVO.setOrganizationName(organization.getName());
                workflowVO.setFormId(form.getId());
                workflowVO.setFormName(form.getName());
                workflowVO.setWorkflowNodes(convertWorkflowNodeVO(data));
                // 添加流程
                workflowFacade.saveWorkflow(workflowVO);
            } else {
                // 更新流程
                // throw new ServiceException("流程已存在, 暂不支持更新");
                logger.warn("流程已存在, 暂不支持更新");
            }
        }
    }

    private List<WorkflowNodeVO> convertWorkflowNodeVO(FlowInitDataVO data) throws Exception {
        List<WorkflowNodeVO> lstNodes = new ArrayList<>();
        WorkflowNodeVO node1 = new WorkflowNodeVO();
        node1.setName("審核人一");
        if (StringUtils.isNotBlank(data.getUser1Name()) && StringUtils.isNotBlank(data.getUser1Email())) {
            node1.setUserId(getOrInitUser(data.getCompanyName(), data.getDeptName(), data.getUser1Name(), data.getUser1Email()).getId());
        }
        lstNodes.add(node1);

        WorkflowNodeVO node2 = new WorkflowNodeVO();
        node2.setName("審核人二");
        if (StringUtils.isNotBlank(data.getUser2Name()) && StringUtils.isNotBlank(data.getUser2Email())) {
            node2.setUserId(getOrInitUser(data.getCompanyName(), data.getDeptName(), data.getUser2Name(), data.getUser2Email()).getId());
        }
        lstNodes.add(node2);

        WorkflowNodeVO node3 = new WorkflowNodeVO();
        node3.setName("審核人三");
        if (StringUtils.isNotBlank(data.getUser3Name()) && StringUtils.isNotBlank(data.getUser3Email())) {
            node3.setUserId(getOrInitUser(data.getCompanyName(), data.getDeptName(), data.getUser3Name(), data.getUser3Email()).getId());
        }
        lstNodes.add(node3);

        WorkflowNodeVO node4 = new WorkflowNodeVO();
        node4.setName("審核人四");
        if (StringUtils.isNotBlank(data.getUser4Name()) && StringUtils.isNotBlank(data.getUser4Email())) {
            node4.setUserId(getOrInitUser(data.getCompanyName(), data.getDeptName(), data.getUser4Name(), data.getUser4Email()).getId());
        }
        lstNodes.add(node4);

        return lstNodes;
    }

    private Splitter splitter = Splitter.on("@").trimResults().omitEmptyStrings();
    private User getOrInitUser(String companyName, String deptName, String name, String email) throws Exception {
        UserService userService = SpringContextUtil.getBean(UserService.class);
        String username = splitter.split(email).iterator().next();
        UserExample example = new UserExample();
        example.or().andIsEnabledEqualTo(Boolean.TRUE).andUsernameEqualTo(username);
        example.or().andIsEnabledEqualTo(Boolean.TRUE).andEmailEqualTo(email);
        List<User> lstUser = userService.selectByExample(example);
        if (CollectionUtils.size(lstUser) > 1) {
            throw new ServiceException("用户数据异常，存在多个用户");
        }
        User user = lstUser.stream().findFirst().orElse(null);
        if (user == null) {
            UserInitializer userInitializer = SpringContextUtil.getBean(UserInitializer.class);
            UserData userData = new UserData();
            userData.setName(name);
            userData.setEmail(email);
            userData.setCompanyName(companyName);
            userData.setDeptName(deptName);
            userData.setMobile("");
            userData.setPosition("");
            userInitializer.initByUserData(userData);
            // 初始化用户之后再查询出来
            user = userService.selectByExample(example).stream().findFirst().orElse(null);
        }
        return user;
    }

    private void validateData(FlowInitDataVO data) {
        checkExist(data.getTopCompanyName(), "顶级公司名称不能为空");
        checkExist(data.getCompanyName(), "公司名称不能为空");
        checkExist(data.getDeptName(), "项目或部门名称不能为空");
    }

    private Organization findOrganization(FlowInitDataVO data) {
        OrganizationService organizationService = SpringContextUtil.getBean(OrganizationService.class);
        Organization organization = organizationService.getOrganizationByName(data.getTopCompanyName());
        checkExist(organization, "顶级公司不存在: " + data.getTopCompanyName());

        OrganizationExample example = new OrganizationExample();
        example.or().andNoLike(organization.getNo() + "%").andNameEqualTo(data.getCompanyName()).andIsDeletedEqualTo(Boolean.FALSE);
        Organization company = organizationService.selectByExample(example).stream().findFirst().orElseThrow(() -> new ServiceException("公司不存在: " + data.getCompanyName()));

        OrganizationExample example2 = new OrganizationExample();
        example2.or().andNoLike(organization.getNo() + "%").andNameEqualTo(data.getDeptName()).andIsDeletedEqualTo(Boolean.FALSE).andParentIdEqualTo(company.getId());

        return organizationService.selectByExample(example2).stream().findFirst().orElseThrow(() -> new ServiceException("项目或部门不存在: " + data.getDeptName()));
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        logger.info("所有数据解析完成!----------------");
        if (CollectionUtils.isNotEmpty(failedDataList)) {
            logger.warn("以下数据解析失败: {}", gson.toJson(failedDataList));
        } else {
            logger.info("所有数据解析成功!");
        }
    }

}
