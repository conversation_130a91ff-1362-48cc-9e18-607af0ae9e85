package com.csci.susdev.util.orginit;

import com.alibaba.excel.EasyExcel;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.InputStream;

@Component
public class OrganizationInitializer {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(OrganizationInitializer.class);

    public void init() {
        initByFileName("orgs/xingye.xlsx");
        initByFileName("orgs/tumu.xlsx");
        initByFileName("orgs/fangwu.xlsx");
        initByFileName("orgs/guotou.xlsx");
        initByFileName("orgs/macau.xlsx");
        initByFileName("orgs/zhuhai.xlsx");
    }

    public void initByFileName(String filename) {
        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(filename)) {
            EasyExcel.read(is, OrganizationData.class, new OrganizationListener()).sheet().doRead();
        } catch (Exception e) {
            logger.error("初始化出错", e);
        }
    }

    public void initByOrgName(String parentName, String name) {
        OrganizationListener listener = new OrganizationListener();
        OrganizationData data = new OrganizationData();
        data.setName(name);
        data.setParentName(parentName);
        if (StringUtils.isBlank(parentName)) {
            listener.initTopOrg(data);
        } else {
            listener.initSubOrg(data);
        }
    }
}
