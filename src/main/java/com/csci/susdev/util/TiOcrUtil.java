package com.csci.susdev.util;

import com.csci.common.exception.ServiceException;
import com.csci.common.util.HttpSimulator;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.configuration.MinioConfig;
import com.csci.susdev.model.Airport;
import com.csci.susdev.model.request.TiOcrRequest;
import com.csci.susdev.model.response.TiOcrResponse;
import com.csci.susdev.util.context.ContextUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.imageio.stream.ImageOutputStream;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 *  TiOcr 工具类
 * <AUTHOR>
 */
@Component
@LogMethod
@Slf4j
public class TiOcrUtil {

    @Value("${tiocr.base-url}")
    private String tiOcrBaseUrl;
    @Value("${tiocr.smart_structural_ocr_v3}")
    private String tiOcrSmartStructuralOcrV3;

    private Map<String, String> getHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        return headers;
    }

    private String getUrl(String uri) {
        return tiOcrBaseUrl + uri;
    }


    public String convertFile2Base64(MultipartFile file) throws IOException {
        String[] availableTypes = {"jpg","jpeg","png","pdf"};
        String fileType = FileUtil.getFileType(file.getBytes());
        if (!Arrays.asList(availableTypes).contains(fileType)){
            throw new ServiceException("请选择图片或者PDF类型文件进行解析！支持的文件类型: " + String.join(", ", availableTypes) + "，当前文件类型: " + fileType);
        }

        try {
            if ("pdf".equals(FileUtil.getFileType(file.getBytes()))) {
                byte[] bytes = convertPdf2Jpg(file);
                // 图片转base64
                return new String(Base64.encodeBase64(bytes));
            }else {
                return new String(Base64.encodeBase64(file.getBytes()));
            }
        } catch (Exception e) {
            throw new ServiceException("文件转换失败", e);
        }
    }

    public String ocrStructureInfo(String base64Image) {

        if (StringUtils.isBlank(base64Image)) {
            log.error("图片Base64为空，请检查！");
            return null;
        }
        Map<String, String> headers = getHeader();

        var sid = UUID.randomUUID().toString();
        log.info("TiOCR识别开始, sessionId: {}", sid);

        TiOcrRequest tiOcrRequest = TiOcrRequest.builder().appId("123").sessionId(sid).image(base64Image).ocrTemplate("ocr").build();
        String url = getUrl(tiOcrSmartStructuralOcrV3);
        String s = HttpSimulator.sendPostRequest(url, CommonUtil.toJson(tiOcrRequest), headers);

        log.info("TiOCR识别结束, sessionId: {}, 响应结果: {}", sid, s);


        TiOcrResponse tiOcrResponse = null;
        try {
            tiOcrResponse = JsonUtils.getObjectMapper().readValue(s, TiOcrResponse.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        log.info("TiOCR识别结束, tiOcrResponse: {}", tiOcrResponse);

        var res = tiOcrResponse.getRecognizeList().get(0);
        if (res == null || res.getItemContent() == null || CollectionUtils.isEmpty(res.getItemContent().getItemList())) {
            log.warn("TiOCR识别结果为空, sessionId: {}", sid);
            return null;
        }
        String result = handleItemList(res.getItemContent().getItemList());
        log.info("解析后的result:{}", result);

        return result;
    }

    private byte[] convertPdf2Jpg(MultipartFile pdf) throws IOException {
        try (final PDDocument document = Loader.loadPDF(pdf.getInputStream());
             ByteArrayOutputStream imgBos = new ByteArrayOutputStream();
             ImageOutputStream imgOs = ImageIO.createImageOutputStream(imgBos)) {

            if (document == null) {
                throw new IOException("PDF 文档为空");
            }

            PDFRenderer pdfRenderer = new PDFRenderer(document);
            int pageSize = document.getNumberOfPages();
            int y = 0;
            BufferedImage pdfImage = null;

            for (int i = 0; i < pageSize; ++i) {
                BufferedImage bim = pdfRenderer.renderImageWithDPI(i, 149, ImageType.RGB);

                if (i == 0) {
                    pdfImage = new BufferedImage(bim.getWidth(), bim.getHeight() * pageSize, BufferedImage.TYPE_INT_RGB);
                }

                pdfImage.getGraphics().drawImage(bim, 0, y, null);
                y += bim.getHeight();
            }

            if (pdfImage == null) {
                throw new IOException("生成 PDF 图像失败");
            }

            ImageIO.write(pdfImage, "jpg", imgOs);
            return imgBos.toByteArray();
        }
    }



    private static String handleItemList(List<TiOcrResponse.RecognizeResult.ItemContent.Item> itemList) {
        // 定义误差范围
        double tolerance = 3.0;

        // 先对 itemList 按照 y 坐标进行排序
        itemList.sort(Comparator.comparingDouble(item -> item.getCoord().getY()));

        // 分组逻辑
        Map<Integer, List<TiOcrResponse.RecognizeResult.ItemContent.Item>> groupedItems = new HashMap<>();
        int groupKey = 0;

        for (var item : itemList) {
            if (groupedItems.isEmpty()) {
                // 如果是第一个元素，创建一个新的组
                groupedItems.put(groupKey, new ArrayList<>());
                groupedItems.get(groupKey).add(item);
            } else {
                // 获取当前组的最后一个元素
                var currentGroup = groupedItems.get(groupKey);
                var lastItem = currentGroup.get(currentGroup.size() - 1);

                // 比较当前元素的 y 值与最后一个元素的 y 值
                double yDiff = Math.abs(item.getCoord().getY() - lastItem.getCoord().getY());

                if (yDiff <= tolerance) {
                    // 如果差值在误差范围内，添加到当前组
                    currentGroup.add(item);
                } else {
                    // 否则，创建一个新的组
                    groupKey++;
                    groupedItems.put(groupKey, new ArrayList<>());
                    groupedItems.get(groupKey).add(item);
                }
            }
        }

        // 构建输出字符串
        StringBuilder result = new StringBuilder();
        for (var group : groupedItems.values()) {
            for (var item : group) {
                result.append(item.getContent()).append(" ");
            }
            result.append("\n");
        }
        log.info("ocr result: {}", result);
        // 打印结果
        return result.toString();
    }

    private static String extractValue(String content, String regex, int group) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        return matcher.find() ? matcher.group(group) : "N/A";
    }

    private static String formatDate(String rawDate) {
        if (rawDate == null || rawDate.equals("N/A")) return rawDate;
        String[] parts = rawDate.split("-");
        return "20" + parts[2] + "-" + parts[1] + "-" + parts[0];
    }


    public static void main(String[] args) throws Exception {

//        MultipartFile file = (MultipartFile) new File("C:\\Users\\<USER>\\Desktop\\bjy水电费\\電費\\21.11.10-21.11.25.pdf");
//        File file2 = new File("C:\\Users\\<USER>\\Desktop\\bjy水电费\\電費\\21.11.10-21.11.25_00.png");
        File file2 = new File("E:\\WorkFile\\ZJHK\\ESG\\OCR\\电费\\青岛光电产业园员工宿舍项目\\2025_01_17\\7-12月电费1737104687107.jpg");
        // pdf 转图片
//        byte[] bytes = convertPdf2Png(file);

//        File file2 = new File("C:\\Users\\<USER>\\Desktop\\bjy水电费\\電費\\21.11.10-21.11.25_00.png");
//        File file2 = new File("C:\\Users\\<USER>\\Desktop\\bjy水电费\\水費\\21.12.28-22.01.25_00.png");
//        File file2 = new File("E:\\WorkFile\\ZJHK\\ESG\\OCR\\环境绩效水电费\\水费\\内地水费发票1_00.png");
        try (FileInputStream fis = new FileInputStream(file2)) {
            byte[] bytes = new byte[(int) file2.length()];
            fis.read(bytes); // 将文件内容读取到字节数组
            // 图片转base64
            String base64Image = new String(Base64.encodeBase64(bytes));

            // header
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            // body

            Map<String, Object> body = new HashMap<>();
            var sid = UUID.randomUUID().toString();
            body.put("app_id", "123");
            body.put("session_id", sid);
            body.put("image", base64Image);
            body.put("ocr_template", "ocr");

            TiOcrRequest tiOcrRequest = TiOcrRequest.builder().appId("123").sessionId(sid).image(base64Image).ocrTemplate("ocr").build();

            log.info("TiOCR识别开始, sessionId: {}", sid);
            String url = "http://10.148.42.13:60099/youtu/ocrapi/smart_structural_ocr_v3";
            String s = HttpSimulator.sendPostRequest(url, CommonUtil.toJson(tiOcrRequest), headers);
            TiOcrResponse tiOcrResponse = JsonUtils.getObjectMapper().readValue(s, TiOcrResponse.class);
            log.info("TiOCR识别结束, sessionId: {}, 响应结果: {}", sid, s);
            log.info("TiOCR识别结束, tiOcrResponse: {}", tiOcrResponse);

            var res = tiOcrResponse.getRecognizeList().get(0);
            if (res == null || res.getItemContent() == null || CollectionUtils.isEmpty(res.getItemContent().getItemList())) {
                log.warn("TiOCR识别结果为空, sessionId: {}", sid);
            }
            String result = handleItemList(res.getItemContent().getItemList());
            log.info("result:{}", result);
            String issueDate = extractValue(result, "由.*?(\\d{2}-\\d{2}-\\d{2})", 1);
            String issueDate2 = extractValue(result, "至.*?(\\d{2}-\\d{2}-\\d{2})", 1);

            System.out.println("发单日期: " + formatDate(issueDate));
            System.out.println("发单日期: " + formatDate(issueDate2));

            String accountNumber = extractAccountNumber(result);
            System.out.println("編號號碼: " + accountNumber);
            String accountNumberAlt = extractAccountNumberByLines(result);
            System.out.println("編號號碼（备选）: " + accountNumberAlt);
        } catch (IOException e) {
            System.err.println("读取文件失败: " + e.getMessage());
        }
    }


    private static String extractAccountNumber(String content) {
//        Pattern pattern = Pattern.compile("編賬號碼Account Number\\s*(\\d+-\\d+-\\d+)");
        Pattern pattern = Pattern.compile("編賬號碼\\s*Account\\s*Number\\s*\\n(\\d+-\\d+-\\d+)");
        Matcher matcher = pattern.matcher(content);
        return matcher.find() ? matcher.group(1) : "N/A";
    }

    private static String extractAccountNumberByLines(String content) {
        String[] lines = content.split("\\r?\\n");
        for (int i = 0; i < lines.length; i++) {
            if (lines[i].contains("編賬號碼") && lines[i].contains("Account Number")) {
                if (i + 1 < lines.length) {
                    String candidate = lines[i + 1].trim();
                    if (candidate.matches("\\d+-\\d+-\\d+")) {
                        return candidate;
                    }
                }
            }
        }
        return "N/A";
    }

}

