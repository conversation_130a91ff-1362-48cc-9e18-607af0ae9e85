package com.csci.susdev.util.voutil;

import com.csci.susdev.model.UserRole;
import com.csci.susdev.vo.UserRoleVO;
import com.google.common.base.Converter;
import org.springframework.beans.BeanUtils;

/**
 * 用于UserRole与VO的转换
 */
public class UserRoleConverter extends Converter<UserRole, UserRoleVO> {
    @Override
    protected UserRoleVO doForward(UserRole userRole) {
        UserRoleVO userRoleVO = new UserRoleVO();
        BeanUtils.copyProperties(userRole, userRoleVO);
        return userRoleVO;
    }

    @Override
    protected UserRole doBackward(UserRoleVO userRoleVO) {
        UserRole userRole = new UserRole();
        BeanUtils.copyProperties(userRoleVO, userRole);
        return userRole;
    }
}
