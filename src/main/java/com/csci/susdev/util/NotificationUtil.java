package com.csci.susdev.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class NotificationUtil {
    // 获取日志记录器
    static Logger logger = LoggerFactory.getLogger(NotificationUtil.class);

    // 从配置文件中获取配置信息
    @Value("${notification.apiAddress}")
    private String apiAddress;

    @Value("${notification.fromAppName}")
    private String fromAppName;

    // 发送邮件的异步方法
    @Async
    public void sendMail(List<String> listAddress, String title, String content) {
        /*
        try {
            OkHttpClient client = new OkHttpClient();
            MediaType mediaType = MediaType.parse("application/json");

            // 组装请求体
            Map<String, Object> mapBody = new HashMap<>();
            mapBody.put("title", title);
            mapBody.put("content", content);
            mapBody.put("fromAppName", fromAppName);
            mapBody.put("messageFormat", "html");

            List<Map<String, String>> mapAddress = new ArrayList<>();
            for (String address : listAddress) {
                Map<String, String> map = new HashMap<>();
                map.put("email", address);
                mapAddress.add(map);
            }
            mapBody.put("userList", mapAddress);

            String jsonBody = new JSONObject(mapBody).toJSONString();
            RequestBody body = RequestBody.create(mediaType, jsonBody);

            // 创建请求
            Request request = new Request.Builder()
                    .url(apiAddress)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 执行请求并处理响应
            Response response = client.newCall(request).execute();
            ObjectMapper mapper = JsonUtils.getObjectMapper();
            Map<String, Object> mapResponse = mapper.readValue(response.body().string(), Map.class);
            response.body().close();

            if ((int) mapResponse.get("code") < 0) {
                throw new Exception((String) mapResponse.get("msg"));
            }
            logger.info("发送邮件: " + new JSONObject(mapResponse).toJSONString());
        } catch (Exception e) {
            logger.error("发送邮件失败! " + e.getMessage());
        }

         */
    }

}
