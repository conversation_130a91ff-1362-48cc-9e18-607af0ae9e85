package com.csci.susdev.util;

import com.csci.common.util.CustomGsonBuilder;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.google.gson.Gson;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.URI;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Pattern;

import static java.math.BigDecimal.ZERO;

/**
 * 通用工具类
 *
 * <AUTHOR>
 * @date 2019-09-17
 */
public abstract class DemoUtils {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(DemoUtils.class);
    private static final Gson GSON = CustomGsonBuilder.createGson();

    /**
     * 读取位于classpath下面资源文件内容，主要是是指位于resources目录下的文件
     *
     * @param fileName 文件名称
     * @return 字符串
     * @throws IOException
     */
    public static String readResourceFile(String fileName) throws IOException {
        String content = "";
        try (InputStream is = DemoUtils.class.getClassLoader().getResourceAsStream(fileName)) {
            if (is != null) {
                return IOUtils.toString(is, StandardCharsets.UTF_8);
            }
        }
        return content;
    }

    public static String readAbsoluteFile(String fileName) throws IOException {
        String content = "";
        content = new String(Files.readAllBytes(Paths.get(fileName)));
        return content;
    }

    /**
     * 将布尔值转换为字符串类型的1或者0
     * <br>
     * eg.<br>
     * true->1<br>
     * false->0<br>
     * null->0
     *
     * @param bFlag 待转换布尔值
     * @return {@link String}
     */
    public static String convertBooleanToString(Boolean bFlag) {
        return Optional.ofNullable(bFlag).orElse(false) ? "1" : "0";
    }

    /**
     * 生成一个32位的guid
     *
     * @return
     */
    public static String generateGuid() {
        return Optional.of(randomUuid()).map(x -> x.replaceAll("-", "")).map(String::toUpperCase).orElseThrow(NullPointerException::new);
    }

    /**
     * 生成一个uuid，不包含-号
     *
     * @return
     */
    public static String randomUuid() {
        return UUID.randomUUID().toString();
    }

    /**
     * 将数字转化为4位的编码，左边空位补0
     *
     * @param target
     * @return
     */
    public static String convertIntToCode(int target) {
        DecimalFormat format = new DecimalFormat("0000");
        return format.format(target);
    }

    /**
     * 将数字转化为length位的编码，左边空位补0
     *
     * @param target
     * @param length
     * @return
     */
    public static String convertIntToCode(int target, int length) {
        StringBuilder sbPattern = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sbPattern.append("0");
        }
        DecimalFormat format = new DecimalFormat(sbPattern.toString());
        return format.format(target);
    }

    /**
     * 写入相对路径，如：resources下面的文件
     *
     * @param content
     * @param fileName
     */
    public static void writeToRelativePath(String content, String fileName) {
        try {
            URL url = DemoUtils.class.getClassLoader().getResource(fileName);
            if (url == null) {
                logger.error("CommonUtils#writeToRelativePath, 未找到指定的文件, fileName={}", fileName);
                return;
            }
            URI uri = url.toURI();
            Files.write(Paths.get(url.toURI()), content.getBytes());
        } catch (Exception e) {
            logger.error("CommonUtils#writeToRelativePath, 写入文件出错, fileName={}", fileName);
        }

    }

    /**
     * 将文本内容写入指定的文件
     *
     * @param content
     * @param absoluteFilePath
     * @throws IOException
     */
    public static void writeContentToFile(String content, String absoluteFilePath) throws IOException {
        Files.write(Paths.get(absoluteFilePath), content.getBytes());
    }

    /**
     * 追加到文件末尾
     *
     * @param absoluteFilePath
     * @param content
     * @throws IOException
     */
    public static void appendToFile(String absoluteFilePath, String content) throws IOException {
        Path path = Paths.get(absoluteFilePath);
        if (Files.exists(path)) {
            Files.write(path, content.getBytes(StandardCharsets.UTF_8), StandardOpenOption.APPEND);
        } else {
            Files.write(path, content.getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * todo 需要删除的代码
     *
     * @param content
     * @param path
     */
    public static void writeForTest(String content, String path) {
        Environment env = (Environment) SpringContextUtil.getBean("environment");
        String activeProfile = Optional.of(env).map(x -> x.getProperty("spring.profiles.active")).orElse("");
        if (Objects.equals("dev", activeProfile)) {
            try {
                DemoUtils.writeContentToFile(content, path);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 当token包含空格时，移除空格之前的字符串
     *
     * @param token
     * @return
     */
    public static String removePrefixForToken(String token) {
        if (StringUtils.isNotBlank(token) && token.contains(" ")) {
            return StringUtils.trim(token.substring(token.indexOf(" ")));
        }
        return token;
    }

    public static boolean isSuccess(ResultBase resultBase) {
        return Objects.equals(SusDevConsts.RespStatus.SUCCESS, resultBase.getCode());
    }

    public static boolean isFailure(ResultBase resultBase) {
        return !isSuccess(resultBase);
    }

    /**
     * 计算a和b相加的结果
     *
     * @param a
     * @param b
     * @return
     */
    public static BigDecimal add(BigDecimal a, BigDecimal b) {
        return Optional.ofNullable(a).orElse(BigDecimal.ZERO).add(Optional.ofNullable(b).orElse(BigDecimal.ZERO));
    }

    /**
     * 计算a和b相减的结果
     *
     * @param a
     * @param b
     * @return
     */
    public static BigDecimal subtract(BigDecimal a, BigDecimal b) {
        return Optional.ofNullable(a).orElse(BigDecimal.ZERO).subtract(Optional.ofNullable(b).orElse(BigDecimal.ZERO));
    }

    /**
     * check if the target if greater than zero
     *
     * @param target
     * @return
     */
    public static boolean isGreaterThenZero(BigDecimal target) {
        return Optional.ofNullable(target).orElse(ZERO).doubleValue() > 0;
    }

    public static boolean isLessThenZero(BigDecimal target) {
        return Optional.ofNullable(target).orElse(ZERO).doubleValue() < 0;
    }

    /**
     * 判断指定的数是否为0
     *
     * @param target
     * @return
     */
    public static boolean isEqualToZero(BigDecimal target) {
        return Optional.ofNullable(target).orElse(ZERO).doubleValue() == 0;
    }

    /**
     * 比较两个 BigDecimal 的大小，null 视为 0。
     * 返回比较结果：1 表示 value1 > value2，-1 表示 value1 < value2，0 表示相等。
     */
    public static int compareWithNullAsZero(BigDecimal value1, BigDecimal value2) {
        BigDecimal nonNullValue1 = (value1 != null) ? value1 : BigDecimal.ZERO;
        BigDecimal nonNullValue2 = (value2 != null) ? value2 : BigDecimal.ZERO;
        return nonNullValue1.compareTo(nonNullValue2);
    }

    /**
     * 0或者null表示初始状态
     *
     * @param status
     * @return
     */
    public static boolean isInitialStatus(Integer status) {
        return Objects.isNull(status) || Objects.equals(status, 0);
    }

    /**
     * 获取字符串所占字节数（通常用于计算数据库字段长度）
     * utf-8编码来计算
     *
     * @param target
     * @return
     */
    public static int getStringLength(String target) {
        return getStringLength(target, StandardCharsets.UTF_8);
    }

    /**
     * 根据编码类型获取字符串所占字节数
     *
     * @param target
     * @param charsets
     * @return
     */
    public static int getStringLength(String target, Charset charsets) {
        return Optional.ofNullable(target).map(x -> x.getBytes(charsets)).map(x -> x.length).orElse(0);
    }

    public static String getLocalIps() {
        Enumeration<NetworkInterface> netInterfaces;
        StringBuilder allIps = new StringBuilder();
        try {
            netInterfaces = NetworkInterface.getNetworkInterfaces();
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = netInterfaces.nextElement();
                //System.out.println("DisplayName:" + ni.getDisplayName());
                //System.out.println("Name:" + ni.getName());
                Enumeration<InetAddress> ips = ni.getInetAddresses();
                while (ips.hasMoreElements()) {
                    String addr = ips.nextElement().getHostAddress();
                    if (addr.length() <= 15 && !addr.contains("127.0.0.1") && !addr.contains("0:0:0:0")) {
                        allIps.append(addr);
                        allIps.append(";");
                    }
                }
            }
        } catch (Exception e) {
            logger.error("获取当前服务器ip地址失败", e);
        }
        return allIps.toString();
    }

    /**
     * md5方式加密
     *
     * @param target
     * @return
     */
    public static String md5(String target) {
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            return "";
        }
        md.update(target.getBytes(StandardCharsets.UTF_8));
        return new String(Hex.encodeHex(md.digest()));
    }

    /**
     * 生成电子签名
     *
     * @param encryptkey
     * @param bean
     * @return
     */
    public static String generateSign(String encryptkey, Object bean) {
        String json = GSON.toJson(bean);
        Map<String, ?> map = GSON.fromJson(json, HashMap.class);
        StringBuilder sbSource = new StringBuilder();
        map.keySet().stream().sorted().forEach(key -> {
            System.out.println(key);
            if (!StringUtils.equals("sign", key)) {
                sbSource.append(key).append("=").append(map.get(key)).append("&");
            }
        });
        // sbSource.deleteCharAt(sbSource.length() - 1);
        sbSource.append("key=").append(encryptkey);
        logger.info("CommonUtils#generateSign, sbSource: {}", sbSource.toString());
        return md5(sbSource.toString());
    }

    /**
     * 将域账号的域前缀和用户名分开
     *
     * @param username
     * @return
     */
    public static Map.Entry<String, String> splitDomainUsername(String username) {
        int separatorIndex = StringUtils.lastIndexOf(username, "\\");
        String key, value;
        if (separatorIndex == -1) {
            key = "";
            value = username;
            return new AbstractMap.SimpleEntry<>("", username);
        } else {
            key = StringUtils.substring(username, 0, separatorIndex);
            value = StringUtils.substring(username, separatorIndex + 1);
        }
        return new AbstractMap.SimpleEntry<>(key, value);
    }

    /**
     * 获取指定资源文件的字节流
     *
     * @param resource 资源文件路径
     * @return
     */
    public static InputStream getResourceAsStream(String resource) {
        return Thread.currentThread().getContextClassLoader().getResourceAsStream(resource);
    }

    public static boolean isMatch(String target, String regex) {
        return Pattern.matches(regex, target);
    }


    public static void main(String[] args) {
        String guid = generateGuid();
        String uuid = randomUuid();
        System.out.println("guid : " + guid + " : " + guid.length());
        System.out.println("uuid : " + uuid + " : " + uuid.length());

        System.out.println("Number format: " + convertIntToCode(1));
        System.out.println("Number format: " + convertIntToCode(11));
        System.out.println("Number format: " + convertIntToCode(132));


        // writeToRelativePath("hahaha, test string!", "params.json");

        System.out.println(removePrefixForToken("bear slkfjl;asdkfa;sldkfas"));
        System.out.println(removePrefixForToken("slkfjl;asdkfa;sldkfas"));

        System.out.println("99999999999999999999999999");
        System.out.println(getStringLength("0126992004654(白亞賓＆周惠如）"));
        System.out.println(getStringLength("中国汉字䯂"));
        System.out.println(getStringLength("AP、CCB、2006、0260 支付予冼嘉威(Staff No:030444-黃竹坑AIL46段上蓋發展項目)的推薦人獎賞計劃獎金，因其推薦余浩勤(Staff No:032988)到恆大屯門管翠路500住宅"));

        System.out.println(md5("this is a test String"));

    }
}
