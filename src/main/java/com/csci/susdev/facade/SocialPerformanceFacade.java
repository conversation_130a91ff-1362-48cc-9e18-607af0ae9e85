package com.csci.susdev.facade;

import com.csci.common.util.CustomGsonBuilder;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.model.SocialPerformanceDetail;
import com.csci.susdev.model.SocialPerformanceDetailExample;
import com.csci.susdev.model.SocialPerformanceHead;
import com.csci.susdev.model.SocialPerformanceHeadExample;
import com.csci.susdev.service.SocialPerformanceDetailService;
import com.csci.susdev.service.SocialPerformanceService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
@LogMethod
public class SocialPerformanceFacade {

    private final Gson gson = CustomGsonBuilder.createGson();

    @Resource
    private SocialPerformanceService socialPerformanceService;

    @Resource
    private SocialPerformanceDetailService socialPerformanceDetailService;

    /**
     * 修正报表数据
     * 由于报表数据通过excel导入，导致有些数据在复制过程中使用了填充序列，导致数据不正确
     */
    public void correctReportItem() {
        // 查询出所有head
        SocialPerformanceHeadExample headExample = new SocialPerformanceHeadExample();
        headExample.or().andIsActiveEqualTo(Boolean.TRUE);
        List<SocialPerformanceHead> lstHead = socialPerformanceService.selectByExample(headExample);

        // 逐条处理
        for (SocialPerformanceHead head : lstHead) {
            System.out.println(head.getId());
            SocialPerformanceDetailExample detailExample = new SocialPerformanceDetailExample();
            detailExample.or().andHeadIdEqualTo(head.getId());
            detailExample.setOrderByClause("seq");
            List<SocialPerformanceDetail> lstDetail = socialPerformanceDetailService.selectByExample(detailExample);
            List<String> lstCategory = lstDetail.stream().map(SocialPerformanceDetail::getCategory).distinct().collect(Collectors.toList());

            for (String category : lstCategory) {
                List<SocialPerformanceDetail> lstDetailByCategory = lstDetail.stream().filter(detail -> category.equals(detail.getCategory())).collect(Collectors.toList());
                SocialPerformanceDetail firstDetail = lstDetailByCategory.get(0);
                if (StringUtils.isBlank(firstDetail.getReportItem())) {
                    continue;
                }
                for (int i = 1; i < lstDetailByCategory.size(); i++) {
                    SocialPerformanceDetail detail = lstDetailByCategory.get(i);
                    SocialPerformanceDetail record = new SocialPerformanceDetail();
                    record.setId(detail.getId());
                    record.setReportItem(firstDetail.getReportItem());
                    System.out.println(gson.toJson(record));
                    socialPerformanceDetailService.updateByPrimaryKeySelective(record);
                }
            }
        }
    }
}
