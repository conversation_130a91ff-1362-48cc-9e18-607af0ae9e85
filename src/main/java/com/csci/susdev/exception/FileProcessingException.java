package com.csci.susdev.exception;

/**
 * Exception thrown when file processing fails during OCR operations
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
public class FileProcessingException extends OcrException {
    
    private static final long serialVersionUID = 1L;
    
    public FileProcessingException(String message) {
        super("FILE_PROCESSING_ERROR", message);
    }
    
    public FileProcessingException(String message, Throwable cause) {
        super("FILE_PROCESSING_ERROR", message, cause);
    }
    
    /**
     * Exception for unsupported file types
     */
    public static class UnsupportedFileTypeException extends FileProcessingException {
        public UnsupportedFileTypeException(String fileType) {
            super("不支持的文件类型: " + fileType + "。支持的文件类型: JPG, JPEG, PNG, PDF");
        }
    }
    
    /**
     * Exception for file size limit exceeded
     */
    public static class FileSizeExceededException extends FileProcessingException {
        public FileSizeExceededException(long fileSize, long maxSize) {
            super("文件大小超出限制。文件大小: " + fileSize + " bytes, 最大允许: " + maxSize + " bytes");
        }
    }
    
    /**
     * Exception for corrupted or invalid files
     */
    public static class InvalidFileException extends FileProcessingException {
        public InvalidFileException(String message) {
            super("无效的文件: " + message);
        }
        
        public InvalidFileException(String message, Throwable cause) {
            super("无效的文件: " + message, cause);
        }
    }
}
