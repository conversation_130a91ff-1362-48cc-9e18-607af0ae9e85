package com.csci.susdev.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ExcleFcCarbonFactorEsgDatasetFactorSelectionVO {

    @ExcelProperty("組織機構")
    private String organizationName;

    @ExcelProperty("年份")
    private Integer year;
    @ExcelProperty("協議")
    private String protocolNameEn;

    @ExcelProperty("場景")
    private String carbonEmissionLocationName;

    @ExcelProperty("協議大類")
    private String categoryName;
    @ExcelProperty("協議小類")
    private String subCategoryName;

    //因子管理

    @ExcelProperty("因子類型")
    private String fcCarbonFactorType;

    @ExcelProperty("表單")
    private String formName;
    @ExcelProperty("类别")
    private String typeA;
    @ExcelProperty("小类")
    private String typeB;
    @ExcelProperty("子类")
    private String typeC;
    @ExcelProperty("表单单位")
    private String typeD;
    @ExcelProperty("编码")
    private String typeE;
    @ExcelProperty("平台類型")
    private String platform;
    @ExcelProperty("物料中文名稱")
    private String chineseName;
    @ExcelProperty("種類")
    private String category;
    @ExcelProperty("排放來源")
    private String emissionSource;
    @ExcelProperty("單位")
    private String unit;
    @ExcelProperty("碳排放因子")
    private BigDecimal carbonFactor;

    @ExcelProperty("碳因子單位")
    private String carbonFactorUnit;

    @ExcelProperty("描述")
    private String description;

    @ExcelProperty("來源")
    private String sourceDescription;

}
