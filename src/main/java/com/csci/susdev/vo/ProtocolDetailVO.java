package com.csci.susdev.vo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ProtocolDetailVO {

    private String id;

    private String carbonEmissionLocationId;

    private String carbonEmissionLocationName;

    private String carbonEmissionLocationNameSc;

    private String carbonEmissionLocationNameEn;

    private String protocolId;

    private String protocolName;

    private String protocolNameSc;

    private String protocolNameEn;

    private String categoryId;

    private String categoryName;

    private String categoryNameSc;

    private String categoryNameEn;

    private String subCategoryId;

    private String subCategoryName;

    private String subCategoryNameSc;

    private String subCategoryNameEn;

    private String description;

    private Integer seq;

    private Integer lastUpdateVersion;

}
