package com.csci.susdev.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/26 9:16
 * @description 因子选择导入
 */
@Data
public class FactorSelectionImportExcelVO {

    List<ExcleFcCarbonFactorHkFactorSelectionVO> hkFactorSelectionVOS;

    List<ExcleFcCarbonFactorGbt51366VOFactorSelectionVO> gbt51366VOFactorSelectionVOS;

    List<ExcleFcCarbonFactorFlcFactorSelectionVO> flcFactorSelectionVOS;

    List<ExcleFcCarbonFactorEsgDatasetFactorSelectionVO> esgDatasetFactorSelectionVOS;

    List<ExcleFcEnergyFactorEsgDatasetFactorSelectionVO> energyFactorEsgDatasetFactorSelectionVOS;

    List<ExcleFcAirPollutionFactorEsgDatasetFactorSelectionVO> airPollutionFactorEsgDatasetFactorSelectionVOS;

}
