package com.csci.susdev.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "審核設置")
public class AuditSettingDetailVO extends AuditSettingVO {
	
    @Schema(description = "組織名稱")
	private String organizationName;

    @Schema(description = "表單名稱")
	private String formName;

    @Schema(description = "審核人用户名")
    private String username1;

    @Schema(description = "審核人姓名")
    private String userRealName1;

    @Schema(description = "審核人用户名")
    private String username2;

    @Schema(description = "審核人姓名")
    private String userRealName2;

    @Schema(description = "審核人用户名")
    private String username3;

    @Schema(description = "審核人姓名")
    private String userRealName3;

    @Schema(description = "審核人用户名")
    private String username4;

    @Schema(description = "審核人姓名")
    private String userRealName4;
}
