package com.csci.susdev.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class CeBasicInfoImportDataVO {

    @ExcelProperty("col1")
    private String col1;
    @ExcelProperty("col2")
    private String col2;
    @ExcelProperty("col3")
    private String col3;
    @ExcelProperty("col4")
    private String col4;
    @ExcelProperty("col5")
    private String col5;
    @ExcelProperty("col6")
    private String col6;
    @ExcelProperty("col7")
    private String col7;
    @ExcelProperty("col8")
    private String col8;
    @ExcelProperty("col9")
    private String col9;
    @ExcelProperty("col10")
    private String col10;
    @ExcelProperty("col11")
    private String col11;
    @ExcelProperty("col12")
    private String col12;
    @ExcelProperty("col13")
    private String col13;
    @ExcelProperty("col14")
    private String col14;
    @ExcelProperty("col15")
    private String col15;
    @ExcelProperty("col16")
    private String col16;
    @ExcelProperty("col17")
    private String col17;
    @ExcelProperty("col18")
    private String col18;
    @ExcelProperty("col19")
    private String col19;
    @ExcelProperty("col20")
    private String col20;
    @ExcelProperty("col21")
    private String col21;
    @ExcelProperty("col22")
    private String col22;
    @ExcelProperty("col23")
    private String col23;
    @ExcelProperty("col24")
    private String col24;
    @ExcelProperty("header")
    private Boolean header;
}
