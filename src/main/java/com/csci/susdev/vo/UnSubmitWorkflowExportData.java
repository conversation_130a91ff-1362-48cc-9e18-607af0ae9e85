package com.csci.susdev.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class UnSubmitWorkflowExportData {

    @ExcelProperty("编号")
    private String id;

    @ExcelProperty("表單名稱")
    private String formName;

    @ExcelProperty("上級組織名稱")
    private String parentName;

    @ExcelProperty("年份")
    private String year;

    @ExcelProperty("組織／地盤名稱")
    private String organizationName;

    @ExcelProperty("提交人")
    private String representativeRealName;

    @ExcelProperty("提交人中海通帳號")
    private String representativeName;

    @ExcelProperty("提交人電話")
    private String representativeMobile;

}
