package com.csci.susdev.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class WorkflowControlVO {

    private String id;

    private String workflowId;
    /**
     * 流程最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    /**
     * 创建时间时间
     */
    private LocalDateTime creationTime;

    private String businessId;

    private String organizationId;

    private String organizationName;

    private String parentOrgName;

    private String formId;

    private String formName;

    private String currentNodeId;
    /**
     * 当前节点名字
     */
    private String currentNodeName;
    /**
     * 流程状态 0未提交 1审批中 2已完成 3已取消 4已终止
     */
    private Integer state;

    private String stateName;

    private Boolean isUrgentEditable;

    private Integer year;

    private Integer month;

    private Integer lastUpdateVersion;

    private String createUserId;

    private String createUserRealName;

    private String createUserName;

    private String createUserMobile;
    /**
     * 当前节点处理人的名字
     */
    private String currentNodeUserName;

    private String currentNodeUserEmail;

    private String representativeId;

    private String representativeRealName;

    private String representativeName;

    private String representativeMobile;

    private Boolean isAllowSubmit;

    private List<WorkflowNodeVO> workflowNodes;
}
