package com.csci.susdev.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class FcCarbonFactorGbt51366VO {

    private String id;

    private String chineseName;

    private String specification;

    private String energyConsumption;

    private BigDecimal heatCo2Factor;

    private BigDecimal effectiveCo2FactorDefault;

    private BigDecimal effectiveCo2FactorLower;

    private BigDecimal effectiveCo2FactorUpper;

    private BigDecimal carbonFactor;

    private String carbonFactorUnit;

    private String description;

    private String sourceDescription;

    private Integer lastUpdateVersion;

    private Integer recordYear;

}
