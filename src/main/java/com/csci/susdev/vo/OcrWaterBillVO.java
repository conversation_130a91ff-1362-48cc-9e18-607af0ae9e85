package com.csci.susdev.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class OcrWaterBillVO {
	
	private String ChargeNumber;
	private String AccountNumber;
    private String TotalAmountDue;
    private String TotalCharges;
    
    private String LastPaymentDate;
    private String LastPayment;
    private String DepositHeld;
    private String DisputeAmount;
    private String InstalmentAmount;
    
    private String MeterNo;
    private String DateStart;
    private String ReadingStart;
    private String DateEnd;
    private String ReadingEnd;
    private String Consumption;
    private String AverageDailyConsumption;
}
