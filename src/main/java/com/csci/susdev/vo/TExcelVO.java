package com.csci.susdev.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: barry
 * @create: 2024-11-20 09:58
 */
@Data
public class TExcelVO {
    @ExcelProperty("类别")
    private String category;

    @ExcelProperty("小类")
    private String subCategory;

    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("别名")
    private String alias;

    @ExcelProperty("规格")
    private String specification;

    @ExcelProperty("用能类型")
    private String energyType;

    @ExcelProperty("用能单位")
    private String energyUnit;

    @ExcelProperty("能源用量")
    private BigDecimal energyConsumption;

    @ExcelProperty("排放单位")
    private String emissionUnit;

    @ExcelProperty("碳中和因子數值")
    private BigDecimal emissionFactor;

    @ExcelProperty("试用区域")
    private String applicableArea;

    @ExcelProperty("因子来源")
    private String factorSource;

    @ExcelProperty("开始时间")
    private String startTime;

    @ExcelProperty("结束时间")
    private String endTime;

    @ExcelProperty("有效状态")
    private String validStatus;

    @ExcelProperty("编辑时间")
    private String editTime;

    @ExcelProperty("资源消耗")
    private String resourceConsumption;

    @ExcelProperty("消耗单位")
    private String consumptionUnit;

    @ExcelProperty("排放量")
    private String emissionAmount;

    @ExcelProperty("排放时间")
    private String emissionTime;

    @ExcelProperty("源头")
    private String source;

    @ExcelProperty("表单名")
    private String formName;

    @ExcelProperty("行列号")
    private String rowColumnNumber;

    @ExcelProperty("列名")
    private String columnName;
}
