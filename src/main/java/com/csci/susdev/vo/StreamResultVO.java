package com.csci.susdev.vo;

import lombok.Data;

import java.io.InputStream;

@Data
public class StreamResultVO {

    private InputStream inputStream;
    private long contentLength;
    private String contentType;

    // 构造方法 + Getter
    public StreamResultVO(InputStream inputStream, long contentLength, String contentType) {
        this.inputStream = inputStream;
        this.contentLength = contentLength;
        this.contentType = contentType;
    }

}
