package com.csci.susdev.vo;

import com.csci.cohl.model.EmpCommutingDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class EmpCommutingHeadVO {

    private String id;

    private String organizationId;

    private Integer year;

    private Integer month;

    private Integer lastUpdateVersion;

    private EmpCommutingDetail empCommutingDetail;

    private Integer workflowControlState;

    private String workflowControlStateName;

    private List<EmpCommutingDetail> empCommutingDetailList;

    @Schema(description = "要删除的明细ID集合")
    private List<String> deleteDetailIdList;
}
