package com.csci.susdev.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class WorkflowNodeVO {

    private String id;

    private String workflowId;

    @Schema(description = "节点名称")
    private String name;

    private String previousNodeId;

    @Schema(hidden = true)
    private Boolean isBeginNode;

    @Schema(description = "审批人id")
    private String userId;

    @Schema(description = "审批人姓名")
    private String userRealName;

    @Schema(description = "审批人帳號")
    private String userName;

    @Schema(description = "電話")
    private String userMobile;
    @Schema(description = "最新更新时间")
    private LocalDateTime lastUpdateTime;
    @Schema(description = "最新更新时间字符串")
    private String lastUpdateTimeStr;
}
