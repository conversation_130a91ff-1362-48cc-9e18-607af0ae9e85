package com.csci.susdev.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import lombok.Data;


@Data
public class ExcelCeIdentificationRunawayEmissionVO {

    @ExcelProperty(value = "模块", index = 0)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    private String module;

    @ExcelProperty(value = {"确认项"}, index = 1)
    @ColumnWidth(30)
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    private String confirmationItemOne;

    @ExcelProperty(value = {"确认项"}, index = 2)
    @ColumnWidth(30)
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    private String confirmationItemTwo;

    @ExcelProperty(value = {"确认项"}, index = 3)
    @ColumnWidth(30)
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    private String confirmationItemThree;

    @ExcelProperty("说明")
    @ColumnWidth(50)
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    private String instructions;

    @ExcelProperty(" ")
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    private String value;

    @ExcelProperty(" ")
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    private byte[] imgData;
}
