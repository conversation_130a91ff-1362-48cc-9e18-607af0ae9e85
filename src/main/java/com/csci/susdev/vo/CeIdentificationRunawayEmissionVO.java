package com.csci.susdev.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CeIdentificationRunawayEmissionVO {

    @Schema(description ="主键ID")
    private String id;
    @Schema(description ="碳排识别表头ID")
    private String ceIdentificationHeadId;
    @Schema(description ="是否有自己运营管理的化粪池")
    private String isOwnSepticTank;
    @Schema(description ="正式员工人数（个）")
    private String regularEmployeeNum;
    @Schema(description ="年平均工作天数（天）")
    private String averageWorkDayYear;
    @Schema(description ="日平均工作小时数-需通勤（h）")
    private String averageDailyCommuting;
    @Schema(description ="日平均工作小时数-需住宿（h）")
    private String averageDailyAccommodation;
    @Schema(description ="空调铭牌-制冷剂型号")
    private String airconditionRefrigerantType;
    @Schema(description ="空调铭牌-制冷剂充注量")
    private String airconditionRefrigerantChargeAmount;
    @Schema(description ="空调铭牌-拍摄图片")
    private String airconditionTakePhoto;
    @Schema(description ="冰箱/冰柜铭牌--制冷剂型号")
    private String fridgeRefrigerantType;
    @Schema(description ="冰箱/冰柜铭牌-制冷剂充注量")
    private String fridgeRefrigerantChargeAmount;
    @Schema(description ="冰箱/冰柜铭牌-拍摄图片")
    private String fridgeTakePhoto;
    @Schema(description ="公务车制冷剂型号")
    private String serviceCarRefrigerantType;
    @Schema(description ="公务车制冷剂充注量")
    private String serviceCarRefrigerantChargeAmount;
    @Schema(description ="公务车拍摄图片")
    private String serviceCarTakePhoto;
    @Schema(description ="最后一次更新版本号")
    private Integer lastUpdateVersion;

}
