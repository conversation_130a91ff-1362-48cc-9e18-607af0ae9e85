package com.csci.susdev.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "因子管理-因子换算VO对象")
public class FcFactorConversionVO {

    @Schema(description = "ID")
    private String id;
    @Schema(description = "温室气体类型")
    private String greenhouseGasTypes;
    @Schema(description = "GWP数值")
    private BigDecimal gwpValue;
    @Schema(description = "单位名称")
    private String unit;
    @Schema(description = "数据来源")
    private String dataSource;
    @Schema(description = "版本")
    private String version;

    @Schema(description = "最后一次更新版本号")
    private Integer lastUpdateVersion;

}
