package com.csci.susdev.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class AmbientEnergyBillVO {

    private String id;
    private String headId;
    //水費單：water
    //電費單：electricity
    private String type;
    //帳單號
    private String billNo;
    //起始時間
    private LocalDateTime fromTime;
    //结束時間
    private LocalDateTime toTime;
    //用量（電：千瓦时、水：立方米）
    private BigDecimal consumption;
    //附件ID
    private String attachment_id;
    private Integer lastUpdateVersion;

}
