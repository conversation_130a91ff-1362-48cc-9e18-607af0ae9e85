package com.csci.susdev.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ExcleFcCarbonFactorFlcFactorSelectionVO {

    @ExcelProperty("組織機構")
    private String organizationName;

    @ExcelProperty("年份")
    private Integer year;
    @ExcelProperty("協議")
    private String protocolNameEn;

    @ExcelProperty("場景")
    private String carbonEmissionLocationName;

    @ExcelProperty("協議大類")
    private String categoryName;
    @ExcelProperty("協議小類")
    private String subCategoryName;

    //因子管理

    @ExcelProperty("因子類型")
    private String fcCarbonFactorType;

    @ExcelProperty("表單")
    private String formName;
    @ExcelProperty("类别")
    private String typeA;
    @ExcelProperty("小类")
    private String typeB;
    @ExcelProperty("子类")
    private String typeC;
    @ExcelProperty("表单单位")
    private String typeD;
    @ExcelProperty("编码")
    private String typeE;
    @ExcelProperty("物料中文名稱")
    private String chineseName;
    @ExcelProperty("上游排放")
    private BigDecimal upstreamEmission;
    @ExcelProperty("下游排放")
    private BigDecimal downstreamEmission;
    @ExcelProperty("物料單位")
    private String emissionUnit;
    @ExcelProperty("描述")
    private String description;
    @ExcelProperty("碳中和因子數值")
    private BigDecimal carbonFactor;

    @ExcelProperty("碳中和因子單位")
    private String carbonFactorUnit;

    @ExcelProperty("來源")
    private String datasource;

}
