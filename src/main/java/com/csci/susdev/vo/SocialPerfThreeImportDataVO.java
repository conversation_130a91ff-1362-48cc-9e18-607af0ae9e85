package com.csci.susdev.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class SocialPerfThreeImportDataVO {

    @ExcelProperty("category")
    private String category;

    @ExcelProperty("reportItem")
    private String reportItem;

    @ExcelProperty("classification1")
    private String classification1;

    @ExcelProperty("classification2")
    private String classification2;

    @ExcelProperty("classification3")
    private String classification3;

    @ExcelProperty("classification4")
    private String classification4;

    @ExcelProperty("classification5")
    private String classification5;

    @ExcelProperty("classification6")
    private String classification6;

    @ExcelProperty("classification7")
    private String classification7;

    @ExcelProperty("remark")
    private String remark;

    @ExcelProperty("header")
    private Boolean header;
}
