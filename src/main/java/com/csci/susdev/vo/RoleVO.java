package com.csci.susdev.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "角色")
public class RoleVO {

    private String id;

    private String code;

    private String name;

    private String description;

    private Integer lastUpdateVersion;

    private List<PermissionVO> permissions;

    @Schema(description = "角色关联的权限id列表")
    private List<String> permissionIds;

    @Schema(description = "角色关联的组织id列表")
    private List<String> roleOrganizationIds;

}
