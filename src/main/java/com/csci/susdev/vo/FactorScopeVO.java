package com.csci.susdev.vo;

import lombok.Data;


@Data
public class FactorScopeVO {

    private String id;

    //協議明細

    private String protocolDetailId;

    private String carbonEmissionLocationId;

    private String carbonEmissionLocationName;

    private String carbonEmissionLocationNameSc;

    private String carbonEmissionLocationNameEn;

    private String protocolId;

    private String protocolName;

    private String protocolNameSc;

    private String protocolNameEn;

    private String categoryId;

    private String categoryName;

    private String categoryNameSc;

    private String categoryNameEn;

    private String subCategoryId;

    private String subCategoryName;

    private String subCategoryNameSc;

    private String subCategoryNameEn;

    //表單明細

    private String formDetailId;


    private String formCode;
    private String formCodeEnum;

    private Integer year;

    private String typeA;

    private String typeB;

    private String typeC;

    private String typeD;

    private String typeE;

    //因子類別

    private String factorType;

    private Integer lastUpdateVersion;

}
