package com.csci.susdev.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class EmissionReductionInfoVO {

    private String id;
    private String headId;
    //节能减排措施描述
    private String description;
    //实施时间
    private LocalDateTime implementTime;
    //预计年节能量（tce）
    private BigDecimal estAnnualEnergySaving;
    //预计年减排量（tCO2）
    private BigDecimal estAnnualEmissionReduction;
    //去年节能量（tce）
    private BigDecimal estAnnualEnergySavingLastYear;
    //去年减排量（tCO2）
    private BigDecimal estAnnualEmissionReductionLastYear;
    //证据文件名称
    private String evidence;
    private Integer lastUpdateVersion;

}
