package com.csci.susdev.vo;

import lombok.Data;

import java.util.List;

@Data
public class UserVO {
	
	private String id;

    private String name;
    
    private int gender;
    
    private String domainPrefix;

    private String username;
    
    private String password;
    
    private String email;

    private String mobile;

    private String staffNo;

    private String companyName;

    private String birthDate;

    private String position;

    private String description;

    private List<String> roleIds;

    private List<String> bsRoleNames;

    private List<String> organizationIds;

    private List<String> bsOrganizationIds;

    private Boolean isEnabled;

    private Boolean isDeleted;

    private Boolean isAdAccount;

    private Boolean isReadonly;

    private Integer lastUpdateVersion;
}
