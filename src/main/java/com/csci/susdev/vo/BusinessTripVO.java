package com.csci.susdev.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BusinessTripVO {

    private String id;

    private String ambientHeadId;

    private String vehicleType;

    private Integer monthValue;

    private String startPlace;

    private String destination;

    private String routeNo;

    private String level;

    private Integer tripType;

    private Integer ticketType;

    private Integer personCount;

    private BigDecimal flightDistance;

    private BigDecimal carbonEmission;

    private String remark;

    private Integer lastUpdateVersion;

    private String startPlaceAll;

    private String destinationAll;

}
