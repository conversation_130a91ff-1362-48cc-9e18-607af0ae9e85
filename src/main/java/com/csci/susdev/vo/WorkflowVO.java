package com.csci.susdev.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class WorkflowVO {

    private String id;

    private String organizationId;

    private String organizationName;

    private Integer year;

    private Integer month;

    private String formId;

    private String formName;

    private String representativeId;

    private String representativeRealName;

    private String representativeName;

    private String representativeMobile;

    private String name;

    private String code;

    private Integer lastUpdateVersion;

    private List<WorkflowNodeVO> workflowNodes;

}
