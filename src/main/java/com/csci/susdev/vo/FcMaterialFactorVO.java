package com.csci.susdev.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "因子管理-物料因子VO对象")
public class FcMaterialFactorVO {

    @Schema(description = "ID")
    private String id;
    @Schema(description = "物料名称")
    private String chineseName;
    @Schema(description = "效能规格")
    private String performanceSpecifications;
    @Schema(description = "用量单位")
    private String unit;
    @Schema(description = "排放因子数值")
    private BigDecimal carbonFactor;
    @Schema(description = "排放因子单位")
    private String carbonFactorUnit;
    @Schema(description = "因子描述")
    private String description;
    @Schema(description = "来源")
    private String sourceDescription;
    @Schema(description = "最后一次更新版本号")
    private Integer lastUpdateVersion;

}
