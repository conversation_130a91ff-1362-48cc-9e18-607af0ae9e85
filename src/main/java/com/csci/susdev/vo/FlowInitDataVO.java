package com.csci.susdev.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class FlowInitDataVO {

    @ExcelProperty("平台公司")
    private String topCompanyName;

    @ExcelProperty("公司名")
    private String companyName;

    @ExcelProperty("部门/项目")
    private String deptName;

    @ExcelProperty("审批人1")
    private String user1Name;

    @ExcelProperty("审批人1邮箱")
    private String user1Email;

    @ExcelProperty("审批人2")
    private String user2Name;

    @ExcelProperty("审批人2邮箱")
    private String user2Email;

    @ExcelProperty("审批人3")
    private String user3Name;

    @ExcelProperty("审批人3邮箱")
    private String user3Email;

    @ExcelProperty("审批人4")
    private String user4Name;

    @ExcelProperty("审批人4邮箱")
    private String user4Email;

    private transient String errorMsg;
}
