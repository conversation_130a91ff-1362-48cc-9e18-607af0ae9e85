package com.csci.susdev.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "会话列表对象")
public class AiAgentConversationVO {

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "会话ID" )
    private String appConversationId;

    @Schema(description = "会话名称" )
    private String conversationName;

    @Schema(description = "会话IDs" )
    private List<String> appConversationIdList;

    @Schema(description = "手动修改" )
    private Boolean manualUpdateFlag;

}
