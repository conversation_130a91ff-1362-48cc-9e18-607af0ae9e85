package com.csci.susdev.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum TiOcrCodeEnum {

    SUCCESS(0, "SUCCESS", "成功"),

    HTTP_BAD_REQUEST(400, "HTTP_BAD_REQUEST", "请求不合法，包体格式错误"),

    HTTP_UNAUTHORIZED(401, "HTTP_UNAUTHORIZED", "权限验证失败"),

    HTTP_FORBIDDEN(403, "HTTP_FORBIDDEN", "鉴权信息不合法，禁止访问"),

    HTTP_NOTFOUND(404, "HTTP_NOTFOUND", "请求失败"),

    HTTP_REQ_NO_LENGTH(411, "HTTP_REQ_NOLENGTH", "请求没有指定ContentLength"),

    HTTP_REQUEST_LARGE(413, "HTTP_REQUEST_LARGE", "请求包体太大"),

    HTTP_METHOD_NOTFOUND(424, "HTTP_METHOD_NOTFOUND", "请求的方法没有找到"),

    HTTP_INTERNAL_SERVER_ERROR(500, "HTTP_INTERNAL_SERVER_ERROR", "服务内部错误"),

    HTTP_BAD_GATEWAY(502, "HTTP_BAD_GATEWAY", "网关错误，计算后台服务不可用"),

    HTTP_SERVICE_UNAVAILABLE(503, "HTTP_SERVICE_UNAVAILABLE", "服务不可用"),

    HTTP_GATEWAY_TIME_OUT(504, "HTTP_GATEWAY_TIME_OUT", "后端服务超时 或者 处理失败"),

    HTTP_NOT_EXTENDED(510, "HTTP_NOT_EXTENDED", "请求频率限制"),

    ERROR_IMAGE_EMPTY(-1300, "ERROR_IMAGE_EMPTY", "图片为空"),

    ERROR_PARAMETER_EMPTY(-1301, "ERROR_PARAMETER_EMPTY", "参数为空"),

    ERROR_DOWNLOAD_IMAGE_FAILED(-1308, "ERROR_DOWNLOAD_IMAGE_FAILED", "url图片下载失败"),

    ERROR_APPID_INVALID(-1320, "ERROR_APPID_INVALID", "Appid无效"),

    ERROR_IMAGE_DOWNLOAD_FAILED(-1403, "ERROR_IMAGE_DOWNLOAD_FAILED", "图片下载失败"),

    ERROR_DOWNLOAD_IMAGE_SIZE_EXCEED(-1408, "ERROR_DOWNLOAD_IMAGE_SIZE_EXCEED", "图片超出下载限制"),

    ERROR_INVALID_URL_FORMAT(-1505, "ERROR_IVADLID_URL_FORMAT", "图像请求URL的格式错误"),

    ERROR_CONNECT_DOWNLOAD_SERVER(-1507, "ERROR_CONNECT_DOWNLOAD_SERVER", "无法连接图像下载服务器"),

    ERROR_HOST_OVERLOAD_FORBIDDEN_ACCESS(-10001, "ERROR_HOST_OVERLOAD_FORBIDDEN_ACCESS", "过载保护"),

    ERROR_LICENSE_INVALID_FORBIDDEN_ACCESS(-10002, "ERROR_LICENSE_INVALID_FORBIDDEN_ACCESS", "license无效"),

    ERROR_FREQ_LIMIT_FORBIDDEN_ACCESS(-10003, "ERROR_FREQ_LIMIT_FORBIDDEN_ACCESS", "频率限制"),

    ERROR_SERVER_NAME_NOT_EXIST_IN_LICENSE(-10004, "ERROR_SERVER_NAME_NOT_EXIST_IN_LICENSE", "无效的服务名称"),

    ERROR_SERVER_NAME_UNAUTHORIZED_IN_LICENSE(-10005, "ERROR_SERVER_NAME_UNAUTHORIZED_IN_LICENSE", "license中未授权该服务"),

    OCR_SERVER_INTERN_ERROR(-5208, "OCR_SERVER_INTERN_ERROR", "服务器内部错误"),

    SDK_IMAGE_DECODE_FAILED(-1102, "SDK_IMAGE_DECODE_FAILED", "图片解码失败"),

    ERROR_OCR_TEMPLATE_NOT_MATCH(-1400, "ERROR_OCR_TEMPLATE_NOT_MATCH", "模板不匹配"),

    OCR_RECOGNIZE_FAILED(-9003, "GLOCR_RECOG_FAILED", "OCR识别失败");

    private final Integer code;

    private final String msg;

    private final String desc;


}
