package com.csci.susdev.constant;

/**
 * 流程控制状态
 *
 * <AUTHOR>
 */
public enum WorkflowControlState {
    /**
     * 未提交
     */
    NOT_STARTED(0, "未提交"),
    /**
     * 进行中
     */
    IN_PROGRESS(1, "审批中"),
    /**
     * 已完成
     */
    COMPLETED(2, "已完成"),
    /**
     * 已取消
     */
    CANCELED(3, "已取消"),
    /**
     * 已终止
     */
    TERMINATED(4, "已终止"),
    /**
     * 已撤回
     */
    RECALL(5, "已撤回"),
    ;

    private final int code;

    private final String name;

    WorkflowControlState(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static WorkflowControlState getWorkflowControlState(int code) {
        for (WorkflowControlState workflowControlState : WorkflowControlState.values()) {
            if (workflowControlState.getCode() == code) {
                return workflowControlState;
            }
        }
        return null;
    }
}
