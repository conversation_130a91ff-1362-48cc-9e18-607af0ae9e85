package com.csci.susdev.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashSet;
import java.util.Set;

@Getter
@AllArgsConstructor
public enum CabinCodeEnum {

    ECONOMY("经济舱", "YBMHKLQTXVNOGWSER"),  // 经济舱字符：Y,B,M,H,K,L,Q,T,X,V,N,O,G,W,S,E,R
    BUSINESS("商务舱", "CJDZI"),            // 商务舱字符：C,J,D,Z,I
    FIRST_CLASS("头等舱", "FAP");           // 头等舱字符：F,A,P

    private final String chineseName;
    private final Set<Character> codes;

    CabinCodeEnum(String chineseName, String codeStr) {
        this.chineseName = chineseName;
        this.codes = new HashSet<>();
        for (char c : codeStr.toCharArray()) {
            codes.add(c);
        }
    }

    // 根据字符解析舱位类型
    public static CabinCodeEnum fromCode(String code) {
        if (code == null || code.isEmpty()) {
            return null;
        }
        char target = code.toUpperCase().charAt(0);  // 不区分大小写
        for (CabinCodeEnum cabin : values()) {
            if (cabin.codes.contains(target)) {
                return cabin;
            }
        }
        return null;  // 未找到匹配项
    }

    // 直接获取中文名称
    public static String getChineseName(String code) {
        CabinCodeEnum cabin = fromCode(code);
        return cabin != null ? cabin.chineseName : "";
    }


}
