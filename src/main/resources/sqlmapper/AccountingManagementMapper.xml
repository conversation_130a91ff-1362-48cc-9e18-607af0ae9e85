<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.AccountingManagementMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.AccountingManagement">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 17 15:11:03 CST 2025.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="protocol_configuration_id" jdbcType="CHAR" property="protocolConfigurationId" />
    <result column="chinese_name" jdbcType="NVARCHAR" property="chineseName" />
    <result column="count_one" jdbcType="NUMERIC" property="countOne" />
    <result column="count_two" jdbcType="NUMERIC" property="countTwo" />
    <result column="compute_symbol" jdbcType="NVARCHAR" property="computeSymbol" />
    <result column="calculation_result" jdbcType="NUMERIC" property="calculationResult" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="CHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="CHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 17 15:11:03 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 17 15:11:03 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 17 15:11:03 CST 2025.
    -->
    id, protocol_configuration_id, chinese_name, count_one, count_two, compute_symbol, 
    calculation_result, creation_time, create_username, create_user_id, last_update_time, 
    last_update_username, last_update_user_id, last_update_version, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.AccountingManagementExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 17 15:11:03 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_accounting_management
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 17 15:11:03 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_accounting_management
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 17 15:11:03 CST 2025.
    -->
    delete from t_accounting_management
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.AccountingManagementExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 17 15:11:03 CST 2025.
    -->
    delete from t_accounting_management
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.AccountingManagement">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 17 15:11:03 CST 2025.
    -->
    insert into t_accounting_management (id, protocol_configuration_id, chinese_name, 
      count_one, count_two, compute_symbol, 
      calculation_result, creation_time, create_username, 
      create_user_id, last_update_time, last_update_username, 
      last_update_user_id, last_update_version, is_deleted
      )
    values (#{id,jdbcType=CHAR}, #{protocolConfigurationId,jdbcType=CHAR}, #{chineseName,jdbcType=NVARCHAR}, 
      #{countOne,jdbcType=NUMERIC}, #{countTwo,jdbcType=NUMERIC}, #{computeSymbol,jdbcType=NVARCHAR}, 
      #{calculationResult,jdbcType=NUMERIC}, #{creationTime,jdbcType=TIMESTAMP}, #{createUsername,jdbcType=NVARCHAR}, 
      #{createUserId,jdbcType=CHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUsername,jdbcType=NVARCHAR}, 
      #{lastUpdateUserId,jdbcType=CHAR}, #{lastUpdateVersion,jdbcType=INTEGER}, #{isDeleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.AccountingManagement">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 17 15:11:03 CST 2025.
    -->
    insert into t_accounting_management
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="protocolConfigurationId != null">
        protocol_configuration_id,
      </if>
      <if test="chineseName != null">
        chinese_name,
      </if>
      <if test="countOne != null">
        count_one,
      </if>
      <if test="countTwo != null">
        count_two,
      </if>
      <if test="computeSymbol != null">
        compute_symbol,
      </if>
      <if test="calculationResult != null">
        calculation_result,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="protocolConfigurationId != null">
        #{protocolConfigurationId,jdbcType=CHAR},
      </if>
      <if test="chineseName != null">
        #{chineseName,jdbcType=NVARCHAR},
      </if>
      <if test="countOne != null">
        #{countOne,jdbcType=NUMERIC},
      </if>
      <if test="countTwo != null">
        #{countTwo,jdbcType=NUMERIC},
      </if>
      <if test="computeSymbol != null">
        #{computeSymbol,jdbcType=NVARCHAR},
      </if>
      <if test="calculationResult != null">
        #{calculationResult,jdbcType=NUMERIC},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=CHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=CHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.AccountingManagementExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 17 15:11:03 CST 2025.
    -->
    select count(*) from t_accounting_management
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 17 15:11:03 CST 2025.
    -->
    update t_accounting_management
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.protocolConfigurationId != null">
        protocol_configuration_id = #{record.protocolConfigurationId,jdbcType=CHAR},
      </if>
      <if test="record.chineseName != null">
        chinese_name = #{record.chineseName,jdbcType=NVARCHAR},
      </if>
      <if test="record.countOne != null">
        count_one = #{record.countOne,jdbcType=NUMERIC},
      </if>
      <if test="record.countTwo != null">
        count_two = #{record.countTwo,jdbcType=NUMERIC},
      </if>
      <if test="record.computeSymbol != null">
        compute_symbol = #{record.computeSymbol,jdbcType=NVARCHAR},
      </if>
      <if test="record.calculationResult != null">
        calculation_result = #{record.calculationResult,jdbcType=NUMERIC},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=CHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=CHAR},
      </if>
      <if test="record.lastUpdateVersion != null">
        last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 17 15:11:03 CST 2025.
    -->
    update t_accounting_management
    set id = #{record.id,jdbcType=CHAR},
      protocol_configuration_id = #{record.protocolConfigurationId,jdbcType=CHAR},
      chinese_name = #{record.chineseName,jdbcType=NVARCHAR},
      count_one = #{record.countOne,jdbcType=NUMERIC},
      count_two = #{record.countTwo,jdbcType=NUMERIC},
      compute_symbol = #{record.computeSymbol,jdbcType=NVARCHAR},
      calculation_result = #{record.calculationResult,jdbcType=NUMERIC},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=NVARCHAR},
      create_user_id = #{record.createUserId,jdbcType=CHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=CHAR},
      last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.AccountingManagement">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 17 15:11:03 CST 2025.
    -->
    update t_accounting_management
    <set>
      <if test="protocolConfigurationId != null">
        protocol_configuration_id = #{protocolConfigurationId,jdbcType=CHAR},
      </if>
      <if test="chineseName != null">
        chinese_name = #{chineseName,jdbcType=NVARCHAR},
      </if>
      <if test="countOne != null">
        count_one = #{countOne,jdbcType=NUMERIC},
      </if>
      <if test="countTwo != null">
        count_two = #{countTwo,jdbcType=NUMERIC},
      </if>
      <if test="computeSymbol != null">
        compute_symbol = #{computeSymbol,jdbcType=NVARCHAR},
      </if>
      <if test="calculationResult != null">
        calculation_result = #{calculationResult,jdbcType=NUMERIC},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=CHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=CHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.AccountingManagement">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 17 15:11:03 CST 2025.
    -->
    update t_accounting_management
    set protocol_configuration_id = #{protocolConfigurationId,jdbcType=CHAR},
      chinese_name = #{chineseName,jdbcType=NVARCHAR},
      count_one = #{countOne,jdbcType=NUMERIC},
      count_two = #{countTwo,jdbcType=NUMERIC},
      compute_symbol = #{computeSymbol,jdbcType=NVARCHAR},
      calculation_result = #{calculationResult,jdbcType=NUMERIC},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=NVARCHAR},
      create_user_id = #{createUserId,jdbcType=CHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=CHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=BIT}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>