<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.TzhBsMenuMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.TzhBsMenu">
    <!--@mbg.generated-->
    <!--@Table Tzh_Bs_Menu-->
    <id column="id" jdbcType="OTHER" property="id" />
    <result column="page_name" jdbcType="VARCHAR" property="pageName" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="title_en" jdbcType="VARCHAR" property="titleEN" />
    <result column="seq" jdbcType="VARCHAR" property="seq" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="title_sc" jdbcType="VARCHAR" property="titleSC" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="module" jdbcType="INTEGER" property="module" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, page_name, path, name, component, title_en, seq, is_deleted, title_sc, title,
    remark, type,module
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Object" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from Tzh_Bs_Menu
    where id = #{id,jdbcType=OTHER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Object">
    <!--@mbg.generated-->
    delete from Tzh_Bs_Menu
    where id = #{id,jdbcType=OTHER}
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.TzhBsMenu">
    <!--@mbg.generated-->
    insert into Tzh_Bs_Menu (id, page_name, path,
    name, component, title_en,
    seq, is_deleted, title_sc,
    title, remark, type,module
    )
    values (#{id,jdbcType=OTHER}, #{pageName,jdbcType=VARCHAR}, #{path,jdbcType=VARCHAR},
    #{name,jdbcType=VARCHAR}, #{component,jdbcType=VARCHAR}, #{titleEN,jdbcType=VARCHAR},
    #{seq,jdbcType=VARCHAR}, #{isDeleted,jdbcType=BIT}, #{titleSC,jdbcType=VARCHAR},
    #{title,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{module,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.TzhBsMenu">
      <!--@mbg.generated-->
      insert into Tzh_Bs_Menu
      <trim prefix="(" suffix=")" suffixOverrides=",">
          <if test="id != null and id != ''">
              id,
          </if>
          <if test="pageName != null and pageName != ''">
              page_name,
          </if>
          <if test="path != null and path != ''">
              path,
          </if>
          <if test="name != null and name != ''">
              name,
          </if>
          <if test="component != null and component != ''">
              component,
          </if>
          <if test="titleEN != null and titleEN != ''">
              title_en,
          </if>
          <if test="seq != null and seq != ''">
              seq,
          </if>
          <if test="isDeleted != null">
              is_deleted,
          </if>
          <if test="titleSC != null and titleSC != ''">
              title_sc,
          </if>
          <if test="title != null and title != ''">
              title,
          </if>
          <if test="remark != null and remark != ''">
              remark,
          </if>
          <if test="type != null">
              [type],
          </if>
          <if test="module != null">
              module,
          </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
          <if test="id != null and id != ''">
              #{id,jdbcType=OTHER},
          </if>
          <if test="pageName != null and pageName != ''">
              #{pageName,jdbcType=VARCHAR},
          </if>
          <if test="path != null and path != ''">
              #{path,jdbcType=VARCHAR},
          </if>
          <if test="name != null and name != ''">
              #{name,jdbcType=VARCHAR},
          </if>
          <if test="component != null and component != ''">
              #{component,jdbcType=VARCHAR},
          </if>
          <if test="titleEN != null and titleEN != ''">
              #{titleEN,jdbcType=VARCHAR},
          </if>
          <if test="seq != null and seq != ''">
              #{seq,jdbcType=VARCHAR},
          </if>
          <if test="isDeleted != null">
              #{isDeleted,jdbcType=BIT},
          </if>
          <if test="titleSC != null and titleSC != ''">
              #{titleSC,jdbcType=VARCHAR},
          </if>
          <if test="title != null and title != ''">
              #{title,jdbcType=VARCHAR},
          </if>
          <if test="remark != null and remark != ''">
              #{remark,jdbcType=VARCHAR},
          </if>
          <if test="type != null">
              #{type,jdbcType=INTEGER},
          </if>
          <if test="module != null">
              #{module,jdbcType=VARCHAR},
          </if>
      </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.TzhBsMenu">
    <!--@mbg.generated-->
    update Tzh_Bs_Menu
    <set>
      <if test="pageName != null ">
        page_name = #{pageName,jdbcType=VARCHAR},
      </if>
      <if test="path != null ">
        path = #{path,jdbcType=VARCHAR},
      </if>
      <if test="name != null ">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="component != null ">
        component = #{component,jdbcType=VARCHAR},
      </if>
      <if test="titleEN != null ">
        title_en = #{titleEN,jdbcType=VARCHAR},
      </if>
      <if test="seq != null ">
        seq = #{seq,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="titleSC != null">
        title_sc = #{titleSC,jdbcType=VARCHAR},
      </if>
      <if test="title != null ">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="remark != null ">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
        <if test="module != null">
            module = #{module,jdbcType=VARCHAR},
        </if>
    </set>
    where id = #{id,jdbcType=OTHER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.TzhBsMenu">
    <!--@mbg.generated-->
    update Tzh_Bs_Menu
    set page_name = #{pageName,jdbcType=VARCHAR},
    path = #{path,jdbcType=VARCHAR},
    name = #{name,jdbcType=VARCHAR},
    component = #{component,jdbcType=VARCHAR},
    title_en = #{titleEN,jdbcType=VARCHAR},
    seq = #{seq,jdbcType=VARCHAR},
    is_deleted = #{isDeleted,jdbcType=BIT},
    title_sc = #{titleSC,jdbcType=VARCHAR},
    title = #{title,jdbcType=VARCHAR},
    remark = #{remark,jdbcType=VARCHAR},
    type = #{type,jdbcType=INTEGER},
    module = #{module,jdbcType=VARCHAR}
    where 1=1 <if test="id!=null"> and  id = #{id,jdbcType=OTHER}</if>
  </update>

  <select id="selectByTitleEn" resultType="int">
    select count(1) from Tzh_Bs_Menu where title_en = #{titleEn} and is_deleted = 0
  </select>


  <update id="logicallyDelete">
    update Tzh_Bs_Menu set is_deleted = 1 where id = #{id}
  </update>

  <select id="selectByNameLike" resultMap="BaseResultMap">
      select  <include refid="Base_Column_List" />
      from tzh_bs_menu
      where title like concat('%', #{title}, '%')
         or title_sc like concat('%', #{title}, '%')
         or title_en like concat('%', #{title}, '%')
      order by title_en
  </select>

  <select id="queryAll" resultMap="BaseResultMap" parameterType="com.csci.susdev.qo.TzhBsMenuQO">
    select <include refid="Base_Column_List" />
    from Tzh_Bs_Menu
    <where>
      <if test="pageName != null and pageName != ''">
        and (title like concat('%', #{pageName}, '%')
        or title_sc like concat('%', #{pageName}, '%')
        or title_en like concat('%', #{pageName}, '%'))
      </if>
      and is_deleted = 0
    </where>
    order by title_en
  </select>
</mapper>
