<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.FormDetailMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.FormDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 20:49:50 HKT 2024.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="form_code" jdbcType="NVARCHAR" property="formCode" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="type_a" jdbcType="NVARCHAR" property="typeA" />
    <result column="type_b" jdbcType="NVARCHAR" property="typeB" />
    <result column="type_c" jdbcType="NVARCHAR" property="typeC" />
    <result column="type_d" jdbcType="NVARCHAR" property="typeD" />
    <result column="type_e" jdbcType="NVARCHAR" property="typeE" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="NVARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="NVARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="month" jdbcType="INTEGER" property="month" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 20:49:50 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 20:49:50 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 20:49:50 HKT 2024.
    -->
    id, form_code, year, type_a, type_b, type_c, type_d, type_e, creation_time, create_username, 
    create_user_id, last_update_time, last_update_username, last_update_user_id, last_update_version, 
    is_deleted, seq, month
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.FormDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 20:49:50 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_form_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 20:49:50 HKT 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_form_detail
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 20:49:50 HKT 2024.
    -->
    delete from t_form_detail
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.FormDetailExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 20:49:50 HKT 2024.
    -->
    delete from t_form_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.FormDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 20:49:50 HKT 2024.
    -->
    insert into t_form_detail (id, form_code, year, 
      type_a, type_b, type_c, 
      type_d, type_e, creation_time, 
      create_username, create_user_id, last_update_time, 
      last_update_username, last_update_user_id, 
      last_update_version, is_deleted, seq,
      month)
    values (#{id,jdbcType=CHAR}, #{formCode,jdbcType=NVARCHAR}, #{year,jdbcType=INTEGER}, 
      #{typeA,jdbcType=NVARCHAR}, #{typeB,jdbcType=NVARCHAR}, #{typeC,jdbcType=NVARCHAR}, 
      #{typeD,jdbcType=NVARCHAR}, #{typeE,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createUsername,jdbcType=NVARCHAR}, #{createUserId,jdbcType=NVARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{lastUpdateUsername,jdbcType=NVARCHAR}, #{lastUpdateUserId,jdbcType=NVARCHAR}, 
      #{lastUpdateVersion,jdbcType=INTEGER}, #{isDeleted,jdbcType=BIT}, #{seq,jdbcType=INTEGER},
      #{month,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.FormDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 20:49:50 HKT 2024.
    -->
    insert into t_form_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="formCode != null">
        form_code,
      </if>
      <if test="year != null">
        year,
      </if>
      <if test="typeA != null">
        type_a,
      </if>
      <if test="typeB != null">
        type_b,
      </if>
      <if test="typeC != null">
        type_c,
      </if>
      <if test="typeD != null">
        type_d,
      </if>
      <if test="typeE != null">
        type_e,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="month != null">
        month,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="formCode != null">
        #{formCode,jdbcType=NVARCHAR},
      </if>
      <if test="year != null">
        #{year,jdbcType=INTEGER},
      </if>
      <if test="typeA != null">
        #{typeA,jdbcType=NVARCHAR},
      </if>
      <if test="typeB != null">
        #{typeB,jdbcType=NVARCHAR},
      </if>
      <if test="typeC != null">
        #{typeC,jdbcType=NVARCHAR},
      </if>
      <if test="typeD != null">
        #{typeD,jdbcType=NVARCHAR},
      </if>
      <if test="typeE != null">
        #{typeE,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="month != null">
        #{month,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.FormDetailExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 20:49:50 HKT 2024.
    -->
    select count(*) from t_form_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 20:49:50 HKT 2024.
    -->
    update t_form_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.formCode != null">
        form_code = #{record.formCode,jdbcType=NVARCHAR},
      </if>
      <if test="record.year != null">
        year = #{record.year,jdbcType=INTEGER},
      </if>
      <if test="record.typeA != null">
        type_a = #{record.typeA,jdbcType=NVARCHAR},
      </if>
      <if test="record.typeB != null">
        type_b = #{record.typeB,jdbcType=NVARCHAR},
      </if>
      <if test="record.typeC != null">
        type_c = #{record.typeC,jdbcType=NVARCHAR},
      </if>
      <if test="record.typeD != null">
        type_d = #{record.typeD,jdbcType=NVARCHAR},
      </if>
      <if test="record.typeE != null">
        type_e = #{record.typeE,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateVersion != null">
        last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.seq != null">
        seq = #{record.seq,jdbcType=INTEGER},
      </if>
      <if test="record.month != null">
        month = #{record.month,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 20:49:50 HKT 2024.
    -->
    update t_form_detail
    set id = #{record.id,jdbcType=CHAR},
      form_code = #{record.formCode,jdbcType=NVARCHAR},
      year = #{record.year,jdbcType=INTEGER},
      type_a = #{record.typeA,jdbcType=NVARCHAR},
      type_b = #{record.typeB,jdbcType=NVARCHAR},
      type_c = #{record.typeC,jdbcType=NVARCHAR},
      type_d = #{record.typeD,jdbcType=NVARCHAR},
      type_e = #{record.typeE,jdbcType=NVARCHAR},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=NVARCHAR},
      create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      seq = #{record.seq,jdbcType=INTEGER},
      month = #{record.month,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.FormDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 20:49:50 HKT 2024.
    -->
    update t_form_detail
    <set>
      <if test="formCode != null">
        form_code = #{formCode,jdbcType=NVARCHAR},
      </if>
      <if test="year != null">
        year = #{year,jdbcType=INTEGER},
      </if>
      <if test="typeA != null">
        type_a = #{typeA,jdbcType=NVARCHAR},
      </if>
      <if test="typeB != null">
        type_b = #{typeB,jdbcType=NVARCHAR},
      </if>
      <if test="typeC != null">
        type_c = #{typeC,jdbcType=NVARCHAR},
      </if>
      <if test="typeD != null">
        type_d = #{typeD,jdbcType=NVARCHAR},
      </if>
      <if test="typeE != null">
        type_e = #{typeE,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="seq != null">
        seq = #{seq,jdbcType=INTEGER},
      </if>
      <if test="month != null">
        month = #{month,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.FormDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Apr 11 20:49:50 HKT 2024.
    -->
    update t_form_detail
    set form_code = #{formCode,jdbcType=NVARCHAR},
      year = #{year,jdbcType=INTEGER},
      type_a = #{typeA,jdbcType=NVARCHAR},
      type_b = #{typeB,jdbcType=NVARCHAR},
      type_c = #{typeC,jdbcType=NVARCHAR},
      type_d = #{typeD,jdbcType=NVARCHAR},
      type_e = #{typeE,jdbcType=NVARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=NVARCHAR},
      create_user_id = #{createUserId,jdbcType=NVARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=BIT},
      seq = #{seq,jdbcType=INTEGER},
      month = #{month,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>