<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.ApiLogMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.ApiLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 16 15:40:18 HKT 2024.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="username" jdbcType="NVARCHAR" property="username" />
    <result column="token" jdbcType="NVARCHAR" property="token" />
    <result column="app_id" jdbcType="NVARCHAR" property="appId" />
    <result column="app_key" jdbcType="NVARCHAR" property="appKey" />
    <result column="page" jdbcType="NVARCHAR" property="page" />
    <result column="method" jdbcType="NVARCHAR" property="method" />
    <result column="api" jdbcType="NVARCHAR" property="api" />
    <result column="ip" jdbcType="NVARCHAR" property="ip" />
    <result column="mac" jdbcType="NVARCHAR" property="mac" />
    <result column="code" jdbcType="NVARCHAR" property="code" />
    <result column="parameter" jdbcType="NVARCHAR" property="parameter" />
    <result column="request" jdbcType="NVARCHAR" property="request" />
    <result column="response" jdbcType="NVARCHAR" property="response" />
    <result column="type" jdbcType="NVARCHAR" property="type" />
    <result column="start_time" jdbcType="NVARCHAR" property="startTime" />
    <result column="end_time" jdbcType="NVARCHAR" property="endTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 16 15:40:18 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 16 15:40:18 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 16 15:40:18 HKT 2024.
    -->
    id, username, token, app_id, app_key, page, method, api, ip, mac, code, parameter, 
    request, response, type, start_time, end_time
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.ApiLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 16 15:40:18 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_api_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 16 15:40:18 HKT 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_api_log
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 16 15:40:18 HKT 2024.
    -->
    delete from t_api_log
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.ApiLogExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 16 15:40:18 HKT 2024.
    -->
    delete from t_api_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.ApiLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 16 15:40:18 HKT 2024.
    -->
    insert into t_api_log (id, username, token, 
      app_id, app_key, page, 
      method, api, ip, 
      mac, code, parameter, 
      request, response, type, 
      start_time, end_time)
    values (#{id,jdbcType=CHAR}, #{username,jdbcType=NVARCHAR}, #{token,jdbcType=NVARCHAR}, 
      #{appId,jdbcType=NVARCHAR}, #{appKey,jdbcType=NVARCHAR}, #{page,jdbcType=NVARCHAR}, 
      #{method,jdbcType=NVARCHAR}, #{api,jdbcType=NVARCHAR}, #{ip,jdbcType=NVARCHAR}, 
      #{mac,jdbcType=NVARCHAR}, #{code,jdbcType=NVARCHAR}, #{parameter,jdbcType=NVARCHAR}, 
      #{request,jdbcType=NVARCHAR}, #{response,jdbcType=NVARCHAR}, #{type,jdbcType=NVARCHAR}, 
      #{startTime,jdbcType=NVARCHAR}, #{endTime,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.ApiLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 16 15:40:18 HKT 2024.
    -->
    insert into t_api_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="token != null">
        token,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="appKey != null">
        app_key,
      </if>
      <if test="page != null">
        page,
      </if>
      <if test="method != null">
        method,
      </if>
      <if test="api != null">
        api,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="mac != null">
        mac,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="parameter != null">
        parameter,
      </if>
      <if test="request != null">
        request,
      </if>
      <if test="response != null">
        response,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="username != null">
        #{username,jdbcType=NVARCHAR},
      </if>
      <if test="token != null">
        #{token,jdbcType=NVARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=NVARCHAR},
      </if>
      <if test="appKey != null">
        #{appKey,jdbcType=NVARCHAR},
      </if>
      <if test="page != null">
        #{page,jdbcType=NVARCHAR},
      </if>
      <if test="method != null">
        #{method,jdbcType=NVARCHAR},
      </if>
      <if test="api != null">
        #{api,jdbcType=NVARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=NVARCHAR},
      </if>
      <if test="mac != null">
        #{mac,jdbcType=NVARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=NVARCHAR},
      </if>
      <if test="parameter != null">
        #{parameter,jdbcType=NVARCHAR},
      </if>
      <if test="request != null">
        #{request,jdbcType=NVARCHAR},
      </if>
      <if test="response != null">
        #{response,jdbcType=NVARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=NVARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=NVARCHAR},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.ApiLogExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 16 15:40:18 HKT 2024.
    -->
    select count(*) from t_api_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 16 15:40:18 HKT 2024.
    -->
    update t_api_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.username != null">
        username = #{record.username,jdbcType=NVARCHAR},
      </if>
      <if test="record.token != null">
        token = #{record.token,jdbcType=NVARCHAR},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=NVARCHAR},
      </if>
      <if test="record.appKey != null">
        app_key = #{record.appKey,jdbcType=NVARCHAR},
      </if>
      <if test="record.page != null">
        page = #{record.page,jdbcType=NVARCHAR},
      </if>
      <if test="record.method != null">
        method = #{record.method,jdbcType=NVARCHAR},
      </if>
      <if test="record.api != null">
        api = #{record.api,jdbcType=NVARCHAR},
      </if>
      <if test="record.ip != null">
        ip = #{record.ip,jdbcType=NVARCHAR},
      </if>
      <if test="record.mac != null">
        mac = #{record.mac,jdbcType=NVARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=NVARCHAR},
      </if>
      <if test="record.parameter != null">
        parameter = #{record.parameter,jdbcType=NVARCHAR},
      </if>
      <if test="record.request != null">
        request = #{record.request,jdbcType=NVARCHAR},
      </if>
      <if test="record.response != null">
        response = #{record.response,jdbcType=NVARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=NVARCHAR},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=NVARCHAR},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 16 15:40:18 HKT 2024.
    -->
    update t_api_log
    set id = #{record.id,jdbcType=CHAR},
      username = #{record.username,jdbcType=NVARCHAR},
      token = #{record.token,jdbcType=NVARCHAR},
      app_id = #{record.appId,jdbcType=NVARCHAR},
      app_key = #{record.appKey,jdbcType=NVARCHAR},
      page = #{record.page,jdbcType=NVARCHAR},
      method = #{record.method,jdbcType=NVARCHAR},
      api = #{record.api,jdbcType=NVARCHAR},
      ip = #{record.ip,jdbcType=NVARCHAR},
      mac = #{record.mac,jdbcType=NVARCHAR},
      code = #{record.code,jdbcType=NVARCHAR},
      parameter = #{record.parameter,jdbcType=NVARCHAR},
      request = #{record.request,jdbcType=NVARCHAR},
      response = #{record.response,jdbcType=NVARCHAR},
      type = #{record.type,jdbcType=NVARCHAR},
      start_time = #{record.startTime,jdbcType=NVARCHAR},
      end_time = #{record.endTime,jdbcType=NVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.ApiLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 16 15:40:18 HKT 2024.
    -->
    update t_api_log
    <set>
      <if test="username != null">
        username = #{username,jdbcType=NVARCHAR},
      </if>
      <if test="token != null">
        token = #{token,jdbcType=NVARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=NVARCHAR},
      </if>
      <if test="appKey != null">
        app_key = #{appKey,jdbcType=NVARCHAR},
      </if>
      <if test="page != null">
        page = #{page,jdbcType=NVARCHAR},
      </if>
      <if test="method != null">
        method = #{method,jdbcType=NVARCHAR},
      </if>
      <if test="api != null">
        api = #{api,jdbcType=NVARCHAR},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=NVARCHAR},
      </if>
      <if test="mac != null">
        mac = #{mac,jdbcType=NVARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=NVARCHAR},
      </if>
      <if test="parameter != null">
        parameter = #{parameter,jdbcType=NVARCHAR},
      </if>
      <if test="request != null">
        request = #{request,jdbcType=NVARCHAR},
      </if>
      <if test="response != null">
        response = #{response,jdbcType=NVARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=NVARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=NVARCHAR},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=NVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.ApiLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 16 15:40:18 HKT 2024.
    -->
    update t_api_log
    set username = #{username,jdbcType=NVARCHAR},
      token = #{token,jdbcType=NVARCHAR},
      app_id = #{appId,jdbcType=NVARCHAR},
      app_key = #{appKey,jdbcType=NVARCHAR},
      page = #{page,jdbcType=NVARCHAR},
      method = #{method,jdbcType=NVARCHAR},
      api = #{api,jdbcType=NVARCHAR},
      ip = #{ip,jdbcType=NVARCHAR},
      mac = #{mac,jdbcType=NVARCHAR},
      code = #{code,jdbcType=NVARCHAR},
      parameter = #{parameter,jdbcType=NVARCHAR},
      request = #{request,jdbcType=NVARCHAR},
      response = #{response,jdbcType=NVARCHAR},
      type = #{type,jdbcType=NVARCHAR},
      start_time = #{startTime,jdbcType=NVARCHAR},
      end_time = #{endTime,jdbcType=NVARCHAR}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>