<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.AttachmentMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.Attachment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="ref_id" jdbcType="VARCHAR" property="refId" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="section" jdbcType="VARCHAR" property="section" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="VARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="VARCHAR" property="lastUpdateUserId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.csci.susdev.model.Attachment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    <result column="data" jdbcType="LONGVARBINARY" property="data" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    id, ref_id, category, section, type, name, creation_time, create_username, create_user_id, 
    last_update_time, last_update_username, last_update_user_id
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    data
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.csci.susdev.model.AttachmentExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from t_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.csci.susdev.model.AttachmentExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from t_attachment
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    delete from t_attachment
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.AttachmentExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    delete from t_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.Attachment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    insert into t_attachment (id, ref_id, category, 
      section, type, name, 
      creation_time, create_username, create_user_id, 
      last_update_time, last_update_username, last_update_user_id, 
      data)
    values (#{id,jdbcType=VARCHAR}, #{refId,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, 
      #{section,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{createUsername,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, 
      #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUsername,jdbcType=VARCHAR}, #{lastUpdateUserId,jdbcType=VARCHAR}, 
      #{data,jdbcType=LONGVARBINARY})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.Attachment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    insert into t_attachment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="refId != null">
        ref_id,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="section != null">
        section,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="data != null">
        data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="refId != null">
        #{refId,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="section != null">
        #{section,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="data != null">
        #{data,jdbcType=LONGVARBINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.AttachmentExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    select count(*) from t_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    update t_attachment
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=VARCHAR},
      </if>
      <if test="row.refId != null">
        ref_id = #{row.refId,jdbcType=VARCHAR},
      </if>
      <if test="row.category != null">
        category = #{row.category,jdbcType=VARCHAR},
      </if>
      <if test="row.section != null">
        section = #{row.section,jdbcType=VARCHAR},
      </if>
      <if test="row.type != null">
        type = #{row.type,jdbcType=VARCHAR},
      </if>
      <if test="row.name != null">
        name = #{row.name,jdbcType=VARCHAR},
      </if>
      <if test="row.creationTime != null">
        creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createUsername != null">
        create_username = #{row.createUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.createUserId != null">
        create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateTime != null">
        last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.lastUpdateUsername != null">
        last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateUserId != null">
        last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="row.data != null">
        data = #{row.data,jdbcType=LONGVARBINARY},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    update t_attachment
    set id = #{row.id,jdbcType=VARCHAR},
      ref_id = #{row.refId,jdbcType=VARCHAR},
      category = #{row.category,jdbcType=VARCHAR},
      section = #{row.section,jdbcType=VARCHAR},
      type = #{row.type,jdbcType=VARCHAR},
      name = #{row.name,jdbcType=VARCHAR},
      creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      create_username = #{row.createUsername,jdbcType=VARCHAR},
      create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR},
      data = #{row.data,jdbcType=LONGVARBINARY}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    update t_attachment
    set id = #{row.id,jdbcType=VARCHAR},
      ref_id = #{row.refId,jdbcType=VARCHAR},
      category = #{row.category,jdbcType=VARCHAR},
      section = #{row.section,jdbcType=VARCHAR},
      type = #{row.type,jdbcType=VARCHAR},
      name = #{row.name,jdbcType=VARCHAR},
      creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      create_username = #{row.createUsername,jdbcType=VARCHAR},
      create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.Attachment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    update t_attachment
    <set>
      <if test="refId != null">
        ref_id = #{refId,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="section != null">
        section = #{section,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="data != null">
        data = #{data,jdbcType=LONGVARBINARY},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.csci.susdev.model.Attachment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    update t_attachment
    set ref_id = #{refId,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      section = #{section,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      data = #{data,jdbcType=LONGVARBINARY}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.Attachment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 26 15:03:47 HKT 2023.
    -->
    update t_attachment
    set ref_id = #{refId,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      section = #{section,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>