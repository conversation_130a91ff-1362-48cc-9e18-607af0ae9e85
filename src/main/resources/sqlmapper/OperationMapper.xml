<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.OperationMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.Operation">
    <!--@mbg.generated-->
    <!--@Table t_operation-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="code" jdbcType="TINYINT" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="method" jdbcType="VARCHAR" property="method" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="VARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="VARCHAR" property="lastUpdateUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    t.id, t.code, t.name, t.description, t.url, t.method, t.creation_time, t.create_username, t.create_user_id, t.last_update_time, t.last_update_username, t.last_update_user_id
  </sql>

  <select id="getOperationByPermissionList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_operation t  join t_permission_operation on t.id = t_permission_operation.operation_id
    where t_permission_operation.permission_id in
    <foreach collection="permissionIds" item="permission" open="(" close=")" separator=",">
      #{permission}
    </foreach>
  </select>
</mapper>
