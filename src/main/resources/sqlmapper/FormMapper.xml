<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.FormMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.Form">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 12 15:51:26 HKT 2024.
    -->
    <id column="id" jdbcType="NVARCHAR" property="id" />
    <result column="code" jdbcType="NVARCHAR" property="code" />
    <result column="name" jdbcType="NVARCHAR" property="name" />
    <result column="description" jdbcType="NVARCHAR" property="description" />
    <result column="is_factor_related" jdbcType="BIT" property="isFactorRelated" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="NVARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="NVARCHAR" property="lastUpdateUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 12 15:51:26 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 12 15:51:26 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 12 15:51:26 HKT 2024.
    -->
    id, code, name, description, is_factor_related, creation_time, create_username, create_user_id, 
    last_update_time, last_update_username, last_update_user_id
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.FormExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 12 15:51:26 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_form
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 12 15:51:26 HKT 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_form
    where id = #{id,jdbcType=NVARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 12 15:51:26 HKT 2024.
    -->
    delete from t_form
    where id = #{id,jdbcType=NVARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.FormExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 12 15:51:26 HKT 2024.
    -->
    delete from t_form
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.Form">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 12 15:51:26 HKT 2024.
    -->
    insert into t_form (id, code, name, 
      description, is_factor_related, creation_time, 
      create_username, create_user_id, last_update_time, 
      last_update_username, last_update_user_id)
    values (#{id,jdbcType=NVARCHAR}, #{code,jdbcType=NVARCHAR}, #{name,jdbcType=NVARCHAR}, 
      #{description,jdbcType=NVARCHAR}, #{isFactorRelated,jdbcType=BIT}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createUsername,jdbcType=NVARCHAR}, #{createUserId,jdbcType=NVARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{lastUpdateUsername,jdbcType=NVARCHAR}, #{lastUpdateUserId,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.Form">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 12 15:51:26 HKT 2024.
    -->
    insert into t_form
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="isFactorRelated != null">
        is_factor_related,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=NVARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=NVARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=NVARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=NVARCHAR},
      </if>
      <if test="isFactorRelated != null">
        #{isFactorRelated,jdbcType=BIT},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.FormExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 12 15:51:26 HKT 2024.
    -->
    select count(*) from t_form
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 12 15:51:26 HKT 2024.
    -->
    update t_form
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=NVARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=NVARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=NVARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=NVARCHAR},
      </if>
      <if test="record.isFactorRelated != null">
        is_factor_related = #{record.isFactorRelated,jdbcType=BIT},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 12 15:51:26 HKT 2024.
    -->
    update t_form
    set id = #{record.id,jdbcType=NVARCHAR},
      code = #{record.code,jdbcType=NVARCHAR},
      name = #{record.name,jdbcType=NVARCHAR},
      description = #{record.description,jdbcType=NVARCHAR},
      is_factor_related = #{record.isFactorRelated,jdbcType=BIT},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=NVARCHAR},
      create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.Form">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 12 15:51:26 HKT 2024.
    -->
    update t_form
    <set>
      <if test="code != null">
        code = #{code,jdbcType=NVARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=NVARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=NVARCHAR},
      </if>
      <if test="isFactorRelated != null">
        is_factor_related = #{isFactorRelated,jdbcType=BIT},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=NVARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.Form">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 12 15:51:26 HKT 2024.
    -->
    update t_form
    set code = #{code,jdbcType=NVARCHAR},
      name = #{name,jdbcType=NVARCHAR},
      description = #{description,jdbcType=NVARCHAR},
      is_factor_related = #{isFactorRelated,jdbcType=BIT},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=NVARCHAR},
      create_user_id = #{createUserId,jdbcType=NVARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR}
    where id = #{id,jdbcType=NVARCHAR}
  </update>
</mapper>