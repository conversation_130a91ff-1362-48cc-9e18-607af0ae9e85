<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.csci.susdev.mapper.WorkflowCustomMapper">
    <select id="selectWorkflowNodeByWorkflowId" resultType="com.csci.susdev.vo.WorkflowNodeVO">
        select
                n.id               as id,
                w.id               as workflowId,
                n.name             as name,
                n.previous_node_id as previousNodeId,
                n.is_begin_node    as isBeginNode,
                n.last_update_time as lastUpdateTIme,
                u.id as userId,
                u.name as userRealName,
                u.mobile as userMobile,
                u.username as userName
        from t_workflow w
        left join t_workflow_node n on w.id = n.workflow_id
        left join t_workflow_node_user nu on n.id = nu.node_id
        left join t_user u on nu.user_id = u.id
        where w.id = #{workflowId}
        <!--and n.is_begin_node = 0-->
        and w.is_active = 1
    </select>

    <select id="selectUnSubmitWorkflowExportData" resultType="com.csci.susdev.vo.UnSubmitWorkflowExportData" parameterType="com.csci.susdev.qo.WorkflowControlQO">
        select null as id, wf.id as workflowId, null as businessId, o.id as organizationId, o.name as organizationName,
        (select name from t_organization where id = o.parent_id) as parentName,
        wf.year as year,
        f.id as formId, f.name as formName, wf.representative_id as currentNodeId, u.name as currentNodeName, 0 as state,
        year(getdate()) as year, month(getdate()) as month, isnull(ah.last_update_version, 0) as lastUpdateVersion, u.id as createUserId, u.name as createUserRealName, u.mobile as createUserMobile, u.username as createUserName,
        0 as isUrgentEditable,
        (select name as parentOrgName from t_organization where id = o.parent_id) as parentOrgName,
        wf.representative_id as representativeId, ru.name as representativeRealName, ru.mobile as representativeMobile, ru.username as representativeName
        from t_workflow  wf
        inner join t_organization o on o.id = wf.organization_id
        inner join t_form f ON wf.form_id = f.id
        left join t_user u on wf.representative_id = u.id
        left join t_user ru on wf.representative_id  = ru.id
        left join (
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'ambient' as form_code  FROM t_ambient_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'sociology-index-two' as form_code FROM t_social_perf_two_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'sociology-index-three' as form_code  FROM t_social_perf_three_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'sociology-index-one' as form_code  FROM t_social_performance_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'ff-cm-fixed' as form_code  FROM t_ff_cm_fixed_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'ff-cm-mobile' as form_code  FROM t_ff_cm_mobile_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'ce-identification' as form_code  FROM t_ce_identification_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'emission-reduction' as form_code  FROM t_emission_reduction_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'ce-basic-info' as form_code  FROM t_ce_basic_info_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'emp-commuting' as form_code  FROM t_emp_commuting_head
        ) ah on ah.form_code = f.code and ah.organization_id = wf.organization_id and ah.year = wf.year and ah.month = wf.month and ah.is_active = 1
        where wf.is_active = 1
        <if test="organizationId != null">
            and o.no like (select concat(_o.no, '%') from t_organization _o where _o.id = #{organizationId} )
        </if>
        <if test='organizationId == null or organizationId == "" '>
            and exists(select 1 from t_user_organization uo where uo.user_id = #{userId} and uo.organization_id = o.id)
        </if>
        <if test="formId != null">
            and f.id = #{formId}
        </if>
        <if test="year != null">
            and #{year} = wf.year
        </if>
        <if test="month != null">
            and #{month} = wf.month
        </if>
        and wf.id not in (
        select distinct _c.workflow_id
        from t_workflow_control _c
        left join (
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ambient_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_social_perf_two_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_social_perf_three_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_social_performance_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ff_cm_fixed_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ff_cm_mobile_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ce_identification_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_emission_reduction_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ce_basic_info_head
        UNION
        SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_emp_commuting_head
        ) _ah on _c.business_id  = _ah.id
        left join t_organization _o on _o.id = _ah.organization_id
        where _c.is_active = 1
        <if test="organizationId != null">
            and _o.no like (select  TOP 1 concat(__o.no, '%') from t_organization __o where __o.id = #{organizationId})
        </if>
        <if test="year != null">
            and _ah.year = #{year}
        </if>
        <if test="month != null">
            and _ah.month = #{month}
        </if>
        )
        order by o.no, f.name
    </select>

    <select id="selectWorkflowControlExact" resultType="com.csci.susdev.vo.WorkflowControlVO" parameterType="com.csci.susdev.qo.WorkflowControlQO">
        select * from (
            select c.id as id, c.workflow_id as workflowId, ah.id as businessId, o.id as organizationId, o.name as
            organizationName, o.no as organizationNo,
            f.id as formId, f.name as formName, c.current_node_id as currentNodeId, n.name as currentNodeName, isnull(c.state, 0) as state,
            ah.year, ah.month, isnull(ah.last_update_version, 0) as lastUpdateVersion, u.id as createUserId, u.name as createUserRealName, u.mobile as createUserMobile, u.username as createUserName,
            COALESCE(c.last_update_time,w.last_update_time) AS lastUpdateTime, COALESCE(c.creation_time, w.creation_time) AS creationTime,
            c.is_urgent_editable as isUrgentEditable,
            (select name as parentOrgName from t_organization where id = o.parent_id) as parentOrgName,
            w.representative_id as representativeId, ru.name as representativeRealName, ru.mobile as representativeMobile, ru.username as representativeName
            from (
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ambient_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_social_perf_two_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_social_perf_three_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_social_performance_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ff_cm_fixed_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ff_cm_mobile_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ce_identification_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_emission_reduction_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ce_basic_info_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_emp_commuting_head
            ) ah
            left join (select * from t_workflow_control _c where _c.is_active = 1) c on ah.id = c.business_id
            left join t_workflow w on w.id = c.workflow_id and w.is_active = 1
            inner join t_organization o on o.id = ah.organization_id and o.is_deleted = 0
            inner join t_form f ON w.form_id = f.id
            left join t_workflow_node n on n.id = c.current_node_id
            left join t_user u on c.create_user_id = u.id
            left join t_user ru on w.representative_id  = ru.id
            where ah.is_active = 1
            <if test="organizationId != null">
                and o.no like (select TOP 1 _o.no from t_organization _o where _o.id = #{organizationId})
            </if>
            <if test='organizationId == null or organizationId == "" '>
                and exists(select 1 from t_user_organization uo where uo.user_id = #{userId} and uo.organization_id = o.id)
            </if>
            <if test="formId != null">
                and f.id = #{formId}
            </if>
            <if test="year != null">
                and ah.year = #{year}
            </if>
            <if test="month != null">
                and ah.month = #{month}
            </if>

            union

            select null as id, wf.id as workflowId, null as businessId, o.id as organizationId, o.name as organizationName, o.no as organizationNo,
            f.id as formId, f.name as formName, wf.representative_id as currentNodeId, u.name as currentNodeName, 0 as state,
            wf.year as year, wf.month as month, isnull(ah.last_update_version, 0) as lastUpdateVersion, u.id as createUserId, u.name as createUserRealName, u.mobile as createUserMobile, u.username as createUserName,
            wf.last_update_time AS  lastUpdateTime,wf.creation_time AS  creationTime,
            0 as isUrgentEditable,
            (select name as parentOrgName from t_organization where id = o.parent_id) as parentOrgName,
            wf.representative_id as representativeId, ru.name as representativeRealName, ru.mobile as representativeMobile, ru.username as representativeName
            from t_workflow  wf
            inner join t_organization o on o.id = wf.organization_id and o.is_deleted = 0
            inner join t_form f ON wf.form_id = f.id
            left join t_user u on wf.representative_id = u.id
            left join t_user ru on wf.representative_id  = ru.id
            left join (
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'ambient' as form_code  FROM t_ambient_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'sociology-index-two' as form_code FROM t_social_perf_two_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'sociology-index-three' as form_code  FROM t_social_perf_three_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'sociology-index-one' as form_code  FROM t_social_performance_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'ff-cm-fixed' as form_code  FROM t_ff_cm_fixed_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'ff-cm-mobile' as form_code  FROM t_ff_cm_mobile_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'ce-identification' as form_code  FROM t_ce_identification_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'emission-reduction' as form_code  FROM t_emission_reduction_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'ce-basic-info' as form_code  FROM t_ce_basic_info_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'emp-commuting' as form_code  FROM t_emp_commuting_head
            ) ah on ah.form_code = f.code and ah.organization_id = wf.organization_id and ah.year = wf.year and ah.month = wf.month and ah.is_active = 1
            where wf.is_active = 1
            <if test="organizationId != null">
                and o.no like (select TOP 1 _o.no from t_organization _o where _o.id = #{organizationId})
            </if>
            <if test='organizationId == null or organizationId == "" '>
                and exists(select 1 from t_user_organization uo where uo.user_id = #{userId} and uo.organization_id = o.id)
            </if>
            <if test="formId != null">
                and f.id = #{formId}
            </if>
            <if test="year != null">
                and #{year} = wf.year
            </if>
            <if test="month != null">
                and #{month} = wf.month
            </if>
            and wf.id not in (
            select distinct _c.workflow_id
            from t_workflow_control _c
            left join (
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ambient_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_social_perf_two_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_social_perf_three_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_social_performance_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ff_cm_fixed_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ff_cm_mobile_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ce_identification_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_emission_reduction_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ce_basic_info_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_emp_commuting_head
            ) _ah on _c.business_id  = _ah.id
            left join t_organization _o on _o.id = _ah.organization_id and _o.is_deleted = 0
            where _c.is_active = 1
            <if test="organizationId != null">
                and _o.no like (select TOP 1 __o.no from t_organization __o where __o.id = #{organizationId})
            </if>
            <if test="year != null">
                and _ah.year = #{year}
            </if>
            <if test="month != null">
                and _ah.month = #{month}
            </if>
            )
        ) t
        order by t.organizationNo, t.formName
    </select>

    <select id="selectWorkflowControl" resultType="com.csci.susdev.vo.WorkflowControlVO" parameterType="com.csci.susdev.qo.WorkflowControlQO">
        select * from (
            select c.id as id, c.workflow_id as workflowId, c.last_update_time as lastUpdateTime, c.creation_time creationTime, ah.id as businessId, o.id as organizationId, o.name as
            organizationName, o.no as organizationNo,
            f.id as formId, f.name as formName, c.current_node_id as currentNodeId, n.name as currentNodeName, isnull(c.state, 0) as state,
            ah.year, ah.month, isnull(ah.last_update_version, 0) as lastUpdateVersion, u.id as createUserId, u.name as createUserRealName, u.mobile as createUserMobile, u.username as createUserName,
            c.is_urgent_editable as isUrgentEditable,
            (select name as parentOrgName from t_organization where id = o.parent_id) as parentOrgName,
            w.representative_id as representativeId, ru.name as representativeRealName, ru.mobile as representativeMobile, ru.username as representativeName
            from (
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ambient_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_social_perf_two_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_social_perf_three_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_social_performance_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ff_cm_fixed_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ff_cm_mobile_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ce_identification_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_emission_reduction_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ce_basic_info_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_emp_commuting_head
            ) ah
            left join (select * from t_workflow_control _c where _c.is_active = 1) c on ah.id = c.business_id
            left join t_workflow w on w.id = c.workflow_id and w.is_active = 1
            inner join t_organization o on o.id = ah.organization_id and o.is_deleted = 0
            inner join t_form f ON w.form_id = f.id
            left join t_workflow_node n on n.id = c.current_node_id
            left join t_user u on c.create_user_id = u.id
            left join t_user ru on w.representative_id  = ru.id
            where ah.is_active = 1
            <if test="organizationId != null">
                and o.no like (select TOP 1 concat(_o.no, '%') from t_organization _o where _o.id = #{organizationId})
            </if>
            <if test='organizationId == null or organizationId == "" '>
                and exists(select 1 from t_user_organization uo where uo.user_id = #{userId} and uo.organization_id = o.id)
            </if>
            <if test="formId != null">
                and f.id = #{formId}
            </if>
            <if test="year != null">
                and ah.year = #{year}
            </if>
            <if test="month != null">
                and ah.month = #{month}
            </if>

            union

            select null as id, wf.id as workflowId, wf.last_update_time as LastUpdateTime,  wf.creation_time creationTime,null as businessId, o.id as organizationId, o.name as organizationName, o.no as organizationNo,
            f.id as formId, f.name as formName, wf.representative_id as currentNodeId, u.name as currentNodeName, 0 as state,
            wf.year as year, wf.month as month, isnull(ah.last_update_version, 0) as lastUpdateVersion, u.id as createUserId, u.name as createUserRealName, u.mobile as createUserMobile, u.username as createUserName,
            0 as isUrgentEditable,
            (select name as parentOrgName from t_organization where id = o.parent_id) as parentOrgName,
            wf.representative_id as representativeId, ru.name as representativeRealName, ru.mobile as representativeMobile, ru.username as representativeName
            from t_workflow  wf
            inner join t_organization o on o.id = wf.organization_id and o.is_deleted = 0
            inner join t_form f ON wf.form_id = f.id
            left join t_user u on wf.representative_id = u.id
            left join t_user ru on wf.representative_id  = ru.id
            left join (
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'ambient' as form_code  FROM t_ambient_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'sociology-index-two' as form_code FROM t_social_perf_two_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'sociology-index-three' as form_code  FROM t_social_perf_three_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'sociology-index-one' as form_code  FROM t_social_performance_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'ff-cm-fixed' as form_code  FROM t_ff_cm_fixed_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'ff-cm-mobile' as form_code  FROM t_ff_cm_mobile_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'ce-identification' as form_code  FROM t_ce_identification_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'emission-reduction' as form_code  FROM t_emission_reduction_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'ce-basic-info' as form_code  FROM t_ce_basic_info_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id, YEAR, MONTH, is_active, last_update_version, 'emp-commuting' as form_code  FROM t_emp_commuting_head
            ) ah on ah.form_code = f.code and ah.organization_id = wf.organization_id and ah.year = wf.year and ah.month = wf.month and ah.is_active = 1
            where wf.is_active = 1
            <if test="organizationId != null">
                and o.no like (select TOP 1 concat(_o.no, '%') from t_organization _o where _o.id = #{organizationId})
            </if>
            <if test='organizationId == null or organizationId == "" '>
                and exists(select 1 from t_user_organization uo where uo.user_id = #{userId} and uo.organization_id = o.id)
            </if>
            <if test="formId != null">
                and f.id = #{formId}
            </if>
            <if test="year != null">
                and #{year} = wf.year
            </if>
            <if test="month != null">
                and #{month} = wf.month
            </if>
            and wf.id not in (
            select distinct _c.workflow_id
            from t_workflow_control _c
            left join (
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ambient_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_social_perf_two_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_social_perf_three_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_social_performance_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ff_cm_fixed_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ff_cm_mobile_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ce_identification_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_emission_reduction_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_ce_basic_info_head
            UNION
            SELECT cast(id as varchar(100)) as id, cast(organization_id as varchar(100)) as organization_id,YEAR, MONTH, is_active, last_update_version  FROM t_emp_commuting_head
            ) _ah on _c.business_id  = _ah.id
            left join t_organization _o on _o.id = _ah.organization_id and _o.is_deleted = 0
            where _c.is_active = 1
            <if test="organizationId != null">
                and _o.no like (select TOP 1 concat(__o.no, '%') from t_organization __o where __o.id = #{organizationId})
            </if>
            <if test="year != null">
                and _ah.year = #{year}
            </if>
            <if test="month != null">
                and _ah.month = #{month}
            </if>
            )
        ) t
        order by t.organizationNo, t.formName
    </select>

</mapper>
