<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.TzhCarbonPredictionHeadMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.TzhCarbonPredictionHead">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:34:51 HKT 2022.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="organization_id" jdbcType="VARCHAR" property="organizationId" />
    <result column="organization_name" jdbcType="VARCHAR" property="organizationName" />
    <result column="form_code" jdbcType="VARCHAR" property="formCode" />
    <result column="form_name" jdbcType="VARCHAR" property="formName" />
    <result column="reporting_year" jdbcType="INTEGER" property="reportingYear" />
    <result column="reporting_month" jdbcType="INTEGER" property="reportingMonth" />
    <result column="reporting_date" jdbcType="TIMESTAMP" property="reportingDate" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="user_real_name" jdbcType="VARCHAR" property="userRealName" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="VARCHAR" property="lastUpdateUsername" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
    <result column="is_submitted" jdbcType="BIT" property="isSubmitted" />
    <result column="audit_node" jdbcType="INTEGER" property="auditNode" />
    <result column="is_audited" jdbcType="BIT" property="isAudited" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:34:51 HKT 2022.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:34:51 HKT 2022.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:34:51 HKT 2022.
    -->
    id, organization_id, organization_name, form_code, form_name, reporting_year, reporting_month, 
    reporting_date, username, user_real_name, creation_time, create_username, last_update_time, 
    last_update_username, last_update_version, is_submitted, audit_node, is_audited, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.TzhCarbonPredictionHeadExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:34:51 HKT 2022.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_tzh_carbon_prediction_head
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:34:51 HKT 2022.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_tzh_carbon_prediction_head
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:34:51 HKT 2022.
    -->
    delete from t_tzh_carbon_prediction_head
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.TzhCarbonPredictionHeadExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:34:51 HKT 2022.
    -->
    delete from t_tzh_carbon_prediction_head
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.TzhCarbonPredictionHead">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:34:51 HKT 2022.
    -->
    insert into t_tzh_carbon_prediction_head (id, organization_id, organization_name, 
      form_code, form_name, reporting_year, 
      reporting_month, reporting_date, username, 
      user_real_name, creation_time, create_username, 
      last_update_time, last_update_username, last_update_version, 
      is_submitted, audit_node, is_audited, 
      is_deleted)
    values (#{id,jdbcType=VARCHAR}, #{organizationId,jdbcType=VARCHAR}, #{organizationName,jdbcType=VARCHAR}, 
      #{formCode,jdbcType=VARCHAR}, #{formName,jdbcType=VARCHAR}, #{reportingYear,jdbcType=INTEGER}, 
      #{reportingMonth,jdbcType=INTEGER}, #{reportingDate,jdbcType=TIMESTAMP}, #{username,jdbcType=VARCHAR}, 
      #{userRealName,jdbcType=VARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, #{createUsername,jdbcType=VARCHAR}, 
      #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUsername,jdbcType=VARCHAR}, #{lastUpdateVersion,jdbcType=INTEGER}, 
      #{isSubmitted,jdbcType=BIT}, #{auditNode,jdbcType=INTEGER}, #{isAudited,jdbcType=BIT}, 
      #{isDeleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.TzhCarbonPredictionHead">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:34:51 HKT 2022.
    -->
    insert into t_tzh_carbon_prediction_head
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="organizationId != null">
        organization_id,
      </if>
      <if test="organizationName != null">
        organization_name,
      </if>
      <if test="formCode != null">
        form_code,
      </if>
      <if test="formName != null">
        form_name,
      </if>
      <if test="reportingYear != null">
        reporting_year,
      </if>
      <if test="reportingMonth != null">
        reporting_month,
      </if>
      <if test="reportingDate != null">
        reporting_date,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="userRealName != null">
        user_real_name,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
      <if test="isSubmitted != null">
        is_submitted,
      </if>
      <if test="auditNode != null">
        audit_node,
      </if>
      <if test="isAudited != null">
        is_audited,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="organizationId != null">
        #{organizationId,jdbcType=VARCHAR},
      </if>
      <if test="organizationName != null">
        #{organizationName,jdbcType=VARCHAR},
      </if>
      <if test="formCode != null">
        #{formCode,jdbcType=VARCHAR},
      </if>
      <if test="formName != null">
        #{formName,jdbcType=VARCHAR},
      </if>
      <if test="reportingYear != null">
        #{reportingYear,jdbcType=INTEGER},
      </if>
      <if test="reportingMonth != null">
        #{reportingMonth,jdbcType=INTEGER},
      </if>
      <if test="reportingDate != null">
        #{reportingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="userRealName != null">
        #{userRealName,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isSubmitted != null">
        #{isSubmitted,jdbcType=BIT},
      </if>
      <if test="auditNode != null">
        #{auditNode,jdbcType=INTEGER},
      </if>
      <if test="isAudited != null">
        #{isAudited,jdbcType=BIT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.TzhCarbonPredictionHeadExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:34:51 HKT 2022.
    -->
    select count(*) from t_tzh_carbon_prediction_head
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:34:51 HKT 2022.
    -->
    update t_tzh_carbon_prediction_head
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=VARCHAR},
      </if>
      <if test="row.organizationId != null">
        organization_id = #{row.organizationId,jdbcType=VARCHAR},
      </if>
      <if test="row.organizationName != null">
        organization_name = #{row.organizationName,jdbcType=VARCHAR},
      </if>
      <if test="row.formCode != null">
        form_code = #{row.formCode,jdbcType=VARCHAR},
      </if>
      <if test="row.formName != null">
        form_name = #{row.formName,jdbcType=VARCHAR},
      </if>
      <if test="row.reportingYear != null">
        reporting_year = #{row.reportingYear,jdbcType=INTEGER},
      </if>
      <if test="row.reportingMonth != null">
        reporting_month = #{row.reportingMonth,jdbcType=INTEGER},
      </if>
      <if test="row.reportingDate != null">
        reporting_date = #{row.reportingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.username != null">
        username = #{row.username,jdbcType=VARCHAR},
      </if>
      <if test="row.userRealName != null">
        user_real_name = #{row.userRealName,jdbcType=VARCHAR},
      </if>
      <if test="row.creationTime != null">
        creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createUsername != null">
        create_username = #{row.createUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateTime != null">
        last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.lastUpdateUsername != null">
        last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateVersion != null">
        last_update_version = #{row.lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="row.isSubmitted != null">
        is_submitted = #{row.isSubmitted,jdbcType=BIT},
      </if>
      <if test="row.auditNode != null">
        audit_node = #{row.auditNode,jdbcType=INTEGER},
      </if>
      <if test="row.isAudited != null">
        is_audited = #{row.isAudited,jdbcType=BIT},
      </if>
      <if test="row.isDeleted != null">
        is_deleted = #{row.isDeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:34:51 HKT 2022.
    -->
    update t_tzh_carbon_prediction_head
    set id = #{row.id,jdbcType=VARCHAR},
      organization_id = #{row.organizationId,jdbcType=VARCHAR},
      organization_name = #{row.organizationName,jdbcType=VARCHAR},
      form_code = #{row.formCode,jdbcType=VARCHAR},
      form_name = #{row.formName,jdbcType=VARCHAR},
      reporting_year = #{row.reportingYear,jdbcType=INTEGER},
      reporting_month = #{row.reportingMonth,jdbcType=INTEGER},
      reporting_date = #{row.reportingDate,jdbcType=TIMESTAMP},
      username = #{row.username,jdbcType=VARCHAR},
      user_real_name = #{row.userRealName,jdbcType=VARCHAR},
      creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      create_username = #{row.createUsername,jdbcType=VARCHAR},
      last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      last_update_version = #{row.lastUpdateVersion,jdbcType=INTEGER},
      is_submitted = #{row.isSubmitted,jdbcType=BIT},
      audit_node = #{row.auditNode,jdbcType=INTEGER},
      is_audited = #{row.isAudited,jdbcType=BIT},
      is_deleted = #{row.isDeleted,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.TzhCarbonPredictionHead">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:34:51 HKT 2022.
    -->
    update t_tzh_carbon_prediction_head
    <set>
      <if test="organizationId != null">
        organization_id = #{organizationId,jdbcType=VARCHAR},
      </if>
      <if test="organizationName != null">
        organization_name = #{organizationName,jdbcType=VARCHAR},
      </if>
      <if test="formCode != null">
        form_code = #{formCode,jdbcType=VARCHAR},
      </if>
      <if test="formName != null">
        form_name = #{formName,jdbcType=VARCHAR},
      </if>
      <if test="reportingYear != null">
        reporting_year = #{reportingYear,jdbcType=INTEGER},
      </if>
      <if test="reportingMonth != null">
        reporting_month = #{reportingMonth,jdbcType=INTEGER},
      </if>
      <if test="reportingDate != null">
        reporting_date = #{reportingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="userRealName != null">
        user_real_name = #{userRealName,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isSubmitted != null">
        is_submitted = #{isSubmitted,jdbcType=BIT},
      </if>
      <if test="auditNode != null">
        audit_node = #{auditNode,jdbcType=INTEGER},
      </if>
      <if test="isAudited != null">
        is_audited = #{isAudited,jdbcType=BIT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.TzhCarbonPredictionHead">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:34:51 HKT 2022.
    -->
    update t_tzh_carbon_prediction_head
    set organization_id = #{organizationId,jdbcType=VARCHAR},
      organization_name = #{organizationName,jdbcType=VARCHAR},
      form_code = #{formCode,jdbcType=VARCHAR},
      form_name = #{formName,jdbcType=VARCHAR},
      reporting_year = #{reportingYear,jdbcType=INTEGER},
      reporting_month = #{reportingMonth,jdbcType=INTEGER},
      reporting_date = #{reportingDate,jdbcType=TIMESTAMP},
      username = #{username,jdbcType=VARCHAR},
      user_real_name = #{userRealName,jdbcType=VARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=VARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      is_submitted = #{isSubmitted,jdbcType=BIT},
      audit_node = #{auditNode,jdbcType=INTEGER},
      is_audited = #{isAudited,jdbcType=BIT},
      is_deleted = #{isDeleted,jdbcType=BIT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>