<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.FfCmFixedDetailMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.FfCmFixedDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 02 16:05:29 HKT 2024.
    -->
    <result column="id" jdbcType="NVARCHAR" property="id" />
    <result column="head_id" jdbcType="NVARCHAR" property="headId" />
    <result column="col_1" jdbcType="NVARCHAR" property="col1" />
    <result column="col_2" jdbcType="NVARCHAR" property="col2" />
    <result column="col_3" jdbcType="NVARCHAR" property="col3" />
    <result column="col_4" jdbcType="NVARCHAR" property="col4" />
    <result column="col_5" jdbcType="NVARCHAR" property="col5" />
    <result column="col_6" jdbcType="NVARCHAR" property="col6" />
    <result column="col_7" jdbcType="NVARCHAR" property="col7" />
    <result column="col_8" jdbcType="NVARCHAR" property="col8" />
    <result column="col_9" jdbcType="NVARCHAR" property="col9" />
    <result column="col_10" jdbcType="NVARCHAR" property="col10" />
    <result column="col_11" jdbcType="NVARCHAR" property="col11" />
    <result column="col_12" jdbcType="NVARCHAR" property="col12" />
    <result column="col_13" jdbcType="NVARCHAR" property="col13" />
    <result column="col_14" jdbcType="NVARCHAR" property="col14" />
    <result column="col_15" jdbcType="NVARCHAR" property="col15" />
    <result column="col_16" jdbcType="NVARCHAR" property="col16" />
    <result column="col_17" jdbcType="NVARCHAR" property="col17" />
    <result column="col_18" jdbcType="NVARCHAR" property="col18" />
    <result column="col_19" jdbcType="NVARCHAR" property="col19" />
    <result column="col_20" jdbcType="NVARCHAR" property="col20" />
    <result column="col_21" jdbcType="NVARCHAR" property="col21" />
    <result column="col_22" jdbcType="NVARCHAR" property="col22" />
    <result column="col_23" jdbcType="NVARCHAR" property="col23" />
    <result column="col_24" jdbcType="NVARCHAR" property="col24" />
    <result column="col_25" jdbcType="NVARCHAR" property="col25" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 02 16:05:29 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 02 16:05:29 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 02 16:05:29 HKT 2024.
    -->
    id, head_id, col_1, col_2, col_3, col_4, col_5, col_6, col_7, col_8, col_9, col_10, 
    col_11, col_12, col_13, col_14, col_15, col_16, col_17, col_18, col_19, col_20, col_21, 
    col_22, col_23, col_24, col_25, seq, creation_time
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.FfCmFixedDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 02 16:05:29 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_ff_cm_fixed_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.FfCmFixedDetailExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 02 16:05:29 HKT 2024.
    -->
    delete from t_ff_cm_fixed_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.FfCmFixedDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 02 16:05:29 HKT 2024.
    -->
    insert into t_ff_cm_fixed_detail (id, head_id, col_1, 
      col_2, col_3, col_4, 
      col_5, col_6, col_7, 
      col_8, col_9, col_10, 
      col_11, col_12, col_13, 
      col_14, col_15, col_16, 
      col_17, col_18, col_19, 
      col_20, col_21, col_22, 
      col_23, col_24, col_25, 
      seq, creation_time)
    values (#{id,jdbcType=NVARCHAR}, #{headId,jdbcType=NVARCHAR}, #{col1,jdbcType=NVARCHAR}, 
      #{col2,jdbcType=NVARCHAR}, #{col3,jdbcType=NVARCHAR}, #{col4,jdbcType=NVARCHAR}, 
      #{col5,jdbcType=NVARCHAR}, #{col6,jdbcType=NVARCHAR}, #{col7,jdbcType=NVARCHAR}, 
      #{col8,jdbcType=NVARCHAR}, #{col9,jdbcType=NVARCHAR}, #{col10,jdbcType=NVARCHAR}, 
      #{col11,jdbcType=NVARCHAR}, #{col12,jdbcType=NVARCHAR}, #{col13,jdbcType=NVARCHAR}, 
      #{col14,jdbcType=NVARCHAR}, #{col15,jdbcType=NVARCHAR}, #{col16,jdbcType=NVARCHAR}, 
      #{col17,jdbcType=NVARCHAR}, #{col18,jdbcType=NVARCHAR}, #{col19,jdbcType=NVARCHAR}, 
      #{col20,jdbcType=NVARCHAR}, #{col21,jdbcType=NVARCHAR}, #{col22,jdbcType=NVARCHAR}, 
      #{col23,jdbcType=NVARCHAR}, #{col24,jdbcType=NVARCHAR}, #{col25,jdbcType=NVARCHAR}, 
      #{seq,jdbcType=INTEGER}, #{creationTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.FfCmFixedDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 02 16:05:29 HKT 2024.
    -->
    insert into t_ff_cm_fixed_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="headId != null">
        head_id,
      </if>
      <if test="col1 != null">
        col_1,
      </if>
      <if test="col2 != null">
        col_2,
      </if>
      <if test="col3 != null">
        col_3,
      </if>
      <if test="col4 != null">
        col_4,
      </if>
      <if test="col5 != null">
        col_5,
      </if>
      <if test="col6 != null">
        col_6,
      </if>
      <if test="col7 != null">
        col_7,
      </if>
      <if test="col8 != null">
        col_8,
      </if>
      <if test="col9 != null">
        col_9,
      </if>
      <if test="col10 != null">
        col_10,
      </if>
      <if test="col11 != null">
        col_11,
      </if>
      <if test="col12 != null">
        col_12,
      </if>
      <if test="col13 != null">
        col_13,
      </if>
      <if test="col14 != null">
        col_14,
      </if>
      <if test="col15 != null">
        col_15,
      </if>
      <if test="col16 != null">
        col_16,
      </if>
      <if test="col17 != null">
        col_17,
      </if>
      <if test="col18 != null">
        col_18,
      </if>
      <if test="col19 != null">
        col_19,
      </if>
      <if test="col20 != null">
        col_20,
      </if>
      <if test="col21 != null">
        col_21,
      </if>
      <if test="col22 != null">
        col_22,
      </if>
      <if test="col23 != null">
        col_23,
      </if>
      <if test="col24 != null">
        col_24,
      </if>
      <if test="col25 != null">
        col_25,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=NVARCHAR},
      </if>
      <if test="headId != null">
        #{headId,jdbcType=NVARCHAR},
      </if>
      <if test="col1 != null">
        #{col1,jdbcType=NVARCHAR},
      </if>
      <if test="col2 != null">
        #{col2,jdbcType=NVARCHAR},
      </if>
      <if test="col3 != null">
        #{col3,jdbcType=NVARCHAR},
      </if>
      <if test="col4 != null">
        #{col4,jdbcType=NVARCHAR},
      </if>
      <if test="col5 != null">
        #{col5,jdbcType=NVARCHAR},
      </if>
      <if test="col6 != null">
        #{col6,jdbcType=NVARCHAR},
      </if>
      <if test="col7 != null">
        #{col7,jdbcType=NVARCHAR},
      </if>
      <if test="col8 != null">
        #{col8,jdbcType=NVARCHAR},
      </if>
      <if test="col9 != null">
        #{col9,jdbcType=NVARCHAR},
      </if>
      <if test="col10 != null">
        #{col10,jdbcType=NVARCHAR},
      </if>
      <if test="col11 != null">
        #{col11,jdbcType=NVARCHAR},
      </if>
      <if test="col12 != null">
        #{col12,jdbcType=NVARCHAR},
      </if>
      <if test="col13 != null">
        #{col13,jdbcType=NVARCHAR},
      </if>
      <if test="col14 != null">
        #{col14,jdbcType=NVARCHAR},
      </if>
      <if test="col15 != null">
        #{col15,jdbcType=NVARCHAR},
      </if>
      <if test="col16 != null">
        #{col16,jdbcType=NVARCHAR},
      </if>
      <if test="col17 != null">
        #{col17,jdbcType=NVARCHAR},
      </if>
      <if test="col18 != null">
        #{col18,jdbcType=NVARCHAR},
      </if>
      <if test="col19 != null">
        #{col19,jdbcType=NVARCHAR},
      </if>
      <if test="col20 != null">
        #{col20,jdbcType=NVARCHAR},
      </if>
      <if test="col21 != null">
        #{col21,jdbcType=NVARCHAR},
      </if>
      <if test="col22 != null">
        #{col22,jdbcType=NVARCHAR},
      </if>
      <if test="col23 != null">
        #{col23,jdbcType=NVARCHAR},
      </if>
      <if test="col24 != null">
        #{col24,jdbcType=NVARCHAR},
      </if>
      <if test="col25 != null">
        #{col25,jdbcType=NVARCHAR},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.FfCmFixedDetailExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 02 16:05:29 HKT 2024.
    -->
    select count(*) from t_ff_cm_fixed_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 02 16:05:29 HKT 2024.
    -->
    update t_ff_cm_fixed_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=NVARCHAR},
      </if>
      <if test="record.headId != null">
        head_id = #{record.headId,jdbcType=NVARCHAR},
      </if>
      <if test="record.col1 != null">
        col_1 = #{record.col1,jdbcType=NVARCHAR},
      </if>
      <if test="record.col2 != null">
        col_2 = #{record.col2,jdbcType=NVARCHAR},
      </if>
      <if test="record.col3 != null">
        col_3 = #{record.col3,jdbcType=NVARCHAR},
      </if>
      <if test="record.col4 != null">
        col_4 = #{record.col4,jdbcType=NVARCHAR},
      </if>
      <if test="record.col5 != null">
        col_5 = #{record.col5,jdbcType=NVARCHAR},
      </if>
      <if test="record.col6 != null">
        col_6 = #{record.col6,jdbcType=NVARCHAR},
      </if>
      <if test="record.col7 != null">
        col_7 = #{record.col7,jdbcType=NVARCHAR},
      </if>
      <if test="record.col8 != null">
        col_8 = #{record.col8,jdbcType=NVARCHAR},
      </if>
      <if test="record.col9 != null">
        col_9 = #{record.col9,jdbcType=NVARCHAR},
      </if>
      <if test="record.col10 != null">
        col_10 = #{record.col10,jdbcType=NVARCHAR},
      </if>
      <if test="record.col11 != null">
        col_11 = #{record.col11,jdbcType=NVARCHAR},
      </if>
      <if test="record.col12 != null">
        col_12 = #{record.col12,jdbcType=NVARCHAR},
      </if>
      <if test="record.col13 != null">
        col_13 = #{record.col13,jdbcType=NVARCHAR},
      </if>
      <if test="record.col14 != null">
        col_14 = #{record.col14,jdbcType=NVARCHAR},
      </if>
      <if test="record.col15 != null">
        col_15 = #{record.col15,jdbcType=NVARCHAR},
      </if>
      <if test="record.col16 != null">
        col_16 = #{record.col16,jdbcType=NVARCHAR},
      </if>
      <if test="record.col17 != null">
        col_17 = #{record.col17,jdbcType=NVARCHAR},
      </if>
      <if test="record.col18 != null">
        col_18 = #{record.col18,jdbcType=NVARCHAR},
      </if>
      <if test="record.col19 != null">
        col_19 = #{record.col19,jdbcType=NVARCHAR},
      </if>
      <if test="record.col20 != null">
        col_20 = #{record.col20,jdbcType=NVARCHAR},
      </if>
      <if test="record.col21 != null">
        col_21 = #{record.col21,jdbcType=NVARCHAR},
      </if>
      <if test="record.col22 != null">
        col_22 = #{record.col22,jdbcType=NVARCHAR},
      </if>
      <if test="record.col23 != null">
        col_23 = #{record.col23,jdbcType=NVARCHAR},
      </if>
      <if test="record.col24 != null">
        col_24 = #{record.col24,jdbcType=NVARCHAR},
      </if>
      <if test="record.col25 != null">
        col_25 = #{record.col25,jdbcType=NVARCHAR},
      </if>
      <if test="record.seq != null">
        seq = #{record.seq,jdbcType=INTEGER},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 02 16:05:29 HKT 2024.
    -->
    update t_ff_cm_fixed_detail
    set id = #{record.id,jdbcType=NVARCHAR},
      head_id = #{record.headId,jdbcType=NVARCHAR},
      col_1 = #{record.col1,jdbcType=NVARCHAR},
      col_2 = #{record.col2,jdbcType=NVARCHAR},
      col_3 = #{record.col3,jdbcType=NVARCHAR},
      col_4 = #{record.col4,jdbcType=NVARCHAR},
      col_5 = #{record.col5,jdbcType=NVARCHAR},
      col_6 = #{record.col6,jdbcType=NVARCHAR},
      col_7 = #{record.col7,jdbcType=NVARCHAR},
      col_8 = #{record.col8,jdbcType=NVARCHAR},
      col_9 = #{record.col9,jdbcType=NVARCHAR},
      col_10 = #{record.col10,jdbcType=NVARCHAR},
      col_11 = #{record.col11,jdbcType=NVARCHAR},
      col_12 = #{record.col12,jdbcType=NVARCHAR},
      col_13 = #{record.col13,jdbcType=NVARCHAR},
      col_14 = #{record.col14,jdbcType=NVARCHAR},
      col_15 = #{record.col15,jdbcType=NVARCHAR},
      col_16 = #{record.col16,jdbcType=NVARCHAR},
      col_17 = #{record.col17,jdbcType=NVARCHAR},
      col_18 = #{record.col18,jdbcType=NVARCHAR},
      col_19 = #{record.col19,jdbcType=NVARCHAR},
      col_20 = #{record.col20,jdbcType=NVARCHAR},
      col_21 = #{record.col21,jdbcType=NVARCHAR},
      col_22 = #{record.col22,jdbcType=NVARCHAR},
      col_23 = #{record.col23,jdbcType=NVARCHAR},
      col_24 = #{record.col24,jdbcType=NVARCHAR},
      col_25 = #{record.col25,jdbcType=NVARCHAR},
      seq = #{record.seq,jdbcType=INTEGER},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>