<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.ApiLogCustomMapper">


  <select id="listApiLogByHeadId" resultType="com.csci.susdev.vo.ApiLogVO">
    SELECT
      id, username, token, app_id as appId, app_key as appKey, page,
      method, api, ip, mac, code,
      type, start_time as startTime, end_time as endTime
    FROM
      t_api_log
    WHERE
      method = 'POST'
      and api not LIKE '%get%' and api not like '%list%' and code=0
    <if test="headId != null and headId != ''">
      and (PARAMETER LIKE CONCAT('%', #{headId},'%') OR request LIKE CONCAT('%', #{headId},'%'))
    </if>
    ORDER BY
      start_time DESC;
  </select>
</mapper>