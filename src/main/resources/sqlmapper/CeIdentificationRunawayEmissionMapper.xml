<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.CeIdentificationRunawayEmissionMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.CeIdentificationRunawayEmission">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 01 14:16:37 CST 2025.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="ce_identification_head_id" jdbcType="CHAR" property="ceIdentificationHeadId" />
    <result column="is_own_septic_tank" jdbcType="NVARCHAR" property="isOwnSepticTank" />
    <result column="regular_employee_num" jdbcType="NVARCHAR" property="regularEmployeeNum" />
    <result column="average_work_day_year " jdbcType="NVARCHAR" property="averageWorkDayYear" />
    <result column="average_daily_commuting" jdbcType="NVARCHAR" property="averageDailyCommuting" />
    <result column="average_daily_accommodation" jdbcType="NVARCHAR" property="averageDailyAccommodation" />
    <result column="aircondition_refrigerant_type" jdbcType="NVARCHAR" property="airconditionRefrigerantType" />
    <result column="aircondition_refrigerant_charge_amount" jdbcType="NVARCHAR" property="airconditionRefrigerantChargeAmount" />
    <result column="aircondition_take_photo" jdbcType="NVARCHAR" property="airconditionTakePhoto" />
    <result column="fridge_refrigerant_type" jdbcType="NVARCHAR" property="fridgeRefrigerantType" />
    <result column="fridge_refrigerant_charge_amount" jdbcType="NVARCHAR" property="fridgeRefrigerantChargeAmount" />
    <result column="fridge_take_photo" jdbcType="NVARCHAR" property="fridgeTakePhoto" />
    <result column="service_car_refrigerant_type" jdbcType="NVARCHAR" property="serviceCarRefrigerantType" />
    <result column="service_car_refrigerant_charge_amount" jdbcType="NVARCHAR" property="serviceCarRefrigerantChargeAmount" />
    <result column="service_car_take_photo" jdbcType="NVARCHAR" property="serviceCarTakePhoto" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="NVARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="NVARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 01 14:16:37 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 01 14:16:37 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 01 14:16:37 CST 2025.
    -->
    id, ce_identification_head_id, is_own_septic_tank, regular_employee_num, "average_work_day_year ", 
    average_daily_commuting, average_daily_accommodation, aircondition_refrigerant_type, 
    aircondition_refrigerant_charge_amount, aircondition_take_photo, fridge_refrigerant_type, 
    fridge_refrigerant_charge_amount, fridge_take_photo, service_car_refrigerant_type, 
    service_car_refrigerant_charge_amount, service_car_take_photo, creation_time, create_username, 
    create_user_id, last_update_time, last_update_username, last_update_user_id, last_update_version, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.CeIdentificationRunawayEmissionExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 01 14:16:37 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_ce_identification_runaway_emission
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 01 14:16:37 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_ce_identification_runaway_emission
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 01 14:16:37 CST 2025.
    -->
    delete from t_ce_identification_runaway_emission
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.CeIdentificationRunawayEmissionExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 01 14:16:37 CST 2025.
    -->
    delete from t_ce_identification_runaway_emission
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.CeIdentificationRunawayEmission">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 01 14:16:37 CST 2025.
    -->
    insert into t_ce_identification_runaway_emission (id, ce_identification_head_id, is_own_septic_tank, 
      regular_employee_num, "average_work_day_year ", 
      average_daily_commuting, average_daily_accommodation, 
      aircondition_refrigerant_type, aircondition_refrigerant_charge_amount, 
      aircondition_take_photo, fridge_refrigerant_type, 
      fridge_refrigerant_charge_amount, fridge_take_photo, 
      service_car_refrigerant_type, service_car_refrigerant_charge_amount, 
      service_car_take_photo, creation_time, create_username, 
      create_user_id, last_update_time, last_update_username, 
      last_update_user_id, last_update_version, is_deleted
      )
    values (#{id,jdbcType=CHAR}, #{ceIdentificationHeadId,jdbcType=CHAR}, #{isOwnSepticTank,jdbcType=NVARCHAR}, 
      #{regularEmployeeNum,jdbcType=NVARCHAR}, #{averageWorkDayYear,jdbcType=NVARCHAR}, 
      #{averageDailyCommuting,jdbcType=NVARCHAR}, #{averageDailyAccommodation,jdbcType=NVARCHAR}, 
      #{airconditionRefrigerantType,jdbcType=NVARCHAR}, #{airconditionRefrigerantChargeAmount,jdbcType=NVARCHAR}, 
      #{airconditionTakePhoto,jdbcType=NVARCHAR}, #{fridgeRefrigerantType,jdbcType=NVARCHAR}, 
      #{fridgeRefrigerantChargeAmount,jdbcType=NVARCHAR}, #{fridgeTakePhoto,jdbcType=NVARCHAR}, 
      #{serviceCarRefrigerantType,jdbcType=NVARCHAR}, #{serviceCarRefrigerantChargeAmount,jdbcType=NVARCHAR}, 
      #{serviceCarTakePhoto,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, #{createUsername,jdbcType=NVARCHAR}, 
      #{createUserId,jdbcType=NVARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUsername,jdbcType=NVARCHAR}, 
      #{lastUpdateUserId,jdbcType=NVARCHAR}, #{lastUpdateVersion,jdbcType=INTEGER}, #{isDeleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.CeIdentificationRunawayEmission">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 01 14:16:37 CST 2025.
    -->
    insert into t_ce_identification_runaway_emission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="ceIdentificationHeadId != null">
        ce_identification_head_id,
      </if>
      <if test="isOwnSepticTank != null">
        is_own_septic_tank,
      </if>
      <if test="regularEmployeeNum != null">
        regular_employee_num,
      </if>
      <if test="averageWorkDayYear != null">
        "average_work_day_year ",
      </if>
      <if test="averageDailyCommuting != null">
        average_daily_commuting,
      </if>
      <if test="averageDailyAccommodation != null">
        average_daily_accommodation,
      </if>
      <if test="airconditionRefrigerantType != null">
        aircondition_refrigerant_type,
      </if>
      <if test="airconditionRefrigerantChargeAmount != null">
        aircondition_refrigerant_charge_amount,
      </if>
      <if test="airconditionTakePhoto != null">
        aircondition_take_photo,
      </if>
      <if test="fridgeRefrigerantType != null">
        fridge_refrigerant_type,
      </if>
      <if test="fridgeRefrigerantChargeAmount != null">
        fridge_refrigerant_charge_amount,
      </if>
      <if test="fridgeTakePhoto != null">
        fridge_take_photo,
      </if>
      <if test="serviceCarRefrigerantType != null">
        service_car_refrigerant_type,
      </if>
      <if test="serviceCarRefrigerantChargeAmount != null">
        service_car_refrigerant_charge_amount,
      </if>
      <if test="serviceCarTakePhoto != null">
        service_car_take_photo,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="ceIdentificationHeadId != null">
        #{ceIdentificationHeadId,jdbcType=CHAR},
      </if>
      <if test="isOwnSepticTank != null">
        #{isOwnSepticTank,jdbcType=NVARCHAR},
      </if>
      <if test="regularEmployeeNum != null">
        #{regularEmployeeNum,jdbcType=NVARCHAR},
      </if>
      <if test="averageWorkDayYear != null">
        #{averageWorkDayYear,jdbcType=NVARCHAR},
      </if>
      <if test="averageDailyCommuting != null">
        #{averageDailyCommuting,jdbcType=NVARCHAR},
      </if>
      <if test="averageDailyAccommodation != null">
        #{averageDailyAccommodation,jdbcType=NVARCHAR},
      </if>
      <if test="airconditionRefrigerantType != null">
        #{airconditionRefrigerantType,jdbcType=NVARCHAR},
      </if>
      <if test="airconditionRefrigerantChargeAmount != null">
        #{airconditionRefrigerantChargeAmount,jdbcType=NVARCHAR},
      </if>
      <if test="airconditionTakePhoto != null">
        #{airconditionTakePhoto,jdbcType=NVARCHAR},
      </if>
      <if test="fridgeRefrigerantType != null">
        #{fridgeRefrigerantType,jdbcType=NVARCHAR},
      </if>
      <if test="fridgeRefrigerantChargeAmount != null">
        #{fridgeRefrigerantChargeAmount,jdbcType=NVARCHAR},
      </if>
      <if test="fridgeTakePhoto != null">
        #{fridgeTakePhoto,jdbcType=NVARCHAR},
      </if>
      <if test="serviceCarRefrigerantType != null">
        #{serviceCarRefrigerantType,jdbcType=NVARCHAR},
      </if>
      <if test="serviceCarRefrigerantChargeAmount != null">
        #{serviceCarRefrigerantChargeAmount,jdbcType=NVARCHAR},
      </if>
      <if test="serviceCarTakePhoto != null">
        #{serviceCarTakePhoto,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.CeIdentificationRunawayEmissionExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 01 14:16:37 CST 2025.
    -->
    select count(*) from t_ce_identification_runaway_emission
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 01 14:16:37 CST 2025.
    -->
    update t_ce_identification_runaway_emission
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.ceIdentificationHeadId != null">
        ce_identification_head_id = #{record.ceIdentificationHeadId,jdbcType=CHAR},
      </if>
      <if test="record.isOwnSepticTank != null">
        is_own_septic_tank = #{record.isOwnSepticTank,jdbcType=NVARCHAR},
      </if>
      <if test="record.regularEmployeeNum != null">
        regular_employee_num = #{record.regularEmployeeNum,jdbcType=NVARCHAR},
      </if>
      <if test="record.averageWorkDayYear != null">
        "average_work_day_year " = #{record.averageWorkDayYear,jdbcType=NVARCHAR},
      </if>
      <if test="record.averageDailyCommuting != null">
        average_daily_commuting = #{record.averageDailyCommuting,jdbcType=NVARCHAR},
      </if>
      <if test="record.averageDailyAccommodation != null">
        average_daily_accommodation = #{record.averageDailyAccommodation,jdbcType=NVARCHAR},
      </if>
      <if test="record.airconditionRefrigerantType != null">
        aircondition_refrigerant_type = #{record.airconditionRefrigerantType,jdbcType=NVARCHAR},
      </if>
      <if test="record.airconditionRefrigerantChargeAmount != null">
        aircondition_refrigerant_charge_amount = #{record.airconditionRefrigerantChargeAmount,jdbcType=NVARCHAR},
      </if>
      <if test="record.airconditionTakePhoto != null">
        aircondition_take_photo = #{record.airconditionTakePhoto,jdbcType=NVARCHAR},
      </if>
      <if test="record.fridgeRefrigerantType != null">
        fridge_refrigerant_type = #{record.fridgeRefrigerantType,jdbcType=NVARCHAR},
      </if>
      <if test="record.fridgeRefrigerantChargeAmount != null">
        fridge_refrigerant_charge_amount = #{record.fridgeRefrigerantChargeAmount,jdbcType=NVARCHAR},
      </if>
      <if test="record.fridgeTakePhoto != null">
        fridge_take_photo = #{record.fridgeTakePhoto,jdbcType=NVARCHAR},
      </if>
      <if test="record.serviceCarRefrigerantType != null">
        service_car_refrigerant_type = #{record.serviceCarRefrigerantType,jdbcType=NVARCHAR},
      </if>
      <if test="record.serviceCarRefrigerantChargeAmount != null">
        service_car_refrigerant_charge_amount = #{record.serviceCarRefrigerantChargeAmount,jdbcType=NVARCHAR},
      </if>
      <if test="record.serviceCarTakePhoto != null">
        service_car_take_photo = #{record.serviceCarTakePhoto,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateVersion != null">
        last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 01 14:16:37 CST 2025.
    -->
    update t_ce_identification_runaway_emission
    set id = #{record.id,jdbcType=CHAR},
      ce_identification_head_id = #{record.ceIdentificationHeadId,jdbcType=CHAR},
      is_own_septic_tank = #{record.isOwnSepticTank,jdbcType=NVARCHAR},
      regular_employee_num = #{record.regularEmployeeNum,jdbcType=NVARCHAR},
      "average_work_day_year " = #{record.averageWorkDayYear,jdbcType=NVARCHAR},
      average_daily_commuting = #{record.averageDailyCommuting,jdbcType=NVARCHAR},
      average_daily_accommodation = #{record.averageDailyAccommodation,jdbcType=NVARCHAR},
      aircondition_refrigerant_type = #{record.airconditionRefrigerantType,jdbcType=NVARCHAR},
      aircondition_refrigerant_charge_amount = #{record.airconditionRefrigerantChargeAmount,jdbcType=NVARCHAR},
      aircondition_take_photo = #{record.airconditionTakePhoto,jdbcType=NVARCHAR},
      fridge_refrigerant_type = #{record.fridgeRefrigerantType,jdbcType=NVARCHAR},
      fridge_refrigerant_charge_amount = #{record.fridgeRefrigerantChargeAmount,jdbcType=NVARCHAR},
      fridge_take_photo = #{record.fridgeTakePhoto,jdbcType=NVARCHAR},
      service_car_refrigerant_type = #{record.serviceCarRefrigerantType,jdbcType=NVARCHAR},
      service_car_refrigerant_charge_amount = #{record.serviceCarRefrigerantChargeAmount,jdbcType=NVARCHAR},
      service_car_take_photo = #{record.serviceCarTakePhoto,jdbcType=NVARCHAR},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=NVARCHAR},
      create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.CeIdentificationRunawayEmission">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 01 14:16:37 CST 2025.
    -->
    update t_ce_identification_runaway_emission
    <set>
      <if test="ceIdentificationHeadId != null">
        ce_identification_head_id = #{ceIdentificationHeadId,jdbcType=CHAR},
      </if>
      <if test="isOwnSepticTank != null">
        is_own_septic_tank = #{isOwnSepticTank,jdbcType=NVARCHAR},
      </if>
      <if test="regularEmployeeNum != null">
        regular_employee_num = #{regularEmployeeNum,jdbcType=NVARCHAR},
      </if>
      <if test="averageWorkDayYear != null">
        "average_work_day_year " = #{averageWorkDayYear,jdbcType=NVARCHAR},
      </if>
      <if test="averageDailyCommuting != null">
        average_daily_commuting = #{averageDailyCommuting,jdbcType=NVARCHAR},
      </if>
      <if test="averageDailyAccommodation != null">
        average_daily_accommodation = #{averageDailyAccommodation,jdbcType=NVARCHAR},
      </if>
      <if test="airconditionRefrigerantType != null">
        aircondition_refrigerant_type = #{airconditionRefrigerantType,jdbcType=NVARCHAR},
      </if>
      <if test="airconditionRefrigerantChargeAmount != null">
        aircondition_refrigerant_charge_amount = #{airconditionRefrigerantChargeAmount,jdbcType=NVARCHAR},
      </if>
      <if test="airconditionTakePhoto != null">
        aircondition_take_photo = #{airconditionTakePhoto,jdbcType=NVARCHAR},
      </if>
      <if test="fridgeRefrigerantType != null">
        fridge_refrigerant_type = #{fridgeRefrigerantType,jdbcType=NVARCHAR},
      </if>
      <if test="fridgeRefrigerantChargeAmount != null">
        fridge_refrigerant_charge_amount = #{fridgeRefrigerantChargeAmount,jdbcType=NVARCHAR},
      </if>
      <if test="fridgeTakePhoto != null">
        fridge_take_photo = #{fridgeTakePhoto,jdbcType=NVARCHAR},
      </if>
      <if test="serviceCarRefrigerantType != null">
        service_car_refrigerant_type = #{serviceCarRefrigerantType,jdbcType=NVARCHAR},
      </if>
      <if test="serviceCarRefrigerantChargeAmount != null">
        service_car_refrigerant_charge_amount = #{serviceCarRefrigerantChargeAmount,jdbcType=NVARCHAR},
      </if>
      <if test="serviceCarTakePhoto != null">
        service_car_take_photo = #{serviceCarTakePhoto,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.CeIdentificationRunawayEmission">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 01 14:16:37 CST 2025.
    -->
    update t_ce_identification_runaway_emission
    set ce_identification_head_id = #{ceIdentificationHeadId,jdbcType=CHAR},
      is_own_septic_tank = #{isOwnSepticTank,jdbcType=NVARCHAR},
      regular_employee_num = #{regularEmployeeNum,jdbcType=NVARCHAR},
      "average_work_day_year " = #{averageWorkDayYear,jdbcType=NVARCHAR},
      average_daily_commuting = #{averageDailyCommuting,jdbcType=NVARCHAR},
      average_daily_accommodation = #{averageDailyAccommodation,jdbcType=NVARCHAR},
      aircondition_refrigerant_type = #{airconditionRefrigerantType,jdbcType=NVARCHAR},
      aircondition_refrigerant_charge_amount = #{airconditionRefrigerantChargeAmount,jdbcType=NVARCHAR},
      aircondition_take_photo = #{airconditionTakePhoto,jdbcType=NVARCHAR},
      fridge_refrigerant_type = #{fridgeRefrigerantType,jdbcType=NVARCHAR},
      fridge_refrigerant_charge_amount = #{fridgeRefrigerantChargeAmount,jdbcType=NVARCHAR},
      fridge_take_photo = #{fridgeTakePhoto,jdbcType=NVARCHAR},
      service_car_refrigerant_type = #{serviceCarRefrigerantType,jdbcType=NVARCHAR},
      service_car_refrigerant_charge_amount = #{serviceCarRefrigerantChargeAmount,jdbcType=NVARCHAR},
      service_car_take_photo = #{serviceCarTakePhoto,jdbcType=NVARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=NVARCHAR},
      create_user_id = #{createUserId,jdbcType=NVARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=BIT}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>