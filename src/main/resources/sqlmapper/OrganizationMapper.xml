<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.OrganizationMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.Organization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Sep 12 09:58:28 HKT 2023.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="no" jdbcType="VARCHAR" property="no" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="unit_code" jdbcType="VARCHAR" property="unitCode" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="latitude" jdbcType="VARCHAR" property="latitude" />
    <result column="longitude" jdbcType="VARCHAR" property="longitude" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="division" jdbcType="VARCHAR" property="division" />
    <result column="region" jdbcType="VARCHAR" property="region" />
    <result column="power_supply" jdbcType="VARCHAR" property="powerSupply" />
    <result column="start_date" jdbcType="VARCHAR" property="startDate" />
    <result column="end_date" jdbcType="VARCHAR" property="endDate" />
    <result column="is_completed" jdbcType="VARCHAR" property="isCompleted" />
    <result column="jv" jdbcType="VARCHAR" property="jv" />
    <result column="total_cfa" jdbcType="VARCHAR" property="totalCfa" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="scene" jdbcType="VARCHAR" property="scene" />
    <result column="area_id" jdbcType="VARCHAR" property="areaId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="VARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="VARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Sep 12 09:58:28 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Sep 12 09:58:28 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Sep 12 09:58:28 HKT 2023.
    -->
    id, parent_id, no, name, code, unit_code, title, address, latitude, longitude, currency, 
    division, region, power_supply, start_date, end_date, is_completed, jv, total_cfa, 
    sort, scene, area_id, type, creation_time, create_username, create_user_id, last_update_time, 
    last_update_username, last_update_user_id, last_update_version, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.OrganizationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Sep 12 09:58:28 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_organization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Sep 12 09:58:28 HKT 2023.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_organization
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Sep 12 09:58:28 HKT 2023.
    -->
    delete from t_organization
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.OrganizationExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Sep 12 09:58:28 HKT 2023.
    -->
    delete from t_organization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.Organization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Sep 12 09:58:28 HKT 2023.
    -->
    insert into t_organization (id, parent_id, no, 
      name, code, unit_code, 
      title, address, latitude, 
      longitude, currency, division, 
      region, power_supply, start_date, 
      end_date, is_completed, jv, 
      total_cfa, sort, scene, 
      area_id, type, creation_time, 
      create_username, create_user_id, last_update_time, 
      last_update_username, last_update_user_id, last_update_version, 
      is_deleted)
    values (#{id,jdbcType=VARCHAR}, #{parentId,jdbcType=VARCHAR}, #{no,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, #{unitCode,jdbcType=VARCHAR}, 
      #{title,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{latitude,jdbcType=VARCHAR}, 
      #{longitude,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR}, #{division,jdbcType=VARCHAR}, 
      #{region,jdbcType=VARCHAR}, #{powerSupply,jdbcType=VARCHAR}, #{startDate,jdbcType=VARCHAR}, 
      #{endDate,jdbcType=VARCHAR}, #{isCompleted,jdbcType=VARCHAR}, #{jv,jdbcType=VARCHAR}, 
      #{totalCfa,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, #{scene,jdbcType=VARCHAR}, 
      #{areaId,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createUsername,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{lastUpdateUsername,jdbcType=VARCHAR}, #{lastUpdateUserId,jdbcType=VARCHAR}, #{lastUpdateVersion,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.Organization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Sep 12 09:58:28 HKT 2023.
    -->
    insert into t_organization
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="no != null">
        no,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="unitCode != null">
        unit_code,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="latitude != null">
        latitude,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="division != null">
        division,
      </if>
      <if test="region != null">
        region,
      </if>
      <if test="powerSupply != null">
        power_supply,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="isCompleted != null">
        is_completed,
      </if>
      <if test="jv != null">
        jv,
      </if>
      <if test="totalCfa != null">
        total_cfa,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="scene != null">
        scene,
      </if>
      <if test="areaId != null">
        area_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="no != null">
        #{no,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="unitCode != null">
        #{unitCode,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="division != null">
        #{division,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        #{region,jdbcType=VARCHAR},
      </if>
      <if test="powerSupply != null">
        #{powerSupply,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="isCompleted != null">
        #{isCompleted,jdbcType=VARCHAR},
      </if>
      <if test="jv != null">
        #{jv,jdbcType=VARCHAR},
      </if>
      <if test="totalCfa != null">
        #{totalCfa,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="scene != null">
        #{scene,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.OrganizationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Sep 12 09:58:28 HKT 2023.
    -->
    select count(*) from t_organization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Sep 12 09:58:28 HKT 2023.
    -->
    update t_organization
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=VARCHAR},
      </if>
      <if test="row.parentId != null">
        parent_id = #{row.parentId,jdbcType=VARCHAR},
      </if>
      <if test="row.no != null">
        no = #{row.no,jdbcType=VARCHAR},
      </if>
      <if test="row.name != null">
        name = #{row.name,jdbcType=VARCHAR},
      </if>
      <if test="row.code != null">
        code = #{row.code,jdbcType=VARCHAR},
      </if>
      <if test="row.unitCode != null">
        unit_code = #{row.unitCode,jdbcType=VARCHAR},
      </if>
      <if test="row.title != null">
        title = #{row.title,jdbcType=VARCHAR},
      </if>
      <if test="row.address != null">
        address = #{row.address,jdbcType=VARCHAR},
      </if>
      <if test="row.latitude != null">
        latitude = #{row.latitude,jdbcType=VARCHAR},
      </if>
      <if test="row.longitude != null">
        longitude = #{row.longitude,jdbcType=VARCHAR},
      </if>
      <if test="row.currency != null">
        currency = #{row.currency,jdbcType=VARCHAR},
      </if>
      <if test="row.division != null">
        division = #{row.division,jdbcType=VARCHAR},
      </if>
      <if test="row.region != null">
        region = #{row.region,jdbcType=VARCHAR},
      </if>
      <if test="row.powerSupply != null">
        power_supply = #{row.powerSupply,jdbcType=VARCHAR},
      </if>
      <if test="row.startDate != null">
        start_date = #{row.startDate,jdbcType=VARCHAR},
      </if>
      <if test="row.endDate != null">
        end_date = #{row.endDate,jdbcType=VARCHAR},
      </if>
      <if test="row.isCompleted != null">
        is_completed = #{row.isCompleted,jdbcType=VARCHAR},
      </if>
      <if test="row.jv != null">
        jv = #{row.jv,jdbcType=VARCHAR},
      </if>
      <if test="row.totalCfa != null">
        total_cfa = #{row.totalCfa,jdbcType=VARCHAR},
      </if>
      <if test="row.sort != null">
        sort = #{row.sort,jdbcType=INTEGER},
      </if>
      <if test="row.scene != null">
        scene = #{row.scene,jdbcType=VARCHAR},
      </if>
      <if test="row.areaId != null">
        area_id = #{row.areaId,jdbcType=VARCHAR},
      </if>
      <if test="row.type != null">
        type = #{row.type,jdbcType=VARCHAR},
      </if>
      <if test="row.creationTime != null">
        creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createUsername != null">
        create_username = #{row.createUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.createUserId != null">
        create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateTime != null">
        last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.lastUpdateUsername != null">
        last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateUserId != null">
        last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateVersion != null">
        last_update_version = #{row.lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="row.isDeleted != null">
        is_deleted = #{row.isDeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Sep 12 09:58:28 HKT 2023.
    -->
    update t_organization
    set id = #{row.id,jdbcType=VARCHAR},
      parent_id = #{row.parentId,jdbcType=VARCHAR},
      no = #{row.no,jdbcType=VARCHAR},
      name = #{row.name,jdbcType=VARCHAR},
      code = #{row.code,jdbcType=VARCHAR},
      unit_code = #{row.unitCode,jdbcType=VARCHAR},
      title = #{row.title,jdbcType=VARCHAR},
      address = #{row.address,jdbcType=VARCHAR},
      latitude = #{row.latitude,jdbcType=VARCHAR},
      longitude = #{row.longitude,jdbcType=VARCHAR},
      currency = #{row.currency,jdbcType=VARCHAR},
      division = #{row.division,jdbcType=VARCHAR},
      region = #{row.region,jdbcType=VARCHAR},
      power_supply = #{row.powerSupply,jdbcType=VARCHAR},
      start_date = #{row.startDate,jdbcType=VARCHAR},
      end_date = #{row.endDate,jdbcType=VARCHAR},
      is_completed = #{row.isCompleted,jdbcType=VARCHAR},
      jv = #{row.jv,jdbcType=VARCHAR},
      total_cfa = #{row.totalCfa,jdbcType=VARCHAR},
      sort = #{row.sort,jdbcType=INTEGER},
      scene = #{row.scene,jdbcType=VARCHAR},
      area_id = #{row.areaId,jdbcType=VARCHAR},
      type = #{row.type,jdbcType=VARCHAR},
      creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      create_username = #{row.createUsername,jdbcType=VARCHAR},
      create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR},
      last_update_version = #{row.lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{row.isDeleted,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.Organization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Sep 12 09:58:28 HKT 2023.
    -->
    update t_organization
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="no != null">
        no = #{no,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="unitCode != null">
        unit_code = #{unitCode,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        latitude = #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        longitude = #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="division != null">
        division = #{division,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        region = #{region,jdbcType=VARCHAR},
      </if>
      <if test="powerSupply != null">
        power_supply = #{powerSupply,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="isCompleted != null">
        is_completed = #{isCompleted,jdbcType=VARCHAR},
      </if>
      <if test="jv != null">
        jv = #{jv,jdbcType=VARCHAR},
      </if>
      <if test="totalCfa != null">
        total_cfa = #{totalCfa,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="scene != null">
        scene = #{scene,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        area_id = #{areaId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.Organization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Sep 12 09:58:28 HKT 2023.
    -->
    update t_organization
    set parent_id = #{parentId,jdbcType=VARCHAR},
      no = #{no,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      unit_code = #{unitCode,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      latitude = #{latitude,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      division = #{division,jdbcType=VARCHAR},
      region = #{region,jdbcType=VARCHAR},
      power_supply = #{powerSupply,jdbcType=VARCHAR},
      start_date = #{startDate,jdbcType=VARCHAR},
      end_date = #{endDate,jdbcType=VARCHAR},
      is_completed = #{isCompleted,jdbcType=VARCHAR},
      jv = #{jv,jdbcType=VARCHAR},
      total_cfa = #{totalCfa,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      scene = #{scene,jdbcType=VARCHAR},
      area_id = #{areaId,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=BIT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>