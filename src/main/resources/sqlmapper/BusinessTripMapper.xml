<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.BusinessTripMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.BusinessTrip">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Aug 10 09:08:11 HKT 2023.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="ambient_head_id" jdbcType="VARCHAR" property="ambientHeadId" />
    <result column="month_value" jdbcType="INTEGER" property="monthValue" />
    <result column="vehicle_type" jdbcType="VARCHAR" property="vehicleType" />
    <result column="start_place" jdbcType="VARCHAR" property="startPlace" />
    <result column="destination" jdbcType="VARCHAR" property="destination" />
    <result column="route_no" jdbcType="VARCHAR" property="routeNo" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="trip_type" jdbcType="INTEGER" property="tripType" />
    <result column="ticket_type" jdbcType="INTEGER" property="ticketType" />
    <result column="person_count" jdbcType="INTEGER" property="personCount" />
    <result column="flight_distance" jdbcType="DECIMAL" property="flightDistance" />
    <result column="carbon_emission" jdbcType="DECIMAL" property="carbonEmission" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="VARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="VARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Aug 10 09:08:11 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Aug 10 09:08:11 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Aug 10 09:08:11 HKT 2023.
    -->
    id, ambient_head_id, month_value, vehicle_type, start_place, destination, route_no, 
    level, trip_type, ticket_type, person_count, flight_distance, carbon_emission, remark, 
    creation_time, create_username, create_user_id, last_update_time, last_update_username, 
    last_update_user_id, last_update_version
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.BusinessTripExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Aug 10 09:08:11 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_ab_business_trip
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Aug 10 09:08:11 HKT 2023.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_ab_business_trip
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Aug 10 09:08:11 HKT 2023.
    -->
    delete from t_ab_business_trip
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.BusinessTripExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Aug 10 09:08:11 HKT 2023.
    -->
    delete from t_ab_business_trip
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.BusinessTrip">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Aug 10 09:08:11 HKT 2023.
    -->
    insert into t_ab_business_trip (id, ambient_head_id, month_value, 
      vehicle_type, start_place, destination, 
      route_no, level, trip_type, 
      ticket_type, person_count, flight_distance, 
      carbon_emission, remark, creation_time, 
      create_username, create_user_id, last_update_time, 
      last_update_username, last_update_user_id, last_update_version
      )
    values (#{id,jdbcType=VARCHAR}, #{ambientHeadId,jdbcType=VARCHAR}, #{monthValue,jdbcType=INTEGER}, 
      #{vehicleType,jdbcType=VARCHAR}, #{startPlace,jdbcType=VARCHAR}, #{destination,jdbcType=VARCHAR}, 
      #{routeNo,jdbcType=VARCHAR}, #{level,jdbcType=VARCHAR}, #{tripType,jdbcType=INTEGER}, 
      #{ticketType,jdbcType=INTEGER}, #{personCount,jdbcType=INTEGER}, #{flightDistance,jdbcType=DECIMAL}, 
      #{carbonEmission,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createUsername,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{lastUpdateUsername,jdbcType=VARCHAR}, #{lastUpdateUserId,jdbcType=VARCHAR}, #{lastUpdateVersion,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.BusinessTrip">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Aug 10 09:08:11 HKT 2023.
    -->
    insert into t_ab_business_trip
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="ambientHeadId != null">
        ambient_head_id,
      </if>
      <if test="monthValue != null">
        month_value,
      </if>
      <if test="vehicleType != null">
        vehicle_type,
      </if>
      <if test="startPlace != null">
        start_place,
      </if>
      <if test="destination != null">
        destination,
      </if>
      <if test="routeNo != null">
        route_no,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="tripType != null">
        trip_type,
      </if>
      <if test="ticketType != null">
        ticket_type,
      </if>
      <if test="personCount != null">
        person_count,
      </if>
      <if test="flightDistance != null">
        flight_distance,
      </if>
      <if test="carbonEmission != null">
        carbon_emission,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="ambientHeadId != null">
        #{ambientHeadId,jdbcType=VARCHAR},
      </if>
      <if test="monthValue != null">
        #{monthValue,jdbcType=INTEGER},
      </if>
      <if test="vehicleType != null">
        #{vehicleType,jdbcType=VARCHAR},
      </if>
      <if test="startPlace != null">
        #{startPlace,jdbcType=VARCHAR},
      </if>
      <if test="destination != null">
        #{destination,jdbcType=VARCHAR},
      </if>
      <if test="routeNo != null">
        #{routeNo,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=VARCHAR},
      </if>
      <if test="tripType != null">
        #{tripType,jdbcType=INTEGER},
      </if>
      <if test="ticketType != null">
        #{ticketType,jdbcType=INTEGER},
      </if>
      <if test="personCount != null">
        #{personCount,jdbcType=INTEGER},
      </if>
      <if test="flightDistance != null">
        #{flightDistance,jdbcType=DECIMAL},
      </if>
      <if test="carbonEmission != null">
        #{carbonEmission,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.BusinessTripExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Aug 10 09:08:11 HKT 2023.
    -->
    select count(*) from t_ab_business_trip
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Aug 10 09:08:11 HKT 2023.
    -->
    update t_ab_business_trip
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=VARCHAR},
      </if>
      <if test="row.ambientHeadId != null">
        ambient_head_id = #{row.ambientHeadId,jdbcType=VARCHAR},
      </if>
      <if test="row.monthValue != null">
        month_value = #{row.monthValue,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleType != null">
        vehicle_type = #{row.vehicleType,jdbcType=VARCHAR},
      </if>
      <if test="row.startPlace != null">
        start_place = #{row.startPlace,jdbcType=VARCHAR},
      </if>
      <if test="row.destination != null">
        destination = #{row.destination,jdbcType=VARCHAR},
      </if>
      <if test="row.routeNo != null">
        route_no = #{row.routeNo,jdbcType=VARCHAR},
      </if>
      <if test="row.level != null">
        level = #{row.level,jdbcType=VARCHAR},
      </if>
      <if test="row.tripType != null">
        trip_type = #{row.tripType,jdbcType=INTEGER},
      </if>
      <if test="row.ticketType != null">
        ticket_type = #{row.ticketType,jdbcType=INTEGER},
      </if>
      <if test="row.personCount != null">
        person_count = #{row.personCount,jdbcType=INTEGER},
      </if>
      <if test="row.flightDistance != null">
        flight_distance = #{row.flightDistance,jdbcType=DECIMAL},
      </if>
      <if test="row.carbonEmission != null">
        carbon_emission = #{row.carbonEmission,jdbcType=DECIMAL},
      </if>
      <if test="row.remark != null">
        remark = #{row.remark,jdbcType=VARCHAR},
      </if>
      <if test="row.creationTime != null">
        creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createUsername != null">
        create_username = #{row.createUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.createUserId != null">
        create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateTime != null">
        last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.lastUpdateUsername != null">
        last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateUserId != null">
        last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateVersion != null">
        last_update_version = #{row.lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Aug 10 09:08:11 HKT 2023.
    -->
    update t_ab_business_trip
    set id = #{row.id,jdbcType=VARCHAR},
      ambient_head_id = #{row.ambientHeadId,jdbcType=VARCHAR},
      month_value = #{row.monthValue,jdbcType=INTEGER},
      vehicle_type = #{row.vehicleType,jdbcType=VARCHAR},
      start_place = #{row.startPlace,jdbcType=VARCHAR},
      destination = #{row.destination,jdbcType=VARCHAR},
      route_no = #{row.routeNo,jdbcType=VARCHAR},
      level = #{row.level,jdbcType=VARCHAR},
      trip_type = #{row.tripType,jdbcType=INTEGER},
      ticket_type = #{row.ticketType,jdbcType=INTEGER},
      person_count = #{row.personCount,jdbcType=INTEGER},
      flight_distance = #{row.flightDistance,jdbcType=DECIMAL},
      carbon_emission = #{row.carbonEmission,jdbcType=DECIMAL},
      remark = #{row.remark,jdbcType=VARCHAR},
      creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      create_username = #{row.createUsername,jdbcType=VARCHAR},
      create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR},
      last_update_version = #{row.lastUpdateVersion,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.BusinessTrip">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Aug 10 09:08:11 HKT 2023.
    -->
    update t_ab_business_trip
    <set>
      <if test="ambientHeadId != null">
        ambient_head_id = #{ambientHeadId,jdbcType=VARCHAR},
      </if>
      <if test="monthValue != null">
        month_value = #{monthValue,jdbcType=INTEGER},
      </if>
      <if test="vehicleType != null">
        vehicle_type = #{vehicleType,jdbcType=VARCHAR},
      </if>
      <if test="startPlace != null">
        start_place = #{startPlace,jdbcType=VARCHAR},
      </if>
      <if test="destination != null">
        destination = #{destination,jdbcType=VARCHAR},
      </if>
      <if test="routeNo != null">
        route_no = #{routeNo,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=VARCHAR},
      </if>
      <if test="tripType != null">
        trip_type = #{tripType,jdbcType=INTEGER},
      </if>
      <if test="ticketType != null">
        ticket_type = #{ticketType,jdbcType=INTEGER},
      </if>
      <if test="personCount != null">
        person_count = #{personCount,jdbcType=INTEGER},
      </if>
      <if test="flightDistance != null">
        flight_distance = #{flightDistance,jdbcType=DECIMAL},
      </if>
      <if test="carbonEmission != null">
        carbon_emission = #{carbonEmission,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.BusinessTrip">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Aug 10 09:08:11 HKT 2023.
    -->
    update t_ab_business_trip
    set ambient_head_id = #{ambientHeadId,jdbcType=VARCHAR},
      month_value = #{monthValue,jdbcType=INTEGER},
      vehicle_type = #{vehicleType,jdbcType=VARCHAR},
      start_place = #{startPlace,jdbcType=VARCHAR},
      destination = #{destination,jdbcType=VARCHAR},
      route_no = #{routeNo,jdbcType=VARCHAR},
      level = #{level,jdbcType=VARCHAR},
      trip_type = #{tripType,jdbcType=INTEGER},
      ticket_type = #{ticketType,jdbcType=INTEGER},
      person_count = #{personCount,jdbcType=INTEGER},
      flight_distance = #{flightDistance,jdbcType=DECIMAL},
      carbon_emission = #{carbonEmission,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>