<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.csci.susdev.mapper.BatchCustomMapper">

    <insert id="insertBatchIgnoreExist">

        insert into t_cf_batch
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="areaId != null">
                area_id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="active != null">
                is_active,
            </if>
            <if test="effectiveDate != null">
                effective_date,
            </if>
            <if test="creationTime != null">
                creation_time,
            </if>
            <if test="createUsername != null">
                create_username,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="lastUpdateTime != null">
                last_update_time,
            </if>
            <if test="lastUpdateUsername != null">
                last_update_username,
            </if>
            <if test="lastUpdateUserId != null">
                last_update_user_id,
            </if>
            <if test="lastUpdateVersion != null">
                last_update_version,
            </if>
        </trim>
        select
        <trim suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="areaId != null">
                #{areaId,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="active != null">
                #{active,jdbcType=BIT},
            </if>
            <if test="effectiveDate != null">
                #{effectiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="creationTime != null">
                #{creationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUsername != null">
                #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUsername != null">
                #{lastUpdateUsername,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateUserId != null">
                #{lastUpdateUserId,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateVersion != null">
                #{lastUpdateVersion,jdbcType=INTEGER},
            </if>
        </trim>
        where not exists (select 1 from t_cf_batch where
        is_active = 1
        and area_id = #{areaId,jdbcType=VARCHAR}
        and year(effective_date) = year(#{effectiveDate,jdbcType=TIMESTAMP})
        and month(effective_date) = month(#{effectiveDate,jdbcType=TIMESTAMP})
        )


    </insert>

    <select id="selectBatchBy" resultMap="com.csci.susdev.mapper.BatchMapper.BaseResultMap">
        select * from t_cf_batch
        where is_active = 1
        and area_id = #{areaId,jdbcType=VARCHAR}
        and year(effective_date) = #{year}
        and month(effective_date) = #{month}
    </select>
</mapper>