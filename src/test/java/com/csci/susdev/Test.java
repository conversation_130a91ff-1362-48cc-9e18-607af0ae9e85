//package com.csci.susdev;
//
//import com.csci.susdev.mapper.OperationMapper;
//import com.csci.susdev.mapper.PermissionOperationMapper;
//import com.csci.susdev.model.Operation;
//import com.csci.susdev.model.PermissionOperation;
//import com.csci.susdev.service.MenuService;
//import com.csci.susdev.service.PermissionOperationService;
//import io.swagger.v3.oas.models.OpenAPI;
//import io.swagger.v3.oas.models.PathItem;
//import io.swagger.v3.oas.models.Paths;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
//import javax.annotation.Resource;
//import java.util.Map;
//import java.util.UUID;
//
//@SpringBootTest
//@ActiveProfiles("bsj")
//public class Test{
//    @Resource
//    private OperationMapper operationMapper;
//    @Resource
//    @Autowired
//    private PermissionOperationMapper permissionOperationMapper;
//    @Resource
//    private MenuService menuService;
//    @org.junit.jupiter.api.Test
//     void test() {
//        String path = "/Users/<USER>/IdeaProjects/sus-dev/src/test/sus_dev_openapi.yaml";
//        // 解析OpenAPI文件
//        SwaggerParseResult result = new OpenAPIV3Parser().readLocation(path, null, null);
//        OpenAPI openAPI = result.getOpenAPI();
//
//        if (openAPI != null) {
//            // 获取路径信息
//            Paths paths = openAPI.getPaths();
//            for (Map.Entry<String, PathItem> entry : paths.entrySet()) {
//                String url = entry.getKey();
//                if (url.contains("sus-dev-api")) {
//                    continue;
//                }
//                Operation operation = new Operation();
//                String replace = url.replace("/service", "");
//                operation.setUrl(replace);
//                operation.setId(UUID.randomUUID().toString());
//                PathItem pathItem = entry.getValue();
//
//                // 获取路径的描述信息
//                String description = pathItem.getDescription();
//
//                // 输出URL和描述信息
//                System.out.println("URL: " + url);
//                System.out.println("Description: " + (description != null ? description : "No description"));
//
//                // 获取各个HTTP方法的描述信息
//                if (pathItem.getGet() != null) {
//                    System.out.println("GET Description: " + (pathItem.getGet().getDescription() != null ? pathItem.getGet().getDescription() : "No description"));
//                    operation.setMethod("GET");
//                    operation.setDescription(pathItem.getGet().getDescription());
//                }
//                if (pathItem.getPost() != null) {
//                    System.out.println("POST Description: " + (pathItem.getPost().getDescription() != null ? pathItem.getPost().getDescription() : "No description"));
//                    operation.setMethod("POST");
//                    operation.setDescription(pathItem.getPost().getDescription());
//                }
//                if (pathItem.getPut() != null) {
//                    operation.setMethod("PUT");
//                    operation.setDescription(pathItem.getPut().getDescription());
//                    System.out.println("PUT Description: " + (pathItem.getPut().getDescription() != null ? pathItem.getPut().getDescription() : "No description"));
//                }
//                if (pathItem.getDelete() != null) {
//                    operation.setMethod("DELETE");
//                    operation.setDescription(pathItem.getDelete().getDescription());
//                    System.out.println("DELETE Description: " + (pathItem.getDelete().getDescription() != null ? pathItem.getDelete().getDescription() : "No description"));
//                }
//                operationMapper.insert(operation);
//            }
//
//        } else {
//            System.out.println("Failed to parse OpenAPI document");
//        }
//    }
//
//}
