package com.csci.susdev.login;

import com.csci.susdev.BaseTest;
import com.csci.susdev.vo.LoginVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class LoginFacadeTest extends BaseTest {

    @Autowired
    private LoginFacade loginFacade;

    @Test
    void login() throws Exception {
        LoginVO loginVO = new LoginVO();
        loginVO.setUsername("tao_li");
        loginVO.setPassword("taoli@3311");
        loginFacade.login(loginVO);
    }

    @Test
    void performLogin() {
        // 通过这里可以无需输入验证码就登陆成功并生成session
        loginFacade.performLogin("", "");
    }
}