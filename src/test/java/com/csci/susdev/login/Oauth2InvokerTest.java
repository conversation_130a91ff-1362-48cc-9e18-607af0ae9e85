package com.csci.susdev.login;

import com.csci.susdev.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("prod")
class Oauth2InvokerTest extends BaseTest {

    @Resource
    private Oauth2Invoker oauth2Invoker;

    @Test
    void requestAccessToken() {

        oauth2Invoker.requestAccessToken("zouxia", "Zx2022@@");
    }
}