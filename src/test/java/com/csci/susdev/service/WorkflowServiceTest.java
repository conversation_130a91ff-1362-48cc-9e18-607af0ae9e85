package com.csci.susdev.service;

import com.csci.susdev.BaseTest;
import com.csci.susdev.qo.WorkflowQO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

class WorkflowServiceTest extends BaseTest {

    @Resource
    WorkflowService workflowService;

    @Test
    void deleteWorkflow() {
        workflowService.deleteWorkflow("8492301e-0fcb-4ae3-8861-62bc9666af54");
    }

    @Test
    void listWorkflow() {
        WorkflowQO workflowQO = new WorkflowQO();
        workflowService.listWorkflow(workflowQO);
    }

    @Test
    void findWorkflowById() {
        workflowService.findWorkflowById("f2abb892-7d42-4e68-ac39-57a1d4658e2f");
    }
}