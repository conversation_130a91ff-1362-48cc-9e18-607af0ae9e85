package com.csci.susdev.service;

import com.csci.susdev.BaseTest;
import com.csci.susdev.model.Permission;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.model.Role;
import com.csci.susdev.model.RolePermission;
import com.csci.susdev.qo.PermissionPageableQO;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

@ActiveProfiles("prod")
class RolePermissionServiceTest extends BaseTest {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(RolePermissionServiceTest.class);

    @Resource
    RolePermissionService rolePermissionService;

    @Resource
    private RoleService roleService;

    @Resource
    private PermissionService permissionService;

    @Test
    void saveRolePermission() {
        /*Role role = roleService.getRoleByCode("SuperAdmin");
        PermissionPageableQO permissionPageableQO = new PermissionPageableQO();
        permissionPageableQO.setPageSize(0);

        ResultPage<Permission> lstPermission = permissionService.listPermission(permissionPageableQO);
        logger.info("lstPermission: {}", lstPermission.getSize());

        for (Permission permission : lstPermission.getList()) {
            RolePermission rolePermission = new RolePermission();
            // rolePermission.setId();
            rolePermission.setRoleId(role.getId());
            rolePermission.setPermissionId(permission.getId());

            rolePermissionService.saveRolePermission(rolePermission);
        }*/

    }
}