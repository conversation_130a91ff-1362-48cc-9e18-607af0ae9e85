package com.csci.susdev.service;

import com.csci.susdev.BaseTest;
import com.csci.susdev.qo.BusinessTripQO;
import com.csci.susdev.vo.BusinessTripVO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;

class BusinessTripServiceTest extends BaseTest {

    @Resource
    BusinessTripService businessTripService;

    @Test
    void saveBusinessTrip() {
        BusinessTripVO businessTripVO = new BusinessTripVO();
        // businessTripVO.setId();
        businessTripVO.setAmbientHeadId("f6be0353-7c6d-45a7-a36d-d683cbaef1e4");
        businessTripVO.setStartPlace("北京");
        businessTripVO.setDestination("上海");
        businessTripVO.setLevel("1");
        //businessTripVO.setTripType(1);
        businessTripVO.setTicketType(1);
        businessTripVO.setPersonCount(1);
        businessTripVO.setFlightDistance(new BigDecimal(1000));
        businessTripVO.setRemark("备注");
        businessTripVO.setMonthValue(1);
        // businessTripVO.setLastUpdateVersion();

        businessTripService.saveBusinessTrip(businessTripVO);
    }

    @Test
    void batchAdd() {
        for (int i = 0; i < 100; i++) {
            BusinessTripVO businessTripVO = new BusinessTripVO();
            // businessTripVO.setId();
            businessTripVO.setAmbientHeadId("f6be0353-7c6d-45a7-a36d-d683cbaef1e4");
            businessTripVO.setStartPlace("北京");
            businessTripVO.setDestination("上海");
            businessTripVO.setLevel(String.valueOf(i));
            //businessTripVO.setTripType(i);
            businessTripVO.setTicketType(i);
            businessTripVO.setPersonCount(i);
            businessTripVO.setFlightDistance(new BigDecimal(100 * i));
            businessTripVO.setRemark("备注");
            businessTripVO.setMonthValue(1);
            // businessTripVO.setLastUpdateVersion();

            businessTripService.saveBusinessTrip(businessTripVO);
        }
    }

    @Test
    void listBusinessTrip() {
        businessTripService.listBusinessTrip(new BusinessTripQO());
    }
}