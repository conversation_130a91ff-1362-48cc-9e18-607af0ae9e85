package com.csci.susdev.service;

import com.csci.susdev.BaseTest;
import com.csci.susdev.model.Batch;
import com.csci.susdev.qo.BatchIdPageQO;
import com.csci.susdev.vo.AirPollutantsVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class AirPollutantsServiceTest extends BaseTest {

    @Autowired
    AirPollutantsService airPollutantsService;

    @Autowired
    BatchService batchService;

    @Test
    void addAirPollutants() {

        Batch batch = batchService.getOrInitBatch("0ad1cf7c-4550-44f6-bc8b-5d0a2f8eb9ff", 2022, 9);

        for (int i = 0; i < 100; i++) {
            AirPollutantsVO airPollutantsVO = new AirPollutantsVO();
            // airPollutantsVO.setId();
            airPollutantsVO.setBatchId(batch.getId());
            airPollutantsVO.setCategory("Category" + i);
            airPollutantsVO.setEmissionSource("EmissionSource" + i);
            airPollutantsVO.setType("Type" + i);
            airPollutantsVO.setUnit("Unit" + i);
            airPollutantsVO.setAirPollutionType("AirPollutionType" + i);
            airPollutantsVO.setVehicleType("VehicleType" + i);
            airPollutantsVO.setVehicleEmissionStandard("VehicleEmissionStandard" + i);
            airPollutantsVO.setAirPolEmiFactor("" + i);
            airPollutantsVO.setAirPolEmiUnit("AirPolEmiUnit" + i);
            // airPollutantsVO.setLastUpdateVersion( );
            airPollutantsService.addAirPollutants(airPollutantsVO);
        }
    }

    @Test
    void listAirPollutantsByPage() {
        Batch batch = batchService.getOrInitBatch("0ad1cf7c-4550-44f6-bc8b-5d0a2f8eb9ff", 2022, 9);
        BatchIdPageQO batchIdPageQO = new BatchIdPageQO();
        batchIdPageQO.setBatchId(batch.getId());
        airPollutantsService.listAirPollutantsByPage(batchIdPageQO);
    }
}