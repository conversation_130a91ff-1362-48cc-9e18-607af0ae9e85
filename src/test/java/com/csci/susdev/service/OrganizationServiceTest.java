package com.csci.susdev.service;

import com.csci.susdev.BaseTest;
import com.csci.susdev.model.Organization;
import com.csci.susdev.model.User;
import com.csci.susdev.qo.KeywordPageQO;
import com.csci.susdev.qo.KeywordQO;
import com.csci.susdev.util.context.RequestContextManager;
import com.csci.susdev.util.context.impl.RequestContext;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.susdev.vo.OrganizationVO;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;

@ActiveProfiles("prod")
class OrganizationServiceTest extends BaseTest {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(OrganizationServiceTest.class);

    @Resource
    OrganizationService organizationService;

    @Resource
    private UserService userService;

    @Test
    void generateNo() {
        // organizationService.generateNo("17087670-7555-48d2-a743-59a3ad413f04");
    }

    @Test
    void listOrgByParentId() {
        UserInfo userInfo = new UserInfo();
        userInfo.setId("fc51cc0e-07fe-47ca-8daa-ae2db5ca0c90");
        userInfo.setUsername("tao_li");
        userInfo.setRoles(Collections.singletonList("superAdmin"));
        RequestContextManager.setCurrent(new RequestContext(new HashMap<>(), userInfo));
        organizationService.listOrgByParentId("17087670-7555-48d2-a743-59a3ad413f04", 1, 10);
    }

    @Test
    void listOrganizationTreeByParentId() {
        Organization organization = organizationService.getOrganizationByName("中建香港");
        organizationService.listOrganizationTreeByParentId(organization.getId());
    }


    @Test
    void saveOrganization() {
        OrganizationVO organizationVO = new OrganizationVO();
        organizationVO.setName("中建香港");
        organizationService.saveOrganization(organizationVO);

        organizationVO = new OrganizationVO();
        organizationVO.setName("中建澳門");
        organizationService.saveOrganization(organizationVO);
    }

    /**
     * 这个方法会把所有的组织机构的编号都重新生成一遍
     * 目前只在测试方法中使用，用于手动生成
     * 一般是因为经常手动帮助用户删除一些组织机构，导致编号不连续
     */
    @Test
    void reGenerateNo() {
        organizationService.reGenerateNo();
    }

    @Test
    void listDeptByUserId() {
        User user = userService.getUserByUsername("admin");
        organizationService.listDeptByUserId(user.getId());
    }

    @Test
    void listAllOrgs() {
        KeywordPageQO keywordPageQO = new KeywordPageQO();
        keywordPageQO.setKeyword("中");
        organizationService.listAllOrgs(keywordPageQO);
    }

    @Test
    void listCompanyDeptByPage() {
        KeywordQO keywordQO = new KeywordQO();
        keywordQO.setKeyword("中");
        organizationService.listCompanyDeptByPage(keywordQO);
    }

    @Test
    void listCompanies() {
        organizationService.listTopCompanies();
    }

    @Test
    void findOrganizationChildren() {
        organizationService.findLeafChildrenByOrgId("************************************");
    }

    @Test
    void findLeafChildrenByNo() {
    }
}