package com.csci.susdev.facade;

import com.csci.susdev.BaseTest;
import com.csci.susdev.model.Organization;
import com.csci.susdev.model.User;
import com.csci.susdev.service.OrganizationService;
import com.csci.susdev.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("prod")
class UserFacadeTest extends BaseTest {

    @Resource
    private UserFacade userFacade;

    @Resource
    private UserService userService;

    @Resource
    private OrganizationService organizationService;

    @Test
    void addOrgWithChildToUser() {
        User user = userService.getUserByUsername("ida.huang");
        Organization organization = organizationService.getOrganizationByName("中建香港");

        userFacade.addOrgWithChildToUser(organization.getId(), user.getId());
    }
}