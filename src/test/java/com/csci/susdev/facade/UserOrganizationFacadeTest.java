package com.csci.susdev.facade;

import com.csci.susdev.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("prod")
class UserOrganizationFacadeTest extends BaseTest {

    @Resource
    UserOrganizationFacade userOrganizationFacade;

    @Test
    void addAllOrgsToUser() {
        userOrganizationFacade.addAllOrgsToUser("ida.huang");
    }
}