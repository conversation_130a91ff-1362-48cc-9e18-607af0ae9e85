package com.csci.susdev.facade;

import com.csci.susdev.BaseTest;
import com.csci.susdev.model.Organization;
import com.csci.susdev.model.WorkflowControl;
import com.csci.susdev.service.OrganizationService;
import com.csci.susdev.service.WorkflowControlService;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@ActiveProfiles("prod")
class WorkflowFacadeTest extends BaseTest {

    @Resource
    WorkflowFacade workflowFacade;

    @Resource
    private WorkflowControlService workflowControlService;

    @Resource
    private OrganizationService organizationService;

    @Test
    void initWorkflow() {
        workflowFacade.initWorkflow("SocialPerformanceOne", "社会绩效一审批流程");
    }

    @Test
    void doApprove() {
        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId("9938c2c7-88e8-440d-8012-d30fd5d6a4c4");
        workflowFacade.doApprove(workflowControl, "", false);
    }

    @Test
    void finishAllAmbient() {
        workflowFacade.finishAllAmbient(2022, 1);
    }

    @Test
    void removeAllAmbientWorkflowOfOrgWithChildren() {
        Organization organization = organizationService.getOrganizationByName("中建澳門");
        System.out.println("parent name: " + organization.getName());

        workflowFacade.removeAllAmbientWorkflowOfOrgWithChildren(organization.getId(), 2022);
    }

    @Test
    void finishSpecifiedAmbientByOrgIdAndYear() {
        List<String> orgNames = Arrays.asList(
                "CED-搬遷沙田污水處理廠往岩洞-主體岩洞建造工程"
        );

        for (String orgName : orgNames) {
            Organization organization = organizationService.getOrganizationByName(orgName);
            workflowFacade.finishSpecifiedAmbientByOrgIdAndYear(organization.getId(), 2022);
        }
    }

    @Test
    void finishOne() {
        workflowFacade.finishSpecifiedAmbientByOrgIdAndYear("50f0df91-0ba3-4be2-8236-55c59e5a071e", 2022);
    }

}