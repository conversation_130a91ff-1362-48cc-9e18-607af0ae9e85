package com.csci.susdev.util.userinit;

import com.csci.susdev.BaseTest;
import com.csci.susdev.util.orginit.OrganizationInitializer;
import com.google.common.base.Splitter;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@ActiveProfiles("prod")
class UserInitializerTest extends BaseTest {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(UserInitializerTest.class);

    @Resource
    private UserInitializer userInitializer;

    @Resource
    private OrganizationInitializer organizationInitializer;

    @Test
    void testInit() {
        userInitializer.init();
    }

    @Test
    void initCompanyWithUser() {
        List<String> list = new ArrayList<>();

        list.add("安徽公司\t中建国际工程总部项目\t彭晋\<EMAIL>\t18662861989\tC\n");

        for (String s : list) {
            try {
                initByString(s);
            } catch (Exception e) {
                logger.error("初始化账号出错, {}", s, e);
            }
        }
    }

    private void initByString(String targetStr) throws Exception {
        // Iterator<String> it = splitter.split(targetStr).iterator();
        // List<String> list = StreamSupport.stream(splitter.split(targetStr).spliterator(), false).collect(Collectors.toList());
        String[] arr = StringUtils.split(targetStr, "\t");
        if (arr.length == 0 || arr.length == 1) {
            arr = StringUtils.split(targetStr, " ");
        }

        String companyName = StringUtils.trim(arr[0]);
        String deptName = StringUtils.trim(arr[1]);
        String name = StringUtils.trim(arr[2]);
        String email = StringUtils.trim(arr[3]);
        String mobile = StringUtils.trim(arr[4]);
        String position = StringUtils.trim(arr[5]);

        organizationInitializer.initByOrgName(companyName, deptName);

        UserData data = new UserData();
        data.setCompanyName(companyName);
        data.setDeptName(deptName);
        data.setName(name);
        data.setEmail(email);
        data.setMobile(mobile);
        data.setPosition(position);
        userInitializer.initByUserData(data);
    }

    @Test
    void initByUserData() throws Exception {
        UserData data = new UserData();
        data.setCompanyName("房屋公司");
        data.setDeptName("九龍何文田山谷道15-21號住宅重建");
        data.setName("梁凱琪");
        data.setEmail("<EMAIL>");
        data.setMobile("********");
        data.setPosition("高級環保主任");

        organizationInitializer.initByOrgName(data.getCompanyName(), data.getDeptName());
        userInitializer.initByUserData(data);
    }

    @Test
    void initByFileName() {
        userInitializer.initByFileName("users/AccountTemplate.xlsx");
    }

}