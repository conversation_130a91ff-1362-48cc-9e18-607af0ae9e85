package com.csci.susdev.util;

import cn.smallbun.screw.core.Configuration;
import cn.smallbun.screw.core.engine.EngineConfig;
import cn.smallbun.screw.core.engine.EngineFileType;
import cn.smallbun.screw.core.engine.EngineTemplateType;
import cn.smallbun.screw.core.execute.DocumentationExecute;
import cn.smallbun.screw.core.process.ProcessConfig;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;

import javax.sql.DataSource;

import java.util.ArrayList;

public class ScrewTest {

	/*
	@Value("${datasource.susdev.hikari.maximum-pool-size}")
	private String minimumIdle;

	@Value("${datasource.susdev.hikari.minimum-idle}")
	private String maximumPoolSize;

	@Value("${datasource.susdev.hikari.driver-class-name}")
	private String driverClassName;

	@Value("${datasource.susdev.hikari.jdbc-url}")
	private String jdbcUrl;

	@Value("${datasource.susdev.hikari.username}")
	private String username;

	@Value("${datasource.susdev.hikari.password}")
	private String password;
	*/

    @Test
    void testScrew() {
        try {
            // 資料來源
            HikariConfig hikariConfig = new HikariConfig();
            /*
            hikariConfig.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            hikariConfig.setJdbcUrl("************************************************;");;
            hikariConfig.setUsername("esguser");
            hikariConfig.setPassword("Csci3311#");
             */
            hikariConfig.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            hikariConfig.setJdbcUrl("************************************************************************************************;");
            hikariConfig.setUsername("esg");
            hikariConfig.setPassword("7kL8Q57S3hK5R7ls");
            // 設定可以獲取tables remarks資訊
            hikariConfig.addDataSourceProperty("useInformationSchema", "true");
            hikariConfig.setMinimumIdle(5);
            hikariConfig.setMaximumPoolSize(20);
            DataSource dataSource = new HikariDataSource(hikariConfig);

            // 生成配置
            EngineConfig engineConfig = EngineConfig.builder()
                    // 生成檔案路徑
                    .fileOutputDir("d://")
                    // 開啟目錄
                    .openOutputDir(true)
                    // 生成檔案型別：HTML
                    .fileType(EngineFileType.WORD)
                    // 生成模板實現
                    .produceType(EngineTemplateType.freemarker).build();

            // 忽略表
            ArrayList<String> ignoreTableName = new ArrayList<>();
            ignoreTableName.add("test_user");
            ignoreTableName.add("test_group");
            // 忽略表字首
            ArrayList<String> ignorePrefix = new ArrayList<>();
            ignorePrefix.add("test_");
            // 忽略表字尾
            ArrayList<String> ignoreSuffix = new ArrayList<>();
            ignoreSuffix.add("_test");
            ProcessConfig processConfig = ProcessConfig.builder()
                    // 指定生成邏輯、當存在指定表、指定表字首、指定表字尾時，將生成指定表，其餘表不生成、並跳過忽略表配置
                    // 根據名稱指定表生成
                    .designatedTableName(new ArrayList<>())
                    // 根據表字首生成
                    .designatedTablePrefix(new ArrayList<>())
                    // 根據表字尾生成
                    .designatedTableSuffix(new ArrayList<>())
                    // 忽略表名
                    .ignoreTableName(ignoreTableName)
                    // 忽略表字首
                    .ignoreTablePrefix(ignorePrefix)
                    // 忽略表字尾
                    .ignoreTableSuffix(ignoreSuffix).build();
            // 配置
            Configuration config = Configuration.builder()
                    // 版本
                    .version("1.0.0")
                    // 描述,文件名稱
                    .description("ESG AI問答數據結構")
                    // 資料來源
                    .dataSource(dataSource)
                    // 生成配置
                    .engineConfig(engineConfig)
                    // 生成配置
                    .produceConfig(processConfig).build();
            // 執行生成
            new DocumentationExecute(config).execute();
        } catch(Exception e) {
            e.printStackTrace();
        }
    }
}