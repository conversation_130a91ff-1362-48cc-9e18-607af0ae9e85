# 🌱 SUS-DEV 可持續發展管理系統 - 系統需求文檔

<div align="center">

![ESG Management System](https://img.shields.io/badge/ESG-Management%20System-green?style=for-the-badge&logo=leaf&logoColor=white)
![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.6.6-brightgreen?style=flat-square&logo=spring)
![Java](https://img.shields.io/badge/Java-17-orange?style=flat-square&logo=java)
![SQL Server](https://img.shields.io/badge/SQL%20Server-Database-blue?style=flat-square&logo=microsoft-sql-server)

**企業級 ESG（環境、社會、治理）可持續發展管理平台**

*致力於推動企業數字化轉型，實現碳中和目標，提升可持續發展績效*

</div>

---

## 📋 文檔目錄

### 🎯 **第一部分：系統概覽**
1. [🏢 項目概述與業務價值](#1-項目概述與業務價值)
2. [🏗️ 系統架構設計](#2-系統架構設計)
3. [📊 業務背景與 ESG 價值](#3-業務背景與-esg-價值)

### 🔧 **第二部分：功能模塊**
4. [🌍 碳中和管理模塊](#4-碳中和管理模塊)
5. [🌿 環境績效管理模塊](#5-環境績效管理模塊)
6. [👥 社會績效管理模塊](#6-社會績效管理模塊)
7. [🤖 智能文檔識別模塊](#7-智能文檔識別模塊)
8. [⚙️ 工作流與權限管理](#8-工作流與權限管理)
9. [📈 數據分析與 BI 大屏](#9-數據分析與-bi-大屏)

### 🛠️ **第三部分：技術實現**
10. [🗄️ 數據模型與架構](#10-數據模型與架構)
11. [🔌 API 接口設計](#11-api-接口設計)
12. [🔒 安全認證機制](#12-安全認證機制)
13. [🌐 第三方系統集成](#13-第三方系統集成)

### 📋 **第四部分：實施與運維**
14. [🚀 系統部署與配置](#14-系統部署與配置)
15. [📊 性能監控與優化](#15-性能監控與優化)
16. [🔄 業務流程設計](#16-業務流程設計)
17. [📈 系統擴展性設計](#17-系統擴展性設計)

---

## 1. 🏢 項目概述與業務價值

### 1.1 💡 系統簡介

**SUS-DEV 可持續發展管理系統** 是一個基於 Spring Boot 的企業級 ESG（環境、社會、治理）管理平台，專注於幫助企業實現可持續發展目標，提升 ESG 績效表現。系統通過數字化手段，為企業提供全方位的碳中和管理、環境績效監控、社會責任追蹤和可持續發展數據分析服務。

#### 🎯 系統定位
- **戰略層面**：支撐企業 ESG 戰略規劃與執行
- **管理層面**：提供精細化的環境與社會績效管理
- **操作層面**：實現數據自動化收集與智能分析
- **合規層面**：確保符合國際 ESG 標準與法規要求

### 1.2 🌟 核心特性與價值主張

#### 🌍 **環境管理 (Environmental)**
| 功能特性 | 業務價值 | 量化效益 |
|---------|---------|---------|
| **碳排放計算與追蹤** | 精確掌握企業碳足跡 | 提升數據準確性 95%+ |
| **能源消耗智能監控** | 識別節能機會點 | 降低能源成本 10-15% |
| **廢物管理優化** | 提升資源利用效率 | 減少廢物處理成本 20%+ |
| **水資源使用統計** | 優化水資源配置 | 節約用水成本 8-12% |

#### 👥 **社會責任 (Social)**
| 功能特性 | 業務價值 | 量化效益 |
|---------|---------|---------|
| **員工通勤碳足跡** | 推動綠色出行文化 | 減少通勤排放 15%+ |
| **社會績效指標管理** | 提升企業社會形象 | 增強品牌價值 |
| **供應鏈可持續性** | 構建綠色供應鏈 | 降低供應鏈風險 |

#### 🏛️ **治理優化 (Governance)**
| 功能特性 | 業務價值 | 量化效益 |
|---------|---------|---------|
| **智能工作流管理** | 提升審批效率 | 縮短流程時間 50%+ |
| **權限精細化控制** | 確保數據安全性 | 降低合規風險 |
| **審計追蹤機制** | 滿足監管要求 | 提升合規效率 30%+ |

### 1.3 🛠️ 技術架構棧

#### 📊 **核心技術選型對比**

| 技術領域 | 選型方案 | 版本 | 選型理由 | 替代方案 |
|---------|---------|------|---------|---------|
| **🔧 後端框架** | Spring Boot | 2.6.6 | 成熟穩定、生態豐富 | Spring Cloud |
| **☕ Java 版本** | OpenJDK | 17 | LTS 版本、性能優化 | Java 11/21 |
| **🗄️ 主數據庫** | SQL Server | 2019+ | 企業級、事務支持 | PostgreSQL |
| **🔄 ORM 框架** | MyBatis Plus | 3.5+ | 靈活、性能優秀 | JPA/Hibernate |
| **⚡ 緩存系統** | Redis | 6.2+ | 高性能、豐富數據結構 | Hazelcast |
| **📁 文件存儲** | MinIO | Latest | 分佈式、S3 兼容 | AWS S3 |
| **📚 API 文檔** | Swagger/OpenAPI | 3.0 | 標準化、自動生成 | Postman |
| **🤖 OCR 服務** | TiOCR + HiAgent AI | - | 高精度、智能分析 | 百度 OCR |

#### 🏗️ **架構特點**

```mermaid
graph LR
    A[🌐 微服務架構] --> B[📊 多數據源支持]
    B --> C[⚡ 高性能緩存]
    C --> D[🔒 安全認證]
    D --> E[🤖 AI 智能分析]
    E --> F[📈 實時數據處理]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#f1f8e9
```

### 1.4 🎯 業務目標與 KPI

#### 📈 **系統實施目標**

| 目標類別 | 具體指標 | 目標值 | 測量方式 |
|---------|---------|--------|---------|
| **⚡ 效率提升** | 數據收集自動化率 | 85%+ | 自動 vs 手動數據量比 |
| **📊 準確性** | 數據準確率 | 98%+ | 人工驗證 vs 系統計算 |
| **⏱️ 響應速度** | 報表生成時間 | <30秒 | 系統性能監控 |
| **💰 成本節約** | 人力成本降低 | 40%+ | 實施前後人力投入對比 |
| **🔒 合規性** | 審計通過率 | 100% | 外部審計結果 |

#### 🌱 **ESG 績效目標**

```mermaid
pie title ESG 績效提升目標分佈
    "碳排放減少" : 35
    "能源效率提升" : 25
    "廢物減量" : 20
    "水資源節約" : 12
    "社會責任提升" : 8
```

---

## 2. 🏗️ 系統架構設計

### 2.1 🎨 整體架構概覽

系統採用 **分層架構 + 微服務** 的設計理念，確保高可用性、可擴展性和可維護性。

#### 🏛️ **架構設計原則**

| 設計原則 | 實現方式 | 技術選型 | 業務價值 |
|---------|---------|---------|---------|
| **🔄 高可用性** | 多實例部署、故障轉移 | Spring Boot + Redis | 系統可用性 99.9%+ |
| **📈 可擴展性** | 微服務架構、水平擴展 | Docker + K8s | 支持業務快速增長 |
| **🔒 安全性** | 多層安全防護 | JWT + OAuth2 | 保障數據安全 |
| **⚡ 高性能** | 緩存優化、異步處理 | Redis + 消息隊列 | 響應時間 <500ms |

#### 🌐 **系統架構全景圖**

```mermaid
graph TB
    subgraph "🖥️ 用戶交互層"
        A1[💻 Web 管理端<br/>React/Vue]
        A2[📊 BI 數據大屏<br/>ECharts/D3]
        A3[📱 移動端應用<br/>React Native]
        A4[🔌 第三方系統<br/>API 集成]
    end

    subgraph "🌐 API 網關層"
        B1[🚪 Spring Boot Controller<br/>RESTful API]
        B2[🔐 認證攔截器<br/>AuthenticateInterceptor]
        B3[🛡️ 權限攔截器<br/>AuthorityInterceptor]
        B4[📝 日誌攔截器<br/>LogMethod]
    end

    subgraph "⚙️ 業務服務層"
        C1[🌍 碳中和服務<br/>TzhService]
        C2[🌿 環境績效服務<br/>AmbientService]
        C3[👥 社會績效服務<br/>SocialPerformanceService]
        C4[🤖 OCR 識別服務<br/>TiOcrService]
        C5[⚡ 工作流服務<br/>WorkflowService]
        C6[👤 用戶管理服務<br/>UserService]
        C7[📁 文件服務<br/>FileService]
        C8[📊 報表服務<br/>ReportService]
    end

    subgraph "🗄️ 數據訪問層"
        D1[🔄 MyBatis Plus<br/>ORM 框架]
        D2[🔀 多數據源配置<br/>DynamicDataSource]
        D3[⚡ Redis 緩存<br/>RedisTemplate]
        D4[📊 數據計算引擎<br/>Calculation Engine]
    end

    subgraph "💾 數據存儲層"
        E1[(🗄️ SQL Server 主庫<br/>ESG 業務數據)]
        E2[(🌍 SQL Server 碳中和庫<br/>TZH 專用數據)]
        E3[(⚡ Redis 緩存<br/>會話 & 緩存數據)]
        E4[(📁 MinIO 文件存儲<br/>文檔 & 圖片)]
    end

    subgraph "🌐 外部服務層"
        F1[🔐 OAuth2 認證服務<br/>企業 SSO]
        F2[💬 飛書 SSO 服務<br/>統一登錄]
        F3[👁️ TiOCR 識別 API<br/>文檔識別]
        F4[🤖 HiAgent AI 服務<br/>智能分析]
        F5[🌍 碳足跡計算 API<br/>第三方計算]
    end

    %% 連接關係
    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1

    B1 --> B2
    B2 --> B3
    B3 --> B4
    B4 --> C1
    B4 --> C2
    B4 --> C3
    B4 --> C4
    B4 --> C5
    B4 --> C6
    B4 --> C7
    B4 --> C8

    C1 --> D1
    C2 --> D1
    C3 --> D1
    C4 --> D1
    C5 --> D1
    C6 --> D1
    C7 --> D1
    C8 --> D1

    D1 --> D2
    D2 --> E1
    D2 --> E2
    D1 --> D3
    D3 --> E3
    C7 --> E4
    C8 --> D4

    B2 --> F1
    B2 --> F2
    C4 --> F3
    C4 --> F4
    C1 --> F5

    %% 樣式設置
    classDef userLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef apiLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef serviceLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef dataLayer fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef storageLayer fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef externalLayer fill:#f1f8e9,stroke:#689f38,stroke-width:2px

    class A1,A2,A3,A4 userLayer
    class B1,B2,B3,B4 apiLayer
    class C1,C2,C3,C4,C5,C6,C7,C8 serviceLayer
    class D1,D2,D3,D4 dataLayer
    class E1,E2,E3,E4 storageLayer
    class F1,F2,F3,F4,F5 externalLayer
```

### 2.2 🧩 模塊劃分與職責

#### 2.2.1 🎯 核心業務模塊矩陣

| 模塊名稱 | 包路徑 | 核心功能 | 業務價值 | 技術複雜度 |
|---------|--------|---------|---------|-----------|
| **🌍 碳中和管理** | `com.csci.cohl.*` | 碳排放計算、減排管理、碳足跡追蹤 | ⭐⭐⭐⭐⭐ | 🔴 高 |
| **🌿 環境績效** | `com.csci.susdev.service.Ambient*` | 環境數據收集、能源消耗統計 | ⭐⭐⭐⭐ | 🟡 中 |
| **👥 社會績效** | `com.csci.susdev.service.Social*` | 員工通勤、社會責任指標管理 | ⭐⭐⭐ | 🟡 中 |
| **🤖 OCR 識別** | `com.csci.susdev.service.TiOcr*` | 文檔識別、AI 數據提取 | ⭐⭐⭐⭐ | 🔴 高 |
| **⚡ 工作流管理** | `com.csci.susdev.service.Workflow*` | 審批流程、狀態管理 | ⭐⭐⭐ | 🟡 中 |
| **👤 用戶權限** | `com.csci.susdev.service.User*` | 用戶管理、角色權限、組織架構 | ⭐⭐⭐⭐ | 🟡 中 |
| **📊 數據分析** | `com.csci.cohl.service.*Statistics*` | BI 分析、報表生成、數據可視化 | ⭐⭐⭐⭐⭐ | 🔴 高 |
| **🗂️ 基礎數據** | `com.csci.susdev.service.*Factor*` | 排放因子、區域管理、批次管理 | ⭐⭐ | 🟢 低 |

#### 2.2.2 🛠️ 支撐服務模塊

| 模塊類別 | 服務名稱 | 技術實現 | 功能描述 | 依賴關係 |
|---------|---------|---------|---------|---------|
| **🔐 安全服務** | 認證服務 | JWT + Redis | Token 管理、會話控制 | Redis, OAuth2 |
| **🔐 安全服務** | 權限服務 | RBAC 模型 | 角色權限、資源控制 | 用戶服務 |
| **📝 監控服務** | 日誌服務 | AOP + 數據庫 | 操作日誌、API 調用追蹤 | 所有業務模塊 |
| **📁 存儲服務** | 文件服務 | MinIO + 數據庫 | 文件上傳下載、元數據管理 | MinIO |
| **⚡ 緩存服務** | 緩存服務 | Redis | 數據緩存、會話存儲 | Redis |
| **📊 計算服務** | 計算引擎 | 自定義算法 | 碳排放計算、績效指標計算 | 基礎數據服務 |

#### 2.2.3 🔄 模塊間依賴關係圖

```mermaid
graph TD
    subgraph "🎯 核心業務層"
        A[🌍 碳中和管理]
        B[🌿 環境績效]
        C[👥 社會績效]
        D[📊 數據分析]
    end

    subgraph "🛠️ 支撐服務層"
        E[🤖 OCR 識別]
        F[⚡ 工作流管理]
        G[👤 用戶權限]
        H[📁 文件服務]
    end

    subgraph "🗂️ 基礎服務層"
        I[🗂️ 基礎數據管理]
        J[📝 日誌服務]
        K[⚡ 緩存服務]
        L[🔐 認證服務]
    end

    %% 依賴關係
    A --> E
    A --> F
    A --> I
    B --> E
    B --> F
    B --> I
    C --> F
    C --> I
    D --> A
    D --> B
    D --> C

    E --> H
    E --> K
    F --> G
    F --> J
    G --> L
    G --> J
    H --> K

    %% 樣式
    classDef coreModule fill:#e8f5e8,stroke:#4caf50,stroke-width:3px
    classDef supportModule fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    classDef baseModule fill:#fff3e0,stroke:#ff9800,stroke-width:2px

    class A,B,C,D coreModule
    class E,F,G,H supportModule
    class I,J,K,L baseModule
```

#### 2.2.4 📈 模塊成熟度與優先級

| 模塊名稱 | 開發狀態 | 功能完整度 | 業務優先級 | 技術債務 | 下一步計劃 |
|---------|---------|-----------|-----------|---------|-----------|
| **🌍 碳中和管理** | ✅ 已完成 | 95% | 🔴 極高 | 🟢 低 | 性能優化 |
| **🌿 環境績效** | ✅ 已完成 | 90% | 🔴 極高 | 🟡 中 | 功能增強 |
| **👥 社會績效** | ✅ 已完成 | 85% | 🟡 中 | 🟡 中 | 模塊重構 |
| **🤖 OCR 識別** | ✅ 已完成 | 92% | 🔴 高 | 🟢 低 | AI 模型升級 |
| **📊 數據分析** | 🔄 進行中 | 80% | 🔴 極高 | 🟡 中 | 實時分析 |
| **⚡ 工作流管理** | ✅ 已完成 | 88% | 🟡 中 | 🟢 低 | 流程優化 |

---

## 3. 核心業務模塊

### 3.1 碳中和管理模塊

#### 3.1.1 功能概述
負責企業碳排放數據的收集、計算、分析和減排目標管理。

#### 3.1.2 主要功能

**📊 碳排放統計**
- 溫室氣體排放量計算（範圍 1、2、3）
- 碳排放分佈統計
- 月度/年度碳排放趨勢分析
- 碳排放密度計算

**🎯 減排管理**
- 減排目標設定和追蹤
- 減排措施效果評估
- 低碳設計方案管理
- 碳中和規劃制定

**📈 數據分析**
- 能源使用排行榜
- 廢棄物產生量統計
- 原材料使用占比分析
- 省份/地區碳排放對比

#### 3.1.3 核心實體類

```java
// 碳排放主要實體
- TzhProjectInfo: 項目基本信息
- TzhEmissionReductionHead: 減排管理主表
- TzhEmissionReduction: 減排明細數據
- TzhProtocol: 減排協議標準
- TzhProtocolCategory: 協議分類
- TzhProtocolSubCategory: 協議子分類
```

### 3.2 環境績效管理模塊

#### 3.2.1 功能概述
監控和管理企業的環境表現，包括能源消耗、水資源使用、廢物處理等。

#### 3.2.2 主要功能

**⚡ 能源管理**
- 電力消耗統計
- 能源賬單自動識別
- 月度能源消耗計算
- 能源效率分析

**💧 水資源管理**
- 用水量統計
- 排水量監控
- 水資源使用密度計算
- 水費賬單處理

**🗑️ 廢物管理**
- 有害/無害廢物分類統計
- 廢物處理記錄
- 廢物產生量趨勢分析

#### 3.2.3 核心實體類

```java
// 環境績效主要實體
- AmbientHead: 環境績效主表
- AmbientDetail: 環境績效明細
- AmbientEnergyBill: 能源賬單
- EmpCommutingHead: 員工通勤主表
- EmpCommutingDetail: 員工通勤明細
```

### 3.3 OCR 智能識別模塊

#### 3.3.1 功能概述
利用 OCR 技術和 AI 智能分析，自動識別和提取各類賬單、票據信息。

#### 3.3.2 主要功能

**📄 文檔識別**
- 支持 JPG、PNG、PDF 格式
- 水費單、電費單自動識別
- 火車票、機票信息提取
- 文檔內容結構化處理

**🤖 AI 智能分析**
- HiAgent AI 模型集成
- 智能數據提取和驗證
- 多語言文檔處理
- 識別結果優化

#### 3.3.3 處理流程

```mermaid
sequenceDiagram
    participant Client as 客戶端
    participant Controller as TiOcrController
    participant Service as TiOcrService
    participant TiOCR as TiOCR API
    participant AI as HiAgent AI
    
    Client->>Controller: 上傳文檔
    Controller->>Service: 調用識別服務
    Service->>Service: 文件格式驗證
    Service->>Service: PDF轉圖像/Base64編碼
    Service->>TiOCR: 發送OCR請求
    TiOCR->>Service: 返回識別結果
    Service->>Service: 文本格式化處理
    Service->>AI: 發送AI分析請求
    AI->>Service: 返回結構化數據
    Service->>Controller: 返回最終結果
    Controller->>Client: 響應處理結果
```

---

## 4. 數據模型設計

### 4.1 數據庫架構

#### 4.1.1 多數據源配置

系統採用多數據源架構，支持不同業務模塊使用獨立的數據庫：

| 數據源名稱 | 用途 | 數據庫類型 |
|-----------|------|-----------|
| **susdev** | 主業務數據庫 | SQL Server |
| **tanzhonghe** | 碳中和專用數據庫 | SQL Server |
| **redis** | 緩存和會話存儲 | Redis |

#### 4.1.2 核心數據表結構

**用戶權限相關表**
```sql
-- 用戶表
t_user (id, username, name, email, mobile, ...)

-- 角色表  
t_role (id, code, name, description, ...)

-- 用戶角色關聯表
t_user_role (user_id, role_id, ...)

-- 組織架構表
t_organization (id, name, parent_id, level, ...)

-- 權限操作表
t_operation (id, name, url, method, ...)
```

**碳中和業務表**
```sql
-- 項目信息表
Tzh_ProjectInfo (Id, Name, Code, Type, SiteId, ...)

-- 減排管理主表
Tzh_EmissionReductionHead (Id, SiteName, CarbonEmissionLocation, ...)

-- 減排明細表
Tzh_EmissionReduction (Id, HeadId, RecordYearMonth, CarbonReductionAmount, ...)

-- 協議標準表
Tzh_Protocol (Id, Name, NameSC, NameEN, Description, ...)

-- 協議分類表
Tzh_ProtocolCategory (Id, ProtocolId, CategoryName, ...)
```

**環境績效表**
```sql
-- 環境績效主表
t_ambient_head (id, organization_id, year, month, ...)

-- 環境績效明細表
t_ambient_detail (id, head_id, unit_code, carbon_amount, ...)

-- 能源賬單表
t_ambient_energy_bill (id, head_id, bill_type, amount, ...)

-- 員工通勤表
t_emp_commuting_head (id, organization_id, year, month, ...)
```

### 4.2 實體關係圖

```mermaid
erDiagram
    USER ||--o{ USER_ROLE : has
    ROLE ||--o{ USER_ROLE : belongs
    USER ||--o{ USER_SESSION : creates
    ORGANIZATION ||--o{ USER : belongs
    ORGANIZATION ||--o{ AMBIENT_HEAD : manages
    
    AMBIENT_HEAD ||--o{ AMBIENT_DETAIL : contains
    AMBIENT_HEAD ||--o{ AMBIENT_ENERGY_BILL : has
    
    TZH_PROJECT ||--o{ TZH_EMISSION_HEAD : tracks
    TZH_EMISSION_HEAD ||--o{ TZH_EMISSION_DETAIL : contains
    TZH_PROTOCOL ||--o{ TZH_PROTOCOL_CATEGORY : categorizes
    TZH_PROTOCOL_CATEGORY ||--o{ TZH_PROTOCOL_SUBCATEGORY : subdivides
    
    WORKFLOW ||--o{ WORKFLOW_NODE : contains
    WORKFLOW_NODE ||--o{ WORKFLOW_NODE_USER : assigns
    WORKFLOW ||--o{ WORKFLOW_CONTROL : controls
```

---

## 5. API 接口設計

### 5.1 接口規範

#### 5.1.1 統一響應格式

```java
// 成功響應
{
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": { ... }
}

// 錯誤響應  
{
    "success": false,
    "code": 400,
    "message": "錯誤信息",
    "data": null
}
```

#### 5.1.2 認證機制

所有 API 請求需要在 Header 中包含認證 Token：

```http
x-auth-token: {JWT_TOKEN}
Menu-Route-Path: {ROUTE_PATH}  // 可選，用於權限控制
```

### 5.2 核心 API 接口

#### 5.2.1 認證相關接口

| 接口路徑 | 方法 | 功能描述 |
|---------|------|----------|
| `/auth/v1/login` | POST | 用戶登錄 |
| `/auth/logout` | POST | 用戶登出 |
| `/auth/captcha` | GET | 獲取驗證碼 |
| `/external/v1/login` | POST | 第三方應用登錄 |

#### 5.2.2 碳中和管理接口

| 接口路徑 | 方法 | 功能描述 |
|---------|------|----------|
| `/bi/tzh-common/get-project-info` | POST | 獲取項目信息 |
| `/bi/tzh-common/list-scope` | POST | 獲取協議標準範圍 |
| `/bi/tzh-bs-main/row/get-emission-reduction-description` | POST | 獲取低碳設計信息 |
| `/bi/tzh-bs-main/row/get-planning` | POST | 獲取碳排規劃 |
| `/api/tzh/pannel/listcarbonamountbyscopemain` | GET | 查詢溫室氣體排放量 |

#### 5.2.3 環境績效接口

| 接口路徑 | 方法 | 功能描述 |
|---------|------|----------|
| `/api/ambient/v1/list` | GET | 查詢環境績效列表 |
| `/api/ambient/v1/save` | POST | 保存環境績效數據 |
| `/api/ambient-energy-bill/cal-monthly-consumption` | GET | 計算月度能耗 |

#### 5.2.4 OCR 識別接口

| 接口路徑 | 方法 | 功能描述 |
|---------|------|----------|
| `/api/tiOcr/analysisWaterBillInfo` | POST | 水費單識別 |
| `/api/tiOcr/analysisElectricityBillInfo` | POST | 電費單識別 |
| `/api/tiOcr/analysisTrainTicketInfo` | POST | 火車票識別 |
| `/api/tiOcr/analysisAirTicketInfo` | POST | 機票識別 |

---

## 6. 技術架構

### 6.1 後端技術架構

#### 6.1.1 Spring Boot 配置

```yaml
# 核心配置
server:
  port: 8091
  servlet:
    context-path: /service  # 生產環境
  
spring:
  datasource:
    # 多數據源配置
    susdev:
      hikari:
        jdbc-url: *************************************************
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
        maximum-pool-size: 20
        minimum-idle: 5
        
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
    password: ${REDIS_PASSWORD}
    
  servlet:
    multipart:
      max-file-size: 25MB
      max-request-size: 25MB
```

#### 6.1.2 核心依賴

```xml
<!-- Spring Boot 核心 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
    <version>2.6.6</version>
</dependency>

<!-- 數據庫相關 -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
</dependency>

<dependency>
    <groupId>org.mariadb.jdbc</groupId>
    <artifactId>mariadb-java-client</artifactId>
    <version>2.7.0</version>
</dependency>

<!-- Redis 緩存 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>

<!-- JWT 認證 -->
<dependency>
    <groupId>com.auth0</groupId>
    <artifactId>java-jwt</artifactId>
</dependency>

<!-- OCR 相關 -->
<dependency>
    <groupId>org.apache.pdfbox</groupId>
    <artifactId>pdfbox</artifactId>
</dependency>

<!-- API 文檔 -->
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-ui</artifactId>
</dependency>
```

### 6.2 系統配置

#### 6.2.1 多數據源配置

系統支持多個數據源，通過 `@DS` 註解進行切換：

```java
@Service
@DS(DatasourceContextEnum.TANZHONGHE)  // 切換到碳中和數據庫
public class TzhPanelService {
    // 業務邏輯
}
```

#### 6.2.2 緩存配置

使用 Redis 進行會話管理和數據緩存：

```java
// JWT Token 緩存配置
jwt.secret=B0KNAPV0HZT02YRF0J3CMWAVPT5XC5RD
jwt.redisKey=susdev
jwt.expire=432000  // Token 過期時間（秒）

// 用戶會話配置
user.session.timeout=7200  // 會話超時時間（分鐘）
user.session.active.count=1  // 同時活躍會話數
```

---

## 7. 安全認證機制

### 7.1 認證流程

#### 7.1.1 用戶登錄流程

```mermaid
sequenceDiagram
    participant Client as 客戶端
    participant Auth as 認證服務
    participant DB as 數據庫
    participant Redis as Redis緩存
    
    Client->>Auth: 提交登錄信息
    Auth->>DB: 驗證用戶憑證
    DB->>Auth: 返回用戶信息
    Auth->>Auth: 生成JWT Token
    Auth->>Redis: 存儲用戶會話
    Auth->>Client: 返回Token和用戶信息
    
    Note over Client,Redis: 後續請求攜帶Token
    Client->>Auth: API請求 + Token
    Auth->>Redis: 驗證Token有效性
    Redis->>Auth: 返回會話信息
    Auth->>Client: 允許訪問
```

#### 7.1.2 權限控制機制

**角色權限模型**
- 用戶 → 角色 → 權限操作
- 支持多角色分配
- 細粒度權限控制到 API 級別

**組織架構權限**
- 基於組織層級的數據權限
- 支持跨組織數據訪問控制
- 最末級組織數據修改限制

### 7.2 第三方認證集成

#### 7.2.1 OAuth2 集成

```properties
# OAuth2 配置
oauth2.url=https://auth.csci.com.hk/api/token
oauth2.appid=111
oauth2.appSecret=cptbtptpbcptdtptp
oauth.validateUrl=https://auth.csci.com.hk/api/token/validate/
```

#### 7.2.2 飛書 SSO 集成

```properties
# 飛書 SSO 配置
feishu.sso.app.url=https://api.csci.com.hk/zhtappsso/api/Token
feishu.sso.app.validate=https://api.csci.com.hk/zhtappsso/api/ValidateToken
feishu.sso.app.code=fin-stat
feishu.sso.app.secret=FSal5YzYz0ZjSZXtYnrWZejs5SodHSZ8
```

---

## 8. 第三方集成

### 8.1 OCR 服務集成

#### 8.1.1 TiOCR API 集成

```properties
# TiOCR 配置
tiocr.base-url=http://10.148.42.13:60099
tiocr.smart_structural_ocr_v3=/youtu/ocrapi/smart_structural_ocr_v3
```

#### 8.1.2 HiAgent AI 服務

```properties
# HiAgent AI 配置
hiagent.url=https://hiagent.3311csci.com
hiagent.create-conversation=/api/proxy/api/v1/create_conversation
hiagent.chat-query=/api/proxy/api/v1/chat_query
hiagent.get_message_info=/api/proxy/api/v1/get_message_info
hiagent.esg.apikey=cvrj68jbg4roomp7ticg
```

### 8.2 文件存儲服務

#### 8.2.1 MinIO 配置

```properties
# MinIO 文件存儲配置
minio.server=http://10.148.2.15
minio.port=31452
minio.accessKey=WU9VUkFDQ0VTU0tFWQ==
minio.secretKey=WU9VUlNFQ1JFVEtFWQ==
minio.bucket=esg-pro
minio.urlPrefix=/minio/
```

### 8.3 外部 API 集成

#### 8.3.1 OA 系統集成

```properties
# OA 登錄驗證
oa.login=https://api.csci.com.hk/adverif/api/Verify
```

#### 8.3.2 碳足跡計算服務

```properties
# 碳中和計算服務
tzh.baseUrl=https://cfp.csci.com.hk/determine
```

---

## 9. 業務流程設計

### 9.1 碳排放數據收集流程

#### 9.1.1 數據收集流程圖

```mermaid
flowchart TD
    A[項目啟動] --> B[組織架構設置]
    B --> C[協議標準選擇]
    C --> D[數據收集範圍確定]
    D --> E[能源賬單上傳]
    E --> F[OCR自動識別]
    F --> G[AI數據提取]
    G --> H[數據驗證]
    H --> I{數據是否正確}
    I -->|是| J[數據入庫]
    I -->|否| K[人工修正]
    K --> H
    J --> L[碳排放計算]
    L --> M[報表生成]
    M --> N[審批流程]
    N --> O[數據發布]
```

#### 9.1.2 工作流狀態管理

| 狀態代碼 | 狀態名稱 | 描述 |
|---------|---------|------|
| `DRAFT` | 草稿 | 數據錄入中，未提交審核 |
| `SUBMITTED` | 已提交 | 等待審核 |
| `APPROVED` | 已審核 | 審核通過，數據生效 |
| `REJECTED` | 已拒絕 | 審核不通過，需要修改 |
| `PUBLISHED` | 已發布 | 數據對外發布 |

### 9.2 環境績效評估流程

#### 9.2.1 評估維度

**能源效率評估**
- 單位面積能耗計算
- 能源使用趨勢分析
- 可再生能源占比統計
- 節能措施效果評估

**水資源管理評估**
- 用水效率指標
- 水資源回收利用率
- 排水水質監控
- 節水措施實施效果

**廢物管理評估**
- 廢物分類處理率
- 危險廢物安全處置
- 廢物減量化措施
- 循環利用率統計

#### 9.2.2 績效指標體系

| 指標類別 | 具體指標 | 計算公式 | 單位 |
|---------|---------|---------|------|
| **能源密度** | 單位面積能耗 | 總能耗 ÷ 建築面積 | kWh/m² |
| **碳排放密度** | 單位產值碳排放 | 總碳排放 ÷ 產值 | tCO₂e/萬元 |
| **水資源密度** | 單位面積用水量 | 總用水量 ÷ 建築面積 | m³/m² |
| **廢物密度** | 單位產值廢物產生量 | 總廢物量 ÷ 產值 | kg/萬元 |

---

## 10. 數據分析與報表

### 10.1 BI 數據大屏功能

#### 10.1.1 主要展示內容

**碳排放概覽**
- 實時碳排放總量
- 月度/年度碳排放趨勢
- 各範圍碳排放占比
- 減排目標達成進度

**能源使用統計**
- 總能耗及分類統計
- 可再生能源發電量
- 能源使用排行榜
- 能源效率對比分析

**環境績效指標**
- 各類環境密度指標
- 省份/地區環境表現對比
- 項目環境績效排名
- 環境改善趨勢分析

#### 10.1.2 數據可視化組件

| 圖表類型 | 應用場景 | 數據源 |
|---------|---------|--------|
| **折線圖** | 趨勢分析 | 月度碳排放、能耗變化 |
| **柱狀圖** | 對比分析 | 各項目、各地區數據對比 |
| **餅圖** | 占比分析 | 能源結構、廢物分類占比 |
| **地圖** | 地理分佈 | 各省份環境績效分佈 |
| **儀表盤** | 實時監控 | KPI 指標達成情況 |

### 10.2 報表生成功能

#### 10.2.1 標準報表類型

**月度環境績效報表**
- 能源消耗統計
- 碳排放計算結果
- 水資源使用情況
- 廢物處理記錄

**年度可持續發展報告**
- ESG 績效總結
- 減排目標達成情況
- 環境改善措施效果
- 未來規劃和目標

**項目碳足跡報告**
- 項目全生命週期碳排放
- 各階段碳排放分析
- 減排措施建議
- 碳中和路徑規劃

#### 10.2.2 報表導出格式

- **PDF**: 正式報告文檔
- **Excel**: 數據分析和進一步處理
- **Word**: 可編輯的報告模板
- **JSON**: API 數據交換格式

---

## 11. 系統部署與運維

### 11.1 部署架構

#### 11.1.1 生產環境部署

```yaml
# Docker Compose 部署配置
version: '3.8'
services:
  sus-dev-app:
    image: sus-dev:latest
    ports:
      - "8091:8091"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=${DB_HOST}
      - REDIS_HOST=${REDIS_HOST}
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - redis
      - sqlserver

  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
```

#### 11.1.2 環境配置

| 環境 | 用途 | 配置文件 | 數據庫 |
|------|------|---------|--------|
| **開發環境** | 本地開發測試 | `application-dev.properties` | ESG_DEV_TEST |
| **測試環境** | 功能測試驗證 | `application-test.properties` | ESG_TEST |
| **生產環境** | 正式運行環境 | `application-prod.properties` | ESG_PROD |

### 11.2 監控與日誌

#### 11.2.1 應用監控

**性能監控指標**
- API 響應時間
- 數據庫連接池狀態
- 內存使用情況
- CPU 使用率
- 磁盤空間使用

**業務監控指標**
- 用戶登錄成功率
- OCR 識別成功率
- 數據處理吞吐量
- 錯誤率統計

#### 11.2.2 日誌管理

**日誌分類**
- **應用日誌**: 業務邏輯執行記錄
- **訪問日誌**: API 調用記錄
- **錯誤日誌**: 異常和錯誤信息
- **審計日誌**: 用戶操作記錄

**日誌配置**
```properties
# 日誌配置
logging.level.com.csci.susdev=INFO
logging.file.name=logs/sus-dev.log
logging.file.max-size=100MB
logging.file.max-history=30
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
```

---

## 12. 系統擴展性設計

### 12.1 微服務架構演進

#### 12.1.1 服務拆分建議

當系統規模擴大時，可考慮按業務域拆分為微服務：

| 服務名稱 | 職責範圍 | 技術棧 |
|---------|---------|--------|
| **用戶服務** | 用戶管理、認證授權 | Spring Boot + JWT |
| **碳中和服務** | 碳排放計算、減排管理 | Spring Boot + MyBatis |
| **環境績效服務** | 環境數據收集、分析 | Spring Boot + MyBatis |
| **OCR服務** | 文檔識別、AI分析 | Spring Boot + AI API |
| **報表服務** | 數據分析、報表生成 | Spring Boot + JasperReports |
| **文件服務** | 文件存儲、管理 | Spring Boot + MinIO |

#### 12.1.2 服務間通信

**同步通信**
- REST API 調用
- OpenFeign 客戶端
- 服務發現與負載均衡

**異步通信**
- 消息隊列（RabbitMQ/Kafka）
- 事件驅動架構
- 最終一致性保證

### 12.2 數據庫擴展

#### 12.2.1 讀寫分離

```yaml
# 主從數據庫配置
datasource:
  master:
    jdbc-url: ************************************************
    username: ${MASTER_DB_USER}
    password: ${MASTER_DB_PASSWORD}

  slave:
    jdbc-url: ***********************************************
    username: ${SLAVE_DB_USER}
    password: ${SLAVE_DB_PASSWORD}
```

#### 12.2.2 分庫分表策略

**垂直分庫**
- 按業務模塊分離數據庫
- 用戶庫、碳中和庫、環境績效庫

**水平分表**
- 按時間分表：月度數據表
- 按組織分表：大型組織獨立表

---

## 總結

SUS-DEV 可持續發展管理系統是一個功能完整、架構清晰的企業級 ESG 管理平台。系統採用現代化的技術架構，支持多業務模塊、多數據源、智能文檔識別等先進功能，為企業的可持續發展管理提供了全面的技術支撐。

### 核心優勢

1. **業務完整性**: 覆蓋碳中和、環境績效、社會責任等 ESG 全領域
2. **技術先進性**: 集成 OCR、AI 智能分析等前沿技術
3. **架構靈活性**: 支持多數據源、微服務演進
4. **安全可靠性**: 完善的認證授權和權限控制機制
5. **擴展性**: 模塊化設計，支持業務快速擴展

### 應用價值

- **提升管理效率**: 自動化數據收集和處理，減少人工工作量
- **增強決策支持**: 豐富的數據分析和可視化功能
- **確保合規性**: 標準化的流程和審計追蹤
- **促進可持續發展**: 科學的目標設定和進度追蹤

通過模塊化的設計和完善的權限控制機制，系統能夠滿足不同規模企業的 ESG 管理需求，並支持與第三方系統的靈活集成，為企業的數字化轉型和可持續發展提供強有力的技術保障。
