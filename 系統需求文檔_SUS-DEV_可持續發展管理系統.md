# 系統需求文檔 - SUS-DEV 可持續發展管理系統

## 📋 目錄

1. [項目概述](#1-項目概述)
2. [系統架構](#2-系統架構)
3. [核心業務模塊](#3-核心業務模塊)
4. [數據模型設計](#4-數據模型設計)
5. [API 接口設計](#5-api-接口設計)
6. [技術架構](#6-技術架構)
7. [安全認證機制](#7-安全認證機制)
8. [第三方集成](#8-第三方集成)

---

## 1. 項目概述

### 1.1 系統簡介

**SUS-DEV 可持續發展管理系統** 是一個基於 Spring Boot 的企業級 ESG（環境、社會、治理）管理平台，專注於碳中和管理、環境績效監控、社會責任追蹤和可持續發展數據分析。

### 1.2 核心特性

- ✅ **碳中和管理**：碳排放計算、減排目標設定、碳足跡追蹤
- ✅ **環境績效監控**：能源消耗、廢物管理、水資源使用統計
- ✅ **智能文檔識別**：OCR 技術自動識別能源賬單、票據信息
- ✅ **數據可視化**：BI 大屏展示、多維度數據分析
- ✅ **工作流管理**：審批流程、權限控制、組織架構管理
- ✅ **第三方集成**：OAuth2 認證、飛書 SSO、外部 API 集成

### 1.3 技術棧

| 技術領域 | 技術選型 | 版本 |
|---------|---------|------|
| **後端框架** | Spring Boot | 2.6.6 |
| **Java 版本** | OpenJDK | 17 |
| **數據庫** | SQL Server | - |
| **ORM 框架** | MyBatis Plus | - |
| **緩存** | Redis | - |
| **文檔存儲** | MinIO | - |
| **API 文檔** | Swagger/OpenAPI 3 | - |
| **OCR 服務** | TiOCR + HiAgent AI | - |

---

## 2. 系統架構

### 2.1 整體架構圖

```mermaid
graph TB
    subgraph "前端層"
        A[Web 前端] --> B[BI 大屏]
        A --> C[移動端]
    end
    
    subgraph "API 網關層"
        D[Spring Boot Controller]
        E[認證攔截器]
        F[權限攔截器]
    end
    
    subgraph "業務服務層"
        G[碳中和服務]
        H[環境績效服務]
        I[工作流服務]
        J[用戶管理服務]
        K[OCR 服務]
    end
    
    subgraph "數據訪問層"
        L[MyBatis Plus]
        M[多數據源配置]
    end
    
    subgraph "數據存儲層"
        N[SQL Server 主庫]
        O[碳中和數據庫]
        P[Redis 緩存]
        Q[MinIO 文件存儲]
    end
    
    subgraph "外部服務"
        R[OAuth2 認證]
        S[飛書 SSO]
        T[TiOCR API]
        U[HiAgent AI]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    F --> H
    F --> I
    F --> J
    F --> K
    G --> L
    H --> L
    I --> L
    J --> L
    K --> L
    L --> M
    M --> N
    M --> O
    M --> P
    M --> Q
    K --> T
    K --> U
    E --> R
    E --> S
```

### 2.2 模塊劃分

#### 2.2.1 核心業務模塊

| 模塊名稱 | 包路徑 | 功能描述 |
|---------|--------|----------|
| **碳中和管理** | `com.csci.cohl` | 碳排放計算、減排管理、碳足跡追蹤 |
| **環境績效** | `com.csci.susdev.service.Ambient*` | 環境數據收集、能源消耗統計 |
| **工作流管理** | `com.csci.susdev.service.Workflow*` | 審批流程、狀態管理 |
| **用戶權限** | `com.csci.susdev.service.User*` | 用戶管理、角色權限、組織架構 |
| **OCR 識別** | `com.csci.susdev.service.TiOcr*` | 文檔識別、AI 數據提取 |

#### 2.2.2 支撐服務模塊

| 模塊名稱 | 功能描述 |
|---------|----------|
| **認證服務** | JWT Token 管理、會話控制 |
| **日誌服務** | 操作日誌記錄、API 調用追蹤 |
| **文件服務** | 文件上傳下載、MinIO 集成 |
| **緩存服務** | Redis 緩存管理、會話存儲 |

---

## 3. 核心業務模塊

### 3.1 碳中和管理模塊

#### 3.1.1 功能概述
負責企業碳排放數據的收集、計算、分析和減排目標管理。

#### 3.1.2 主要功能

**📊 碳排放統計**
- 溫室氣體排放量計算（範圍 1、2、3）
- 碳排放分佈統計
- 月度/年度碳排放趨勢分析
- 碳排放密度計算

**🎯 減排管理**
- 減排目標設定和追蹤
- 減排措施效果評估
- 低碳設計方案管理
- 碳中和規劃制定

**📈 數據分析**
- 能源使用排行榜
- 廢棄物產生量統計
- 原材料使用占比分析
- 省份/地區碳排放對比

#### 3.1.3 核心實體類

```java
// 碳排放主要實體
- TzhProjectInfo: 項目基本信息
- TzhEmissionReductionHead: 減排管理主表
- TzhEmissionReduction: 減排明細數據
- TzhProtocol: 減排協議標準
- TzhProtocolCategory: 協議分類
- TzhProtocolSubCategory: 協議子分類
```

### 3.2 環境績效管理模塊

#### 3.2.1 功能概述
監控和管理企業的環境表現，包括能源消耗、水資源使用、廢物處理等。

#### 3.2.2 主要功能

**⚡ 能源管理**
- 電力消耗統計
- 能源賬單自動識別
- 月度能源消耗計算
- 能源效率分析

**💧 水資源管理**
- 用水量統計
- 排水量監控
- 水資源使用密度計算
- 水費賬單處理

**🗑️ 廢物管理**
- 有害/無害廢物分類統計
- 廢物處理記錄
- 廢物產生量趨勢分析

#### 3.2.3 核心實體類

```java
// 環境績效主要實體
- AmbientHead: 環境績效主表
- AmbientDetail: 環境績效明細
- AmbientEnergyBill: 能源賬單
- EmpCommutingHead: 員工通勤主表
- EmpCommutingDetail: 員工通勤明細
```

### 3.3 OCR 智能識別模塊

#### 3.3.1 功能概述
利用 OCR 技術和 AI 智能分析，自動識別和提取各類賬單、票據信息。

#### 3.3.2 主要功能

**📄 文檔識別**
- 支持 JPG、PNG、PDF 格式
- 水費單、電費單自動識別
- 火車票、機票信息提取
- 文檔內容結構化處理

**🤖 AI 智能分析**
- HiAgent AI 模型集成
- 智能數據提取和驗證
- 多語言文檔處理
- 識別結果優化

#### 3.3.3 處理流程

```mermaid
sequenceDiagram
    participant Client as 客戶端
    participant Controller as TiOcrController
    participant Service as TiOcrService
    participant TiOCR as TiOCR API
    participant AI as HiAgent AI
    
    Client->>Controller: 上傳文檔
    Controller->>Service: 調用識別服務
    Service->>Service: 文件格式驗證
    Service->>Service: PDF轉圖像/Base64編碼
    Service->>TiOCR: 發送OCR請求
    TiOCR->>Service: 返回識別結果
    Service->>Service: 文本格式化處理
    Service->>AI: 發送AI分析請求
    AI->>Service: 返回結構化數據
    Service->>Controller: 返回最終結果
    Controller->>Client: 響應處理結果
```

---

## 4. 數據模型設計

### 4.1 數據庫架構

#### 4.1.1 多數據源配置

系統採用多數據源架構，支持不同業務模塊使用獨立的數據庫：

| 數據源名稱 | 用途 | 數據庫類型 |
|-----------|------|-----------|
| **susdev** | 主業務數據庫 | SQL Server |
| **tanzhonghe** | 碳中和專用數據庫 | SQL Server |
| **redis** | 緩存和會話存儲 | Redis |

#### 4.1.2 核心數據表結構

**用戶權限相關表**
```sql
-- 用戶表
t_user (id, username, name, email, mobile, ...)

-- 角色表  
t_role (id, code, name, description, ...)

-- 用戶角色關聯表
t_user_role (user_id, role_id, ...)

-- 組織架構表
t_organization (id, name, parent_id, level, ...)

-- 權限操作表
t_operation (id, name, url, method, ...)
```

**碳中和業務表**
```sql
-- 項目信息表
Tzh_ProjectInfo (Id, Name, Code, Type, SiteId, ...)

-- 減排管理主表
Tzh_EmissionReductionHead (Id, SiteName, CarbonEmissionLocation, ...)

-- 減排明細表
Tzh_EmissionReduction (Id, HeadId, RecordYearMonth, CarbonReductionAmount, ...)

-- 協議標準表
Tzh_Protocol (Id, Name, NameSC, NameEN, Description, ...)

-- 協議分類表
Tzh_ProtocolCategory (Id, ProtocolId, CategoryName, ...)
```

**環境績效表**
```sql
-- 環境績效主表
t_ambient_head (id, organization_id, year, month, ...)

-- 環境績效明細表
t_ambient_detail (id, head_id, unit_code, carbon_amount, ...)

-- 能源賬單表
t_ambient_energy_bill (id, head_id, bill_type, amount, ...)

-- 員工通勤表
t_emp_commuting_head (id, organization_id, year, month, ...)
```

### 4.2 實體關係圖

```mermaid
erDiagram
    USER ||--o{ USER_ROLE : has
    ROLE ||--o{ USER_ROLE : belongs
    USER ||--o{ USER_SESSION : creates
    ORGANIZATION ||--o{ USER : belongs
    ORGANIZATION ||--o{ AMBIENT_HEAD : manages
    
    AMBIENT_HEAD ||--o{ AMBIENT_DETAIL : contains
    AMBIENT_HEAD ||--o{ AMBIENT_ENERGY_BILL : has
    
    TZH_PROJECT ||--o{ TZH_EMISSION_HEAD : tracks
    TZH_EMISSION_HEAD ||--o{ TZH_EMISSION_DETAIL : contains
    TZH_PROTOCOL ||--o{ TZH_PROTOCOL_CATEGORY : categorizes
    TZH_PROTOCOL_CATEGORY ||--o{ TZH_PROTOCOL_SUBCATEGORY : subdivides
    
    WORKFLOW ||--o{ WORKFLOW_NODE : contains
    WORKFLOW_NODE ||--o{ WORKFLOW_NODE_USER : assigns
    WORKFLOW ||--o{ WORKFLOW_CONTROL : controls
```

---

## 5. API 接口設計

### 5.1 接口規範

#### 5.1.1 統一響應格式

```java
// 成功響應
{
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": { ... }
}

// 錯誤響應  
{
    "success": false,
    "code": 400,
    "message": "錯誤信息",
    "data": null
}
```

#### 5.1.2 認證機制

所有 API 請求需要在 Header 中包含認證 Token：

```http
x-auth-token: {JWT_TOKEN}
Menu-Route-Path: {ROUTE_PATH}  // 可選，用於權限控制
```

### 5.2 核心 API 接口

#### 5.2.1 認證相關接口

| 接口路徑 | 方法 | 功能描述 |
|---------|------|----------|
| `/auth/v1/login` | POST | 用戶登錄 |
| `/auth/logout` | POST | 用戶登出 |
| `/auth/captcha` | GET | 獲取驗證碼 |
| `/external/v1/login` | POST | 第三方應用登錄 |

#### 5.2.2 碳中和管理接口

| 接口路徑 | 方法 | 功能描述 |
|---------|------|----------|
| `/bi/tzh-common/get-project-info` | POST | 獲取項目信息 |
| `/bi/tzh-common/list-scope` | POST | 獲取協議標準範圍 |
| `/bi/tzh-bs-main/row/get-emission-reduction-description` | POST | 獲取低碳設計信息 |
| `/bi/tzh-bs-main/row/get-planning` | POST | 獲取碳排規劃 |
| `/api/tzh/pannel/listcarbonamountbyscopemain` | GET | 查詢溫室氣體排放量 |

#### 5.2.3 環境績效接口

| 接口路徑 | 方法 | 功能描述 |
|---------|------|----------|
| `/api/ambient/v1/list` | GET | 查詢環境績效列表 |
| `/api/ambient/v1/save` | POST | 保存環境績效數據 |
| `/api/ambient-energy-bill/cal-monthly-consumption` | GET | 計算月度能耗 |

#### 5.2.4 OCR 識別接口

| 接口路徑 | 方法 | 功能描述 |
|---------|------|----------|
| `/api/tiOcr/analysisWaterBillInfo` | POST | 水費單識別 |
| `/api/tiOcr/analysisElectricityBillInfo` | POST | 電費單識別 |
| `/api/tiOcr/analysisTrainTicketInfo` | POST | 火車票識別 |
| `/api/tiOcr/analysisAirTicketInfo` | POST | 機票識別 |

---

## 6. 技術架構

### 6.1 後端技術架構

#### 6.1.1 Spring Boot 配置

```yaml
# 核心配置
server:
  port: 8091
  servlet:
    context-path: /service  # 生產環境
  
spring:
  datasource:
    # 多數據源配置
    susdev:
      hikari:
        jdbc-url: *************************************************
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
        maximum-pool-size: 20
        minimum-idle: 5
        
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
    password: ${REDIS_PASSWORD}
    
  servlet:
    multipart:
      max-file-size: 25MB
      max-request-size: 25MB
```

#### 6.1.2 核心依賴

```xml
<!-- Spring Boot 核心 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
    <version>2.6.6</version>
</dependency>

<!-- 數據庫相關 -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
</dependency>

<dependency>
    <groupId>org.mariadb.jdbc</groupId>
    <artifactId>mariadb-java-client</artifactId>
    <version>2.7.0</version>
</dependency>

<!-- Redis 緩存 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>

<!-- JWT 認證 -->
<dependency>
    <groupId>com.auth0</groupId>
    <artifactId>java-jwt</artifactId>
</dependency>

<!-- OCR 相關 -->
<dependency>
    <groupId>org.apache.pdfbox</groupId>
    <artifactId>pdfbox</artifactId>
</dependency>

<!-- API 文檔 -->
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-ui</artifactId>
</dependency>
```

### 6.2 系統配置

#### 6.2.1 多數據源配置

系統支持多個數據源，通過 `@DS` 註解進行切換：

```java
@Service
@DS(DatasourceContextEnum.TANZHONGHE)  // 切換到碳中和數據庫
public class TzhPanelService {
    // 業務邏輯
}
```

#### 6.2.2 緩存配置

使用 Redis 進行會話管理和數據緩存：

```java
// JWT Token 緩存配置
jwt.secret=B0KNAPV0HZT02YRF0J3CMWAVPT5XC5RD
jwt.redisKey=susdev
jwt.expire=432000  // Token 過期時間（秒）

// 用戶會話配置
user.session.timeout=7200  // 會話超時時間（分鐘）
user.session.active.count=1  // 同時活躍會話數
```

---

## 7. 安全認證機制

### 7.1 認證流程

#### 7.1.1 用戶登錄流程

```mermaid
sequenceDiagram
    participant Client as 客戶端
    participant Auth as 認證服務
    participant DB as 數據庫
    participant Redis as Redis緩存
    
    Client->>Auth: 提交登錄信息
    Auth->>DB: 驗證用戶憑證
    DB->>Auth: 返回用戶信息
    Auth->>Auth: 生成JWT Token
    Auth->>Redis: 存儲用戶會話
    Auth->>Client: 返回Token和用戶信息
    
    Note over Client,Redis: 後續請求攜帶Token
    Client->>Auth: API請求 + Token
    Auth->>Redis: 驗證Token有效性
    Redis->>Auth: 返回會話信息
    Auth->>Client: 允許訪問
```

#### 7.1.2 權限控制機制

**角色權限模型**
- 用戶 → 角色 → 權限操作
- 支持多角色分配
- 細粒度權限控制到 API 級別

**組織架構權限**
- 基於組織層級的數據權限
- 支持跨組織數據訪問控制
- 最末級組織數據修改限制

### 7.2 第三方認證集成

#### 7.2.1 OAuth2 集成

```properties
# OAuth2 配置
oauth2.url=https://auth.csci.com.hk/api/token
oauth2.appid=111
oauth2.appSecret=cptbtptpbcptdtptp
oauth.validateUrl=https://auth.csci.com.hk/api/token/validate/
```

#### 7.2.2 飛書 SSO 集成

```properties
# 飛書 SSO 配置
feishu.sso.app.url=https://api.csci.com.hk/zhtappsso/api/Token
feishu.sso.app.validate=https://api.csci.com.hk/zhtappsso/api/ValidateToken
feishu.sso.app.code=fin-stat
feishu.sso.app.secret=FSal5YzYz0ZjSZXtYnrWZejs5SodHSZ8
```

---

## 8. 第三方集成

### 8.1 OCR 服務集成

#### 8.1.1 TiOCR API 集成

```properties
# TiOCR 配置
tiocr.base-url=http://10.148.42.13:60099
tiocr.smart_structural_ocr_v3=/youtu/ocrapi/smart_structural_ocr_v3
```

#### 8.1.2 HiAgent AI 服務

```properties
# HiAgent AI 配置
hiagent.url=https://hiagent.3311csci.com
hiagent.create-conversation=/api/proxy/api/v1/create_conversation
hiagent.chat-query=/api/proxy/api/v1/chat_query
hiagent.get_message_info=/api/proxy/api/v1/get_message_info
hiagent.esg.apikey=cvrj68jbg4roomp7ticg
```

### 8.2 文件存儲服務

#### 8.2.1 MinIO 配置

```properties
# MinIO 文件存儲配置
minio.server=http://10.148.2.15
minio.port=31452
minio.accessKey=WU9VUkFDQ0VTU0tFWQ==
minio.secretKey=WU9VUlNFQ1JFVEtFWQ==
minio.bucket=esg-pro
minio.urlPrefix=/minio/
```

### 8.3 外部 API 集成

#### 8.3.1 OA 系統集成

```properties
# OA 登錄驗證
oa.login=https://api.csci.com.hk/adverif/api/Verify
```

#### 8.3.2 碳足跡計算服務

```properties
# 碳中和計算服務
tzh.baseUrl=https://cfp.csci.com.hk/determine
```

---

## 總結

SUS-DEV 可持續發展管理系統是一個功能完整、架構清晰的企業級 ESG 管理平台。系統採用現代化的技術架構，支持多業務模塊、多數據源、智能文檔識別等先進功能，為企業的可持續發展管理提供了全面的技術支撐。

通過模塊化的設計和完善的權限控制機制，系統能夠滿足不同規模企業的 ESG 管理需求，並支持與第三方系統的靈活集成。
