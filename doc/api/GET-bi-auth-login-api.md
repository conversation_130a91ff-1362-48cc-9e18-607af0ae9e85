# GET /bi/auth/login 接口技術文檔

## 1. 接口概述

### 1.1 接口描述
OAuth 登錄接口，用於處理第三方認證登錄並重定向到目標頁面。該接口通過驗證外部 OAuth token，完成用戶身份認證，並生成本地 token 後重定向到指定的業務頁面。

### 1.2 接口基本信息
- **接口路徑**: `/bi/auth/login`
- **請求方法**: `GET`
- **接口分類**: 鑒權中心
- **是否需要認證**: 否（OAuth token 驗證）
- **返回類型**: 重定向（302）

## 2. 方法調用鏈介紹

### 2.1 主要調用流程
```
BiAuthController.oAuthLogin()
    ↓
AuthService.oAuthValidateToken() - 驗證外部 OAuth token
    ↓
AuthService.oAuthLogin() - 執行本地登錄邏輯
    ↓
HttpServletResponse.sendRedirect() - 重定向到目標頁面
```

### 2.2 實現概述
該接口主要負責 OAuth 第三方登錄的處理，包括：
1. 接收並解析請求參數
2. 驗證外部 OAuth token 的有效性
3. 提取用戶名並執行本地登錄邏輯
4. 生成本地 token 並構建重定向 URL
5. 執行頁面重定向

## 3. 請求參數

### 3.1 Query Parameters

| 參數名 | 類型 | 必填 | 默認值 | 描述 | 示例 |
|--------|------|------|--------|------|------|
| token | String | ✅ | - | OAuth 認證 token | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| sitename | String | ❌ | - | 站點名稱 | `project_site_01` |
| siteid | String | ❌ | - | 站點 ID | `12345` |
| isheader | String | ❌ | `"0"` | 是否顯示頭部 | `"1"` 或 `"0"` |
| protocol | String | ❌ | `"GBT 51366"` | 協議標準 | `"GBT 51366"` |
| language | String | ❌ | `""` | 語言設置 | `"zh-CN"` |
| theme | String | ❌ | `"blue"` | 主題樣式 | `"blue"`, `"dark"` |

### 3.2 請求示例

```http
GET /bi/auth/login?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&sitename=project_site_01&siteid=12345&theme=blue&isheader=1&protocol=GBT%2051366&language=zh-CN HTTP/1.1
Host: your-domain.com
```

## 4. 響應說明

### 4.1 成功響應
**HTTP 狀態碼**: `302 Found`

**響應頭**:
```http
Location: {baseUrl}?theme={theme}&token={localToken}&sitename={sitename}&siteid={siteid}&user={username}&isheader={isheader}&protocol={protocol}&language={language}
```

### 4.2 重定向 URL 參數說明

| 參數名 | 類型 | 描述 | 示例 |
|--------|------|------|------|
| theme | String | 主題樣式 | `blue` |
| token | String | 本地生成的認證 token | `a1b2c3d4-e5f6-7890-abcd-ef1234567890` |
| sitename | String | 站點名稱 | `project_site_01` |
| siteid | String | 站點 ID | `12345` |
| user | String | 用戶名 | `john.doe` |
| isheader | String | 是否顯示頭部 | `1` |
| protocol | String | 協議標準 | `GBT%2051366` |
| language | String | 語言設置 | `zh-CN` |

### 4.3 失敗響應

#### 4.3.1 Token 驗證失敗
**HTTP 狀態碼**: `302 Found`

**響應頭**:
```http
Location: {baseUrl}
```

**說明**: 當 OAuth token 無效或驗證失敗時，直接重定向到基礎 URL，不攜帶任何參數。

#### 4.3.2 權限驗證失敗
**HTTP 狀態碼**: `500 Internal Server Error`

**響應體**:
```json
{
  "timestamp": "2025-07-07T10:30:00.000+00:00",
  "status": 500,
  "error": "Internal Server Error",
  "message": "沒有該地盤權限",
  "path": "/bi/auth/login"
}
```

## 5. 業務邏輯說明

### 5.1 Token 驗證流程
1. 接收外部 OAuth token
2. 調用 `authService.oAuthValidateToken(token)` 驗證 token 有效性
3. 從 TokenVO 中提取用戶信息

### 5.2 用戶名處理
```java
String[] arrUsername = tokenVO.getUsername().split("\\\\");
String username = arrUsername[arrUsername.length-1];
```
- 支持域用戶名格式（如：`DOMAIN\\username`）
- 提取最後一部分作為實際用戶名

### 5.3 本地登錄處理
1. 調用 `authService.oAuthLogin(username, sitename, siteid)` 執行本地登錄
2. 驗證用戶對指定站點的權限
3. 生成本地 token 並創建用戶會話

### 5.4 站點 ID 處理
```java
if (StringUtil.isEmptyNotTrim(siteid)) {
    siteid = authService.getBySiteName(sitename);
}
```
- 如果未提供 siteid，則根據 sitename 查詢對應的 siteid

## 6. 錯誤處理

### 6.1 常見錯誤場景

| 錯誤類型 | 錯誤信息 | 原因 | 解決方案 |
|----------|----------|------|----------|
| Token 無效 | 重定向到基礎 URL | OAuth token 無效或過期 | 檢查 token 有效性，重新獲取有效 token |
| 權限不足 | "沒有該地盤權限" | 用戶沒有指定站點的訪問權限 | 聯繫管理員分配相應站點權限 |
| 參數缺失 | 重定向到基礎 URL | 必需的 token 參數缺失 | 確保請求包含有效的 token 參數 |

### 6.2 異常處理機制
- 所有異常都會導致 HTTP 500 錯誤
- 系統會記錄詳細的錯誤日誌
- 客戶端會收到標準的錯誤響應格式

## 7. 安全考慮

### 7.1 Token 安全
- OAuth token 應通過安全通道傳輸
- 建議使用 HTTPS 協議進行所有通信
- Token 具有時效性，過期後需要重新獲取

### 7.2 權限控制
- 系統會嚴格驗證用戶對站點的訪問權限
- 未授權的訪問會被拒絕並記錄日誌
- 建議定期審核用戶權限配置

### 7.3 參數驗證
- 所有輸入參數都會進行 URL 編碼處理
- 系統會驗證參數格式和有效性
- 惡意參數會被過濾或拒絕

## 8. 調用示例

### 8.1 基本調用
```bash
curl -X GET "https://your-domain.com/bi/auth/login?token=valid_oauth_token&sitename=test_site&theme=blue" \
  -L -v
```

### 8.2 完整參數調用
```bash
curl -X GET "https://your-domain.com/bi/auth/login?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&sitename=project_site_01&siteid=12345&isheader=1&protocol=GBT%2051366&language=zh-CN&theme=dark" \
  -L -v
```

### 8.3 JavaScript 調用
```javascript
// 構建登錄 URL
const loginUrl = new URL('/bi/auth/login', 'https://your-domain.com');
loginUrl.searchParams.set('token', 'your_oauth_token');
loginUrl.searchParams.set('sitename', 'project_site_01');
loginUrl.searchParams.set('theme', 'blue');
loginUrl.searchParams.set('isheader', '1');

// 重定向到登錄 URL
window.location.href = loginUrl.toString();
```

## 9. 相關接口

### 9.1 相關認證接口
- `POST /bi/auth/login` - 表單登錄接口
- `POST /bi/auth/logout` - 登出接口
- `GET /bi/auth/list-current-org-permission` - 獲取用戶權限列表

### 9.2 依賴服務
- OAuth 驗證服務：用於驗證外部 token
- 用戶會話服務：管理用戶登錄狀態
- 權限服務：驗證用戶站點訪問權限

## 10. 注意事項

### 10.1 重定向處理
- 客戶端需要能夠處理 HTTP 302 重定向
- 重定向 URL 中的參數都經過 URL 編碼
- 建議客戶端設置合理的重定向跟隨策略

### 10.2 參數默認值
- 未提供的可選參數會使用系統默認值
- 默認值的設置遵循業務邏輯要求
- 建議明確指定重要參數以避免歧義

### 10.3 兼容性
- 接口支持標準的 HTTP GET 請求
- 兼容主流瀏覽器和 HTTP 客戶端
- 建議使用標準的 HTTP 庫進行調用

---

**文檔版本**: v1.0
**最後更新**: 2025-07-07
**維護團隊**: ESG 開發團隊
