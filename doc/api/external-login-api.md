# 第三方應用登錄接口 API 文檔

## 接口概述

**接口路徑：** `/api/external/v1/login`  
**請求方法：** `POST`  
**接口描述：** 第三方應用登錄認證接口，用於外部系統獲取訪問令牌  
**認證方式：** RSA加密的AppID和AppKey

---

## 請求參數

### 請求頭 (Headers)

| 參數名 | 類型 | 必填 | 描述 |
|--------|------|------|------|
| `External-App-Id` | String | ✅ | 經過RSA加密的應用ID |
| `External-App-Key` | String | ✅ | 經過RSA加密的應用密鑰 |
| `Content-Type` | String | ✅ | 固定值：`application/json` |

> **📋 重要說明**
> `External-App-Id` 和 `External-App-Key` 需要在ESG系統中註冊第三方對接專用賬戶後進行分配。
> 請聯繫系統管理員申請第三方對接權限並獲取相應的AppID和AppKey。

### RSA加密說明

**生產環境RSA公鑰：**
```
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGXG423pFrAhgDGtS1u70ggF+7XHIQc4Jfsv0OzrCob6Wyt8pmD0PJJipXw3NW82Q/kxnDxaE6fxdiQlLK0uLqDKEibxjuJ2tSmGCJmykD4gzM0GobY06ukH076dNhvap5d3oKfeG8XCpYH9lI2SvXcSWbpGRT1reU940xsO5onwIDAQAB
```

**加密要求：**
- 使用上述RSA公鑰對AppID和AppKey進行加密
- 加密算法：RSA/ECB/PKCS1Padding
- 編碼格式：Base64

### 請求體 (Body)

此接口無需請求體參數。

---

## 響應格式

### 成功響應

**HTTP狀態碼：** `200`

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "displayName": "周嘉俊",
    "organizationList": [
      {
        "id": "0ba34fd7-c4ec-4825-a028-78a03909af91",
        "name": "CDM-將軍澳至藍田隧道-主隧道及相關工程",
        "no": "001001001001",
        "companyName": "土木公司",
        "unitCode": "CDM",
        "nameWithCompanyName": "CDM-將軍澳至藍田隧道-主隧道及相關工程（土木公司）"
      },
      {
        "id": "1b8529gb-g493-5df6-a376-gffe9c08311f",
        "name": "深圳分公司",
        "no": "001002",
        "companyName": "中國建築國際集團",
        "unitCode": "SZ002",
        "nameWithCompanyName": "深圳分公司（中國建築國際集團）"
      }
    ],
    "token": "10A58203FDFB47D190555E3BEDF4EEE4"
  }
}
```

**響應字段說明：**

| 字段名 | 類型 | 描述 |
|--------|------|------|
| `code` | Integer | 響應狀態碼，0表示成功，-1表示失敗 |
| `msg` | String | 響應消息描述 |
| `data.token` | String | 生成的訪問令牌，用於後續API調用 |
| `data.displayName` | String | 用戶顯示名稱 |
| `data.organizationList` | Array | 用戶所屬組織列表 |

**organizationList 數組元素結構：**

| 字段名 | 類型 | 描述 |
|--------|------|------|
| `id` | String | 組織ID，唯一標識符 |
| `name` | String | 組織名稱 |
| `no` | String | 組織編號，用於層級關係識別 |
| `companyName` | String | 所屬公司名稱（上級組織名稱） |
| `unitCode` | String | 單位代碼 |
| `nameWithCompanyName` | String | 包含公司名稱的完整組織名稱，格式：組織名（公司名） |

### 錯誤響應

**HTTP狀態碼：** `200`（業務錯誤）

```json
{
  "code": -1,
  "msg": "錯誤描述信息",
  "data": null
}
```

---

## 錯誤碼說明

| 錯誤信息 | 原因 | 解決方案 |
|----------|------|----------|
| `請求需要輸入 AppID 及 AppKey` | 請求頭缺少必要的認證參數 | 確保請求頭包含 `External-App-Id` 和 `External-App-Key` |
| `沒有訪問權限` | 提供的AppID和AppKey無效或未激活 | 檢查AppID和AppKey是否正確，並確保已在系統中激活 |
| `帳號未啟用` | 關聯的用戶賬號未啟用 | 聯繫管理員啟用對應的用戶賬號 |
| `未找到对应的记录` | AppID和AppKey組合不存在 | 確認AppID和AppKey的正確性 |

---

## 調用示例

### cURL

```bash
# 注意：實際使用時需要將AppID和AppKey用RSA公鑰加密後替換下面的占位符
curl -X POST "https://your-domain.com/api/external/v1/login" \
  -H "Content-Type: application/json" \
  -H "External-App-Id: [使用RSA公鑰加密後的AppID]" \
  -H "External-App-Key: [使用RSA公鑰加密後的AppKey]"

# 響應示例：
# {
#   "code": 0,
#   "msg": "操作成功",
#   "data": {
#     "token": "A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6",
#     "displayName": "張三",
#     "organizationList": [...]
#   }
# }
```

**RSA加密工具示例（使用OpenSSL）：**
```bash
# 1. 將RSA公鑰保存到文件
echo "-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGXG423pFrAhgDGtS1u70ggF+7
XHIQc4Jfsv0OzrCob6Wyt8pmD0PJJipXw3NW82Q/kxnDxaE6fxdiQlLK0uLqDKEi
bxjuJ2tSmGCJmykD4gzM0GobY06ukH076dNhvap5d3oKfeG8XCpYH9lI2SvXcSWb
pGRT1reU940xsO5onwIDAQAB
-----END PUBLIC KEY-----" > public_key.pem

# 2. 加密AppID
echo -n "your_app_id" | openssl rsautl -encrypt -pubin -inkey public_key.pem | base64 -w 0

# 3. 加密AppKey
echo -n "your_app_key" | openssl rsautl -encrypt -pubin -inkey public_key.pem | base64 -w 0
```

### JavaScript (Fetch API)

```javascript
const response = await fetch('/api/external/v1/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'External-App-Id': '[RSA加密後的AppID]',
    'External-App-Key': '[RSA加密後的AppKey]'
  }
});

const result = await response.json();

if (result.code === 0) {
  console.log('登錄成功');
  console.log('Token:', result.data.token);
  console.log('用戶名:', result.data.displayName);
  console.log('組織列表:', result.data.organizationList);
} else {
  console.error('登錄失敗:', result.msg);
}
```

### Java (Spring RestTemplate)

```java
import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public class ExternalLoginExample {

    // 生產環境RSA公鑰
    private static final String RSA_PUBLIC_KEY =
        "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGXG423pFrAhgDGtS1u70ggF+7XHIQc4Jfsv0OzrCob6Wyt8pmD0PJJipXw3NW82Q/kxnDxaE6fxdiQlLK0uLqDKEibxjuJ2tSmGCJmykD4gzM0GobY06ukH076dNhvap5d3oKfeG8XCpYH9lI2SvXcSWbpGRT1reU940xsO5onwIDAQAB";

    /**
     * RSA加密方法
     */
    public static String rsaEncrypt(String plainText, String publicKeyStr) throws Exception {
        byte[] decoded = Base64.getDecoder().decode(publicKeyStr);
        PublicKey publicKey = KeyFactory.getInstance("RSA")
            .generatePublic(new X509EncodedKeySpec(decoded));

        Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);

        byte[] encrypted = cipher.doFinal(plainText.getBytes("UTF-8"));
        return Base64.getEncoder().encodeToString(encrypted);
    }

    public void login() throws Exception {
        // 原始AppID和AppKey（從ESG系統獲取）
        String appId = "your_app_id";
        String appKey = "your_app_key";

        // RSA加密
        String encryptedAppId = rsaEncrypt(appId, RSA_PUBLIC_KEY);
        String encryptedAppKey = rsaEncrypt(appKey, RSA_PUBLIC_KEY);

        // 設置請求頭
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("External-App-Id", encryptedAppId);
        headers.set("External-App-Key", encryptedAppKey);

        // 創建請求實體
        HttpEntity<String> entity = new HttpEntity<>(headers);

        // 發送請求
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<ResultBean> response = restTemplate.exchange(
            "/api/external/v1/login",
            HttpMethod.POST,
            entity,
            ResultBean.class
        );

        // 處理響應
        ResultBean result = response.getBody();
        if (result.getCode() == 0) {
            Map<String, Object> data = (Map<String, Object>) result.getData();
            String token = (String) data.get("token");
            String displayName = (String) data.get("displayName");
            // 使用token進行後續操作
        }
    }
}
```

### Python (requests)

```python
import requests
import base64
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding

def rsa_encrypt(plain_text, public_key_str):
    """RSA加密方法"""
    # 解碼Base64公鑰
    public_key_bytes = base64.b64decode(public_key_str)

    # 載入公鑰
    public_key = serialization.load_der_public_key(public_key_bytes)

    # 加密
    encrypted = public_key.encrypt(
        plain_text.encode('utf-8'),
        padding.PKCS1v15()
    )

    # 返回Base64編碼的加密結果
    return base64.b64encode(encrypted).decode('utf-8')

# 生產環境RSA公鑰
RSA_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGXG423pFrAhgDGtS1u70ggF+7XHIQc4Jfsv0OzrCob6Wyt8pmD0PJJipXw3NW82Q/kxnDxaE6fxdiQlLK0uLqDKEibxjuJ2tSmGCJmykD4gzM0GobY06ukH076dNhvap5d3oKfeG8XCpYH9lI2SvXcSWbpGRT1reU940xsO5onwIDAQAB"

# 原始AppID和AppKey（從ESG系統獲取）
app_id = "your_app_id"
app_key = "your_app_key"

# RSA加密
encrypted_app_id = rsa_encrypt(app_id, RSA_PUBLIC_KEY)
encrypted_app_key = rsa_encrypt(app_key, RSA_PUBLIC_KEY)

url = "https://your-domain.com/api/external/v1/login"
headers = {
    "Content-Type": "application/json",
    "External-App-Id": encrypted_app_id,
    "External-App-Key": encrypted_app_key
}

response = requests.post(url, headers=headers)
result = response.json()

if result["code"] == 0:
    token = result["data"]["token"]
    display_name = result["data"]["displayName"]
    organization_list = result["data"]["organizationList"]
    print(f"登錄成功，Token: {token}")
    print(f"用戶名: {display_name}")
    print(f"組織數量: {len(organization_list)}")
else:
    print(f"登錄失敗: {result['msg']}")
```

---

## 重要說明

### 📋 第三方對接賬戶申請

1. **賬戶註冊：** 需要在ESG系統中註冊第三方對接專用賬戶
2. **權限申請：** 聯繫系統管理員申請第三方對接權限
3. **憑證分配：** 系統管理員將為您分配專用的AppID和AppKey
4. **測試環境：** 建議先在測試環境進行集成測試

**申請流程：**
- 提交第三方對接申請表
- 說明對接用途和業務需求
- 等待管理員審核並分配憑證
- 獲取AppID、AppKey和相關技術文檔

### 🔐 安全要求

1. **RSA加密：** `External-App-Id` 和 `External-App-Key` 必須使用生產環境RSA公鑰進行加密
2. **密鑰保護：** 請妥善保管原始的AppID和AppKey，避免洩露
3. **HTTPS：** 生產環境必須使用HTTPS協議
4. **加密算法：** 使用RSA/ECB/PKCS1Padding加密模式

### 🎯 Token使用

1. **後續認證：** 成功獲取的 `token` 用於後續API調用的身份認證
2. **請求頭設置：** 在其他API請求中使用 `x-auth-token` 頭部傳遞token
3. **有效期管理：** Token具有一定的有效期（默認5天），過期後需要重新登錄
4. **會話管理：** 系統支持單用戶單會話，新登錄會使舊token失效

### 📝 使用流程

1. **申請對接權限** - 在ESG系統中註冊第三方對接賬戶
2. **獲取憑證** - 從系統管理員處獲取AppID和AppKey
3. **實施加密** - 使用生產環境RSA公鑰加密AppID和AppKey
4. **調用登錄接口** - 獲取訪問token
5. **業務調用** - 使用token調用其他業務接口

---

## 後續API調用示例

獲取token後，在其他需要認證的API請求中使用：

```bash
curl -X GET "https://your-domain.com/api/some-endpoint" \
  -H "Content-Type: application/json" \
  -H "x-auth-token: [從登錄接口獲取的token]"
```

---

## 聯繫支持

如有問題，請聯繫技術支持團隊獲取幫助。

**文檔版本：** v1.0  
**最後更新：** 2025-07-07
