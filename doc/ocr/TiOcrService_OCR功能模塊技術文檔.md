# TiOcrService OCR 功能模塊技術文檔

---

## 📋 目錄

1. [概述](#概述)
2. [實現方案簡要描述](#1-實現方案簡要描述)
3. [快速入門指南](#2-快速入門指南)
4. [方法調用鏈路追蹤](#3-方法調用鏈路追蹤)
5. [OCR 核心實現分析](#4-ocr-核心實現分析)
6. [第三方 OCR API 調用詳解](#5-第三方-ocr-api-調用詳解)
7. [AI Agent 智能數據提取實現](#6-ai-agent-智能數據提取實現)
8. [參數傳遞詳解](#7-參數傳遞詳解)
9. [技術實現細節](#8-技術實現細節)
10. [配置和依賴說明](#9-配置和依賴說明)
11. [總結](#總結)

---

## 📖 概述

`TiOcrService` 是一個基於 **Spring Boot** 的服務類，專門負責處理 **OCR（光學字符識別）** 相關的業務邏輯。該服務整合了 **TiOCR 第三方 OCR 服務** 和 **AI 智能分析功能**，提供完整的文檔識別和數據提取解決方案。

### 🎯 核心特性

- ✅ **多格式支持**：JPG、PNG、PDF 文件格式
- ✅ **智能識別**：基於 TiOCR 的高精度文字識別
- ✅ **AI 增強**：HiAgent 大語言模型智能數據提取
- ✅ **高性能**：Redis 緩存優化和並發處理
- ✅ **容錯設計**：完善的異常處理和錯誤恢復機制

---

## 1. 實現方案簡要描述

### 1.1 整體架構設計

TiOcrService OCR 功能模塊採用 **分層處理架構**，將文檔識別過程分為以下幾個核心階段：

#### 🔄 處理層次結構

1. **📁 文件接收與預處理層**
   - 文件格式驗證（支持 JPG、PNG、PDF）
   - 文件大小檢查（最大 25MB）
   - PDF 轉圖像處理

2. **🔄 編碼轉換層**
   - 圖像文件 Base64 編碼
   - 多頁 PDF 合併處理
   - 格式標準化

3. **🌐 第三方 OCR 服務調用層**
   - TiOCR API 集成
   - HTTP 請求封裝
   - 會話管理

4. **📝 結果處理與格式化層**
   - 坐標排序算法
   - 文本行分組
   - 結構化輸出

5. **🤖 AI 智能數據提取層**
   - HiAgent 服務集成
   - 智能會話管理
   - 多模型支持（能源賬單、商務旅行）
   - Redis 緩存優化

### 1.2 核心技術選型

| 技術領域 | 選型方案 | 說明 |
|---------|---------|------|
| **PDF 處理** | Apache PDFBox | 高性能 PDF 解析和圖像轉換 |
| **圖像編碼** | Apache Commons Codec | Base64 編碼/解碼 |
| **HTTP 通信** | 自定義 HttpSimulator | 封裝第三方 API 調用 |
| **文本處理** | 坐標智能分組算法 | 基於坐標的智能分組算法 |
| **AI 智能分析** | HiAgent 大語言模型 | 智能數據提取和理解 |
| **緩存管理** | Redis 緩存 | 會話管理和性能優化 |
| **異常處理** | 分層異常設計 | 提供詳細錯誤信息 |

### 1.3 處理流程概覽

```text
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 用戶上傳文件 │ -> │   文件驗證   │ -> │   格式轉換   │ -> │  OCR識別    │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
       ↓                   ↓                   ↓                   ↓
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│MultipartFile│    │   類型檢查   │    │   Base64    │    │ TiOCR API   │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘

┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   文本處理   │ -> │ AI智能提取   │ -> │   結果返回   │
└─────────────┘    └─────────────┘    └─────────────┘
       ↓                   ↓                   ↓
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   坐標排序   │    │ HiAgent AI  │    │ 結構化數據   │
└─────────────┘    └─────────────┘    └─────────────┘
```

---

## 2. 快速入門指南

### 2.1 🚀 5 分鐘理解 OCR 處理流程

如果您是第一次接觸這個模塊，建議按以下順序閱讀：

| 步驟 | 章節 | 內容 | 預估時間 |
|-----|------|------|---------|
| 1️⃣ | **第 3 節** | 調用鏈路追蹤 - 了解整體處理流程 | 2 分鐘 |
| 2️⃣ | **第 4 節** | 核心方法實現 - 理解關鍵實現邏輯 | 2 分鐘 |
| 3️⃣ | **第 6 節** | 技術實現細節 - 深入了解算法實現 | 1 分鐘 |

### 2.2 💡 核心處理邏輯一句話總結

```text
📄 文件 → ✅ 驗證 → 🔄 轉Base64 → 🔍 調用TiOCR → 📊 智能排序 → 🤖 AI智能提取 → 📋 結構化數據
```

### 2.3 🎯 主要使用場景

#### 📊 能源賬單處理
- **💧 水費單智能識別**：AI 提取帳單號、用量、日期等關鍵信息
- **⚡ 電費單智能識別**：AI 解析電力消費數據和賬戶信息

#### 🎫 票據處理
- **🚄 票據智能識別**：AI 處理火車票、機票等旅行憑證
- **📄 通用文檔OCR**：支持各種格式的文字識別需求

#### 🔧 高級功能
- **🎯 複雜格式處理**：當規則匹配失敗時，AI 自動接管智能提取
- **🌐 多語言支持**：AI 模型支持中英文混合文檔識別

---

## 3. 方法調用鏈路追蹤

### 3.1 🔄 完整調用鏈路圖

```mermaid
graph TD
    A["🌐 Controller接收文件"] --> B["📋 TiOcrService.convertFileToStr"]
    B --> C["✅ validateFile - 文件驗證"]
    C --> D["🔄 TiOcrUtil.convertFile2Base64"]
    D --> E{"📄 文件類型判斷"}
    E -->|PDF| F["🖼️ convertPdf2Jpg - PDF轉JPG"]
    E -->|圖像| G["📊 直接Base64編碼"]
    F --> H["🔐 Base64編碼"]
    G --> H
    H --> I["🔍 TiOcrUtil.ocrStructureInfo"]
    I --> J["📦 構建TiOcrRequest"]
    J --> K["🌐 HTTP POST到TiOCR API"]
    K --> L["📥 解析TiOcrResponse"]
    L --> M["📝 handleItemList - 文本處理"]
    M --> N["📊 坐標排序"]
    N --> O["🔗 行分組算法"]
    O --> P["📋 文本重組"]
    P --> Q["📤 返回格式化文本"]
    Q --> R{"🤖 是否需要AI智能提取"}
    R -->|是| S["🚀 調用AI Agent方法"]
    R -->|否| T["📄 返回OCR結果"]
    S --> U{"📊 文檔類型判斷"}
    U -->|能源賬單| V["⚡ analysisTiOcrEnergyBillInfoByAi"]
    U -->|商務旅行| W["🎫 analysisBusinessTripInfoByAi"]
    V --> X["🔍 檢查Redis緩存會話"]
    W --> Y["🔍 檢查Redis緩存會話"]
    X --> Z{"💾 會話是否存在"}
    Y --> Z
    Z -->|否| AA["🆕 創建新AI會話"]
    Z -->|是| BB["♻️ 使用現有會話"]
    AA --> CC["🤖 HiAgentFacade.createConversation"]
    BB --> DD["📝 構建AI查詢請求"]
    CC --> EE["💾 緩存會話ID到Redis"]
    EE --> DD
    DD --> FF["💬 HiAgentFacade.chatQuery"]
    FF --> GG["🆔 獲取MessageID"]
    GG --> HH["📥 HiAgentFacade.getMessageInfo"]
    HH --> II["🔍 解析AI響應JSON"]
    II --> JJ["✅ 數據驗證和格式化"]
    JJ --> KK["📋 返回結構化數據"]

    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef ai fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px

    class A,T,KK startEnd
    class B,C,D,F,G,H,I,J,K,L,M,N,O,P,Q,S,V,W,X,Y,AA,BB,CC,DD,EE,FF,GG,HH,II,JJ process
    class E,R,U,Z decision
    class V,W,CC,FF,HH ai
```

### 3.2 🔗 關鍵方法調用順序

#### 📋 OCR 基礎處理流程

| 步驟 | 方法名稱 | 功能描述 | 輸入/輸出 |
|-----|---------|---------|----------|
| 1️⃣ | `convertFileToStr()` | 入口方法 | `MultipartFile` → `String` |
| 2️⃣ | `validateFile()` | 文件驗證 | `MultipartFile` → `void` |
| 3️⃣ | `tiOcrUtil.convertFile2Base64()` | 格式轉換 | `MultipartFile` → `String` |
| 4️⃣ | `tiOcrUtil.ocrStructureInfo()` | OCR 識別 | `String` → `String` |
| 5️⃣ | `handleItemList()` | 文本處理 | `List<Item>` → `String` |

#### 🤖 AI Agent 智能提取流程

| 步驟 | 方法名稱 | 功能描述 | 應用場景 |
|-----|---------|---------|----------|
| 6️⃣ | `analysisTiOcrEnergyBillInfoByAi()` | 能源賬單分析 | 水費單、電費單 |
| 6️⃣ | `analysisBusinessTripInfoByAi()` | 商務旅行分析 | 火車票、機票 |
| 7️⃣ | 會話管理 | 檢查和創建 AI 會話 | Redis 緩存優化 |
| 8️⃣ | `hiAgentFacade.chatQuery()` | 智能查詢 | AI 模型調用 |
| 9️⃣ | `hiAgentFacade.getMessageInfo()` | 結果獲取 | 異步消息處理 |
| 🔟 | JSON 解析 | 數據解析 | 業務對象轉換 |

### 3.3 📊 數據流轉過程

#### 🔄 完整數據流轉鏈路

```text
📄 MultipartFile
    ↓
🔢 byte[]
    ↓
🔐 Base64 String
    ↓
📦 TiOcrRequest
    ↓
🌐 HTTP Request
    ↓
📥 TiOcrResponse
    ↓
📋 List<Item>
    ↓
📊 坐標排序
    ↓
🔗 行分組
    ↓
📝 格式化文本
    ↓
🤖 AI查詢請求
    ↓
🚀 HiAgent API
    ↓
💬 AI響應
    ↓
🔍 JSON解析
    ↓
📋 業務對象
```

#### 🎯 AI Agent 調用時機

| 場景類型 | 觸發條件 | 處理策略 |
|---------|---------|---------|
| **💧 能源賬單場景** | OCR 文本無法通過規則匹配提取關鍵信息 | 自動切換到 AI 智能提取 |
| **🎫 商務旅行場景** | 票據格式不符合預定義模式 | 使用專用 AI 模型分析 |
| **📄 通用場景** | 需要智能理解和結構化提取 | 根據文檔類型選擇模型 |

### 3.4 ⏱️ 處理時間分析

| 處理階段 | 預估耗時 | 主要操作 | 性能等級 |
|---------|---------|---------|----------|
| 📋 文件驗證 | `< 10ms` | 大小檢查、類型檢測 | 🟢 極快 |
| 🖼️ PDF轉換 | `100-500ms` | 多頁渲染、圖像合併 | 🟡 中等 |
| 🔐 Base64編碼 | `50-200ms` | 字節數組編碼 | 🟢 快速 |
| 🔍 OCR識別 | `1-3秒` | 第三方API調用 | 🟠 較慢 |
| 📝 文本處理 | `< 50ms` | 坐標排序、分組 | 🟢 極快 |
| **🤖 AI會話創建** | **`200-500ms`** | **首次創建會話** | **🟡 中等** |
| **🚀 AI智能查詢** | **`2-5秒`** | **AI分析和提取** | **🔴 最慢** |
| **🔍 結果解析** | **`< 100ms`** | **JSON解析驗證** | **🟢 快速** |

---

## 4. OCR 核心實現分析

### 4.1 📋 convertFileToStr() 方法完整實現邏輯

#### 🔍 方法簽名與功能

```java
public String convertFileToStr(MultipartFile file) throws FileProcessingException
```

**功能**：將上傳的文件轉換為 OCR 識別後的文本字符串

#### 💻 完整實現代碼

```java
public String convertFileToStr(MultipartFile file) throws FileProcessingException {
    try {
        // ✅ 1. 文件驗證階段
        validateFile(file);

        // 🔄 2. 文件轉換階段
        String base64 = tiOcrUtil.convertFile2Base64(file);

        // 🔍 3. OCR 識別階段
        String ocrResult = tiOcrUtil.ocrStructureInfo(base64);

        // ✅ 4. 結果驗證階段
        if (StringUtil.isEmptyNotTrim(ocrResult)) {
            throw new FileProcessingException.InvalidFileException(
                "OCR未能识别文件内容，请确保文件清晰且格式正确"
            );
        }

        return ocrResult;
    } catch (IOException e) {
        // 🚨 IO異常處理
        throw new FileProcessingException("文件处理失败: " + e.getMessage(), e);
    } catch (Exception e) {
        // 🚨 通用異常處理
        if (e instanceof FileProcessingException) {
            throw e;
        }
        throw new FileProcessingException(
            "文件转换过程中发生未知错误: " + e.getMessage(), e
        );
    }
}
```

#### 🔄 實現邏輯詳解

| 階段 | 步驟 | 功能描述 | 異常處理 |
|-----|------|---------|----------|
| **1️⃣ 文件驗證** | `validateFile(file)` | 文件基礎驗證 | 檢查文件是否為空、大小限制（25MB） |
| **2️⃣ 文件轉換** | `tiOcrUtil.convertFile2Base64(file)` | Base64 編碼轉換 | 支持 JPG、JPEG、PNG、PDF 格式 |
| **3️⃣ OCR 識別** | `tiOcrUtil.ocrStructureInfo(base64)` | OCR 文字識別 | 返回結構化的文本識別結果 |
| **4️⃣ 結果驗證** | `StringUtil.isEmptyNotTrim()` | 結果有效性檢查 | 空結果拋出 `InvalidFileException` |
| **5️⃣ 異常處理** | `try-catch` 機制 | 統一異常處理 | IOException → 業務異常轉換 |

#### 🎯 關鍵設計特點

- ✅ **分層驗證**：文件驗證 → 格式轉換 → OCR識別 → 結果驗證
- 🔄 **異常轉換**：將底層技術異常轉換為業務友好的異常信息
- 📋 **結果保證**：確保返回的文本不為空，提供可靠的處理結果
- 🚨 **錯誤追蹤**：保留原始異常信息，便於問題排查

### 4.2 🔄 TiOcrUtil.convertFile2Base64() 底層調用機制

#### 🔍 方法簽名與功能

```java
public String convertFile2Base64(MultipartFile file) throws IOException
```

**功能**：將多媒體文件轉換為 Base64 編碼字符串，支持圖像和 PDF 文件

#### 💻 完整實現代碼

```java
public String convertFile2Base64(MultipartFile file) throws IOException {
    // 📋 支持的文件類型定義
    String[] availableTypes = {"jpg", "jpeg", "png", "pdf"};

    // 🔍 文件類型檢測
    String fileType = FileUtil.getFileType(file.getBytes());

    // ✅ 類型驗證
    if (!Arrays.asList(availableTypes).contains(fileType)) {
        throw new ServiceException(
            "请选择图片或者PDF类型文件进行解析！" +
            "支持的文件类型: " + String.join(", ", availableTypes) +
            "，当前文件类型: " + fileType
        );
    }

    try {
        // 🔄 條件分支處理
        if ("pdf".equals(FileUtil.getFileType(file.getBytes()))) {
            // 📄 PDF 文件處理
            byte[] bytes = convertPdf2Jpg(file);
            return new String(Base64.encodeBase64(bytes));
        } else {
            // 🖼️ 圖像文件處理
            return new String(Base64.encodeBase64(file.getBytes()));
        }
    } catch (Exception e) {
        throw new ServiceException("文件转换失败", e);
    }
}
```

#### 🔄 底層調用機制詳解

| 步驟 | 功能模塊 | 實現細節 | 技術要點 |
|-----|---------|---------|----------|
| **1️⃣** | **文件類型檢測** | `FileUtil.getFileType()` | 基於文件頭部字節判斷，非擴展名 |
| **2️⃣** | **類型驗證** | 支持類型檢查 | JPG、JPEG、PNG、PDF |
| **3️⃣** | **條件分支** | PDF vs 圖像處理 | 不同文件類型採用不同策略 |
| **4️⃣** | **PDF 轉換** | `convertPdf2Jpg()` | 多頁 PDF 合併為單一 JPG |
| **5️⃣** | **Base64 編碼** | `Base64.encodeBase64()` | Apache Commons Codec |

#### 🎯 處理策略對比

| 文件類型 | 處理流程 | 輸出格式 | 特殊處理 |
|---------|---------|---------|----------|
| **📄 PDF** | `PDF → JPG → Base64` | Base64 字符串 | 多頁合併、DPI 優化 |
| **🖼️ 圖像** | `Image → Base64` | Base64 字符串 | 直接編碼，無需轉換 |

#### 🔧 技術實現亮點

- ✅ **類型安全**：基於文件頭部字節的真實類型檢測
- 🔄 **智能分支**：根據文件類型自動選擇最佳處理策略
- 📋 **錯誤友好**：詳細的錯誤信息，包含支持的文件類型
- ⚡ **性能優化**：圖像文件直接編碼，避免不必要的轉換

### 4.3 📄 PDF 轉 JPG 處理流程和技術實現

#### 🔍 方法簽名與功能

```java
private byte[] convertPdf2Jpg(MultipartFile pdf) throws IOException
```

**功能**：將多頁 PDF 文檔轉換為單一的 JPG 圖像，支持多頁垂直拼接

#### 💻 完整實現代碼

```java
private byte[] convertPdf2Jpg(MultipartFile pdf) throws IOException {
    // 🔄 使用 try-with-resources 確保資源自動釋放
    try (final PDDocument document = Loader.loadPDF(pdf.getInputStream());
         ByteArrayOutputStream imgBos = new ByteArrayOutputStream();
         ImageOutputStream imgOs = ImageIO.createImageOutputStream(imgBos)) {

        // ✅ PDF 文檔驗證
        if (document == null) {
            throw new IOException("PDF 文档为空");
        }

        // 🖼️ 初始化渲染器和變量
        PDFRenderer pdfRenderer = new PDFRenderer(document);
        int pageSize = document.getNumberOfPages();
        int y = 0;
        BufferedImage pdfImage = null;

        // 🔄 遍歷所有頁面進行渲染
        for (int i = 0; i < pageSize; ++i) {
            // 📊 渲染單頁，DPI=149，RGB模式
            BufferedImage bim = pdfRenderer.renderImageWithDPI(i, 149, ImageType.RGB);

            // 🆕 首頁：創建總畫布
            if (i == 0) {
                pdfImage = new BufferedImage(
                    bim.getWidth(),
                    bim.getHeight() * pageSize,
                    BufferedImage.TYPE_INT_RGB
                );
            }

            // 🔗 垂直拼接頁面
            pdfImage.getGraphics().drawImage(bim, 0, y, null);
            y += bim.getHeight();
        }

        // ✅ 結果驗證
        if (pdfImage == null) {
            throw new IOException("生成 PDF 图像失败");
        }

        // 💾 輸出 JPG 格式
        ImageIO.write(pdfImage, "jpg", imgOs);
        return imgBos.toByteArray();
    }
}
```

#### 🔄 技術實現詳解

| 階段 | 技術組件 | 實現細節 | 關鍵參數 |
|-----|---------|---------|----------|
| **1️⃣ 文檔加載** | `PDFBox Loader` | 加載 PDF 文檔 | `try-with-resources` 資源管理 |
| **2️⃣ 渲染配置** | `PDFRenderer` | 頁面渲染器 | `DPI=149`, `RGB 色彩模式` |
| **3️⃣ 畫布創建** | `BufferedImage` | 總畫布初始化 | `寬度×(高度×頁數)` |
| **4️⃣ 頁面拼接** | `Graphics.drawImage()` | 垂直拼接 | `Y 坐標累加` |
| **5️⃣ 圖像輸出** | `ImageIO.write()` | JPG 格式輸出 | `字節數組返回` |

#### 🎯 處理流程圖

```text
📄 PDF 文檔
    ↓
🔍 文檔驗證 (非空檢查)
    ↓
📊 頁面計數 (獲取總頁數)
    ↓
🖼️ 創建總畫布 (寬×高×頁數)
    ↓
🔄 逐頁渲染循環
    ├─ 📋 渲染單頁 (DPI=149, RGB)
    ├─ 🔗 垂直拼接 (Y坐標累加)
    └─ ➡️ 下一頁
    ↓
✅ 結果驗證 (畫布非空)
    ↓
💾 JPG 輸出 (字節數組)
```

#### 🔧 技術亮點與優化

| 特性 | 實現方式 | 優勢 |
|-----|---------|------|
| **🔄 資源管理** | `try-with-resources` | 自動釋放 PDF、流資源 |
| **📊 高清渲染** | `DPI=149` | 平衡清晰度與文件大小 |
| **🔗 智能拼接** | 垂直拼接算法 | 保持頁面順序和完整性 |
| **⚡ 內存優化** | 流式處理 | 避免大文件內存溢出 |
| **✅ 錯誤處理** | 多層驗證 | 確保輸出質量可靠 |

## 5. 第三方 OCR API 調用詳解

### 5.1 TiOcrUtil.ocrStructureInfo() 方法詳解

````java
public String ocrStructureInfo(String base64Image) {
    if (StringUtils.isBlank(base64Image)) {
        log.error("图片Base64为空，请检查！");
        return null;
    }
    Map<String, String> headers = getHeader();

    var sid = UUID.randomUUID().toString();
    log.info("TiOCR识别开始, sessionId: {}", sid);

    TiOcrRequest tiOcrRequest = TiOcrRequest.builder().appId("123").sessionId(sid).image(base64Image).ocrTemplate("ocr").build();
    String url = getUrl(tiOcrSmartStructuralOcrV3);
    String s = HttpSimulator.sendPostRequest(url, CommonUtil.toJson(tiOcrRequest), headers);

    log.info("TiOCR识别结束, sessionId: {}, 响应结果: {}", sid, s);

    TiOcrResponse tiOcrResponse = null;
    try {
        tiOcrResponse = JsonUtils.getObjectMapper().readValue(s, TiOcrResponse.class);
    } catch (Exception e) {
        throw new RuntimeException(e);
    }
    log.info("TiOCR识别结束, tiOcrResponse: {}", tiOcrResponse);

    var res = tiOcrResponse.getRecognizeList().get(0);
    if (res == null || res.getItemContent() == null || CollectionUtils.isEmpty(res.getItemContent().getItemList())) {
        log.warn("TiOCR识别结果为空, sessionId: {}", sid);
        return null;
    }
    String result = handleItemList(res.getItemContent().getItemList());
    log.info("解析后的result:{}", result);

    return result;
}
````

#### 調用鏈路詳解：

1. **參數驗證**
   - 檢查 Base64 圖像字符串是否為空
   - 記錄錯誤日誌並返回 null

2. **請求準備**
   - 生成唯一的 sessionId 用於追蹤
   - 構建 HTTP 請求頭（Content-Type: application/json）
   - 創建 TiOcrRequest 對象

3. **API 調用**
   - 使用 `HttpSimulator.sendPostRequest()` 發送 HTTP POST 請求
   - 目標 URL：`{base-url}/youtu/ocrapi/smart_structural_ocr_v3`
   - 請求體：JSON 格式的 TiOcrRequest

4. **響應處理**
   - 使用 Jackson 解析 JSON 響應為 TiOcrResponse 對象
   - 提取識別結果列表中的第一個結果

5. **結果處理**
   - 調用 `handleItemList()` 處理識別項目列表
   - 返回格式化的文本結果

## 6. AI Agent 智能數據提取實現

### 6.1 AI Agent 調用流程概述

AI Agent 智能數據提取是在 OCR 基礎文本識別之後的高級處理階段，主要用於從格式化文本中智能提取結構化的業務數據。該功能通過集成 HiAgent 服務，利用大語言模型的理解能力，實現對不同類型文檔的智能解析。

#### 調用時機判斷：

```java
// 水費單解析示例
if (strResult.contains("水務署") && strResult.contains("用戶編號") && strResult.contains("付款通知書")) {
    // 使用規則匹配提取
    String billNo = extractValue(strResult, "用戶編[号號][:：]\\s*(\\d+(?:\\s+\\d+)*)", 1);
    // ... 其他規則匹配邏輯
} else {
    // 當規則匹配失敗時，調用 AI Agent
    tiOcrEnergyBillListVO = analysisTiOcrEnergyBillInfoByAi(strResult, "水費單");
}
```

### 6.2 能源賬單 AI 提取實現

#### 6.2.1 analysisTiOcrEnergyBillInfoByAi() 方法詳解

```java
public TiOcrEnergyBillListVO analysisTiOcrEnergyBillInfoByAi(String strResult, String type)
        throws OcrAnalysisException, JsonProcessingException {
    if (StringUtil.isEmptyNotTrim(strResult)) {
        throw new OcrAnalysisException.DataExtractionException("OCR结果为空，无法进行" + type + "分析");
    }
    TiOcrEnergyBillListVO tiOcrEnergyBillListVO = new TiOcrEnergyBillListVO();
    Object cacheObj = redisUtil.get(esgRedisKey + CacheConstants.OCR_AGENT_CONVERSATION + ContextUtils.getCurrentUser().getUsername());
    String appConversationID = StringUtil.cast(cacheObj);
    if (StringUtil.isEmpty(appConversationID)) {
        HiAgentParamVO hiAgentParamVO = new HiAgentParamVO();
        hiAgentParamVO.setApp(3);
        Map<String, Object> data = new HashMap<>();
        hiAgentParamVO.setData(data);
        String conversation = hiAgentFacade.createConversation(hiAgentParamVO);
        AgentCreateConversationResponse createConversationResponse = null;
        try {
            createConversationResponse = JsonUtils.getObjectMapper().readValue(conversation, AgentCreateConversationResponse.class);
            redisUtil.set(esgRedisKey + CacheConstants.OCR_AGENT_CONVERSATION + ContextUtils.getCurrentUser().getUsername(), createConversationResponse.getConversation().getAppConversationID(), 300);
            appConversationID = createConversationResponse.getConversation().getAppConversationID();
        } catch (Exception e) {
            throw new OcrAnalysisException.AiAnalysisException("创建AI对话失败: " + e.getMessage(), e);
        }
    }
    HiAgentParamVO paramVO = new HiAgentParamVO();
    paramVO.setApp(3);
    Map<String, Object> data = new HashMap<>();
    data.put("AppConversationID", appConversationID);
    String query = "提取" + type + "帳單號、起始日期、結束日期、用量";
    data.put("Query", query + "\n" + strResult);
    data.put("ResponseMode", "streaming");
    paramVO.setData(data);
    try {
        String queryResult = hiAgentFacade.chatQuery(paramVO);
        String messageID = extractTaskId(queryResult);

        if (StringUtil.isEmptyNotTrim(messageID)) {
            throw new OcrAnalysisException.AiAnalysisException("AI查询失败，未获取到有效的消息ID");
        }

        data.clear();
        data.put("MessageID", messageID);
        paramVO.setData(data);
        String messageInfo = hiAgentFacade.getMessageInfo(paramVO);

        AgentMessageInfoResponse messageInfoResponse = JsonUtils.getObjectMapper().readValue(messageInfo, AgentMessageInfoResponse.class);
        String answer = messageInfoResponse.getMessageInfo().getAnswerInfo().getAnswer().replace("```","").replace("json", "");
        tiOcrEnergyBillListVO = JsonUtils.getObjectMapper().readValue(answer, TiOcrEnergyBillListVO.class);

        // Validate the result
        if (tiOcrEnergyBillListVO == null || tiOcrEnergyBillListVO.getList() == null || tiOcrEnergyBillListVO.getList().isEmpty()) {
            throw new OcrAnalysisException.DocumentParsingException(type);
        }

    } catch (Exception e) {
        if (e instanceof OcrAnalysisException) {
            throw e;
        }
        throw new OcrAnalysisException.AiAnalysisException("AI分析" + type + "失败: " + e.getMessage(), e);
    }
    return tiOcrEnergyBillListVO;
}
```

#### 6.2.2 實現邏輯詳解：

1. **輸入驗證**
   - 檢查 OCR 結果字符串是否為空
   - 如果為空則拋出 `DataExtractionException`

2. **會話管理**
   - 從 Redis 緩存中獲取用戶的 AI 會話 ID
   - 緩存鍵格式：`{esgRedisKey}:OCR_AGENT_CONVERSATION:{username}`
   - 會話有效期：300 秒（5 分鐘）

3. **創建新會話**（如果緩存中不存在）
   - 使用 `app=3`（能源賬單專用 AI 模型）
   - 調用 `hiAgentFacade.createConversation()` 創建會話
   - 將會話 ID 緩存到 Redis

4. **構建 AI 查詢**
   - 查詢模板：`"提取" + type + "帳單號、起始日期、結束日期、用量"`
   - 將 OCR 文本附加到查詢中
   - 設置響應模式為 `streaming`

5. **執行 AI 查詢**
   - 調用 `hiAgentFacade.chatQuery()` 發送查詢
   - 從響應中提取 `messageID`
   - 使用 `messageID` 獲取最終結果

6. **結果處理**
   - 解析 AI 響應的 JSON 格式
   - 清理 JSON 中的 markdown 標記
   - 轉換為 `TiOcrEnergyBillListVO` 對象
   - 驗證結果的完整性

### 6.3 商務旅行 AI 提取實現

#### 6.3.1 analysisBusinessTripInfoByAi() 方法完整實現

```java
public TiOcrBusinessTripListVO analysisBusinessTripInfoByAi(String strResult, String type)
        throws OcrAnalysisException, JsonProcessingException {
    if (StringUtil.isEmptyNotTrim(strResult)) {
        throw new OcrAnalysisException.DataExtractionException("OCR结果为空，无法进行" + type + "分析");
    }
    TiOcrBusinessTripListVO tiOcrBusinessTripListVO = new TiOcrBusinessTripListVO();
    Object cacheObj = redisUtil.get(esgRedisKey + CacheConstants.OCR_AGENT_CONVERSATION + ContextUtils.getCurrentUser().getUsername());
    String appConversationID = StringUtil.cast(cacheObj);
    if (StringUtil.isEmpty(appConversationID)) {
        HiAgentParamVO hiAgentParamVO = new HiAgentParamVO();
        hiAgentParamVO.setApp(2);  // 使用商務旅行專用模型
        Map<String, Object> data = new HashMap<>();
        hiAgentParamVO.setData(data);
        String conversation = hiAgentFacade.createConversation(hiAgentParamVO);
        AgentCreateConversationResponse createConversationResponse = null;
        try {
            createConversationResponse = JsonUtils.getObjectMapper().readValue(conversation, AgentCreateConversationResponse.class);
            redisUtil.set(esgRedisKey + CacheConstants.OCR_AGENT_CONVERSATION + ContextUtils.getCurrentUser().getUsername(), createConversationResponse.getConversation().getAppConversationID(), 300);
            appConversationID = createConversationResponse.getConversation().getAppConversationID();
        } catch (Exception e) {
            throw new OcrAnalysisException.AiAnalysisException("创建AI对话失败: " + e.getMessage(), e);
        }
    }
    HiAgentParamVO paramVO = new HiAgentParamVO();
    paramVO.setApp(2);  // 使用商務旅行專用模型
    Map<String, Object> data = new HashMap<>();
    data.put("AppConversationID", appConversationID);
    String query = "提取" + type + "起始地、目的地、座位等级、月份";
    data.put("Query", query + "\n" + strResult);
    data.put("ResponseMode", "streaming");
    paramVO.setData(data);
    try {
        String queryResult = hiAgentFacade.chatQuery(paramVO);
        String messageID = extractTaskId(queryResult);

        if (StringUtil.isEmptyNotTrim(messageID)) {
            throw new OcrAnalysisException.AiAnalysisException("AI查询失败，未获取到有效的消息ID");
        }

        data.clear();
        data.put("MessageID", messageID);
        paramVO.setData(data);
        String messageInfo = hiAgentFacade.getMessageInfo(paramVO);

        AgentMessageInfoResponse messageInfoResponse = JsonUtils.getObjectMapper().readValue(messageInfo, AgentMessageInfoResponse.class);
        String answer = messageInfoResponse.getMessageInfo().getAnswerInfo().getAnswer().replace("```","").replace("json", "");
        tiOcrBusinessTripListVO = JsonUtils.getObjectMapper().readValue(answer, TiOcrBusinessTripListVO.class);

        // Validate the result
        if (tiOcrBusinessTripListVO == null || tiOcrBusinessTripListVO.getList() == null || tiOcrBusinessTripListVO.getList().isEmpty()) {
            throw new OcrAnalysisException.DocumentParsingException(type);
        }

        // 特殊處理：座位等級轉換
        for(TiOcrBusinessTripVO vo : tiOcrBusinessTripListVO.getList()) {
            vo.setLevel(CabinCodeEnum.getChineseName(vo.getLevel()));
        }

    } catch (Exception e) {
        if (e instanceof OcrAnalysisException) {
            throw e;
        }
        throw new OcrAnalysisException.AiAnalysisException("AI分析" + type + "失败: " + e.getMessage(), e);
    }
    return tiOcrBusinessTripListVO;
}
```

#### 6.3.2 與能源賬單的差異：

1. **AI 模型差異**
   - 商務旅行使用 `app=2`
   - 能源賬單使用 `app=3`

2. **提取字段差異**
   - 商務旅行：起始地、目的地、座位等級、月份
   - 能源賬單：帳單號、起始日期、結束日期、用量

3. **後處理邏輯**
   - 商務旅行需要進行座位等級的中文轉換
   - 使用 `CabinCodeEnum.getChineseName()` 進行映射

### 6.4 HiAgentFacade 調用機制

#### 6.4.1 核心方法說明

1. **createConversation()**
   - 創建新的 AI 對話會話
   - 返回會話 ID 用於後續查詢
   - 支持不同的 AI 應用模型

2. **chatQuery()**
   - 發送查詢請求到 AI 服務
   - 支持流式響應模式
   - 返回包含 messageID 的響應

3. **getMessageInfo()**
   - 根據 messageID 獲取 AI 處理結果
   - 返回結構化的 JSON 響應
   - 包含提取的業務數據

#### 6.4.2 參數配置詳解

```java
// HiAgentParamVO 參數結構
{
    "app": 2 或 3,  // AI 應用模型選擇
    "data": {
        "AppConversationID": "會話ID",
        "Query": "提取指令 + OCR文本",
        "ResponseMode": "streaming",
        "MessageID": "消息ID"  // 僅在 getMessageInfo 時使用
    }
}
```

### 6.5 AI 會話管理和 Redis 緩存策略

#### 6.5.1 緩存鍵設計

```java
// 緩存鍵格式
String cacheKey = esgRedisKey + CacheConstants.OCR_AGENT_CONVERSATION + ContextUtils.getCurrentUser().getUsername();

// 實際示例
// dev-susdev:OCR_AGENT_CONVERSATION:john.doe
// susdev:OCR_AGENT_CONVERSATION:jane.smith
```

#### 6.5.2 緩存策略

1. **緩存有效期**：300 秒（5 分鐘）
2. **緩存粒度**：按用戶維度緩存
3. **緩存更新**：每次創建新會話時更新
4. **緩存失效**：自動過期或手動清理

#### 6.5.3 會話復用邏輯

```java
// 檢查緩存中的會話
Object cacheObj = redisUtil.get(cacheKey);
String appConversationID = StringUtil.cast(cacheObj);

if (StringUtil.isEmpty(appConversationID)) {
    // 創建新會話
    createNewConversation();
} else {
    // 復用現有會話
    reuseExistingConversation(appConversationID);
}
```

### 6.6 不同文檔類型的 AI 提取邏輯差異

#### 6.6.1 文檔類型映射表

| 文檔類型 | AI App ID | 查詢模板 | 輸出對象 |
|---------|-----------|---------|---------|
| 水費單 | 3 | 提取水費單帳單號、起始日期、結束日期、用量 | TiOcrEnergyBillListVO |
| 電費單 | 3 | 提取電費單帳單號、起始日期、結束日期、用量 | TiOcrEnergyBillListVO |
| 車票 | 2 | 提取車票起始地、目的地、座位等级、月份 | TiOcrBusinessTripListVO |
| 機票 | 2 | 提取機票起始地、目的地、座位等级、月份 | TiOcrBusinessTripListVO |

#### 6.6.2 查詢語句構建邏輯

```java
// 能源賬單查詢構建
String query = "提取" + type + "帳單號、起始日期、結束日期、用量";
data.put("Query", query + "\n" + strResult);

// 商務旅行查詢構建
String query = "提取" + type + "起始地、目的地、座位等级、月份";
data.put("Query", query + "\n" + strResult);
```

### 6.7 AI 響應的 JSON 解析和結果驗證機制

#### 6.7.1 響應處理流程

```java
// 1. 獲取 AI 響應
AgentMessageInfoResponse messageInfoResponse = JsonUtils.getObjectMapper().readValue(messageInfo, AgentMessageInfoResponse.class);

// 2. 清理響應內容
String answer = messageInfoResponse.getMessageInfo().getAnswerInfo().getAnswer()
    .replace("```","")
    .replace("json", "");

// 3. 解析為業務對象
tiOcrEnergyBillListVO = JsonUtils.getObjectMapper().readValue(answer, TiOcrEnergyBillListVO.class);

// 4. 驗證結果完整性
if (tiOcrEnergyBillListVO == null ||
    tiOcrEnergyBillListVO.getList() == null ||
    tiOcrEnergyBillListVO.getList().isEmpty()) {
    throw new OcrAnalysisException.DocumentParsingException(type);
}
```

#### 6.7.2 數據結構定義

**TiOcrEnergyBillListVO 結構：**

```java
@Data
public class TiOcrEnergyBillListVO {
    private List<TiOcrEnergyBillVO> list;

    public TiOcrEnergyBillListVO() {
        list = new ArrayList<>();
    }
}

@Data
public class TiOcrEnergyBillVO {
    //帳單號
    private String billNo;
    //起始時間
    private String fromTime;
    //结束時間
    private String toTime;
    //用量
    private String consumption;
}
```

**TiOcrBusinessTripListVO 結構：**

```java
@Data
public class TiOcrBusinessTripListVO {
    private List<TiOcrBusinessTripVO> list;
}

@Data
public class TiOcrBusinessTripVO {
    // 月份
    private Integer monthValue;
    // 起始地点
    private String startPlace;
    // 目的地
    private String destination;
    // 火车班次
    private String routeNo;
    // 座位
    private String level;
}
```

#### 6.7.3 錯誤處理和異常恢復機制

**異常類型層次：**

1. **OcrAnalysisException.DataExtractionException**
   - OCR 結果為空時拋出
   - 提供清晰的錯誤提示

2. **OcrAnalysisException.AiAnalysisException**
   - AI 服務調用失敗
   - 會話創建失敗
   - JSON 解析失敗

3. **OcrAnalysisException.DocumentParsingException**
   - AI 返回結果為空或格式錯誤
   - 數據驗證失敗

**異常恢復策略：**

```java
try {
    // AI 處理邏輯
    String queryResult = hiAgentFacade.chatQuery(paramVO);
    // ... 其他處理
} catch (Exception e) {
    if (e instanceof OcrAnalysisException) {
        // 重新拋出業務異常
        throw e;
    }
    // 包裝為 AI 分析異常
    throw new OcrAnalysisException.AiAnalysisException("AI分析" + type + "失败: " + e.getMessage(), e);
}
```

### 6.8 extractTaskId() 輔助方法

#### 6.8.1 消息 ID 提取邏輯

```java
public String extractTaskId(String rawData) {
    ObjectMapper mapper = new ObjectMapper();
    String[] lines = rawData.split("\\r?\\n"); // 兼容不同系统的换行符

    for (String line : lines) {
        // 匹配有效数据行
        if (line.startsWith("data:data: ")) {
            try {
                String jsonStr = line.substring("data:data: ".length()).trim();
                JsonNode node = mapper.readTree(jsonStr);
                if (node.has("task_id")) {
                    return node.get("task_id").asText();
                }
            } catch (Exception e) {
                System.err.println("JSON解析失败: " + e.getMessage());
            }
        }
    }
    return null;
}
```

#### 6.8.2 流式響應解析

1. **響應格式**：Server-Sent Events (SSE) 格式
2. **數據前綴**：`data:data: `
3. **JSON 結構**：包含 `task_id` 字段
4. **錯誤處理**：忽略解析失敗的行，繼續處理

## 7. 參數傳遞詳解

### 7.1 TiOcrRequest 對象結構

````java
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TiOcrRequest {

    /**
     * 接入服务时生成的唯一id，用于唯一标识接入业务(私有化部署之后，app_id填写任意的由数字组成的字符串即可)
     * 必填
     */
    @JsonProperty("app_id")
    private String appId;

    /**
     * 图像base64编码后的字符串，图像需是JPG、PNG、BMP其中之一的格式
     * 必填
     */
    private String image;

    /**
     * 用户自定义的唯一会话id
     * 选填
     */
    @JsonProperty("session_id")
    private String sessionId;

    /**
     * 要运行服务的模板名称,根据传入的模板来调用不同算法模型返回结果。
     * 1、当模板为rec时，调用切片文本识别；
     * 2、当模板为ocr时，调用检测识别；
     * 3、当模板为iocr时，调用智能结构化；
     * 4、当模板为det时,调用智能检测；
     * 5、当模板为det_ocr时，调用定制结构化；
     * 6、当模板为det_ocr_iocr时，调用定制结构化+智能结构化；
     * 必填
     */
    @JsonProperty("ocr_template")
    private String ocrTemplate;

    private Options options;
}
````

#### 參數說明：

- **appId**: 固定值 "123"，用於標識應用
- **image**: Base64 編碼的圖像數據
- **sessionId**: UUID 生成的會話標識符
- **ocrTemplate**: 固定值 "ocr"，使用檢測識別模板
- **options**: 可選配置項（當前未使用）

### 7.2 TiOcrResponse 對象結構

````java
@Data
public class TiOcrResponse {

    /**
     * 错误码
     */
    @JsonProperty("error_code")
    private Integer errorCode;

    /**
     * 错误码消息
     */
    @JsonProperty("error_message")
    private String errorMessage;

    /**
     * 相应请求的session标识符，可用于结果查询
     */
    @JsonProperty("session_id")
    private String sessionId;

    @JsonProperty("recognize_list")
    private List<RecognizeResult> recognizeList;

    @Data
    public static class RecognizeResult {
        /**
         * 图片旋转角度(角度制)，文本的水平方向为0°；顺时针为正，逆时针为负
         */
        private Double angle;

        /**
         * 识别出的OCR信息
         */
        @JsonProperty("item_content")
        private ItemContent itemContent;

        @Data
        public static class ItemContent {
            @JsonProperty("item_list")
            private List<Item> itemList;

            @Data
            public static class Item {
                /**
                 * 实体编号
                 */
                private Integer id;

                /**
                 * 当item_type为key时，该字段为标准key名；其他情况下为字段名称（即key）
                 */
                private String name;

                /**
                 * 当item_type为key时，该字段为原文key名；其他情况下为字段内容（即value）
                 */
                private String content;

                /**
                 * 实体类型(ocr/kv/kv-unhinted/key/row/group)
                 */
                @JsonProperty("item_type")
                private String itemType;

                /**
                 * 字段内容在原图中的矩形框坐标, 左上角(x,y)，width为框宽，height为框高
                 */
                private Coord coord;

                /**
                 * 字段内容的置信度
                 */
                private Double conf;
            }
        }
    }
}
````

#### 響應結構說明：

- **errorCode/errorMessage**: 錯誤信息
- **sessionId**: 會話標識符
- **recognizeList**: 識別結果列表
  - **angle**: 圖像旋轉角度
  - **itemContent**: 識別內容
    - **itemList**: 識別項目列表
      - **id**: 實體編號
      - **name**: 字段名稱
      - **content**: 字段內容
      - **itemType**: 實體類型
      - **coord**: 坐標信息
      - **conf**: 置信度

## 7. 技術實現細節

### 7.1 文件類型檢測和驗證機制

````java
private void validateFile(MultipartFile file) throws FileProcessingException {
    if (file == null || file.isEmpty()) {
        throw new FileProcessingException.InvalidFileException("文件不能为空");
    }

    // Check file size (25MB limit as per application.properties)
    long maxSize = 25 * 1024 * 1024; // 25MB in bytes
    if (file.getSize() > maxSize) {
        throw new FileProcessingException.FileSizeExceededException(file.getSize(), maxSize);
    }

    // Additional validation can be added here
}
````

#### 驗證機制：

1. **空文件檢查**
   - 檢查 MultipartFile 對象是否為 null
   - 檢查文件是否為空（isEmpty()）

2. **文件大小限制**
   - 最大文件大小：25MB（配置在 application.properties 中）
   - 超過限制時拋出 `FileSizeExceededException`

3. **文件類型驗證**
   - 在 `convertFile2Base64()` 方法中進行
   - 使用 `FileUtil.getFileType()` 檢測真實文件類型
   - 支持：JPG、JPEG、PNG、PDF

### 7.2 OCR 結果文本處理和格式化邏輯

````java
private static String handleItemList(List<TiOcrResponse.RecognizeResult.ItemContent.Item> itemList) {
    // 定义误差范围
    double tolerance = 3.0;

    // 先对 itemList 按照 y 坐标进行排序
    itemList.sort(Comparator.comparingDouble(item -> item.getCoord().getY()));

    // 分组逻辑
    Map<Integer, List<TiOcrResponse.RecognizeResult.ItemContent.Item>> groupedItems = new HashMap<>();
    int groupKey = 0;

    for (var item : itemList) {
        if (groupedItems.isEmpty()) {
            // 如果是第一个元素，创建一个新的组
            groupedItems.put(groupKey, new ArrayList<>());
            groupedItems.get(groupKey).add(item);
        } else {
            // 获取当前组的最后一个元素
            var currentGroup = groupedItems.get(groupKey);
            var lastItem = currentGroup.get(currentGroup.size() - 1);

            // 比较当前元素的 y 值与最后一个元素的 y 值
            double yDiff = Math.abs(item.getCoord().getY() - lastItem.getCoord().getY());

            if (yDiff <= tolerance) {
                // 如果差值在误差范围内，添加到当前组
                currentGroup.add(item);
            } else {
                // 否则，创建一个新的组
                groupKey++;
                groupedItems.put(groupKey, new ArrayList<>());
                groupedItems.get(groupKey).add(item);
            }
        }
    }

    // 构建输出字符串
    StringBuilder result = new StringBuilder();
    for (var group : groupedItems.values()) {
        for (var item : group) {
            result.append(item.getContent()).append(" ");
        }
        result.append("\n");
    }
    log.info("ocr result: {}", result);
    // 打印结果
    return result.toString();
}
````

#### 文本處理算法：

1. **坐標排序**
   - 按照 Y 坐標對識別項目進行排序
   - 確保文本按照從上到下的順序處理

2. **行分組算法**
   - 設置容差值（tolerance = 3.0）
   - 比較相鄰項目的 Y 坐標差值
   - 差值在容差範圍內的項目歸為同一行

3. **文本重組**
   - 同一行內的文本用空格連接
   - 不同行之間用換行符分隔
   - 構建最終的文本字符串

### 7.3 異常處理和錯誤恢復機制

#### 異常層次結構：

1. **FileProcessingException**
   - `InvalidFileException`: 無效文件異常
   - `FileSizeExceededException`: 文件大小超限異常

2. **OcrAnalysisException**
   - `DataExtractionException`: 數據提取異常
   - `AiAnalysisException`: AI 分析異常
   - `DocumentParsingException`: 文檔解析異常

#### 錯誤恢復策略：

1. **文件處理階段**
   - 捕獲 IOException 並轉換為業務異常
   - 提供詳細的錯誤信息和建議

2. **OCR 識別階段**
   - 檢查 OCR 結果的有效性
   - 空結果時提供清晰的錯誤提示

3. **AI 分析階段**
   - 驗證 AI 響應的完整性
   - 處理 JSON 解析異常

## 8. 配置和依賴說明

### 8.1 OCR 服務配置參數

```properties
# TiOCR 配置
tiocr.base-url=http://************:60099
tiocr.smart_structural_ocr_v3=/youtu/ocrapi/smart_structural_ocr_v3

# HiAgent AI 服務配置
hiagent.url=https://hiagent.3311csci.com
hiagent.create-conversation=/api/proxy/api/v1/create_conversation
hiagent.chat-query=/api/proxy/api/v1/chat_query
hiagent.get_message_info=/api/proxy/api/v1/get_message_info
hiagent.esg.apikey=cvrj68jbg4roomp7ticg
hiagent.esg.trip.ocrApikey=d0gke6bbg4roomp9rj50
hiagent.esg.energy.ocrApikey=d18h3e16lovqj16ts8gg

# 文件上傳限制
spring.servlet.multipart.max-file-size=25MB
spring.servlet.multipart.max-request-size=25MB
```

### 8.2 相關依賴庫

#### 核心依賴：

1. **PDFBox** (org.apache.pdfbox)
   - 版本：用於 PDF 文檔處理
   - 功能：PDF 轉圖像、文檔解析

2. **Apache Commons Codec**
   - 功能：Base64 編碼/解碼

3. **Jackson** (com.fasterxml.jackson)
   - 功能：JSON 序列化/反序列化

4. **Spring Boot Web**
   - 功能：MultipartFile 處理、HTTP 客戶端

5. **Spring Data Redis**
   - 功能：Redis 緩存操作、會話管理

#### AI Agent 相關依賴：

1. **HiAgentFacade**: AI 服務調用門面
   - 功能：創建會話、發送查詢、獲取結果
   - 支持：流式響應、多模型切換

2. **AgentCreateConversationResponse**: AI 會話響應模型
   - 功能：會話創建結果封裝

3. **AgentMessageInfoResponse**: AI 消息響應模型
   - 功能：AI 分析結果封裝

#### 工具類依賴：

1. **HttpSimulator**: HTTP 請求工具
2. **JsonUtils**: JSON 處理工具
3. **FileUtil**: 文件類型檢測工具
4. **StringUtil**: 字符串處理工具
5. **RedisUtil**: Redis 緩存操作工具
6. **ContextUtils**: 用戶上下文工具

### 8.3 性能優化和資源管理策略

#### 性能優化：

1. **內存管理**
   - 使用 try-with-resources 自動釋放資源
   - 及時清理大型 BufferedImage 對象
   - 流式處理避免內存溢出

2. **並發處理**
   - 使用 UUID 生成唯一會話 ID
   - 支持多線程並發 OCR 請求
   - 無狀態設計確保線程安全

3. **緩存策略**
   - Redis 緩存 AI 會話信息
   - 避免重複創建會話，提高響應速度
   - 會話有效期 300 秒，平衡性能和資源使用

4. **AI 服務優化**
   - 智能會話復用機制
   - 多模型並行支持（app=2, app=3）
   - 流式響應處理，減少等待時間
   - 異步消息處理，提高併發能力

#### 資源管理：

1. **文件處理**
   - 限制文件大小（25MB）
   - 及時釋放文件流和圖像資源

2. **網絡連接**
   - 使用連接池管理 HTTP 連接
   - 設置合理的超時時間

3. **日誌記錄**
   - 詳細記錄處理過程和性能指標
   - 便於問題排查和性能監控

## 總結

TiOcrService 的 OCR 功能模塊通過整合 TiOCR 第三方服務、智能文本處理算法和 AI Agent 智能數據提取，提供了完整的端到端文檔識別解決方案。該模塊具有以下特點：

### 核心技術優勢

- **高可靠性**：完善的異常處理和錯誤恢復機制
- **高性能**：優化的內存管理和並發處理能力
- **高擴展性**：模塊化設計，易於擴展新的文檔類型
- **高可維護性**：清晰的代碼結構和詳細的日誌記錄

### AI 智能化特色

- **智能理解**：集成 HiAgent 大語言模型，具備文檔內容理解能力
- **自適應提取**：當規則匹配失敗時自動切換到 AI 智能提取
- **多模型支持**：針對不同文檔類型使用專門的 AI 模型
- **會話管理**：智能的 Redis 緩存會話機制，提高處理效率

### 業務價值

- **準確性提升**：AI 輔助提取顯著提高了複雜文檔的識別準確率
- **適應性強**：能夠處理各種格式和佈局的文檔
- **處理效率**：會話復用和緩存機制大幅提升處理速度
- **維護成本低**：減少了手工規則維護的工作量

### 技術架構亮點

- **分層處理**：OCR 基礎識別 + AI 智能提取的雙層架構
- **容錯設計**：多級異常處理和降級策略
- **性能優化**：緩存、並發、資源管理的全方位優化
- **監控完善**：全鏈路日誌記錄和性能監控

通過本文檔的詳細說明，開發團隊可以更好地理解和維護 OCR 功能模塊，特別是 AI Agent 智能數據提取的實現機制，為後續的功能擴展和性能優化提供技術支持。該模塊為企業數字化轉型中的文檔自動化處理提供了強有力的技術保障。
