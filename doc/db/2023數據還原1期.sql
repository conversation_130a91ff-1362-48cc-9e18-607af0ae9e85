select h.organization_id, count(*) 
from t_ambient_head h
left join t_ambient_detail d on h.id = d.head_id
where year = 2023
group by h.organization_id
having organization_id not in (

select h.organization_id 
from t_ambient_head_bak_baoshijie h
left join t_ambient_detail_bak_baoshijie d on h.id = d.head_id
where year = 2023
group by h.organization_id
)


--數據還原
--1把t_ambient_head 和t_ambient_detail備份到t_ambient_head_new 和t_ambient_detail_new 
--2把要還原的項目的t_ambient_head 和t_ambient_detail刪除，再進去環境積效表單頁面進行初始化
--3根據unit_code把t_ambient_detail_20241107的month_value,season_value,year_total_value,remark的數值賦到初始化後的t_ambient_detail 裡（這裡可以用orgainzation_id做關聯去找出是哪一個表，
--或者直接在頁面接口上看head_id)
--4如果新字段有填數值的話，也可以把t_ambient_detail_new的month_value,season_value,year_total_value,remark的數值賦到初始化後的t_ambient_detail 裡
SELECT *
INTO t_ambient_head_bak_baoshijie
FROM t_ambient_head
WHERE 1 = 0;
INSERT into t_ambient_head_bak_baoshijie
select * from  t_ambient_head;
SELECT *
INTO t_ambient_detail_bak_baoshijie
FROM t_ambient_detail
WHERE 1 = 0;
INSERT into t_ambient_detail_bak_baoshijie
select * from  t_ambient_detail;

DELETE h 
from t_ambient_head h
where h.id in (
select d.head_id
from t_ambient_head_bak_baoshijie h
left join t_ambient_detail_bak_baoshijie d on h.id = d.head_id
where year = 2023
group by head_id
having count(*) = 34
);

DELETE d 
from t_ambient_detail d
where d.head_id in (
select d.head_id
from t_ambient_head_bak_baoshijie h
left join t_ambient_detail d on h.id = d.head_id
where year = 2023
group by head_id
having count(*) = 34
)

--在這裡先去頁面進行初始化
select o.name
from t_ambient_head h
left join t_organization o on h.organization_id = o.id
where h.id in (
select d.head_id
from t_ambient_head h
left join t_ambient_detail d on h.id = d.head_id
where year = 2023
group by head_id
having count(*) = 34
)

UPDATE d set d.head_id = (
	select _h.id 
	from t_ambient_head_bak_baoshijie _h 
	where _h.organization_id = h.organization_id 
	and _h.year = h.year and _h.month = h.month
)
from t_ambient_head h
left join t_ambient_detail d on d.head_id = h.id
where h.organization_id in (
	select h.organization_id
	from t_ambient_head_bak_baoshijie h
	left join t_ambient_detail_bak_baoshijie d on h.id = d.head_id
	where year = 2023
	group by organization_id
	having count(*) = 34
)
and h.year = 2023 and h.month = 12

UPDATE h set h.id = (
	select _h.id 
	from t_ambient_head_bak_baoshijie _h 
	where _h.organization_id = h.organization_id 
	and _h.year = h.year and _h.month = h.month
)
from t_ambient_head h
left join t_ambient_detail d on d.head_id = h.id
where h.organization_id in (
	select h.organization_id
	from t_ambient_head_bak_baoshijie h
	left join t_ambient_detail_bak_baoshijie d on h.id = d.head_id
	where year = 2023
	group by organization_id
	having count(*) = 34
) 
and h.year = 2023 and h.month = 12


UPDATE t_ambient_detail
SET 
    month_value_1 = t6.month_value_1,
    month_value_2 = t6.month_value_2,
    month_value_3 = t6.month_value_3,
    season_value_1 = t6.season_value_1,
    month_value_4 = t6.month_value_4,
    month_value_5 = t6.month_value_5,
    month_value_6 = t6.month_value_6,
    season_value_2 = t6.season_value_2,
    month_value_7 = t6.month_value_7,
    month_value_8 = t6.month_value_8,
    month_value_9 = t6.month_value_9,
    season_value_3 = t6.season_value_3,
    month_value_10 = t6.month_value_10,
    month_value_11 = t6.month_value_11,
    month_value_12 = t6.month_value_12,
    season_value_4 = t6.season_value_4,
    year_total_value = t6.year_total_value,
    remark = t6.remark
FROM (
    SELECT t1.did, t2.*
    FROM (
        SELECT d.id AS did, h.organization_id, d.unit_code
        FROM t_ambient_head h
        LEFT JOIN t_ambient_detail d ON h.id = d.head_id
		where h.id in (
			select d.head_id
			from t_ambient_head_bak_baoshijie h
			left join t_ambient_detail_bak_baoshijie d on h.id = d.head_id
			where year = 2023 and month = 12
			group by head_id
			having count(*) = 34
		)
    ) t1
    JOIN (
        SELECT h.organization_id, d.unit_code, d.month_value_1, d.month_value_2, d.month_value_3, d.season_value_1, d.month_value_4, d.month_value_5, d.month_value_6, d.season_value_2, d.month_value_7, d.month_value_8, d.month_value_9, d.season_value_3, d.month_value_10, d.month_value_11, d.month_value_12, d.season_value_4, d.year_total_value, d.remark, d.seq
        FROM t_ambient_head_bak_baoshijie h
        LEFT JOIN t_ambient_detail_20241107 d ON h.id = d.head_id
		where h.id in (
			select d.head_id
			from t_ambient_head_bak_baoshijie h
			left join t_ambient_detail_bak_baoshijie d on h.id = d.head_id
			where year = 2023 and month = 12
			group by head_id
			having count(*) = 34
		)
		and try_cast(d.year_total_value as decimal(18, 4)) > 0 
    ) t2 ON t1.organization_id = t2.organization_id AND t1.unit_code = t2.unit_code
) t6
WHERE t_ambient_detail.id = t6.did

UPDATE t_ambient_detail
SET 
    month_value_1 = t6.month_value_1,
    month_value_2 = t6.month_value_2,
    month_value_3 = t6.month_value_3,
    season_value_1 = t6.season_value_1,
    month_value_4 = t6.month_value_4,
    month_value_5 = t6.month_value_5,
    month_value_6 = t6.month_value_6,
    season_value_2 = t6.season_value_2,
    month_value_7 = t6.month_value_7,
    month_value_8 = t6.month_value_8,
    month_value_9 = t6.month_value_9,
    season_value_3 = t6.season_value_3,
    month_value_10 = t6.month_value_10,
    month_value_11 = t6.month_value_11,
    month_value_12 = t6.month_value_12,
    season_value_4 = t6.season_value_4,
    year_total_value = t6.year_total_value,
    remark = t6.remark
FROM (
    SELECT t1.did, t2.*
    FROM (
        SELECT d.id AS did, h.organization_id, d.unit_code
        FROM t_ambient_head h
        LEFT JOIN t_ambient_detail d ON h.id = d.head_id
		where year = 2023 and month = 12
    ) t1
    JOIN (
        SELECT h.organization_id, d.unit_code, d.month_value_1, d.month_value_2, d.month_value_3, d.season_value_1, d.month_value_4, d.month_value_5, d.month_value_6, d.season_value_2, d.month_value_7, d.month_value_8, d.month_value_9, d.season_value_3, d.month_value_10, d.month_value_11, d.month_value_12, d.season_value_4, d.year_total_value, d.remark, d.seq
        FROM t_ambient_head_bak_baoshijie h
        LEFT JOIN t_ambient_detail_bak_baoshijie d ON h.id = d.head_id
		where year = 2023 and month = 12
    ) t2 ON t1.organization_id = t2.organization_id AND t1.unit_code = t2.unit_code
) t6
WHERE t_ambient_detail.id = t6.did;


UPDATE t_ambient_detail
SET 
    month_value_1 = t6.month_value_1,
    month_value_2 = t6.month_value_2,
    month_value_3 = t6.month_value_3,
    season_value_1 = t6.season_value_1,
    month_value_4 = t6.month_value_4,
    month_value_5 = t6.month_value_5,
    month_value_6 = t6.month_value_6,
    season_value_2 = t6.season_value_2,
    month_value_7 = t6.month_value_7,
    month_value_8 = t6.month_value_8,
    month_value_9 = t6.month_value_9,
    season_value_3 = t6.season_value_3,
    month_value_10 = t6.month_value_10,
    month_value_11 = t6.month_value_11,
    month_value_12 = t6.month_value_12,
    season_value_4 = t6.season_value_4,
    year_total_value = t6.year_total_value,
    remark = t6.remark
FROM (
    SELECT t1.did, t2.*
    FROM (
        SELECT d.id AS did, h.organization_id, d.*
        FROM t_ambient_head h
        LEFT JOIN t_ambient_detail d ON h.id = d.head_id
		where year = 2023 and month = 12
    ) t1
    JOIN (
        SELECT h.organization_id, d.unit_code, d.month_value_1, d.month_value_2, d.month_value_3, d.season_value_1, d.month_value_4, d.month_value_5, d.month_value_6, d.season_value_2, d.month_value_7, d.month_value_8, d.month_value_9, d.season_value_3, d.month_value_10, d.month_value_11, d.month_value_12, d.season_value_4, d.year_total_value, d.remark, d.seq
        FROM t_ambient_head_bak_baoshijie_1119 h
        LEFT JOIN t_ambient_detail_bak_baoshijie_1119 d ON h.id = d.head_id
		where try_cast(d.year_total_value as decimal(18, 4)) > 0 
		and year = 2023 and month = 12
    ) t2 ON t1.organization_id = t2.organization_id AND t1.unit_code = t2.unit_code
	where try_cast(t1.year_total_value as decimal(18, 4)) < try_cast(t2.year_total_value as decimal(18, 4)) or t1.year_total_value is null
) t6
WHERE t_ambient_detail.id = t6.did
