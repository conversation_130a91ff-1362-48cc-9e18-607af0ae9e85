--用來修改所有不一致的字段字符集
SELECT a.name, b.name AS colName, b.collation, b.prec, b.isnullable, 
'ALTER TABLE dbo.' + a.name + ' ALTER COLUMN ' + b.name + ' NVARCHAR(' + CAST(b.prec AS nvarchar) + ')  COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS ' + (CASE WHEN b.isnullable = 0 THEN 'NOT NULL' ELSE 'NULL' END)  as sqlStr
FROM ESG..SysObjects a
LEFT JOIN SysColumns b ON b.id = Object_Id(a.Name)
Where a.XType='U' AND b.collation != N'Chinese_Hong_Kong_Stroke_90_CI_AS'
ORDER BY a.name, b.name