-- auto-generated definition
create table t_carbon_factor
(
    id                   varchar(50)    not null
        primary key,
    rec_value            decimal(10, 2) null comment '记录的碳因子数值',
    rec_date             datetime       null comment '记录获取碳因子数值的时间',
    material_code        varchar(50)    null comment '材料id',
    supplier_code        varchar(50)    null comment '供应商编码',
    creation_time        datetime       null,
    create_username      varchar(50)    null,
    last_update_time     datetime       null,
    last_update_username varchar(50)    null,
    last_update_version  int            null
)
    comment '碳因子';

-- auto-generated definition
create table t_carbon_neutral
(
    id                   varchar(50)    not null
        primary key,
    material_code        varchar(50)    null comment '材料编码',
    stat_date            datetime       null comment '统计时间',
    stat_value           decimal(20, 2) null comment '数值',
    project_code         varchar(50)    null comment '项目编码',
    company_code         varchar(50)    null comment '公司编码',
    creation_time        datetime       null,
    create_username      varchar(50)    null,
    last_update_time     datetime       null,
    last_update_username varchar(50)    null,
    last_update_version  int            null
)
    comment '碳中和指标';

-- auto-generated definition
create table t_material
(
    id                   varchar(50)  not null
        primary key,
    code                 varchar(50)  null comment '材料编码',
    name                 varchar(500) null comment '材料名称',
    creation_time        datetime     null,
    create_username      varchar(50)  null,
    last_update_time     datetime     null,
    last_update_username varchar(50)  null,
    last_update_version  int          null,
    constraint t_material_material_code_uindex
        unique (code)
)
    comment '材料表';

-- auto-generated definition
create table t_project
(
    id                   varchar(50)  not null
        primary key,
    code                 varchar(50)  null comment '项目编码',
    name                 varchar(500) null comment '项目名称',
    creation_time        datetime     null,
    create_username      varchar(50)  null,
    last_update_time     datetime     null,
    last_update_username varchar(50)  null,
    last_update_version  int          null,
    constraint t_project_code_uindex
        unique (code)
)
    comment '项目信息表';

-- auto-generated definition
create table t_supplier
(
    id                   varchar(50)  not null
        primary key,
    code                 varchar(50)  null comment '供应商编码',
    name                 varchar(500) null comment '供应商名称',
    creation_time        datetime     null,
    create_username      varchar(50)  null,
    last_update_time     datetime     null,
    last_update_username varchar(50)  null,
    last_update_version  int          null,
    constraint t_supplier_code_uindex
        unique (code)
)
    comment '供应商信息';

