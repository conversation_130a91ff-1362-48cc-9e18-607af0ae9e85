DECLARE @dtStart DATETIME
DECLARE @dtEnd DATETIME
DECLARE @dtCurrent DATETIME
 
SET @dtStart='2014-10-01'
SET @dtEnd='2022-10-02'
SET @dtCurrent=@dtStart

DELETE FROM EquipmentMonthlyUsage WHERE CalculateDate = CONVERT(date, GETDATE())

--生成月份列表
INSERT INTO EquipmentMonthlyUsage
SELECT EU.SiteName, P.NameEN, CONVERT(VARCHAR(6),DATEADD(month,number,@dtStart),112) AS RecordYearMonth, 
EU.Name, EU.Model, EU.CarbonFactor, EU.CarbonFactorUnit, NULL AS Qty, '小時' AS Unit, 
NULL AS CarbonAmount, '機械設備' AS Scope, GETDATE() AS CalculateDate
FROM master..spt_values 
CROSS JOIN Tzh_EquipmentUsage EU 
LEFT JOIN Tzh_Protocol P ON EU.ProtocolId = P.Id
WHERE type = 'p' AND DATEADD(month,number,@dtStart) <= @dtEnd AND EU.IsDeleted = 0

--計算每月日數
WHILE(@dtCurrent<@dtEnd) --如果小于结束日期,一直下面
BEGIN

UPDATE EquipmentMonthlyUsage 
SET Qty = ISNULL(Qty, 0) + EU.DailyWorkingHour, CarbonAmount = (ISNULL(Qty, 0) + EU.DailyWorkingHour) * EU.CarbonFactor
FROM EquipmentMonthlyUsage EMU 
LEFT JOIN Tzh_EquipmentUsage EU ON EMU.Name = EU.Name AND EMU.Model = EU.Model AND EU.IsDeleted = 0
WHERE RecordYearMonth = YEAR(@dtCurrent)*100 + MONTH(@dtCurrent)

SET @dtCurrent=dateadd(day,1,@dtCurrent)
END

SELECT * FROM EquipmentMonthlyUsage

