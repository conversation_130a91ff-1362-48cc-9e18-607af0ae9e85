USE [ESG_NEW]
GO
/****** Object:  StoredProcedure [dbo].[sp_Daily_Calculate_All]    Script Date: 9/1/2023 2:44:01 pm ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

--[sp_Daily_Calculate_All] 

ALTER PROCEDURE [dbo].[sp_Daily_Calculate_All]
AS
BEGIN

SET NOCOUNT ON


--CALCULATE SYSTEM DATA FROM CDMS FOR MATERIAL & WASTE WATER
INSERT INTO ESG_SYS_ETL_LOG
SELECT 'ESG_ETL_Material_WasteWater', 1, 'CALCULATE Material_WasteWater    '+  ' START', GETDATE() --+ CONVERT(VARCHAR(10),@SiteId) +

EXEC sp_Daily_Calculate_Material_WasteWater 

INSERT INTO ESG_SYS_ETL_LOG
SELECT 'ESG_ETL_Material_WasteWater', 1, 'CALCULATE Material_WasteWater    ' + ' END', GETDATE() --+ CONVERT(VARCHAR(10),@SiteId) +

INSERT INTO ESG_SYS_ETL_LOG
SELECT 'ESG_ETL_Electricity_Water_Gas', 1, 'CALCULATE Electricity_Water_Gas    '+  ' START', GETDATE() --+ CONVERT(VARCHAR(10),@SiteId) +

EXEC sp_Daily_Calculate_Electricity_Water_Gas 

INSERT INTO ESG_SYS_ETL_LOG
SELECT 'ESG_ETL_Electricity_Water_Gas', 1, 'CALCULATE Electricity_Water_Gas    ' + ' END', GETDATE() --+ CONVERT(VARCHAR(10),@SiteId) +


DELETE FROM F_Result_Latest WHERE CalculateDate = CONVERT(NVARCHAR(10),GETDATE(),120)

DECLARE @SiteId_R INT
DECLARE @SiteName NVARCHAR(100) 
DECLARE @s_date DATE
DECLARE @e_date DATE

DECLARE MY_CURSOR_RESULT CURSOR 
	LOCAL STATIC READ_ONLY FORWARD_ONLY
FOR 
SELECT SO.Id AS SiteId, P.Name AS SiteName, P.StartDate, IIF(EndDate<GETDATE(), EndDate, CONVERT(DATE, GETDATE())) AS EndDate FROM sysOrganization(NOLOCK) SO 
LEFT JOIN Tzh_ProjectInfo(NOLOCK) P ON P.Code = SO.Code AND SO.IsDeleted = 0 
WHERE P.IsDeleted = 0 AND P.MaterialSource = N'工程管理系統'
OPEN MY_CURSOR_RESULT
FETCH NEXT FROM MY_CURSOR_RESULT INTO @SiteId_R,@SiteName,@s_date,@e_date
WHILE @@FETCH_STATUS = 0

BEGIN

INSERT INTO ESG_SYS_ETL_LOG
SELECT 'ESG_ETL_F_Result_Latest', 2, 'INSERT INTO F_Result_Latest for siteid ' + CONVERT(VARCHAR(10),@SiteId_R) +' and sitename' + @SiteName + ' END', GETDATE()

IF OBJECT_ID('tempdb.dbo.#TEMP_D_DATE2', 'U') IS NOT NULL
	DROP TABLE #TEMP_D_DATE2; 

;with cte as (
	select convert(date,left(convert(varchar,@s_date,112),6) + '01') startDate,
			month(@s_date) n
	union all
	select dateadd(month,n,convert(date,convert(varchar,year(@s_date)) + '0101')) startDate,
		(n+1) n
	from cte
	where n < month(@s_date) + datediff(month,@s_date,@e_date)
)
select startdate, dateadd(month,1,startdate) enddate,convert(int,convert(nvarchar(6),startdate,112)) as RecordYearMonth
INTO #TEMP_D_DATE2
from cte
option (maxrecursion 0);

INSERT INTO F_Result_Latest
SELECT @SiteId_R,@SiteName,A.RecordYearMonth,A.CarbonEmissionLocation,A.ScopeMain,A.ScopeDetail,CarbonAmount,CONVERT(NVARCHAR(10),GETDATE(),120) as CalculateDate
FROM (
SELECT D.RecordYearMonth,S.* 
FROM #TEMP_D_DATE2 D,[D_Scope] S
) A
LEFT JOIN 
(
SELECT SiteId,SiteName,RecordYearMonth,CarbonEmissionLocation,Scope,SUM(CarbonAmount) AS CarbonAmount
FROM 
(
	SELECT SiteId,SiteName,RecordYearMonth,CarbonEmissionLocation,Scope,SUM(CarbonAmount) AS CarbonAmount 
	FROM [F_CombineAll] WHERE CalculateDate = CONVERT(NVARCHAR(10),GETDATE(),120) and SiteId = @SiteId_R AND Status <> 'ESTIMATE'
	GROUP BY SiteId,SiteName,RecordYearMonth,CarbonEmissionLocation,Scope

	UNION ALL
	--FOR MATERIAL TRANSFORT FACTOR
	SELECT SiteId,SiteName,RecordYearMonth,C.CarbonEmissionLocation,TransportScope AS Scope ,SUM(TransportCarbonAmount) AS CarbonAmount 
	FROM [F_CombineAll] C 
	WHERE CalculateDate = CONVERT(NVARCHAR(10),GETDATE(),120) and SiteId = @SiteId_R AND Status <> 'ESTIMATE'
	GROUP BY SiteId,SiteName,RecordYearMonth,C.CarbonEmissionLocation,TransportScope
	UNION ALL
	--FOR 廢棄物運輸 TRANSFORT FACTOR
	SELECT SiteId,SiteName,RecordYearMonth,C.CarbonEmissionLocation,TransportScope AS Scope ,SUM(TransportCarbonAmount) AS CarbonAmount 
	FROM [F_CombineAll] C 
	WHERE CalculateDate = CONVERT(NVARCHAR(10),GETDATE(),120) and SiteId = @SiteId_R AND Status <> 'ESTIMATE' AND TransportScope = N'廢棄物運輸'
	GROUP BY SiteId,SiteName,RecordYearMonth,C.CarbonEmissionLocation,TransportScope
) R 
GROUP BY SiteId,SiteName,RecordYearMonth,CarbonEmissionLocation,Scope
) C ON C.CarbonEmissionLocation = A.CarbonEmissionLocation AND C.Scope= A.ScopeDetail and C.RecordYearMonth = A.RecordYearMonth
order by RecordYearMonth


FETCH NEXT FROM MY_CURSOR_RESULT INTO @SiteId_R,@SiteName,@s_date,@e_date

END
CLOSE MY_CURSOR_RESULT
DEALLOCATE MY_CURSOR_RESULT


DECLARE @SiteName_T NVARCHAR(100) 
DECLARE @s_date_T DATE
DECLARE @e_date_T DATE

DECLARE MY_CURSOR_RESULT CURSOR 
	LOCAL STATIC READ_ONLY FORWARD_ONLY
FOR 
SELECT DISTINCT Name AS SiteName,StartDate,IIF(EndDate<GETDATE(), EndDate, CONVERT(DATE, GETDATE())) AS EndDate FROM [dbo].[Tzh_ProjectInfo](NOLOCK) 
WHERE IsDeleted = 0 AND MaterialSource != N'工程管理系統'
OPEN MY_CURSOR_RESULT
FETCH NEXT FROM MY_CURSOR_RESULT INTO @SiteName_T,@s_date_T,@e_date_T
WHILE @@FETCH_STATUS = 0

BEGIN

IF OBJECT_ID('tempdb.dbo.#TEMP_D_DATE1', 'U') IS NOT NULL
	DROP TABLE #TEMP_D_DATE1; 

;with cte as (
	select convert(date,left(convert(varchar,@s_date_T,112),6) + '01') startDate,
			month(@s_date_T) n
	union all
	select dateadd(month,n,convert(date,convert(varchar,year(@s_date_T)) + '0101')) startDate,
		(n+1) n
	from cte
	where n < month(@s_date_T) + datediff(month,@s_date_T,@e_date_T)
)
select startdate, dateadd(month,1,startdate) enddate,convert(int,convert(nvarchar(6),startdate,112)) as RecordYearMonth
INTO #TEMP_D_DATE1
from cte
option (maxrecursion 0);

INSERT INTO F_Result_Latest (SiteId,SiteName,RecordYearMonth,CarbonEmissionLocation,ScopeMain,ScopeDetail, CalculateDate, CarbonAmount)
SELECT SiteId,@SiteName_T,A.RecordYearMonth,A.CarbonEmissionLocation,A.ScopeMain,A.ScopeDetail,CONVERT(NVARCHAR(10),GETDATE(),120) as CalculateDate, CarbonAmount 
FROM (
SELECT D.RecordYearMonth,S.* 
FROM #TEMP_D_DATE1 D,[D_Scope] S
) A
LEFT JOIN 
(
SELECT SiteId,SiteName,RecordYearMonth,CarbonEmissionLocation,Scope,SUM(CarbonAmount) AS CarbonAmount
FROM 
(
	SELECT SiteId,SiteName,RecordYearMonth,CarbonEmissionLocation,Scope,SUM(CarbonAmount) AS CarbonAmount 
	FROM [F_CombineAll] WHERE CalculateDate = CONVERT(NVARCHAR(10),GETDATE(),120) and SiteName = @SiteName_T
	GROUP BY SiteId,SiteName,RecordYearMonth,CarbonEmissionLocation,Scope
	
	UNION ALL
	--FOR MATERIAL TRANSFORT FACTOR
	SELECT SiteId,SiteName,RecordYearMonth,CarbonEmissionLocation,TransportScope AS Scope ,SUM(TransportCarbonAmount) AS CarbonAmount 
	FROM [F_CombineAll] C 
	WHERE CalculateDate = CONVERT(NVARCHAR(10),GETDATE(),120) and SiteName = @SiteName_T AND Status <> 'ESTIMATE'
	GROUP BY SiteId,SiteName,RecordYearMonth,CarbonEmissionLocation,TransportScope
	

) R 
GROUP BY SiteId,SiteName,RecordYearMonth,CarbonEmissionLocation,Scope
) C ON C.CarbonEmissionLocation = A.CarbonEmissionLocation AND C.Scope= A.ScopeDetail and C.RecordYearMonth = A.RecordYearMonth
order by RecordYearMonth



FETCH NEXT FROM MY_CURSOR_RESULT INTO @SiteName_T,@s_date_T,@e_date_T

END
CLOSE MY_CURSOR_RESULT
DEALLOCATE MY_CURSOR_RESULT


END

