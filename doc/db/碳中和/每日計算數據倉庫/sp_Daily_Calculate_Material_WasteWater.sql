USE [ESG_NEW]
GO
/****** Object:  StoredProcedure [dbo].[sp_Daily_Calculate_Material_WasteWater]    Script Date: 9/1/2023 2:39:37 pm ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


ALTER PROCEDURE [dbo].[sp_Daily_Calculate_Material_WasteWater]
AS
BEGIN

SET NOCOUNT ON
--F_Material
DELETE FROM F_Material_Detail WHERE CalculateDate = CONVERT(date,GETDATE(),120)
INSERT INTO F_Material_Detail
(Region,SiteId,SiteName,RecordYearMonth,CarbonEmissionLocation,MaterialCode,ChineseName,
Unit,Description,CarbonFactor,CarbonFactorUnit,Qty,CDMS_Unit,CarbonAmount,Scope,CalculateDate,
BillNo,BizDate,DeliveryNoteNo,DeliveryDate,FubandanNo)
SELECT P.Region AS Region,inv.DepartmentId AS SiteId,P.Name,FORMAT(inv.BizDate,'yyyyMM') as <PERSON><PERSON><PERSON><PERSON> ,<PERSON><PERSON>,
<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>.<PERSON>,<PERSON>.<PERSON>, <PERSON><PERSON>,M.<PERSON>t,
dd.ReceivedQty,BM.Unit AS CDMS_Unit,M.CarbonFactor * dd.ReceivedQty  AS CarbonAmount,
M.Scope,CONVERT(date,GETDATE(),120) as CalculateDate,INV.BillNo,INV.BizDate,INVD.DeliveryNoteNo,INVD.DeliveryDate,PM.billNo AS fubandan_no 
FROM purInvoice  INV
LEFT JOIN purInvoiceDetail INVD ON INV.Id  = INVD.BillId
LEFT JOIN purDeliveryDetail dd  ON dd.BillId = INVD.DeliveryNoteId 
LEFT JOIN  basMaterial BM ON BM.id = dd.MaterialId 
LEFT JOIN purPaymentDetail PD ON PD.InvoiceId = INV.Id
LEFT JOIN purPayment PM ON PM.Id = PD.BillId
LEFT JOIN sysOrganization SO ON SO.Id = inv.DepartmentId AND SO.IsDeleted = 0
LEFT JOIN Tzh_ProjectInfo P ON P.Code = SO.Code AND P.IsDeleted = 0
LEFT JOIN D_CDMS_MaterialCarbonFactor M ON M.MaterialCode = BM.Code AND ((M.MaterialAttribute IS NULL) OR (M.MaterialAttribute IS NOT NULL AND M.MaterialAttribute = dd.MaterAttribute1)) AND M.SiteName = P.Name AND ((M.MaterialType IS NULL AND dd.carbonFactorType IS NULL) OR (M.MaterialType IS NOT NULL AND M.MaterialType = dd.carbonFactorType))
WHERE INV.IsDeleted = 0 AND  INVD.IsDeleted = 0 
AND dd.IsDeleted = 0 
AND BM.Code IS NOT NULL
AND INV.BizDate >= P.StartDate AND INV.BizDate < P.EndDate
AND M.isDeleted = 0
AND (PD.isDeleted = 0 or PD.isDeleted is NULL)
AND (PM.isDeleted = 0 or PM.isDeleted is NULL)

DELETE FROM F_Material_invoice WHERE CalculateDate = CONVERT(date,GETDATE(),120)
INSERT INTO F_Material_invoice
(Region,SiteId,SiteName,RecordYearMonth,CarbonEmissionLocation,MaterialCode,ChineseName,
Unit,Description,CarbonFactor,CarbonFactorUnit,transportFactor,transportFactorUnit,transportDistance, transportDistanceUnit,
Qty   ,CDMS_Unit,CarbonAmount,TransportCarbonAmount, Scope,TransportScope, CalculateDate,
BillNo,FubandanNo)
SELECT
	t1.Region,
	t1.SiteId,
	t1.SiteName,
	t1.RecordYearMonth,
	t1.CarbonEmissionLocation,
	t1.MaterialCode,
	t1.ChineseName,
	t1.Unit,
	t1.Description,
	t1.CarbonFactor,
	t1.CarbonFactorUnit,
	ISNULL(t2.transportFactor, 0) AS transportFactor,
	t2.transportFactorUnit,
	ISNULL(t2.transportDistance, 0) AS transportDistance,
	t2.transportDistanceUnit,
	t1.qtytotal,
	t1.CDMS_Unit,
	t1.CarbonAmountTotal,
	ISNULL(t1.qtytotal,0) * ISNULL(t2.transportFactor,0) * ISNULL(t2.transportDistance,0) AS TransportCarbonAmountTotal, 
	t1.Scope,
	N'建材、物資及機械設備' as TransportScope,
	t1.CalculateDate,
	t1.BillNo,
	t1.FubandanNo
FROM
	(
	SELECT
		Region,
		SiteId,
		SiteName,
		RecordYearMonth,
		CarbonEmissionLocation,
		MaterialCode,
		ChineseName,
		Unit,
		Description,
		CarbonFactor,
		CarbonFactorUnit,
		CDMS_Unit,
		Scope,
		CalculateDate,
		BillNo,
		FubandanNo,
		SUM ( ISNULL(Qty,0) ) AS qtytotal, 
		SUM ( ISNULL(CarbonAmount,0) ) AS CarbonAmountTotal 
	FROM
		F_Material_Detail
	GROUP BY
		Region,
		SiteId,
		SiteName,
		RecordYearMonth,
		CarbonEmissionLocation,
		MaterialCode,
		ChineseName,
		Unit,
		Description,
		CarbonFactor,
		CarbonFactorUnit,
		CDMS_Unit,
		Scope,
		CalculateDate,
		BillNo,
		FubandanNo 
	) t1
	LEFT JOIN Tzh_MaterialInvoiceTransport t2 ON t1.SiteName = t2.SiteName 
	AND t1.BillNo = t2.BillNo 
	AND t1.MaterialCode = t2.MaterialCode  
	AND t2.isDeleted = 0



DELETE FROM F_Material WHERE CalculateDate = CONVERT(date,GETDATE(),120) 
insert into F_Material (
Region,
	SiteId,
	SiteName,
	RecordYearMonth,
	CarbonEmissionLocation,
	MaterialCode,
	ChineseName,
	Unit,
	Description,
	CarbonFactor,
	CarbonFactorUnit,
	Scope,
	TransportScope,
	CalculateDate,Qty,CarbonAmount,TransportCarbonAmount
)
SELECT
	Region,
	SiteId,
	SiteName,
	RecordYearMonth,
	CarbonEmissionLocation,
	MaterialCode,
	ChineseName,
	Unit,
	Description,
	CarbonFactor,
	CarbonFactorUnit,
	Scope,
	TransportScope,
	CalculateDate,
	SUM ( ISNULL(Qty,0) ) AS Qty,
	SUM ( ISNULL(CarbonAmount,0) ) AS CarbonAmount,
	SUM ( ISNULL(TransportCarbonAmount,0) ) AS TransportCarbonAmount 
FROM
	F_Material_invoice
GROUP BY
	Region,
	SiteId,
	SiteName,
	RecordYearMonth,
	CarbonEmissionLocation,
	MaterialCode,
	ChineseName,
	Unit,
	Description,
	CarbonFactor,
	CarbonFactorUnit,
	CDMS_Unit,
	Scope,
	TransportScope,
	CalculateDate
	

--针对中海外写字楼和其余地盘的相关场景  明细数据
DELETE FROM  Tzh_OtherSite_Detail WHERE CalculateDate = CONVERT(date,GETDATE(),120) 
insert into  Tzh_OtherSite_Detail(
	Region,
	SiteName,
	RecordYearMonth,
	CarbonEmissionLocation,
	MaterialCode,
	ChineseName,
	Unit,
	Description,
	CarbonFactor,
	CarbonFactorUnit,
	Qty,
	CarbonAmount,
	Scope,
	CalculateDate,
	billNo,
	FubandanNo,
	TransportFactor,
	TransportFactorUnit,
	TransportDistance,
	TransportDistanceUnit,
	TransportCarbonAmount,
	TransportScope
)
SELECT
	p.Region,
	p.Name,
	t.RecordYearMonth,
	f.CarbonEmissionLocation,
	t.MaterialCode,
	t.ChineseName,
	t.Unit,
	f.Description AS Description,
	f.CarbonFactor AS CarbonFactor,
	f.CarbonFactorUnit AS CarbonFactorUnit,
	ISNULL(t.Qty,0) as QTY,
	( ISNULL(t.Qty,0) * ISNULL(f.CarbonFactor,0) ) AS CarbonAmount,
	f.Scope AS Scope,
	CAST ( GETDATE( ) AS DATE ) AS CalculateDate,
	t.BillNo,
	t.BillNo AS FubandanNo,
	t.TransportFactor AS TransportFactor,
	t.TransportFactorUnit AS TransportFactorUnit,
	t.TransportDistance,
	t.TransportDistanceUnit,	
	( ISNULL(t.Qty,0) * ISNULL(t.TransportFactor,0) * ISNULL(t.TransportDistance,0) ) AS TransportCarbonAmount,
	CASE ( ISNULL(t.Qty,0) * ISNULL(t.TransportFactor,0) * ISNULL(t.TransportDistance,0) ) WHEN 0 THEN NULL ELSE N'建材、物資及機械設備' END AS TransportScope
FROM
	Tzh_MonthlyMaterialCarbon t
	LEFT JOIN Tzh_ProjectInfo p ON p.Name = t.SiteName AND p.IsDeleted = 0
	LEFT JOIN D_CDMS_MaterialCarbonFactor f ON f.MaterialCode = t.MaterialCode 
	AND (f.MaterialAttribute = t.MaterialAttribute OR (f.MaterialAttribute IS NULL AND f.MaterialAttribute IS NULL)) 
	AND f.SiteName = t.SiteName
	AND f.IsDeleted = 0
	WHERE p.MaterialSource != N'工程管理系統' 
	AND	t.IsDeleted = 0

--针对中海外写字楼和其余地盘的相关场景  汇总数据
DELETE FROM  Tzh_OtherSite WHERE CalculateDate = CONVERT(date,GETDATE(),120) 
insert into  Tzh_OtherSite(
	Region,
	SiteName,
	RecordYearMonth,
	CarbonEmissionLocation,
	MaterialCode,
	MaterialAttribute,
	MaterialType,
	ChineseName,
	Unit,
	Description,
	CarbonFactor,
	CarbonFactorUnit,
	Scope,
	TransportScope,
	CalculateDate,
	QtyTotal,
	CarbonAmountTotal,
	TransportCarbonAmountTotal
)
SELECT 
	Region,
	SiteName,
	RecordYearMonth,
	CarbonEmissionLocation,
	MaterialCode,
	MaterialAttribute,
	MaterialType,
	ChineseName,
	Unit,
	Description,
	CarbonFactor,
	CarbonFactorUnit,
	Scope,
	TransportScope,
	CalculateDate,
	SUM(qty) AS qtyTotal,
	SUM(CarbonAmount) AS CarbonAmountTotal,
	SUM(TransportCarbonAmount) AS TransportCarbonAmountTotal
FROM  Tzh_OtherSite_Detail WHERE CalculateDate = CONVERT(date,GETDATE(),120)  
	group by 
	Region,
	SiteName,
	RecordYearMonth,
	CarbonEmissionLocation,
	MaterialCode,
	MaterialAttribute,
	MaterialType,
	ChineseName,
	Description,
	CarbonFactor,
	CarbonFactorUnit,
	Scope,
	Unit,
	TransportScope,
	CalculateDate

--F_WasterWater
DELETE FROM F_WasterWater_Detail WHERE CalculateDate = CONVERT(date,GETDATE(),120)

INSERT INTO F_WasterWater_Detail 
(Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,Qty,QtyUnit,CarbonFactorRecordYearMonth,Volume,ProportionIndex,CarbonFactor,CarbonFactorUnit,Scope,CarbonAmount,CalculateDate,
BillNo,BizDate,DeliveryNoteNo,DeliveryDate,FubandanNo)
SELECT 
	P.Region,
	so.id,
	p.name,
	N'地盤',
	CONVERT ( INT, FORMAT ( inv.BizDate, 'yyyyMM' ) ) AS RecordYearMonth,
	SUM ( purd.Qty * 12 * 0.9 ) AS QTY,
	N'立方米',
	CONVERT ( INT, FORMAT ( inv.BizDate, 'yyyyMM' ) ) AS CarbonFactorRecordYearMonth,
	12,
	0.9,
	P.WasterWaterCarbonFactor,
	P.WasterWaterCarbonFactorUnit,
	N'市政用水',
	SUM ( 12 * purd.Qty* 0.9 * P.WasterWaterCarbonFactor ) AS CarbonAmount,
	CONVERT ( DATE, GETDATE( ), 120 ) AS CalculateDate,
	inv.BillNo,
	inv.BizDate,
	invd.DeliveryNoteNo,
	invd.DeliveryDate,
	PM.billNo AS fubandan_no 
FROM
	purInvoice inv
	LEFT JOIN purInvoiceDetail invd ON inv.id = invd.BillId
	LEFT JOIN purDeliveryExpense purd ON purd.billid = invd.DeliveryNoteId
	LEFT JOIN basExpenseType bet ON bet.Id = purd.ExpenseTypeId
	LEFT JOIN purPaymentDetail PD ON PD.InvoiceId = INV.Id
	LEFT JOIN purPayment PM ON PM.Id = PD.BillId 
	LEFT JOIN sysOrganization SO ON SO.Id = inv.DepartmentId AND SO.IsDeleted = 0
	LEFT JOIN Tzh_ProjectInfo P ON P.Code = SO.Code AND P.IsDeleted = 0 
WHERE
	inv.IsDeleted = 0 
	AND invd.IsDeleted = 0 

	AND INV.BizDate >= P.StartDate 
	AND INV.BizDate < P.EndDate 
	AND bet.Descr IS NOT NULL 
	AND bet.Descr IN ( N'化糞缸清潔服務', N'泥漿清潔服務' ) 
	AND P.WasterWaterCarbonFactor IS NOT NULL 
	AND ( PD.isDeleted = 0 OR PD.isDeleted IS NULL ) 
	AND ( PM.isDeleted = 0 OR PM.isDeleted IS NULL )
	GROUP BY  P.Region,
	so.id,
	p.name,
	P.WasterWaterCarbonFactor,
	P.WasterWaterCarbonFactorUnit,
	inv.BillNo,
	inv.BizDate,
	invd.DeliveryNoteNo,
	invd.DeliveryDate,
	PM.billNo



DELETE FROM F_WasterWater WHERE CalculateDate = CONVERT(date,GETDATE(),120) 
INSERT INTO F_WasterWater 
(Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,Qty,QtyUnit,CarbonFactorRecordYearMonth,Volume,ProportionIndex,CarbonFactor,CarbonFactorUnit,Scope,CarbonAmount,CalculateDate)
SELECT
	P.Region,
	so.id,
	P.name,
	N'地盤',
	CONVERT ( INT, FORMAT ( inv.BizDate, 'yyyyMM' ) ) AS RecordYearMonth,
	SUM ( purd.Qty * 12 * 0.9 ) AS QTY,
	N'立方米',
	CONVERT ( INT, FORMAT ( inv.BizDate, 'yyyyMM' ) ) AS CarbonFactorRecordYearMonth,
	12,
	0.9,
	P.WasterWaterCarbonFactor,
	P.WasterWaterCarbonFactorUnit,
	N'市政用水',
	SUM ( 12 * purd.Qty* 0.9 * P.WasterWaterCarbonFactor ) AS CarbonAmount,
	CONVERT ( DATE, GETDATE( ), 120 ) AS CalculateDate 
FROM
	purInvoice inv
	LEFT JOIN purInvoiceDetail invd ON inv.id = invd.BillId
	LEFT JOIN purDeliveryExpense purd ON purd.billid = invd.DeliveryNoteId
	LEFT JOIN basExpenseType bet ON bet.Id = purd.ExpenseTypeId
	LEFT JOIN sysOrganization SO ON SO.Id = inv.DepartmentId AND SO.IsDeleted = 0 
	LEFT JOIN Tzh_ProjectInfo P ON P.Code = SO.Code AND P.IsDeleted = 0 
WHERE
	inv.IsDeleted = 0 
	AND invd.IsDeleted = 0 
	AND INV.BizDate >= P.StartDate 
	AND INV.BizDate < P.EndDate 
	AND bet.Descr IS NOT NULL 
	AND bet.Descr IN ( N'化糞缸清潔服務', N'泥漿清潔服務' ) 
	AND P.WasterWaterCarbonFactor IS NOT NULL 
GROUP BY
	P.Region,
	so.id,
	P.name,
	P.WasterWaterCarbonFactor,
	P.WasterWaterCarbonFactorUnit,
	inv.BizDate 
ORDER BY
	BizDate

--F_WasteTransportation
DELETE FROM F_WasteTransportation WHERE CalculateDate = CONVERT(date,GETDATE(),120) --AND RecordYearMonth = CONVERT(INT,FORMAT(@START_DATE,'yyyyMM')) AND SiteId =@SiteId

INSERT INTO F_WasteTransportation
(Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,[WasterType],[HandleMethod],[HandleLocation],TransportationType,Qty,QtyUnit,
TransportFactor,[TransportFactorUnit],TransportScope,TransportCarbonAmount,CalculateDate)
SELECT P.[Region],SO.[Id],P.[Name],A.[CarbonEmissionLocation],[RecordYearMonth],[WasterType],[HandleMethod],[HandleLocation],[TransportationType],[Qty],[Qty_Unit],
[TransportFactor],[TransportFactorUnit],[TransportScope], [Qty] * [TransportFactor] AS TransportCarbonAmount,
CONVERT(date,GETDATE(),120) AS CalculateDate
FROM [dbo].[F_CDMS_WasteTransportationCarbonFactor] A
LEFT JOIN Tzh_ProjectInfo P ON P.Name = A.SiteName AND P.IsDeleted = 0
LEFT JOIN sysOrganization SO ON P.Code = SO.Code AND SO.IsDeleted = 0
WHERE A.[IsDeleted] = 0

--F_CombineAll
DELETE FROM F_CombineAll WHERE CalculateDate= CONVERT(date,GETDATE(),120) AND MATERIALNAME NOT IN (N'電費',N'水費',N'地盤天然氣') --AND RecordYearMonth = CONVERT(INT,FORMAT(@START_DATE,'yyyyMM')) AND SiteId = @SiteId
INSERT INTO F_CombineAll (Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,MaterialCode,MaterialName,Qty,QtyUnit,Scope,CarbonFactor,CarbonFactorUnit,CarbonAmount,CarbonAmount_Unit,CalculateDate,Status)
SELECT Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,'' as MaterialCode,
N'污水處理' AS MaterialName, Qty, QtyUnit AS QtyUnit,Scope,CarbonFactor, CarbonFactorUnit,CarbonAmount,'kgco2e' AS CarbonAmount_Unit, CalculateDate,'NORMAL'
FROM [dbo].[F_WasterWater] WHERE CalculateDate = CONVERT(date,GETDATE(),120) --AND RecordYearMonth = CONVERT(INT,FORMAT(@START_DATE,'yyyyMM')) AND SiteId = @SiteId

INSERT INTO F_CombineAll (Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,MaterialCode,MaterialName,Qty,QtyUnit,Scope,CarbonFactor,CarbonFactorUnit,
CarbonAmount,CarbonAmount_Unit,TransportCarbonAmount,TransportScope,CalculateDate,Status)
SELECT Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,MaterialCode,
[ChineseName] AS MaterialName, Qty , Unit AS QtyUnit, Scope,CarbonFactor,CarbonFactorUnit,CarbonAmount,'kgco2e' AS CarbonAmount_Unit,TransportCarbonAmount,TransportScope, CalculateDate,'NORMAL'
FROM [dbo].F_Material WHERE CalculateDate = CONVERT(date,GETDATE(),120) --AND RecordYearMonth = CONVERT(INT,FORMAT(@START_DATE,'yyyyMM'))  AND SiteId = @SiteId

INSERT INTO F_CombineAll (Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,MaterialCode,MaterialName,Qty,QtyUnit,
TransportCarbonAmount,TransportScope,CalculateDate,Status)
SELECT Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,'' as MaterialCode,
N'廢棄物運輸' AS MaterialName, Qty, QtyUnit AS QtyUnit, TransportCarbonAmount,TransportScope, CalculateDate,'NORMAL'
FROM [dbo].[F_WasteTransportation] WHERE CalculateDate = CONVERT(date,GETDATE(),120) --AND RecordYearMonth = CONVERT(INT,FORMAT(@START_DATE,'yyyyMM')) AND SiteId = @SiteId

-- 针对中海外写字楼以及外部地盘的相关处理，先删除，后写入，支持重复运算逻辑
DELETE FROM F_CombineAll WHERE CalculateDate= CONVERT(date,GETDATE(),120) AND SiteId is NULL  --没有siteid的说法
INSERT INTO F_CombineAll (Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,MaterialCode,MaterialName,Qty,QtyUnit,Scope,CarbonFactor,CarbonFactorUnit,
CarbonAmount,CarbonAmount_Unit,TransportCarbonAmount,TransportScope,CalculateDate,Status)
SELECT  Region, NULL,SiteName,CarbonEmissionLocation,RecordYearMonth,MaterialCode, ChineseName, QtyTotal, Unit,Scope,CarbonFactor,CarbonFactorUnit,
CarbonAmountTotal, 'kgco2e', TransportCarbonAmountTotal, TransportScope, CalculateDate, 'NORMAL'
  from  [dbo].[Tzh_OtherSite] WHERE CalculateDate = CONVERT(date,GETDATE(),120) 




END





