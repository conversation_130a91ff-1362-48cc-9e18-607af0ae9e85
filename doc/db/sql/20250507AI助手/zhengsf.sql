
CREATE TABLE [dbo].[ai_carbon_emission_result] (
                                                   [id] nvarchar(254) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NOT NULL,
                                                   [organization_name] nvarchar(200) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                   [organization_no] nvarchar(200) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                   [parent_organization_name] nvarchar(200) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                   [platform_company] nvarchar(200) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                   [protocol] nvarchar(200) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                   [record_year_month] int  NULL,
                                                   [record_year] int  NULL,
                                                   [record_month] int  NULL,
                                                   [carbon_emission_location] nvarchar(100) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                   [carbon_emission_location_sc] nvarchar(100) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                   [carbon_emission_location_en] nvarchar(100) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                   [category_name] nvarchar(100) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                   [category_name_sc] nvarchar(100) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                   [category_name_en] nvarchar(100) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                   [sub_category_name] nvarchar(100) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                   [sub_category_name_sc] nvarchar(100) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                   [sub_category_name_en] nvarchar(100) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                   [carbon_amount] numeric(38,6)  NULL,
                                                   [unit] nvarchar(100) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                   [is_new_result] bit  NULL,
                                                   CONSTRAINT [PK_ai_carbon_emission_result] PRIMARY KEY CLUSTERED ([id])
                                                       WITH (PAD_INDEX = OFF, FILLFACTOR = 80, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
                                                       ON [PRIMARY]
)
    ON [PRIMARY]
GO

ALTER TABLE [dbo].[ai_carbon_emission_result] SET (LOCK_ESCALATION = TABLE)
GO

CREATE NONCLUSTERED INDEX [index_00001]
    ON [dbo].[ai_carbon_emission_result] (
                                          [organization_name] ASC
        )
    WITH (
        FILLFACTOR = 80
        )
GO

EXEC sp_addextendedproperty
     'MS_Description', N'项目名称',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'organization_name'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'项目编号',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'organization_no'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'上级组织',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'parent_organization_name'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'平台公司',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'platform_company'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'协议',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'protocol'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'记录年月',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'record_year_month'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'年份',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'record_year'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'月份',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'record_month'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'场景繁体',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'carbon_emission_location'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'场景简体',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'carbon_emission_location_sc'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'场景英文',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'carbon_emission_location_en'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'大类繁体',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'category_name'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'大类简体',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'category_name_sc'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'大类英文',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'category_name_en'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'小类繁体',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'sub_category_name'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'小类简体',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'sub_category_name_sc'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'小类英文',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'sub_category_name_en'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'总量/排放量',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'carbon_amount'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'单位',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'unit'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'是否新结果表（0 否 1 是）',
     'SCHEMA', N'dbo',
     'TABLE', N'ai_carbon_emission_result',
     'COLUMN', N'is_new_result'






--


CREATE TABLE [dbo].[t_ai_agent_conversation] (
                                                 [id] uniqueidentifier DEFAULT newid() NOT NULL,
                                                 [username] nvarchar(50) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS DEFAULT NULL NULL,
                                                 [app_conversation_id] nvarchar(50) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                 [conversation_name] nvarchar(100) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                 [creation_time] datetime2(7) DEFAULT getdate() NULL,
                                                 [create_username] nvarchar(50) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                 [create_user_id] uniqueidentifier  NULL,
                                                 [last_update_time] datetime2(7) DEFAULT getdate() NULL,
                                                 [last_update_username] nvarchar(50) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                 [last_update_user_id] uniqueidentifier  NULL,
                                                 [last_update_version] int DEFAULT 0 NULL,
                                                 [is_deleted] bit DEFAULT 0 NULL,
                                                 [is_manual_update] bit DEFAULT 0 NULL,
                                                 CONSTRAINT [PK_t_ai_agent_conversation] PRIMARY KEY CLUSTERED ([id])
                                                     WITH (PAD_INDEX = OFF, FILLFACTOR = 80, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
                                                     ON [PRIMARY]
)
    ON [PRIMARY]
GO

ALTER TABLE [dbo].[t_ai_agent_conversation] SET (LOCK_ESCALATION = TABLE)
GO

CREATE NONCLUSTERED INDEX [index_00001]
    ON [dbo].[t_ai_agent_conversation] (
                                        [username] ASC,
                                        [is_deleted] ASC
        )
    WITH (
        FILLFACTOR = 80
        )
GO

EXEC sp_addextendedproperty
     'MS_Description', N'用户名',
     'SCHEMA', N'dbo',
     'TABLE', N't_ai_agent_conversation',
     'COLUMN', N'username'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'会话ID',
     'SCHEMA', N'dbo',
     'TABLE', N't_ai_agent_conversation',
     'COLUMN', N'app_conversation_id'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'会话名称',
     'SCHEMA', N'dbo',
     'TABLE', N't_ai_agent_conversation',
     'COLUMN', N'conversation_name'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'是否手动修改标题标记',
     'SCHEMA', N'dbo',
     'TABLE', N't_ai_agent_conversation',
     'COLUMN', N'is_manual_update'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'AI-Agent会话列表',
     'SCHEMA', N'dbo',
     'TABLE', N't_ai_agent_conversation'







--

CREATE TABLE [dbo].[t_ai_agent_open_question] (
                                                  [id] uniqueidentifier DEFAULT newid() NOT NULL,
                                                  [question] nvarchar(100) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                  [seq] int  NULL,
                                                  [creation_time] datetime2(7) DEFAULT getdate() NULL,
                                                  [create_username] nvarchar(50) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                  [create_user_id] uniqueidentifier  NULL,
                                                  [last_update_time] datetime2(7) DEFAULT getdate() NULL,
                                                  [last_update_username] nvarchar(50) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
                                                  [last_update_user_id] uniqueidentifier  NULL,
                                                  [last_update_version] int DEFAULT 0 NULL,
                                                  [is_deleted] bit DEFAULT 0 NULL,
                                                  CONSTRAINT [PK_t_ai_agent_open_question] PRIMARY KEY CLUSTERED ([id])
                                                      WITH (PAD_INDEX = OFF, FILLFACTOR = 80, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
                                                      ON [PRIMARY]
)
    ON [PRIMARY]
GO

ALTER TABLE [dbo].[t_ai_agent_open_question] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
     'MS_Description', N'问题',
     'SCHEMA', N'dbo',
     'TABLE', N't_ai_agent_open_question',
     'COLUMN', N'question'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'序号',
     'SCHEMA', N'dbo',
     'TABLE', N't_ai_agent_open_question',
     'COLUMN', N'seq'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'AI助手-开场问题',
     'SCHEMA', N'dbo',
     'TABLE', N't_ai_agent_open_question'


-- 垂直越权

INSERT INTO [dbo].[t_operation] ([id], [code], [name], [description], [url], [method], [creation_time], [create_username], [create_user_id], [last_update_time], [last_update_username], [last_update_user_id]) VALUES (N'0EEB044B-23B7-4758-8EE2-120F5B5CE4D8', 0, NULL, N'获取开场问题', N'/api/hi-agent/queryOpenQuestion', N'POST', '2025-05-07 11:45:23.0196190', N'SystemUser', N'00000', '2025-05-07 11:45:23.0196240', N'SystemUser', N'00000');
INSERT INTO [dbo].[t_operation] ([id], [code], [name], [description], [url], [method], [creation_time], [create_username], [create_user_id], [last_update_time], [last_update_username], [last_update_user_id]) VALUES (N'3B9192D1-48DB-4693-A00B-DA2BBD469B34', 0, NULL, N'获取消息内容', N'/api/hi-agent/getMessageInfo', N'POST', '2025-05-07 11:45:23.0196190', N'SystemUser', N'00000', '2025-05-07 11:45:23.0196240', N'SystemUser', N'00000');
INSERT INTO [dbo].[t_operation] ([id], [code], [name], [description], [url], [method], [creation_time], [create_username], [create_user_id], [last_update_time], [last_update_username], [last_update_user_id]) VALUES (N'4D9B28AF-D541-4F87-9BCB-A509C25ECC36', 0, NULL, N'停止响应', N'/api/hi-agent/stopMessage', N'POST', '2025-05-07 11:45:23.0196190', N'SystemUser', N'00000', '2025-05-07 11:45:23.0196240', N'SystemUser', N'00000');
INSERT INTO [dbo].[t_operation] ([id], [code], [name], [description], [url], [method], [creation_time], [create_username], [create_user_id], [last_update_time], [last_update_username], [last_update_user_id]) VALUES (N'52DDB188-2E13-4A5C-BDEC-982B9A8C0178', 0, NULL, N'聊天查询(流式响应)', N'/api/hi-agent/chatQueryStream', N'POST', '2025-05-07 11:45:23.0196190', N'SystemUser', N'00000', '2025-05-07 11:45:23.0196240', N'SystemUser', N'00000');
INSERT INTO [dbo].[t_operation] ([id], [code], [name], [description], [url], [method], [creation_time], [create_username], [create_user_id], [last_update_time], [last_update_username], [last_update_user_id]) VALUES (N'8033F6CE-7403-472B-95E7-7ED769AA339E', 0, NULL, N'更新会话名称', N'/api/hi-agent/updateConversationName', N'POST', '2025-05-07 11:45:23.0196190', N'SystemUser', N'00000', '2025-05-07 11:45:23.0196240', N'SystemUser', N'00000');
INSERT INTO [dbo].[t_operation] ([id], [code], [name], [description], [url], [method], [creation_time], [create_username], [create_user_id], [last_update_time], [last_update_username], [last_update_user_id]) VALUES (N'8E404E96-3904-411B-9963-9AFE2C05A829', 0, NULL, N'批量删除', N'/api/hi-agent/batchRemoveConversation', N'POST', '2025-05-07 11:45:23.0196190', N'SystemUser', N'00000', '2025-05-07 11:45:23.0196240', N'SystemUser', N'00000');
INSERT INTO [dbo].[t_operation] ([id], [code], [name], [description], [url], [method], [creation_time], [create_username], [create_user_id], [last_update_time], [last_update_username], [last_update_user_id]) VALUES (N'A346A007-7307-41B1-A1B6-456F0160AEA0', 0, NULL, N'获取建议问题', N'/api/hi-agent/getSuggestedQuestions', N'POST', '2025-05-07 11:45:23.0196190', N'SystemUser', N'00000', '2025-05-07 11:45:23.0196240', N'SystemUser', N'00000');
INSERT INTO [dbo].[t_operation] ([id], [code], [name], [description], [url], [method], [creation_time], [create_username], [create_user_id], [last_update_time], [last_update_username], [last_update_user_id]) VALUES (N'A8688BAA-B9A2-417A-8F36-0936979C248F', 0, NULL, N'创建会话', N'/api/hi-agent/createConversation', N'POST', '2025-05-07 11:45:23.0196190', N'SystemUser', N'00000', '2025-05-07 11:45:23.0196240', N'SystemUser', N'00000');
INSERT INTO [dbo].[t_operation] ([id], [code], [name], [description], [url], [method], [creation_time], [create_username], [create_user_id], [last_update_time], [last_update_username], [last_update_user_id]) VALUES (N'B45BADEF-D0B4-4713-AFE8-8F8AD24C8E2C', 0, NULL, N'获取会话列表', N'/api/hi-agent/getConversationList', N'POST', '2025-05-07 11:45:23.0196190', N'SystemUser', N'00000', '2025-05-07 11:45:23.0196240', N'SystemUser', N'00000');
INSERT INTO [dbo].[t_operation] ([id], [code], [name], [description], [url], [method], [creation_time], [create_username], [create_user_id], [last_update_time], [last_update_username], [last_update_user_id]) VALUES (N'C505122F-1234-4E71-992B-4469618C692E', 0, NULL, N'聊天查询', N'/api/hi-agent/chatQuery', N'POST', '2025-05-07 11:45:23.0196190', N'SystemUser', N'00000', '2025-05-07 11:45:23.0196240', N'SystemUser', N'00000');
INSERT INTO [dbo].[t_operation] ([id], [code], [name], [description], [url], [method], [creation_time], [create_username], [create_user_id], [last_update_time], [last_update_username], [last_update_user_id]) VALUES (N'CF72EED4-A13E-4941-A28A-806A83C5AF2E', 0, NULL, N'获取会话消息', N'/api/hi-agent/getConversationMessages', N'POST', '2025-05-07 11:45:23.0196190', N'SystemUser', N'00000', '2025-05-07 11:45:23.0196240', N'SystemUser', N'00000');
INSERT INTO [dbo].[t_operation] ([id], [code], [name], [description], [url], [method], [creation_time], [create_username], [create_user_id], [last_update_time], [last_update_username], [last_update_user_id]) VALUES (N'E001BE31-DB2A-4F2D-B0E5-F115E23FB8B4', 0, NULL, N'重新生成聊天查询', N'/api/hi-agent/queryAgain', N'POST', '2025-05-07 11:45:23.0196190', N'SystemUser', N'00000', '2025-05-07 11:45:23.0196240', N'SystemUser', N'00000');
INSERT INTO [dbo].[t_operation] ([id], [code], [name], [description], [url], [method], [creation_time], [create_username], [create_user_id], [last_update_time], [last_update_username], [last_update_user_id]) VALUES (N'F3C23564-72F0-4C17-8129-F18244C8FEAF', 0, NULL, N'重新生成聊天查询(流式响应)', N'/api/hi-agent/queryAgainStream', N'POST', '2025-05-07 11:45:23.0196190', N'SystemUser', N'00000', '2025-05-07 11:45:23.0196240', N'SystemUser', N'00000');
INSERT INTO [dbo].[t_operation] ([id], [code], [name], [description], [url], [method], [creation_time], [create_username], [create_user_id], [last_update_time], [last_update_username], [last_update_user_id]) VALUES (N'FA35A7CE-B6EB-4F9D-8B14-45F0B2BF9F5A', 0, NULL, N'获取应用配置', N'/api/hi-agent/getAppConfig', N'POST', '2025-05-07 11:45:23.0196190', N'SystemUser', N'00000', '2025-05-07 11:45:23.0196240', N'SystemUser', N'00000');

