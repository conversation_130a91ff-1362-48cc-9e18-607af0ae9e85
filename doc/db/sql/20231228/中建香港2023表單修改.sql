-- 業務修改(中建香港底下,物資部以外)
SELECT ad.id FROM t_ambient_detail ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
 WHERE ad.seq < 31 AND ah.year = 2023 AND o.`no` LIKE '001%'
order by ad.head_id, ad.seq 



delete from t_ambient_detail 
where id in (
SELECT ad.id FROM t_ambient_detail ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
 WHERE ad.seq < 31 AND ah.year = 2023 AND o.`no` LIKE '001%'
 )
 
 insert into t_ambient_detail 
SELECT ad.* FROM t_ambient_detail_20231227 ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
 WHERE ad.seq < 31 AND ah.year = 2023 AND o.`no` LIKE '001%'
 and head_id = '8e56b457-c9da-4222-8f77-c40965fb8e46'
 
 
 
 -- 港交所修改(中建香港底下)
SELECT distinct ad.head_id FROM t_ambient_detail ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
 WHERE ah.year = 2023 AND o.`no` LIKE '001%'  and ad.unit_code in ('2DA') and ad.seq = 68
order by ad.head_id, ad.seq 


SELECT distinct ad.head_id FROM t_ambient_detail ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
WHERE ah.year = 2023 AND o.`no` LIKE '001%'  and ad.unit_code in ('2DA') and ad.seq = 38
order by ad.head_id, ad.seq 

SELECT ad.* FROM t_ambient_detail ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
 WHERE ah.year = 2023 AND o.`no` LIKE '001%'  and ad.unit_code in ('2DA', '2DB', '2DC') and ad.seq >= 68 and ad.seq <= 70
order by  ad.seq 

SELECT ad.* FROM t_ambient_detail ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
 WHERE ah.year = 2023 AND o.`no` LIKE '001%'  and ad.unit_code in ('2DA', '2DB', '2DC') and ad.seq >= 38 and ad.seq <= 40
order by ad.head_id, ad.seq 

SELECT ad.* FROM t_ambient_detail ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
WHERE ad.head_id = 'e79b715f-f73d-4bf9-aa0a-3dc8286d9fe8' and ad.unit_code in ('2DA', '2DB', '2DC')
order by ad.head_id, ad.seq 

SELECT ad.* FROM t_ambient_detail ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
WHERE ad.head_id = 'e79b715f-f73d-4bf9-aa0a-3dc8286d9fe8' and ad.unit_code in ('2DD', '2DE', '2DF', '2DG', '2DH')
order by ad.head_id, ad.seq 


update t_ambient_detail set type = '非惰性（金屬）', type2 = '廢金屬（例如，鋼筋、廢鐵等），回收循環再造（數據從運輸記録獲取）；'
where id in (
SELECT ad.id FROM t_ambient_detail ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
 WHERE ah.year = 2023 AND o.`no` LIKE '001%'  and ad.unit_code in ('2DA')
order by  ad.seq 
)

update t_ambient_detail set type = '非惰性（金屬）', type2 = '廢金屬（例如，鋼筋、廢鐵等），運往堆填區作處理（數據從地盤獲取）'
where id in (
SELECT ad.id FROM t_ambient_detail ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
 WHERE ah.year = 2023 AND o.`no` LIKE '001%'  and ad.unit_code in ('2DB')
order by  ad.seq 
)

update t_ambient_detail set type = '非惰性（竹枝）', type2 = '廢竹枝，回收循環再造（數據從運輸記録獲取）；'
where id in (
SELECT ad.id FROM t_ambient_detail ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
 WHERE ah.year = 2023 AND o.`no` LIKE '001%'  and ad.unit_code in ('2DC')
order by  ad.seq 
)

insert into t_ambient_detail 
SELECT UUID(), t.head_id, ad.category, ad.category_digest, ad.`type`, ad.type2, ad.unit, ad.unit_code, ad.month_value_1, ad.month_value_2, ad.month_value_3, ad.season_value_1, ad.month_value_4, ad.month_value_5, ad.month_value_6, ad.season_value_2, ad.month_value_7, ad.month_value_8, ad.month_value_9, ad.season_value_3, ad.month_value_10, ad.month_value_11, ad.month_value_12, ad.season_value_4, ad.year_total_value, ad.remark, ad.seq+64, curdate() FROM t_ambient_detail ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
cross join (
SELECT distinct ad.head_id FROM t_ambient_detail ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
 WHERE ah.year = 2023 AND o.`no` LIKE '001%'  and ad.unit_code in ('2DA') and ad.seq = 68
) t
WHERE ad.head_id = 'e79b715f-f73d-4bf9-aa0a-3dc8286d9fe8' and ad.unit_code in ('2DD', '2DE', '2DF', '2DG', '2DH')
order by t.head_id, ad.seq 

insert into t_ambient_detail 
SELECT UUID(), t.head_id, ad.category, ad.category_digest, ad.`type`, ad.type2, ad.unit, ad.unit_code, ad.month_value_1, ad.month_value_2, ad.month_value_3, ad.season_value_1, ad.month_value_4, ad.month_value_5, ad.month_value_6, ad.season_value_2, ad.month_value_7, ad.month_value_8, ad.month_value_9, ad.season_value_3, ad.month_value_10, ad.month_value_11, ad.month_value_12, ad.season_value_4, ad.year_total_value, ad.remark, ad.seq+34, curdate() FROM t_ambient_detail ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
cross join (
SELECT distinct ad.head_id FROM t_ambient_detail ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
 WHERE ah.year = 2023 AND o.`no` LIKE '001%'  and ad.unit_code in ('2DA') and ad.seq = 38
) t
WHERE ad.head_id = 'e79b715f-f73d-4bf9-aa0a-3dc8286d9fe8' and ad.unit_code in ('2DD', '2DE', '2DF', '2DG', '2DH')
order by t.head_id, ad.seq 

-- 等下來做
update t_ambient_detail  set seq = seq + 5
where head_id in (
SELECT distinct ad.head_id FROM t_ambient_detail ad
INNER JOIN t_ambient_head ah ON ad.head_id = ah.id
INNER JOIN t_organization o ON o.id = ah.organization_id AND o.is_deleted = 0
 WHERE ah.year = 2023 AND o.`no` LIKE '001%'  and ad.unit_code in ('2DA') and ad.seq = 68
order by  ad.seq 
)
and seq > 70
and unit_code not in ('2DD', '2DE', '2DF', '2DG', '2DH')