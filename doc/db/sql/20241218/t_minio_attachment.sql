-- 新增minio附件表
CREATE TABLE [dbo].[t_minio_attachment] (
    [id] nvarchar(254) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
    [ref_id] nvarchar(254) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
    [category] nvarchar(254) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
    [section] nvarchar(254) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
    [type] nvarchar(254) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
    [name] nvarchar(254) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
    [minio_file_name] nvarchar(254) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
    [minio_file_url] nvarchar(1000) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
    [creation_time] datetime2(7)  NULL,
    [create_username] nvarchar(254) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
    [create_user_id] nvarchar(254) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
    [last_update_time] datetime2(7)  NULL,
    [last_update_username] nvarchar(254) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL,
    [last_update_user_id] nvarchar(254) COLLATE Chinese_Hong_Kong_Stroke_90_CI_AS  NULL
)
    ON [PRIMARY]
GO

ALTER TABLE [dbo].[t_minio_attachment] SET (LOCK_ESCALATION = TABLE)
GO

CREATE NONCLUSTERED INDEX [missing_index_1761]
    ON [dbo].[t_minio_attachment] (
                                   [ref_id] ASC
        )
    INCLUDE ([id], [category], [section], [type], [name], [minio_file_name], [minio_file_url], [creation_time], [create_username], [create_user_id], [last_update_time], [last_update_username], [last_update_user_id])
    WITH (
        FILLFACTOR = 80
        )
GO

CREATE NONCLUSTERED INDEX [missing_index_1892]
    ON [dbo].[t_minio_attachment] (
                                   [ref_id] ASC
        )
    WITH (
        FILLFACTOR = 80
        )
GO

CREATE NONCLUSTERED INDEX [missing_index_1763]
    ON [dbo].[t_minio_attachment] (
                                   [ref_id] ASC
        )
    INCLUDE ([id], [category], [section], [type], [name], [minio_file_name], [minio_file_url], [creation_time], [create_username], [create_user_id], [last_update_time], [last_update_username], [last_update_user_id])
    WITH (
        FILLFACTOR = 80
        )
GO

CREATE NONCLUSTERED INDEX [missing_index_1910]
    ON [dbo].[t_minio_attachment] (
                                   [id] ASC
        )
    WITH (
        FILLFACTOR = 80
        )