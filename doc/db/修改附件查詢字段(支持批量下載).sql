
UPDATE t_attachment SET section = N'經營信息'
  WHERE ref_id in (select convert(nvarchar(36), id) from t_ce_basic_info_head) 
  
UPDATE t_attachment SET section = N'碳排識別'
  WHERE ref_id in (select convert(nvarchar(36), id) from t_ce_identification_head) 

UPDATE t_attachment SET section = N'環境績效'
  WHERE ref_id in (select convert(nvarchar(36), id) from t_ambient_head)

  --TODO:更新ref_id
UPDATE t_attachment SET section = N'環境績效'
  WHERE ref_id in (select convert(nvarchar(36), id) from t_ambient_energy_bill)
  
UPDATE t_attachment SET section = N'分判商固定源'
  WHERE ref_id in (select convert(nvarchar(36), id) from t_ff_cm_fixed_head) 

UPDATE t_attachment SET section = N'分判商移動源'
  WHERE ref_id in (select convert(nvarchar(36), id) from t_ff_cm_mobile_head) 
  
  --TODO:更新ref_id
UPDATE t_attachment SET section = N'車輛用油'
  WHERE ref_id in (select convert(nvarchar(36), id) from t_ab_vehicle_fuel_usage) 
  
  --TODO:更新ref_id
UPDATE t_attachment SET section = N'商務旅行'
  WHERE ref_id in (select convert(nvarchar(36), id) from t_ab_business_trip) 

UPDATE t_attachment SET section = N'減排和其他'
  WHERE ref_id in (select convert(nvarchar(36), id) from t_emission_reduction_head) 

  -- 根據主表查詢附件
SELECT [id]
      ,[ref_id]
      ,[category]
      ,[section]
      ,[type]
      ,[name]
      ,[creation_time]
      ,[create_username]
      ,[create_user_id]
      ,[last_update_time]
      ,[last_update_username]
      ,[last_update_user_id]
  FROM [ESG].[dbo].[t_attachment]
  WHERE ref_id in (select convert(nvarchar(36), id) from t_emission_reduction_info)