
    /**
     * 验证 token, 通过token获取当前登录用户信息并设置到session中
     *
     * @param sourceToken
     */
    private boolean checkTokenAndSetContext(HttpServletRequest request, HttpServletResponse response,String sourceToken) {
        try {
            if (StringUtils.isBlank(sourceToken)) {
                return false;
            }

            String oriToken = StringUtils.trim(sourceToken);
            oriToken = CommonUtils.removePrefixForToken(oriToken);

            UserSessionService userSessionService = SpringContextUtil.getBean(UserSessionService.class);
            UserSession userSession = userSessionService.getUserSessionByTokenOrAccessToken(oriToken);

            if (userSession == null) {
                return false;
            }

            setRequestContext(userSession);

            // 檢查權限
            Map<String, List<String>> routeUriMap = new HashMap<>();

            //菜单管理
            List<String> authorityMenu = new ArrayList<>();
            authorityMenu.add("/api/menu/tree/list");
            authorityMenu.add("/api/menu/save");
            authorityMenu.add("/api/menu/delete");
            routeUriMap.put("authority/menu", authorityMenu);

            //角色管理
            List<String> authorityRoleManagement = new ArrayList<>();
            authorityRoleManagement.add("/api/role/save");
            authorityRoleManagement.add("/api/role/list");
            authorityRoleManagement.add("/api/role/delete");
            authorityRoleManagement.add("/api/role/get");
            authorityRoleManagement.add("/api/menu/tree/list");
            routeUriMap.put("authority/role-management", authorityRoleManagement);

            //用户管理
            List<String> authorityUserInfo = new ArrayList<>();
            authorityUserInfo.add("/user/delete");
            authorityUserInfo.add("/user/get");
            authorityUserInfo.add("/user/save");
            authorityUserInfo.add("/user/list");
            authorityUserInfo.add("/user/list/detail");
            authorityUserInfo.add("/user/list/save");
            authorityUserInfo.add("/api/role/list");
            authorityUserInfo.add("/api/organization/list");
            routeUriMap.put("authority/user-info", authorityUserInfo);

            //组织架构管理
            List<String> authorityIndex = new ArrayList<>();
            authorityIndex.add("/api/organization/deletetree");
            authorityIndex.add("/api/organization");
            authorityIndex.add("/api/organization/list");
            authorityIndex.add("/api/organization/list-by-parent");
            authorityIndex.add("/api/organization/save");
            authorityIndex.add("/api/organization/savelist");
            authorityIndex.add("/api/cf/v1/area/list");
            routeUriMap.put("authority/index", authorityIndex);

            //流程记录
            List<String> authorityExamine = new ArrayList<>();
            authorityExamine.add("/api/auditnode/list");
            authorityExamine.add("/api/form/list");
            authorityExamine.add("/api/workflow/control/setUrgentEditable");
            authorityExamine.add("/api/workflow/control/recall");
            authorityExamine.add("/api/organization/list-my-orgs");
            authorityExamine.add("/api/workflow/control/list");
            authorityExamine.add("/api/once-token/fetch");
            routeUriMap.put("authority/examine", authorityExamine);

            //审核设置
            List<String> authorityExamineSetting = new ArrayList<>();
            authorityExamineSetting.add("/api/auditsetting/delete");
            authorityExamineSetting.add("/api/form/list");
            authorityExamineSetting.add("/api/organization/list-all");
            authorityExamineSetting.add("/user/list");
            authorityExamineSetting.add("/api/workflow/delete");
            authorityExamineSetting.add("/api/workflow/get");
            authorityExamineSetting.add("/api/workflow/list");
            authorityExamineSetting.add("/api/workflow/save");
            routeUriMap.put("authority/examine-setting", authorityExamineSetting);

            //經營信息
            List<String> businessInformation = new ArrayList<>();
            businessInformation.add("/api/auditsetting/delete");
            businessInformation.add("/api/form/list");
            businessInformation.add("/api/organization/list-all");
            businessInformation.add("/user/list");
            businessInformation.add("/api/workflow/delete");
            businessInformation.add("/api/workflow/get");
            businessInformation.add("/api/workflow/list");
            businessInformation.add("/api/workflow/save");
            routeUriMap.put("business-Information", businessInformation);

            //碳排識別	
            List<String> carbonEmissionRecognition = new ArrayList<>();
            carbonEmissionRecognition.add("/api/auditsetting/delete");
            carbonEmissionRecognition.add("/api/form/list");
            carbonEmissionRecognition.add("/api/organization/list-all");
            carbonEmissionRecognition.add("/user/list");
            carbonEmissionRecognition.add("/api/workflow/delete");
            carbonEmissionRecognition.add("/api/workflow/get");
            carbonEmissionRecognition.add("/api/workflow/list");
            carbonEmissionRecognition.add("/api/workflow/save");
            routeUriMap.put("carbon-emission-recognition", carbonEmissionRecognition);


            List<String> routePathLst = getRoutePath(userSession.getUsername());
            for(String routePath : routePathLst) {
                List<String> uriList = routeUriMap.get(routePath);
                for(String uri : uriList) {
                    String requestUri = request.getRequestURI();
                    if(requestUri.toLowerCase().contains(uri.toLowerCase())) {
                        return true;
                    }
                }
            }

            logger.error(userSession.getUsername() + " 沒有權限 : " + request.getRequestURI());
            return false;
        } catch (Exception e) {
            logger.error("验证token出错", e);
            return false;
        }
    }